# Postman Socket.IO Testing Guide

This guide provides step-by-step instructions for testing all Socket.IO functionalities using Postman's Socket.IO client.

## Prerequisites & Setup

1. Ensure the server is running and Socket.IO is reachable at `ws://localhost:3000`
2. Obtain valid JWT tokens by following the authentication flow:
   * Call `/api/auth/send-otp` with a phone number
   * Call `/api/auth/verify-otp` with the received OTP
   * Copy the `accessToken` from the response
3. In Postman (Desktop), create an **Environment** with variables:
   * `WS_URL` = `ws://localhost:3000`
   * `USER_A_TOKEN`, `USER_B_TOKEN` = valid JWTs for each user (the `accessToken` from `/verify-otp`)
   * `CONVERSATION_ID` = an existing conversation id

## Create Two Socket.IO Tabs (One Per User)

1. In Postman: **New → Socket.IO Request**. Enter `{{WS_URL}}`. Click **Connect**.
2. Repeat to open a second tab for the second user.
3. In each tab, add authentication by opening **Headers** and adding:
   * **User A Tab**: `Authorization: Bearer {{USER_A_TOKEN}}`
   * **User B Tab**: `Authorization: Bearer {{USER_B_TOKEN}}`

## Add Listeners (Both Tabs)

In each Socket.IO tab, click **Add Listener** and add the following event names:

* `connected`, `error`, `disconnect`
* `message:new`
* `message:read`
* `typing:start`, `typing:stop`

## Socket.IO Event Handlers

The following Socket.IO event handlers are available:

### `messages:conversations:list` - Get User's Conversations
Get a list of all conversations for the authenticated user.

**Parameters:**
```json
{
  "page": 1,
  "limit": 20
}
```

**Callback Response:**
```json
{
  "success": true,
  "message": "Conversations retrieved successfully",
  "conversations": [...],
  "pagination": {...}
}
```

### `messages:conversation:get` - Get Conversation Messages
Get messages in a specific conversation and join the conversation room.

**Parameters:**
```json
{
  "conversationId": "uuid",
  "page": 1,
  "limit": 50
}
```

**Callback Response:**
```json
{
  "success": true,
  "message": "Messages retrieved successfully",
  "messages": [...],
  "pagination": {...}
}
```

### `messages:message:send` - Send a Message
Send a new message in a conversation.

**Parameters:**
```json
{
  "conversationId": "uuid",
  "text": "Hello world!",
  "messageType": "text"
}
```

**Callback Response:**
```json
{
  "success": true,
  "message": "Message sent successfully",
  "messageData": {...}
}
```

### `messages:message:read` - Mark Message as Read
Mark a message as read.

**Parameters:**
```json
{
  "messageId": "uuid"
}
```

**Callback Response:**
```json
{
  "success": true,
  "message": "Message marked as read"
}
```

### `messages:typing` - Send Typing Indicator
Send a typing indicator for a conversation.

**Parameters:**
```json
{
  "conversationId": "uuid"
}
```

**No callback response** - broadcasts `typing:start` and `typing:stop` events automatically.

### Server Events

These events are sent from the server to clients:

* `message:new` - A new message was sent in a conversation you're in
* `message:read` - A message was marked as read
* `typing:start` - A user started typing in a conversation
* `typing:stop` - A user stopped typing in a conversation
* `connected` - Successfully connected to Socket.IO
* `error` - An error occurred
* `disconnect` - Disconnected from Socket.IO

## Join the Same Conversation Room

To join a conversation room, get the conversation messages:

From **User A** tab, click **Send**, set:
* **Event name:** `messages:conversation:get`
* **Arguments (JSON):**

```json
{ 
  "conversationId": "{{CONVERSATION_ID}}",
  "page": 1,
  "limit": 50
}
```

Send the same message from **User B** tab.

## Send a Message (A → B)

From **User A** tab, click **Send**:
* **Event name:** `messages:message:send`
* **Arguments (JSON):**

```json
{
  "conversationId": "{{CONVERSATION_ID}}",
  "text": "Hey there 👋",
  "messageType": "text"
}
```

**Expected:** 
- **User A** receives a response with the saved message
- **User B** receives a `message:new` event with the message

## Mark as Read (B → A)

From **User B** tab, click **Send**:
* **Event name:** `messages:message:read`
* **Arguments (JSON):**

```json
{
  "messageId": "<paste id from message:new event>"
}
```

**Expected:** **User A** receives a `message:read` event.

## Typing Indicators

From **User A** tab, click **Send**:
* **Event name:** `messages:typing`
* **Arguments (JSON):**

```json
{
  "conversationId": "{{CONVERSATION_ID}}"
}
```

**Expected:** 
- **User B** sees a `typing:start` event immediately
- **User B** sees a `typing:stop` event after 3 seconds (automatically sent by server)

## Reconnect & Presence Sanity Checks

* Click **Disconnect** and then **Connect** in one tab to ensure your server re-authenticates the socket
* After reconnecting, you'll need to rejoin the conversation by sending the `messages:conversation:get` event again

## Additional Events to Test

### Get Conversations List

From either tab, click **Send**:
* **Event name:** `messages:conversations:list`
* **Arguments (JSON):**

```json
{ 
  "page": 1,
  "limit": 20
}
```

### Get Conversation Messages

From either tab, click **Send**:
* **Event name:** `messages:conversation:get`
* **Arguments (JSON):**

```json
{ 
  "conversationId": "{{CONVERSATION_ID}}",
  "page": 1,
  "limit": 50
}
```

## Notes

1. All events follow the pattern `namespace:event:action` (e.g., `messages:message:send`)
2. Authentication is handled through the `Authorization` header with Bearer tokens
3. Room joining happens automatically when you call `messages:conversation:get`
4. Typing indicators automatically stop after 3 seconds
5. Message broadcasting happens to both the conversation room and the receiver's user room
6. Callback functions are optional - events will still process correctly without them
7. The server gracefully handles missing callbacks without crashing

## Troubleshooting

1. **Connection Issues**: Verify the server is running and the `WS_URL` is correct
2. **Authentication Failures**: Ensure JWT tokens are valid and not expired. The token should be the `accessToken` from the `/verify-otp` response
3. **No Events Received**: Check that both users are in the same conversation room by calling `messages:conversation:get`
4. **CORS Errors**: Ensure the server CORS configuration allows connections from Postman
5. **Server Crashes**: If the server crashes, check logs for error messages and ensure all required parameters are provided

This guide allows you to validate all join, send, receive, typing, and read flows end-to-end using two tabs acting as two users.