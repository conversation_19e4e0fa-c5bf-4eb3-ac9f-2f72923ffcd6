'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import TopAppBar from '@/components/TopAppBar';
import BottomNavigation from '@/components/BottomNavigation';
import OfflineBanner from '@/components/shared/OfflineBanner';
import { ErrorBoundary } from '@/components/shared/ErrorBoundary';

export default function LayoutContent({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const isOnboarding = pathname?.startsWith('/onboarding');

  if (isOnboarding) {
    return (
      <ErrorBoundary>
        <div className="mobile-container">
          {children}
        </div>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary>
      <div className="mobile-container">
        <TopAppBar />
        <OfflineBanner />
        <main className="pt-16 pb-20 min-h-screen">
          {children}
        </main>
        <BottomNavigation />
      </div>
    </ErrorBoundary>
  );
}
