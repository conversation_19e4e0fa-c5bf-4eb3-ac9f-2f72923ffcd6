'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  HomeIcon, 
  ChatBubbleLeftRightIcon, 
  BriefcaseIcon, 
  CurrencyDollarIcon,
  UserIcon 
} from '@heroicons/react/24/outline';
import { 
  HomeIcon as HomeIconSolid, 
  ChatBubbleLeftRightIcon as ChatIconSolid, 
  BriefcaseIcon as BriefcaseIconSolid, 
  CurrencyDollarIcon as CurrencyIconSolid,
  UserIcon as UserIconSolid 
} from '@heroicons/react/24/solid';
import { useTheme } from '@/contexts/ThemeContext';
import { clsx } from 'clsx';

interface NavItem {
  href: string;
  label: string;
  workerLabel: string;
  posterLabel: string;
  icon: React.ComponentType<{ className?: string }>;
  activeIcon: React.ComponentType<{ className?: string }>;
}

const navItems: NavItem[] = [
  {
    href: '/',
    label: 'Home',
    workerLabel: 'Home',
    posterLabel: 'Home',
    icon: HomeIcon,
    activeIcon: HomeIconSolid,
  },
  {
    href: '/messages',
    label: 'Messages',
    workerLabel: 'Messages',
    posterLabel: 'Messages',
    icon: ChatBubbleLeftRightIcon,
    activeIcon: ChatIconSolid,
  },
  {
    href: '/jobs',
    label: 'My Jobs',
    workerLabel: 'My Jobs',
    posterLabel: 'My Jobs',
    icon: BriefcaseIcon,
    activeIcon: BriefcaseIconSolid,
  },
  {
    href: '/earnings',
    label: 'Earnings',
    workerLabel: 'Earnings',
    posterLabel: 'Payments',
    icon: CurrencyDollarIcon,
    activeIcon: CurrencyIconSolid,
  },
  {
    href: '/profile',
    label: 'Profile',
    workerLabel: 'Profile',
    posterLabel: 'Profile',
    icon: UserIcon,
    activeIcon: UserIconSolid,
  },
];

export default function BottomNavigation() {
  const pathname = usePathname();
  const { mode } = useTheme();

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 safe-area-bottom z-50">
      <div className="mobile-container">
        <div className="flex justify-around items-center py-2">
          {navItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = isActive ? item.activeIcon : item.icon;
            const label = mode === 'worker' ? item.workerLabel : item.posterLabel;

            return (
              <Link
                key={item.href}
                href={item.href}
                className={clsx(
                  'flex flex-col items-center justify-center min-w-[44px] min-h-[44px] px-2 py-1 rounded-lg transition-colors',
                  'hover:bg-gray-50 active:bg-gray-100',
                  isActive && mode === 'worker' && 'text-blue-600',
                  isActive && mode === 'poster' && 'text-green-600',
                  !isActive && 'text-gray-500'
                )}
              >
                <Icon className="w-6 h-6 mb-1" />
                <span className="text-xs font-medium leading-none">
                  {label}
                </span>
              </Link>
            );
          })}
        </div>
      </div>
    </nav>
  );
}
