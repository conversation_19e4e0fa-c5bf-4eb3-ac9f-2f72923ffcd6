'use client';

import React, { useState } from 'react';
import { BellIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { useTheme } from '@/contexts/ThemeContext';
import { clsx } from 'clsx';
import ModeSwitcher from '@/components/shared/ModeSwitcher';
import PersonaSelector from '@/components/shared/PersonaSelector';
import NotificationsCenter from '@/components/shared/NotificationsCenter';
import GlobalSearch from '@/components/shared/GlobalSearch';

interface TopAppBarProps {
  title?: string;
  showModeSwitch?: boolean;
  showPersonaChip?: boolean;
  currentPersona?: string;
  onPersonaClick?: () => void;
}

export default function TopAppBar({
  title = "Ozgaar",
  showModeSwitch = true,
  showPersonaChip = true,
  currentPersona = "Plumber",
  onPersonaClick
}: TopAppBarProps) {
  const { mode, toggleMode } = useTheme();
  const [showModeSwitcher, setShowModeSwitcher] = useState(false);
  const [showPersonaSelector, setShowPersonaSelector] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [selectedPersona, setSelectedPersona] = useState(currentPersona);
  const { setMode } = useTheme();

  return (
    <header className="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 safe-area-top z-50">
      <div className="mobile-container">
        <div className="flex items-center justify-between px-4 py-3">
          {/* Left side - Title and Mode Switch */}
          <div className="flex items-center space-x-3">
            <h1 className="text-lg font-semibold text-gray-900">
              {title}
            </h1>
            
            {showModeSwitch && (
              <div className="flex items-center">
                <button
                  onClick={() => setShowModeSwitcher(true)}
                  className={clsx(
                    'flex items-center rounded-full px-3 py-1.5 text-sm font-medium transition-colors',
                    'border border-gray-300 bg-white hover:bg-gray-50',
                    mode === 'worker' && 'border-blue-300 bg-blue-50 text-blue-700',
                    mode === 'poster' && 'border-green-300 bg-green-50 text-green-700'
                  )}
                >
                  <div className={clsx(
                    'w-2 h-2 rounded-full mr-2',
                    mode === 'worker' && 'bg-blue-600',
                    mode === 'poster' && 'bg-green-600'
                  )} />
                  {mode === 'worker' ? 'Worker' : 'Poster'}
                </button>
              </div>
            )}
          </div>

          {/* Center - Persona Chip (Worker only) */}
          {showPersonaChip && mode === 'worker' && (
            <button
              onClick={() => setShowPersonaSelector(true)}
              className="flex items-center px-3 py-1.5 bg-blue-100 text-blue-800 rounded-full text-sm font-medium hover:bg-blue-200 transition-colors"
            >
              <span className="w-2 h-2 bg-blue-600 rounded-full mr-2" />
              {selectedPersona}
            </button>
          )}

          {/* Right side - Search and Notification */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowSearch(true)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors min-w-[44px] min-h-[44px] flex items-center justify-center"
            >
              <MagnifyingGlassIcon className="w-6 h-6" />
            </button>

            <button
              onClick={() => setShowNotifications(true)}
              className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors min-w-[44px] min-h-[44px] flex items-center justify-center"
            >
              <BellIcon className="w-6 h-6" />
              {/* Notification badge */}
              <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                3
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Shared Components */}
      <ModeSwitcher
        isOpen={showModeSwitcher}
        onClose={() => setShowModeSwitcher(false)}
        currentMode={mode}
        onModeChange={(newMode) => {
          // In real app, this would update the global mode state
          setMode(newMode);
          console.log('Mode changed to:', newMode);
        }}
      />

      <PersonaSelector
        isOpen={showPersonaSelector}
        onClose={() => setShowPersonaSelector(false)}
        currentPersona={selectedPersona}
        onPersonaChange={(personaId) => {
          setSelectedPersona(personaId);
          // In real app, this would update the global persona state
          console.log('Persona changed to:', personaId);
        }}
      />

      <NotificationsCenter
        isOpen={showNotifications}
        onClose={() => setShowNotifications(false)}
      />

      <GlobalSearch
        isOpen={showSearch}
        onClose={() => setShowSearch(false)}
        searchContext={mode}
      />
    </header>
  );
}
