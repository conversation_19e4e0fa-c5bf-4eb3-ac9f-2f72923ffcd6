'use client';

import React from 'react';
import { 
  MagnifyingGlassIcon,
  BriefcaseIcon,
  ChatBubbleLeftRightIcon,
  CurrencyRupeeIcon,
  UserGroupIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

export type EmptyStateType = 
  | 'no_jobs' 
  | 'no_applications' 
  | 'no_messages' 
  | 'no_earnings' 
  | 'no_workers' 
  | 'no_search_results'
  | 'no_notifications'
  | 'connection_error'
  | 'no_posted_jobs';

interface EmptyStateProps {
  type: EmptyStateType;
  title?: string;
  description?: string;
  actionLabel?: string;
  onAction?: () => void;
  illustration?: React.ReactNode;
  className?: string;
}

const EMPTY_STATE_CONFIG: Record<EmptyStateType, {
  icon: React.ComponentType<any>;
  title: string;
  description: string;
  actionLabel?: string;
  illustration: string;
  color: string;
}> = {
  no_jobs: {
    icon: BriefcaseIcon,
    title: 'No jobs available right now',
    description: 'New opportunities are posted daily. Check back soon or adjust your search filters to find more jobs.',
    actionLabel: 'Refresh Jobs',
    illustration: '🔍',
    color: 'blue'
  },
  no_applications: {
    icon: BriefcaseIcon,
    title: 'No applications yet',
    description: 'Start applying to jobs that match your skills. The more you apply, the better your chances!',
    actionLabel: 'Find Jobs',
    illustration: '📝',
    color: 'blue'
  },
  no_messages: {
    icon: ChatBubbleLeftRightIcon,
    title: 'No messages yet',
    description: 'When you apply to jobs or get hired, your conversations with posters will appear here.',
    illustration: '💬',
    color: 'purple'
  },
  no_earnings: {
    icon: CurrencyRupeeIcon,
    title: 'No earnings yet',
    description: 'Complete your first job to start earning! Your payment history and earnings will be tracked here.',
    actionLabel: 'Find Jobs',
    illustration: '💰',
    color: 'green'
  },
  no_workers: {
    icon: UserGroupIcon,
    title: 'No workers found',
    description: 'Try expanding your search area or posting a job to attract workers to apply.',
    actionLabel: 'Post a Job',
    illustration: '👥',
    color: 'blue'
  },
  no_search_results: {
    icon: MagnifyingGlassIcon,
    title: 'No results found',
    description: 'Try different keywords, check your spelling, or broaden your search criteria.',
    actionLabel: 'Clear Filters',
    illustration: '🔍',
    color: 'gray'
  },
  no_notifications: {
    icon: BriefcaseIcon,
    title: 'All caught up!',
    description: 'You have no new notifications. We\'ll notify you about job updates, messages, and payments.',
    illustration: '✅',
    color: 'green'
  },
  connection_error: {
    icon: ExclamationTriangleIcon,
    title: 'Connection problem',
    description: 'Please check your internet connection and try again. Your data will sync when you\'re back online.',
    actionLabel: 'Try Again',
    illustration: '📡',
    color: 'red'
  },
  no_posted_jobs: {
    icon: BriefcaseIcon,
    title: 'No jobs posted yet',
    description: 'Post your first job to find skilled workers in your area. It only takes a few minutes!',
    actionLabel: 'Post a Job',
    illustration: '📋',
    color: 'green'
  }
};

export default function EmptyState({
  type,
  title,
  description,
  actionLabel,
  onAction,
  illustration,
  className = ''
}: EmptyStateProps) {
  const config = EMPTY_STATE_CONFIG[type];
  const Icon = config.icon;

  const displayTitle = title || config.title;
  const displayDescription = description || config.description;
  const displayActionLabel = actionLabel || config.actionLabel;
  const displayIllustration = illustration || config.illustration;

  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600',
    purple: 'bg-purple-50 text-purple-600',
    green: 'bg-green-50 text-green-600',
    gray: 'bg-gray-50 text-gray-600',
    red: 'bg-red-50 text-red-600'
  };

  const buttonColorClasses = {
    blue: 'bg-blue-600 hover:bg-blue-700 text-white',
    purple: 'bg-purple-600 hover:bg-purple-700 text-white',
    green: 'bg-green-600 hover:bg-green-700 text-white',
    gray: 'bg-gray-600 hover:bg-gray-700 text-white',
    red: 'bg-red-600 hover:bg-red-700 text-white'
  };

  return (
    <div className={`flex flex-col items-center justify-center py-12 px-4 text-center ${className}`}>
      {/* Illustration */}
      <div className={`w-20 h-20 rounded-full flex items-center justify-center mb-6 ${colorClasses[config.color as keyof typeof colorClasses]}`}>
        {typeof displayIllustration === 'string' ? (
          <span className="text-4xl">{displayIllustration}</span>
        ) : (
          displayIllustration || <Icon className="w-10 h-10" />
        )}
      </div>

      {/* Content */}
      <div className="max-w-sm space-y-4">
        <h3 className="text-xl font-semibold text-gray-900">
          {displayTitle}
        </h3>
        
        <p className="text-gray-600 leading-relaxed">
          {displayDescription}
        </p>

        {/* Action Button */}
        {displayActionLabel && onAction && (
          <button
            onClick={onAction}
            className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-colors ${
              buttonColorClasses[config.color as keyof typeof buttonColorClasses]
            }`}
          >
            {type === 'connection_error' ? (
              <ArrowPathIcon className="w-5 h-5 mr-2" />
            ) : type === 'no_posted_jobs' || type === 'no_workers' ? (
              <PlusIcon className="w-5 h-5 mr-2" />
            ) : (
              <MagnifyingGlassIcon className="w-5 h-5 mr-2" />
            )}
            {displayActionLabel}
          </button>
        )}
      </div>

      {/* Tips (for specific types) */}
      {(type === 'no_jobs' || type === 'no_search_results') && (
        <div className="mt-8 p-4 bg-gray-50 rounded-lg max-w-md">
          <h4 className="font-medium text-gray-900 mb-2">💡 Tips to find more jobs:</h4>
          <ul className="text-sm text-gray-600 space-y-1 text-left">
            <li>• Expand your search radius</li>
            <li>• Try different skill keywords</li>
            <li>• Check both urgent and regular jobs</li>
            <li>• Complete your profile for better matches</li>
          </ul>
        </div>
      )}

      {type === 'no_applications' && (
        <div className="mt-8 p-4 bg-blue-50 rounded-lg max-w-md">
          <h4 className="font-medium text-blue-900 mb-2">🚀 Get started:</h4>
          <ul className="text-sm text-blue-800 space-y-1 text-left">
            <li>• Complete your profile and add skills</li>
            <li>• Upload a profile photo</li>
            <li>• Apply to jobs that match your experience</li>
            <li>• Respond quickly to increase your chances</li>
          </ul>
        </div>
      )}

      {type === 'no_workers' && (
        <div className="mt-8 p-4 bg-green-50 rounded-lg max-w-md">
          <h4 className="font-medium text-green-900 mb-2">📢 Attract more workers:</h4>
          <ul className="text-sm text-green-800 space-y-1 text-left">
            <li>• Offer competitive rates</li>
            <li>• Write clear job descriptions</li>
            <li>• Add photos of the work area</li>
            <li>• Respond quickly to applications</li>
          </ul>
        </div>
      )}
    </div>
  );
}

// Specialized empty state components
export function NoJobsFound({ onRefresh }: { onRefresh?: () => void }) {
  return (
    <EmptyState
      type="no_jobs"
      onAction={onRefresh}
    />
  );
}

export function NoSearchResults({ onClearFilters }: { onClearFilters?: () => void }) {
  return (
    <EmptyState
      type="no_search_results"
      onAction={onClearFilters}
    />
  );
}

export function ConnectionError({ onRetry }: { onRetry?: () => void }) {
  return (
    <EmptyState
      type="connection_error"
      onAction={onRetry}
    />
  );
}

export function NoApplications({ onFindJobs }: { onFindJobs?: () => void }) {
  return (
    <EmptyState
      type="no_applications"
      onAction={onFindJobs}
    />
  );
}
