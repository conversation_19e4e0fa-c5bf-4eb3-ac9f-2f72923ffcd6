'use client';

import React, { useState, useEffect, useRef } from 'react';
import { 
  MagnifyingGlassIcon,
  XMarkIcon,
  ClockIcon,
  MapPinIcon,
  CurrencyRupeeIcon,
  UserIcon,
  BriefcaseIcon,
  StarIcon
} from '@heroicons/react/24/outline';

interface SearchResult {
  id: string;
  type: 'job' | 'worker';
  title: string;
  subtitle: string;
  location?: string;
  rate?: number;
  rateType?: 'hourly' | 'daily' | 'fixed';
  rating?: number;
  distance?: number;
  avatar?: string;
  isUrgent?: boolean;
  postedAt?: string;
}

interface GlobalSearchProps {
  isOpen: boolean;
  onClose: () => void;
  searchContext: 'worker' | 'poster'; // Different contexts show different results
}

// Mock search results
const MOCK_JOB_RESULTS: SearchResult[] = [
  {
    id: '1',
    type: 'job',
    title: 'Kitchen Sink Repair Required',
    subtitle: 'Plumber',
    location: 'Powai, Mumbai',
    rate: 150,
    rateType: 'hourly',
    distance: 1.2,
    isUrgent: true,
    postedAt: '2025-08-25T08:00:00Z'
  },
  {
    id: '2',
    type: 'job',
    title: 'AC Installation Work',
    subtitle: 'Electrician',
    location: 'Andheri, Mumbai',
    rate: 2000,
    rateType: 'fixed',
    distance: 2.5,
    isUrgent: false,
    postedAt: '2025-08-24T14:30:00Z'
  }
];

const MOCK_WORKER_RESULTS: SearchResult[] = [
  {
    id: '1',
    type: 'worker',
    title: 'Rajesh Kumar',
    subtitle: 'Plumber • 8+ years experience',
    location: 'Powai, Mumbai',
    rate: 140,
    rateType: 'hourly',
    rating: 4.8,
    distance: 1.2,
    avatar: '👨‍🔧'
  },
  {
    id: '2',
    type: 'worker',
    title: 'Sunita Patel',
    subtitle: 'Cleaner • 6+ years experience',
    location: 'Bandra, Mumbai',
    rate: 800,
    rateType: 'daily',
    rating: 4.9,
    distance: 3.1,
    avatar: '👩‍🧹'
  }
];

const RECENT_SEARCHES = [
  'Plumber near me',
  'AC repair',
  'House cleaning',
  'Electrician Mumbai'
];

export default function GlobalSearch({ isOpen, onClose, searchContext }: GlobalSearchProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showRecent, setShowRecent] = useState(true);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    if (query.trim().length === 0) {
      setResults([]);
      setShowRecent(true);
      return;
    }

    setShowRecent(false);
    setIsSearching(true);

    // Simulate search delay
    const searchTimeout = setTimeout(() => {
      // Mock search logic based on context
      const mockResults = searchContext === 'worker' 
        ? MOCK_JOB_RESULTS.filter(job => 
            job.title.toLowerCase().includes(query.toLowerCase()) ||
            job.subtitle.toLowerCase().includes(query.toLowerCase())
          )
        : MOCK_WORKER_RESULTS.filter(worker => 
            worker.title.toLowerCase().includes(query.toLowerCase()) ||
            worker.subtitle.toLowerCase().includes(query.toLowerCase())
          );

      setResults(mockResults);
      setIsSearching(false);
    }, 300);

    return () => clearTimeout(searchTimeout);
  }, [query, searchContext]);

  const handleResultClick = (result: SearchResult) => {
    // In real app, this would navigate to the appropriate page
    if (result.type === 'job') {
      console.log(`Navigate to job: /jobs/${result.id}`);
    } else {
      console.log(`Navigate to worker: /workers/${result.id}/profile`);
    }
    onClose();
  };

  const handleRecentSearchClick = (searchTerm: string) => {
    setQuery(searchTerm);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-white z-50">
      {/* Header */}
      <div className="flex items-center space-x-3 p-4 border-b border-gray-200">
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder={
              searchContext === 'worker' 
                ? 'Search for jobs, skills, locations...'
                : 'Search for workers, skills, services...'
            }
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <XMarkIcon className="w-6 h-6 text-gray-600" />
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Recent Searches */}
        {showRecent && (
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Recent Searches</h3>
            <div className="space-y-2">
              {RECENT_SEARCHES.map((search, index) => (
                <button
                  key={index}
                  onClick={() => handleRecentSearchClick(search)}
                  className="flex items-center space-x-3 w-full p-3 hover:bg-gray-50 rounded-lg transition-colors text-left"
                >
                  <ClockIcon className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-700">{search}</span>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Search Results */}
        {!showRecent && (
          <div className="p-4">
            {isSearching ? (
              <div className="text-center py-8">
                <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
                <p className="text-gray-600">Searching...</p>
              </div>
            ) : results.length === 0 && query.trim().length > 0 ? (
              <div className="text-center py-8">
                <MagnifyingGlassIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                <p className="text-gray-600">
                  Try different keywords or check your spelling
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-gray-700">
                  {results.length} result{results.length !== 1 ? 's' : ''} for "{query}"
                </h3>
                
                {results.map((result) => (
                  <button
                    key={result.id}
                    onClick={() => handleResultClick(result)}
                    className="w-full p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all text-left"
                  >
                    <div className="flex items-start space-x-3">
                      {/* Icon/Avatar */}
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                        result.type === 'job' 
                          ? 'bg-blue-100' 
                          : 'bg-green-100'
                      }`}>
                        {result.type === 'job' ? (
                          <BriefcaseIcon className={`w-6 h-6 ${
                            result.type === 'job' ? 'text-blue-600' : 'text-green-600'
                          }`} />
                        ) : result.avatar ? (
                          <span className="text-2xl">{result.avatar}</span>
                        ) : (
                          <UserIcon className="w-6 h-6 text-green-600" />
                        )}
                      </div>
                      
                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-1">
                          <h4 className="font-medium text-gray-900 truncate">
                            {result.title}
                            {result.isUrgent && (
                              <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                                Urgent
                              </span>
                            )}
                          </h4>
                          {result.rate && (
                            <span className="text-sm font-semibold text-gray-900 ml-2">
                              ₹{result.rate}
                              {result.rateType !== 'fixed' && `/${result.rateType === 'hourly' ? 'hr' : 'day'}`}
                            </span>
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2">{result.subtitle}</p>
                        
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          {result.location && (
                            <div className="flex items-center space-x-1">
                              <MapPinIcon className="w-3 h-3" />
                              <span>{result.location}</span>
                            </div>
                          )}
                          
                          {result.distance && (
                            <span>{result.distance} km away</span>
                          )}
                          
                          {result.rating && (
                            <div className="flex items-center space-x-1">
                              <StarIcon className="w-3 h-3 text-yellow-400 fill-current" />
                              <span>{result.rating}</span>
                            </div>
                          )}
                          
                          {result.postedAt && (
                            <span>
                              {new Date(result.postedAt).toLocaleDateString('en-IN', {
                                day: 'numeric',
                                month: 'short'
                              })}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Search Tips */}
      {showRecent && (
        <div className="p-4 bg-gray-50 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Search Tips</h4>
          <div className="text-xs text-gray-600 space-y-1">
            {searchContext === 'worker' ? (
              <>
                <p>• Try "plumber near me" or "urgent AC repair"</p>
                <p>• Use location names like "Bandra" or "Andheri"</p>
                <p>• Search by skill: "electrician", "cleaner", "painter"</p>
              </>
            ) : (
              <>
                <p>• Search by name: "Rajesh Kumar" or "Sunita"</p>
                <p>• Try skills: "experienced plumber" or "verified cleaner"</p>
                <p>• Use location: "plumber in Powai"</p>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
