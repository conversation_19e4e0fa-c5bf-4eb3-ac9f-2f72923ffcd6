'use client';

import React from 'react';

interface SkeletonProps {
  className?: string;
  width?: string;
  height?: string;
  rounded?: boolean;
  animate?: boolean;
}

// Base Skeleton Component
export function Skeleton({ 
  className = '', 
  width = 'w-full', 
  height = 'h-4', 
  rounded = false,
  animate = true 
}: SkeletonProps) {
  return (
    <div 
      className={`bg-gray-200 ${width} ${height} ${rounded ? 'rounded-full' : 'rounded'} ${
        animate ? 'animate-pulse' : ''
      } ${className}`}
    />
  );
}

// Job Card Skeleton
export function JobCardSkeleton() {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-3">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex-1 space-y-2">
          <Skeleton width="w-3/4" height="h-5" />
          <div className="flex items-center space-x-2">
            <Skeleton width="w-16" height="h-4" />
            <Skeleton width="w-1" height="h-1" rounded />
            <Skeleton width="w-24" height="h-4" />
          </div>
        </div>
        <Skeleton width="w-8" height="h-8" rounded />
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Skeleton width="w-full" height="h-4" />
        <Skeleton width="w-5/6" height="h-4" />
      </div>

      {/* Tags */}
      <div className="flex items-center space-x-2">
        <Skeleton width="w-16" height="h-6" />
        <Skeleton width="w-20" height="h-6" />
        <Skeleton width="w-12" height="h-4" />
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between pt-2">
        <div className="flex items-center space-x-4">
          <Skeleton width="w-20" height="h-5" />
          <Skeleton width="w-24" height="h-4" />
        </div>
        <Skeleton width="w-16" height="h-4" />
      </div>
    </div>
  );
}

// Message Skeleton
export function MessageSkeleton({ isOwn = false }: { isOwn?: boolean }) {
  return (
    <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`flex items-start space-x-2 max-w-xs ${isOwn ? 'flex-row-reverse space-x-reverse' : ''}`}>
        {!isOwn && <Skeleton width="w-8" height="h-8" rounded />}
        <div className={`space-y-1 ${isOwn ? 'items-end' : 'items-start'} flex flex-col`}>
          <Skeleton width="w-32" height="h-10" className="rounded-2xl" />
          <Skeleton width="w-16" height="h-3" />
        </div>
      </div>
    </div>
  );
}

// Profile Skeleton
export function ProfileSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <Skeleton width="w-24" height="h-24" rounded className="mx-auto" />
        <div className="space-y-2">
          <Skeleton width="w-32" height="h-6" className="mx-auto" />
          <Skeleton width="w-40" height="h-4" className="mx-auto" />
          <Skeleton width="w-28" height="h-4" className="mx-auto" />
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-gray-50 p-4 rounded-lg text-center space-y-2">
          <Skeleton width="w-8" height="h-8" className="mx-auto" />
          <Skeleton width="w-20" height="h-4" className="mx-auto" />
        </div>
        <div className="bg-gray-50 p-4 rounded-lg text-center space-y-2">
          <Skeleton width="w-8" height="h-8" className="mx-auto" />
          <Skeleton width="w-16" height="h-4" className="mx-auto" />
        </div>
      </div>

      {/* Menu Items */}
      <div className="space-y-2">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg">
            <div className="flex items-center space-x-3">
              <Skeleton width="w-6" height="h-6" />
              <Skeleton width="w-24" height="h-4" />
            </div>
            <Skeleton width="w-4" height="h-4" />
          </div>
        ))}
      </div>
    </div>
  );
}

// List Skeleton
export function ListSkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="flex items-center space-x-3 p-4 bg-white border border-gray-200 rounded-lg">
          <Skeleton width="w-12" height="h-12" rounded />
          <div className="flex-1 space-y-2">
            <Skeleton width="w-3/4" height="h-4" />
            <Skeleton width="w-1/2" height="h-3" />
          </div>
          <Skeleton width="w-16" height="h-4" />
        </div>
      ))}
    </div>
  );
}

// Search Results Skeleton
export function SearchResultsSkeleton() {
  return (
    <div className="space-y-4">
      {/* Search header */}
      <div className="flex items-center justify-between">
        <Skeleton width="w-32" height="h-5" />
        <Skeleton width="w-20" height="h-4" />
      </div>

      {/* Results */}
      <div className="space-y-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="p-4 bg-white border border-gray-200 rounded-lg">
            <div className="flex items-start space-x-3">
              <Skeleton width="w-12" height="h-12" rounded />
              <div className="flex-1 space-y-2">
                <div className="flex items-center justify-between">
                  <Skeleton width="w-48" height="h-5" />
                  <Skeleton width="w-16" height="h-4" />
                </div>
                <Skeleton width="w-32" height="h-4" />
                <div className="flex items-center space-x-4">
                  <Skeleton width="w-20" height="h-3" />
                  <Skeleton width="w-16" height="h-3" />
                  <Skeleton width="w-12" height="h-3" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Notification Skeleton
export function NotificationSkeleton() {
  return (
    <div className="space-y-3">
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="p-4 hover:bg-gray-50">
          <div className="flex items-start space-x-3">
            <Skeleton width="w-10" height="h-10" rounded />
            <div className="flex-1 space-y-2">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-1">
                  <Skeleton width="w-40" height="h-4" />
                  <Skeleton width="w-full" height="h-4" />
                  <Skeleton width="w-24" height="h-3" />
                </div>
                <div className="flex items-center space-x-2 ml-2">
                  <Skeleton width="w-4" height="h-4" />
                  <Skeleton width="w-4" height="h-4" />
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

// Page Loading Skeleton
export function PageLoadingSkeleton() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <Skeleton width="w-24" height="h-6" />
          <div className="flex items-center space-x-2">
            <Skeleton width="w-8" height="h-8" rounded />
            <Skeleton width="w-8" height="h-8" rounded />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 space-y-6">
        <div className="space-y-4">
          <Skeleton width="w-48" height="h-8" />
          <Skeleton width="w-full" height="h-4" />
        </div>

        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <JobCardSkeleton key={i} />
          ))}
        </div>
      </div>
    </div>
  );
}

// Loading Screen with Logo
export function LoadingScreen({ message = 'Loading...' }: { message?: string }) {
  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center space-y-6">
        {/* Logo */}
        <div className="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mx-auto">
          <span className="text-white font-bold text-xl">R</span>
        </div>
        
        {/* Loading Animation */}
        <div className="flex items-center justify-center space-x-2">
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
        </div>
        
        <p className="text-gray-600 font-medium">{message}</p>
      </div>
    </div>
  );
}
