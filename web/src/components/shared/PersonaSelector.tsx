'use client';

import React, { useState } from 'react';
import { 
  XMarkIcon,
  CheckCircleIcon,
  PlusIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';

interface Persona {
  id: string;
  skill: string;
  rate: number;
  rateType: 'hourly' | 'daily';
  experience: string;
  completedJobs: number;
  rating: number;
  reviewCount: number;
  isActive: boolean;
  isVerified: boolean;
}

interface PersonaSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  currentPersona: string | null;
  onPersonaChange: (personaId: string) => void;
}

// Mock worker personas data
const MOCK_PERSONAS: Persona[] = [
  {
    id: 'plumber',
    skill: 'Plumber',
    rate: 140,
    rateType: 'hourly',
    experience: '8+ years',
    completedJobs: 156,
    rating: 4.8,
    reviewCount: 127,
    isActive: true,
    isVerified: true
  },
  {
    id: 'electrician',
    skill: 'Electrician',
    rate: 160,
    rateType: 'hourly',
    experience: '5+ years',
    completedJobs: 89,
    rating: 4.7,
    reviewCount: 76,
    isActive: true,
    isVerified: true
  },
  {
    id: 'cleaner',
    skill: 'Cleaner',
    rate: 600,
    rateType: 'daily',
    experience: '3+ years',
    completedJobs: 45,
    rating: 4.5,
    reviewCount: 32,
    isActive: false,
    isVerified: false
  }
];

export default function PersonaSelector({ 
  isOpen, 
  onClose, 
  currentPersona, 
  onPersonaChange 
}: PersonaSelectorProps) {
  const [selectedPersona, setSelectedPersona] = useState(currentPersona);

  const handlePersonaSelect = (personaId: string) => {
    const persona = MOCK_PERSONAS.find(p => p.id === personaId);
    if (!persona?.isActive) {
      // Show activation prompt
      const shouldActivate = window.confirm(
        `Your ${persona?.skill} profile is inactive. Activate it to start receiving job matches?`
      );
      if (shouldActivate) {
        // In real app, this would call API to activate persona
        persona!.isActive = true;
      } else {
        return;
      }
    }
    
    setSelectedPersona(personaId);
    onPersonaChange(personaId);
    onClose();
  };

  const handleAddPersona = () => {
    onClose();
    // In real app, this would navigate to add persona flow
    alert('Add new skill feature coming soon!');
  };

  if (!isOpen) return null;

  const activePersonas = MOCK_PERSONAS.filter(p => p.isActive);
  const inactivePersonas = MOCK_PERSONAS.filter(p => !p.isActive);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end">
      <div className={`w-full bg-white rounded-t-2xl transform transition-transform duration-300 ${
        isOpen ? 'translate-y-0' : 'translate-y-full'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Select Your Skill</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Content */}
        <div className="max-h-96 overflow-y-auto">
          {/* Active Personas */}
          {activePersonas.length > 0 && (
            <div className="p-4 space-y-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Active Skills</h3>
              {activePersonas.map((persona) => (
                <button
                  key={persona.id}
                  onClick={() => handlePersonaSelect(persona.id)}
                  className={`w-full p-4 rounded-xl border-2 text-left transition-all ${
                    selectedPersona === persona.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-semibold text-gray-900">{persona.skill}</h4>
                        {persona.isVerified && (
                          <CheckCircleIcon className="w-4 h-4 text-green-500" />
                        )}
                        {selectedPersona === persona.id && (
                          <div className="w-2 h-2 bg-blue-600 rounded-full" />
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                        <span>₹{persona.rate}/{persona.rateType === 'hourly' ? 'hr' : 'day'}</span>
                        <span>{persona.experience}</span>
                        <span>{persona.completedJobs} jobs</span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <StarSolidIcon className="w-4 h-4 text-yellow-400" />
                        <span className="text-sm font-medium text-gray-900">{persona.rating}</span>
                        <span className="text-sm text-gray-600">({persona.reviewCount})</span>
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* Inactive Personas */}
          {inactivePersonas.length > 0 && (
            <div className="p-4 border-t border-gray-200 space-y-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Inactive Skills</h3>
              {inactivePersonas.map((persona) => (
                <button
                  key={persona.id}
                  onClick={() => handlePersonaSelect(persona.id)}
                  className="w-full p-4 rounded-xl border-2 border-gray-200 text-left transition-all hover:border-gray-300 opacity-60"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-semibold text-gray-900">{persona.skill}</h4>
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                          Inactive
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                        <span>₹{persona.rate}/{persona.rateType === 'hourly' ? 'hr' : 'day'}</span>
                        <span>{persona.experience}</span>
                        <span>{persona.completedJobs} jobs</span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <StarSolidIcon className="w-4 h-4 text-yellow-400" />
                        <span className="text-sm font-medium text-gray-900">{persona.rating}</span>
                        <span className="text-sm text-gray-600">({persona.reviewCount})</span>
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* Add New Persona */}
          <div className="p-4 border-t border-gray-200">
            <button
              onClick={handleAddPersona}
              className="w-full p-4 rounded-xl border-2 border-dashed border-gray-300 text-center transition-all hover:border-blue-400 hover:bg-blue-50"
            >
              <div className="flex items-center justify-center space-x-2">
                <PlusIcon className="w-5 h-5 text-gray-400" />
                <span className="text-gray-600 font-medium">Add New Skill</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Expand your earning potential with more skills
              </p>
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 bg-gray-50 rounded-t-2xl border-t border-gray-200">
          <p className="text-xs text-gray-600 text-center">
            Jobs will be filtered based on your selected skill. You can switch anytime.
          </p>
        </div>
      </div>
    </div>
  );
}
