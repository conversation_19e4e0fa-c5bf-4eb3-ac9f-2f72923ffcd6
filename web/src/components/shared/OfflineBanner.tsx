'use client';

import React, { useState, useEffect } from 'react';
import { 
  WifiIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface QueuedAction {
  id: string;
  type: 'job_application' | 'message' | 'payment' | 'profile_update';
  description: string;
  timestamp: Date;
}

interface OfflineBannerProps {
  isVisible?: boolean;
  queuedActions?: QueuedAction[];
  onDismiss?: () => void;
}

export default function OfflineBanner({ 
  isVisible = true, 
  queuedActions = [], 
  onDismiss 
}: OfflineBannerProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    // Check initial state
    setIsOnline(navigator.onLine);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Mock queued actions for demo
  const mockQueuedActions: QueuedAction[] = [
    {
      id: '1',
      type: 'job_application',
      description: 'Apply to Kitchen Sink Repair job',
      timestamp: new Date(Date.now() - 5 * 60 * 1000) // 5 minutes ago
    },
    {
      id: '2',
      type: 'message',
      description: 'Send message to Rajesh Kumar',
      timestamp: new Date(Date.now() - 2 * 60 * 1000) // 2 minutes ago
    },
    {
      id: '3',
      type: 'profile_update',
      description: 'Update profile bio',
      timestamp: new Date(Date.now() - 1 * 60 * 1000) // 1 minute ago
    }
  ];

  const displayActions = queuedActions.length > 0 ? queuedActions : mockQueuedActions;

  if (isOnline || !isVisible) {
    return null;
  }

  return (
    <div className="fixed top-16 left-0 right-0 z-40 bg-orange-500 text-white shadow-lg">
      <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <WifiIcon className="w-5 h-5" />
              <span className="font-medium">You're offline</span>
            </div>
            
            {displayActions.length > 0 && (
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="flex items-center space-x-1 bg-orange-600 px-2 py-1 rounded-full text-sm hover:bg-orange-700 transition-colors"
              >
                <ClockIcon className="w-4 h-4" />
                <span>{displayActions.length} queued</span>
              </button>
            )}
          </div>

          {onDismiss && (
            <button
              onClick={onDismiss}
              className="p-1 hover:bg-orange-600 rounded-full transition-colors"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          )}
        </div>

        <p className="text-sm text-orange-100 mt-1">
          Your actions will be saved and synced when you're back online
        </p>

        {/* Queued Actions Details */}
        {showDetails && displayActions.length > 0 && (
          <div className="mt-3 bg-orange-600 rounded-lg p-3">
            <h4 className="text-sm font-medium mb-2">Queued Actions</h4>
            <div className="space-y-2">
              {displayActions.map((action) => (
                <div key={action.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      action.type === 'job_application' ? 'bg-blue-300' :
                      action.type === 'message' ? 'bg-purple-300' :
                      action.type === 'payment' ? 'bg-green-300' :
                      'bg-gray-300'
                    }`} />
                    <span className="text-orange-100">{action.description}</span>
                  </div>
                  <span className="text-orange-200 text-xs">
                    {Math.floor((Date.now() - action.timestamp.getTime()) / 60000)}m ago
                  </span>
                </div>
              ))}
            </div>
            <div className="mt-2 pt-2 border-t border-orange-500">
              <p className="text-xs text-orange-200">
                These actions will be processed automatically when you reconnect
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Connection Status Indicator */}
      <div className="h-1 bg-orange-600">
        <div className="h-full bg-orange-300 animate-pulse" style={{ width: '30%' }} />
      </div>
    </div>
  );
}

// Hook for offline detection
export function useOfflineStatus() {
  const [isOffline, setIsOffline] = useState(false);

  useEffect(() => {
    const handleOnline = () => setIsOffline(false);
    const handleOffline = () => setIsOffline(true);

    setIsOffline(!navigator.onLine);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return isOffline;
}
