'use client';

import React, { useState } from 'react';
import { 
  MapPinIcon,
  CameraIcon,
  BellIcon,
  DevicePhoneMobileIcon,
  CheckCircleIcon,
  XMarkIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export type PermissionType = 'location' | 'camera' | 'notifications' | 'storage';

interface PermissionEducationProps {
  permissionType: PermissionType;
  isOpen: boolean;
  onClose: () => void;
  onAllow: () => void;
  onDeny: () => void;
}

const PERMISSION_CONFIG = {
  location: {
    icon: MapPinIcon,
    title: 'Location Access',
    subtitle: 'Help us find jobs near you',
    benefits: [
      'See jobs within your preferred distance',
      'Get accurate travel time estimates',
      'Auto-fill your location in job applications',
      'Receive location-based job alerts'
    ],
    explanation: 'We use your location to show you the most relevant nearby jobs and help posters find workers in their area.',
    privacy: 'Your exact location is never shared. We only show your general area (like "Powai, Mumbai") to others.',
    required: true
  },
  camera: {
    icon: CameraIcon,
    title: 'Camera Access',
    subtitle: 'Capture work photos and update profile',
    benefits: [
      'Take before/after photos for jobs',
      'Update your profile picture',
      'Submit photo proof of completed work',
      'Build trust with visual documentation'
    ],
    explanation: 'Camera access helps you document your work and build credibility with posters through visual proof.',
    privacy: 'Photos are only used when you choose to take them. You control what gets shared.',
    required: false
  },
  notifications: {
    icon: BellIcon,
    title: 'Notification Access',
    subtitle: 'Stay updated on jobs and messages',
    benefits: [
      'Get notified about new job matches',
      'Receive messages from posters instantly',
      'Know when payments are processed',
      'Stay informed about job status updates'
    ],
    explanation: 'Notifications help you respond quickly to opportunities and stay connected with your work.',
    privacy: 'You can customize which notifications you receive in Settings. We only send essential updates.',
    required: false
  },
  storage: {
    icon: DevicePhoneMobileIcon,
    title: 'Storage Access',
    subtitle: 'Save photos and documents',
    benefits: [
      'Save work photos to your gallery',
      'Store important documents offline',
      'Keep backup of your portfolio',
      'Access files when offline'
    ],
    explanation: 'Storage access lets you save important work-related files and access them anytime.',
    privacy: 'We only access files you explicitly choose to share or save through the app.',
    required: false
  }
};

export default function PermissionEducation({
  permissionType,
  isOpen,
  onClose,
  onAllow,
  onDeny
}: PermissionEducationProps) {
  const [showDetails, setShowDetails] = useState(false);
  const config = PERMISSION_CONFIG[permissionType];
  const Icon = config.icon;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="relative p-6 text-center border-b border-gray-100">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-500" />
          </button>
          
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon className="w-8 h-8 text-blue-600" />
          </div>
          
          <h2 className="text-xl font-bold text-gray-900 mb-2">{config.title}</h2>
          <p className="text-gray-600">{config.subtitle}</p>
          
          {config.required && (
            <div className="mt-3 flex items-center justify-center space-x-2 text-orange-600">
              <ExclamationTriangleIcon className="w-4 h-4" />
              <span className="text-sm font-medium">Required for core features</span>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Benefits */}
          <div className="mb-6">
            <h3 className="font-semibold text-gray-900 mb-3">Why we need this:</h3>
            <div className="space-y-2">
              {config.benefits.map((benefit, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <CheckCircleIcon className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700 text-sm">{benefit}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Explanation */}
          <div className="mb-6">
            <p className="text-gray-700 text-sm leading-relaxed">
              {config.explanation}
            </p>
          </div>

          {/* Privacy Details */}
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="w-full text-left mb-6"
          >
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <span className="font-medium text-gray-900">Privacy & Security</span>
              <div className={`transform transition-transform ${showDetails ? 'rotate-180' : ''}`}>
                <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </button>

          {showDetails && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg className="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium text-blue-900 mb-1">Your privacy matters</h4>
                  <p className="text-blue-800 text-sm leading-relaxed">
                    {config.privacy}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={onAllow}
              className="w-full bg-blue-600 text-white py-4 px-6 rounded-xl font-semibold hover:bg-blue-700 transition-colors"
            >
              Allow {config.title}
            </button>
            
            {!config.required && (
              <button
                onClick={onDeny}
                className="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-xl font-medium hover:bg-gray-200 transition-colors"
              >
                Not Now
              </button>
            )}
            
            {config.required && (
              <p className="text-center text-sm text-gray-500">
                This permission is required to use Ozgaar effectively
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Permission Manager Hook
export function usePermissionManager() {
  const [currentPermission, setCurrentPermission] = useState<PermissionType | null>(null);
  const [permissionStatus, setPermissionStatus] = useState<Record<PermissionType, 'granted' | 'denied' | 'prompt'>>({
    location: 'prompt',
    camera: 'prompt',
    notifications: 'prompt',
    storage: 'prompt'
  });

  const requestPermission = async (type: PermissionType) => {
    setCurrentPermission(type);
    
    try {
      let result: PermissionState = 'prompt';
      
      switch (type) {
        case 'location':
          if ('geolocation' in navigator) {
            await new Promise((resolve, reject) => {
              navigator.geolocation.getCurrentPosition(resolve, reject);
            });
            result = 'granted';
          }
          break;
          
        case 'camera':
          if ('mediaDevices' in navigator) {
            await navigator.mediaDevices.getUserMedia({ video: true });
            result = 'granted';
          }
          break;
          
        case 'notifications':
          if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            result = permission as PermissionState;
          }
          break;
          
        case 'storage':
          // Storage is usually granted by default on web
          result = 'granted';
          break;
      }
      
      setPermissionStatus(prev => ({ ...prev, [type]: result }));
      return result;
    } catch (error) {
      setPermissionStatus(prev => ({ ...prev, [type]: 'denied' }));
      return 'denied';
    }
  };

  const closePermissionDialog = () => {
    setCurrentPermission(null);
  };

  return {
    currentPermission,
    permissionStatus,
    requestPermission,
    closePermissionDialog,
    showPermissionDialog: (type: PermissionType) => setCurrentPermission(type)
  };
}
