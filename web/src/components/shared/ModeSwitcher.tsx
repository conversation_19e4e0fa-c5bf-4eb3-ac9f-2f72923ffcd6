'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { 
  XMarkIcon,
  UserIcon,
  BriefcaseIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface ModeSwitcherProps {
  isOpen: boolean;
  onClose: () => void;
  currentMode: 'worker' | 'poster';
  onModeChange: (mode: 'worker' | 'poster') => void;
}

// Mock user profile completeness data
const MOCK_PROFILE_STATUS = {
  worker: {
    isComplete: true,
    completionPercentage: 85,
    missingFields: ['portfolio', 'certifications']
  },
  poster: {
    isComplete: false,
    completionPercentage: 60,
    missingFields: ['company_info', 'payment_method']
  }
};

export default function ModeSwitcher({ 
  isOpen, 
  onClose, 
  currentMode, 
  onModeChange 
}: ModeSwitcherProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isAnimating, setIsAnimating] = useState(false);

  const handleModeSwitch = async (newMode: 'worker' | 'poster') => {
    if (newMode === currentMode) {
      onClose();
      return;
    }

    const profileStatus = MOCK_PROFILE_STATUS[newMode];
    
    // Check if profile is incomplete
    if (!profileStatus.isComplete) {
      // Show warning and redirect to profile completion
      const shouldContinue = window.confirm(
        `Your ${newMode} profile is ${profileStatus.completionPercentage}% complete. ` +
        `Complete your profile to access all features. Continue anyway?`
      );
      
      if (!shouldContinue) {
        onClose();
        return;
      }
    }

    setIsAnimating(true);
    
    // Simulate theme switching animation
    setTimeout(() => {
      onModeChange(newMode);
      
      // Navigate to appropriate home based on mode
      const targetPath = newMode === 'worker' ? '/' : '/poster-home';
      if (pathname !== targetPath) {
        router.push(targetPath);
      }
      
      setIsAnimating(false);
      onClose();
    }, 300);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end">
      <div className={`w-full bg-white rounded-t-2xl transform transition-transform duration-300 ${
        isOpen ? 'translate-y-0' : 'translate-y-full'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Switch Mode</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Mode Options */}
        <div className="p-4 space-y-4">
          {/* Worker Mode */}
          <button
            onClick={() => handleModeSwitch('worker')}
            disabled={isAnimating}
            className={`w-full p-4 rounded-xl border-2 text-left transition-all ${
              currentMode === 'worker'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
            } ${isAnimating ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <div className="flex items-start space-x-4">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                currentMode === 'worker' ? 'bg-blue-600' : 'bg-blue-100'
              }`}>
                <UserIcon className={`w-6 h-6 ${
                  currentMode === 'worker' ? 'text-white' : 'text-blue-600'
                }`} />
              </div>
              
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h3 className="font-semibold text-gray-900">Worker Mode</h3>
                  {currentMode === 'worker' && (
                    <CheckCircleIcon className="w-5 h-5 text-blue-600" />
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  Find jobs, apply with your skills, and earn money
                </p>
                
                {/* Profile Status */}
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    MOCK_PROFILE_STATUS.worker.isComplete ? 'bg-green-500' : 'bg-yellow-500'
                  }`} />
                  <span className="text-xs text-gray-500">
                    Profile {MOCK_PROFILE_STATUS.worker.completionPercentage}% complete
                  </span>
                  {!MOCK_PROFILE_STATUS.worker.isComplete && (
                    <ExclamationTriangleIcon className="w-4 h-4 text-yellow-500" />
                  )}
                </div>
              </div>
            </div>
          </button>

          {/* Poster Mode */}
          <button
            onClick={() => handleModeSwitch('poster')}
            disabled={isAnimating}
            className={`w-full p-4 rounded-xl border-2 text-left transition-all ${
              currentMode === 'poster'
                ? 'border-green-500 bg-green-50'
                : 'border-gray-200 hover:border-green-300 hover:bg-green-50'
            } ${isAnimating ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <div className="flex items-start space-x-4">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                currentMode === 'poster' ? 'bg-green-600' : 'bg-green-100'
              }`}>
                <BriefcaseIcon className={`w-6 h-6 ${
                  currentMode === 'poster' ? 'text-white' : 'text-green-600'
                }`} />
              </div>
              
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h3 className="font-semibold text-gray-900">Poster Mode</h3>
                  {currentMode === 'poster' && (
                    <CheckCircleIcon className="w-5 h-5 text-green-600" />
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  Post jobs, hire workers, and manage projects
                </p>
                
                {/* Profile Status */}
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    MOCK_PROFILE_STATUS.poster.isComplete ? 'bg-green-500' : 'bg-yellow-500'
                  }`} />
                  <span className="text-xs text-gray-500">
                    Profile {MOCK_PROFILE_STATUS.poster.completionPercentage}% complete
                  </span>
                  {!MOCK_PROFILE_STATUS.poster.isComplete && (
                    <ExclamationTriangleIcon className="w-4 h-4 text-yellow-500" />
                  )}
                </div>
              </div>
            </div>
          </button>
        </div>

        {/* Footer Note */}
        <div className="p-4 bg-gray-50 rounded-t-2xl">
          <p className="text-xs text-gray-600 text-center">
            You can switch between modes anytime. Complete your profile for the best experience.
          </p>
        </div>

        {/* Animation Overlay */}
        {isAnimating && (
          <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center rounded-t-2xl">
            <div className="text-center">
              <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2" />
              <p className="text-sm text-gray-600">Switching mode...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
