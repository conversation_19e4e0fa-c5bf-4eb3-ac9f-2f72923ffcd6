'use client';

import React, { useEffect, useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';

// Theme Consistency Checker
export function ThemeConsistencyChecker() {
  const { mode } = useTheme();
  const [inconsistencies, setInconsistencies] = useState<string[]>([]);

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      checkThemeConsistency();
    }
  }, [mode]);

  const checkThemeConsistency = () => {
    const issues: string[] = [];
    
    // Check for consistent color usage
    const elements = document.querySelectorAll('[class*="bg-"], [class*="text-"], [class*="border-"]');
    const colorUsage = new Map<string, number>();
    elements.forEach(el => {
      if (typeof el.className !== 'string') return
      const classes = el.className.split(' ');
      classes.forEach(cls => {
        if (cls.includes('bg-') || cls.includes('text-') || cls.includes('border-')) {
          colorUsage.set(cls, (colorUsage.get(cls) || 0) + 1);
        }
      });
    });

    // Check for mode-specific styling
    const workerElements = document.querySelectorAll('[class*="blue"]');
    const posterElements = document.querySelectorAll('[class*="green"]');
    
    if (mode === 'worker' && posterElements.length > workerElements.length) {
      issues.push('Worker mode should primarily use blue colors');
    }
    
    if (mode === 'poster' && workerElements.length > posterElements.length) {
      issues.push('Poster mode should primarily use green colors');
    }

    // Check for consistent spacing
    const spacingElements = document.querySelectorAll('[class*="p-"], [class*="m-"], [class*="gap-"]');
    const spacingValues = new Set<string>();
    
    spacingElements.forEach(el => {
      if (typeof el.className !== 'string') return
      const classes = el.className.split(' ');
      classes.forEach(cls => {
        if (cls.match(/^[pm]-\d+$/) || cls.match(/^gap-\d+$/)) {
          spacingValues.add(cls);
        }
      });
    });

    if (spacingValues.size > 10) {
      issues.push('Too many different spacing values - consider using consistent spacing scale');
    }

    setInconsistencies(issues);
  };

  if (process.env.NODE_ENV !== 'development' || inconsistencies.length === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-20 left-4 right-4 bg-yellow-100 border border-yellow-300 rounded-lg p-3 text-sm z-50">
      <h4 className="font-medium text-yellow-800 mb-2">Theme Inconsistencies Detected:</h4>
      <ul className="text-yellow-700 space-y-1">
        {inconsistencies.map((issue, index) => (
          <li key={index}>• {issue}</li>
        ))}
      </ul>
    </div>
  );
}

// Animation Consistency
export const animations = {
  // Standard transitions
  transition: 'transition-all duration-200 ease-in-out',
  transitionFast: 'transition-all duration-150 ease-in-out',
  transitionSlow: 'transition-all duration-300 ease-in-out',
  
  // Hover effects
  hoverScale: 'hover:scale-105 active:scale-95',
  hoverLift: 'hover:-translate-y-1 hover:shadow-lg',
  hoverGlow: 'hover:shadow-lg hover:shadow-blue-500/25',
  
  // Loading animations
  pulse: 'animate-pulse',
  spin: 'animate-spin',
  bounce: 'animate-bounce',
  
  // Slide animations
  slideInUp: 'transform translate-y-full transition-transform duration-300 ease-out',
  slideInDown: 'transform -translate-y-full transition-transform duration-300 ease-out',
  slideInLeft: 'transform -translate-x-full transition-transform duration-300 ease-out',
  slideInRight: 'transform translate-x-full transition-transform duration-300 ease-out',
  
  // Fade animations
  fadeIn: 'opacity-0 transition-opacity duration-300 ease-in-out',
  fadeOut: 'opacity-100 transition-opacity duration-300 ease-in-out'
};

// Color Palette Consistency
export const colors = {
  // Worker Mode (Blue theme)
  worker: {
    primary: 'bg-blue-600 text-white',
    primaryHover: 'hover:bg-blue-700',
    primaryLight: 'bg-blue-50 text-blue-700',
    primaryBorder: 'border-blue-300',
    accent: 'bg-blue-100 text-blue-800',
    gradient: 'bg-gradient-to-r from-blue-600 to-blue-700'
  },
  
  // Poster Mode (Green theme)
  poster: {
    primary: 'bg-green-600 text-white',
    primaryHover: 'hover:bg-green-700',
    primaryLight: 'bg-green-50 text-green-700',
    primaryBorder: 'border-green-300',
    accent: 'bg-green-100 text-green-800',
    gradient: 'bg-gradient-to-r from-green-600 to-green-700'
  },
  
  // Neutral colors
  neutral: {
    background: 'bg-gray-50',
    surface: 'bg-white',
    border: 'border-gray-200',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    textMuted: 'text-gray-500'
  },
  
  // Status colors
  status: {
    success: 'bg-green-500 text-white',
    warning: 'bg-yellow-500 text-white',
    error: 'bg-red-500 text-white',
    info: 'bg-blue-500 text-white'
  }
};

// Typography Consistency
export const typography = {
  // Headings
  h1: 'text-3xl font-bold text-gray-900',
  h2: 'text-2xl font-bold text-gray-900',
  h3: 'text-xl font-semibold text-gray-900',
  h4: 'text-lg font-semibold text-gray-900',
  h5: 'text-base font-semibold text-gray-900',
  
  // Body text
  body: 'text-base text-gray-700',
  bodySmall: 'text-sm text-gray-600',
  caption: 'text-xs text-gray-500',
  
  // Special text
  label: 'text-sm font-medium text-gray-700',
  button: 'font-semibold',
  link: 'text-blue-600 hover:text-blue-700 underline'
};

// Spacing Consistency
export const spacing = {
  // Padding
  paddingXS: 'p-1',
  paddingSM: 'p-2',
  paddingMD: 'p-4',
  paddingLG: 'p-6',
  paddingXL: 'p-8',
  
  // Margins
  marginXS: 'm-1',
  marginSM: 'm-2',
  marginMD: 'm-4',
  marginLG: 'm-6',
  marginXL: 'm-8',
  
  // Gaps
  gapXS: 'gap-1',
  gapSM: 'gap-2',
  gapMD: 'gap-4',
  gapLG: 'gap-6',
  gapXL: 'gap-8'
};

// Component Consistency Helper
export function useConsistentStyling(mode: 'worker' | 'poster') {
  const getThemeColors = () => {
    return mode === 'worker' ? colors.worker : colors.poster;
  };

  const getButtonStyles = (variant: 'primary' | 'secondary' | 'outline' = 'primary') => {
    const themeColors = getThemeColors();
    
    switch (variant) {
      case 'primary':
        return `${themeColors.primary} ${themeColors.primaryHover} ${animations.transition} ${typography.button}`;
      case 'secondary':
        return `${colors.neutral.surface} ${colors.neutral.border} border ${colors.neutral.text} hover:bg-gray-50 ${animations.transition} ${typography.button}`;
      case 'outline':
        return `border ${themeColors.primaryBorder} ${themeColors.primaryLight} ${animations.transition} ${typography.button}`;
      default:
        return '';
    }
  };

  const getCardStyles = () => {
    return `${colors.neutral.surface} ${colors.neutral.border} border rounded-lg ${spacing.paddingMD} ${animations.transition} hover:shadow-md`;
  };

  const getInputStyles = () => {
    return `${colors.neutral.surface} ${colors.neutral.border} border rounded-lg ${spacing.paddingMD} focus:ring-2 focus:ring-blue-500 focus:border-transparent ${animations.transition}`;
  };

  return {
    getThemeColors,
    getButtonStyles,
    getCardStyles,
    getInputStyles
  };
}

// Final Polish Component
export function FinalPolish() {
  const { mode } = useTheme();

  useEffect(() => {
    // Add smooth scrolling
    document.documentElement.style.scrollBehavior = 'smooth';
    
    // Add focus-visible polyfill behavior
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
    });
    
    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });

    // Add theme-specific body classes
    document.body.classList.add(`mode-${mode}`);
    
    return () => {
      document.body.classList.remove(`mode-${mode}`);
    };
  }, [mode]);

  return null;
}

// Micro-interactions Hook
export function useMicroInteractions() {
  const addRippleEffect = (event: React.MouseEvent<HTMLElement>) => {
    const button = event.currentTarget;
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    const ripple = document.createElement('span');
    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: scale(0);
      animation: ripple 0.6s linear;
      pointer-events: none;
    `;
    
    button.style.position = 'relative';
    button.style.overflow = 'hidden';
    button.appendChild(ripple);
    
    setTimeout(() => {
      ripple.remove();
    }, 600);
  };

  const addHapticFeedback = () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(10);
    }
  };

  return {
    addRippleEffect,
    addHapticFeedback
  };
}

// CSS-in-JS for animations
export const keyframes = `
  @keyframes ripple {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }
  
  @keyframes slideInUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  @keyframes fadeInScale {
    from {
      transform: scale(0.95);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  .keyboard-navigation *:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
  }
  
  .high-contrast {
    filter: contrast(150%);
  }
  
  .mode-worker .primary-color {
    color: #2563EB;
  }
  
  .mode-poster .primary-color {
    color: #059669;
  }
`;

// Inject keyframes
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = keyframes;
  document.head.appendChild(style);
}
