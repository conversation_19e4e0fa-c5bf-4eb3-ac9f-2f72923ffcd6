'use client';

import React, { useState } from 'react';
import { 
  XMarkIcon,
  BellIcon,
  CheckIcon,
  TrashIcon,
  ClockIcon,
  UserIcon,
  BriefcaseIcon,
  CurrencyRupeeIcon,
  ChatBubbleLeftIcon
} from '@heroicons/react/24/outline';

interface Notification {
  id: string;
  type: 'job_application' | 'job_accepted' | 'payment' | 'message' | 'rating' | 'system';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  isUrgent: boolean;
  actionUrl?: string;
  metadata?: {
    jobId?: string;
    workerId?: string;
    amount?: number;
  };
}

interface NotificationsCenterProps {
  isOpen: boolean;
  onClose: () => void;
}

// Mock notifications data with categories
const MOCK_NOTIFICATIONS: Notification[] = [
  {
    id: '1',
    type: 'job_application',
    title: 'New Job Application',
    message: '<PERSON><PERSON> applied for your Kitchen Sink Repair job',
    timestamp: '2025-08-25T11:30:00Z',
    isRead: false,
    isUrgent: false,
    actionUrl: '/jobs/1/applicants',
    metadata: { jobId: '1', workerId: '1' }
  },
  {
    id: '2',
    type: 'payment',
    title: 'Payment Confirmed',
    message: 'Payment of ₹800 confirmed for House Cleaning job',
    timestamp: '2025-08-25T10:15:00Z',
    isRead: false,
    isUrgent: false,
    actionUrl: '/payments/history',
    metadata: { amount: 800 }
  },
  {
    id: '3',
    type: 'message',
    title: 'New Message',
    message: 'Sunita Patel: "I can start the cleaning work tomorrow morning"',
    timestamp: '2025-08-25T09:45:00Z',
    isRead: true,
    isUrgent: false,
    actionUrl: '/messages/2',
    metadata: { workerId: '2' }
  },
  {
    id: '4',
    type: 'job_accepted',
    title: 'Job Application Accepted',
    message: 'Your application for AC Installation was accepted!',
    timestamp: '2025-08-24T16:20:00Z',
    isRead: true,
    isUrgent: true,
    actionUrl: '/jobs/3/status',
    metadata: { jobId: '3' }
  },
  {
    id: '5',
    type: 'rating',
    title: 'New Rating Received',
    message: 'Amit Singh rated you 5 stars for plumbing work',
    timestamp: '2025-08-24T14:10:00Z',
    isRead: true,
    isUrgent: false,
    actionUrl: '/profile/ratings'
  },
  {
    id: '6',
    type: 'system',
    title: 'Profile Verification',
    message: 'Your electrician certification has been verified',
    timestamp: '2025-08-23T12:00:00Z',
    isRead: true,
    isUrgent: false,
    actionUrl: '/profile'
  }
];

export default function NotificationsCenter({ isOpen, onClose }: NotificationsCenterProps) {
  const [notifications, setNotifications] = useState(MOCK_NOTIFICATIONS);
  const [activeTab, setActiveTab] = useState<'all' | 'unread'>('all');

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'job_application':
      case 'job_accepted':
        return BriefcaseIcon;
      case 'payment':
        return CurrencyRupeeIcon;
      case 'message':
        return ChatBubbleLeftIcon;
      case 'rating':
        return UserIcon;
      case 'system':
        return BellIcon;
      default:
        return BellIcon;
    }
  };

  const getNotificationColor = (type: string, isUrgent: boolean) => {
    if (isUrgent) return 'text-red-600 bg-red-50';
    
    switch (type) {
      case 'job_application':
      case 'job_accepted':
        return 'text-blue-600 bg-blue-50';
      case 'payment':
        return 'text-green-600 bg-green-50';
      case 'message':
        return 'text-purple-600 bg-purple-50';
      case 'rating':
        return 'text-yellow-600 bg-yellow-50';
      case 'system':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId 
          ? { ...notif, isRead: true }
          : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, isRead: true }))
    );
  };

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev => 
      prev.filter(notif => notif.id !== notificationId)
    );
  };

  const filteredNotifications = activeTab === 'unread' 
    ? notifications.filter(n => !n.isRead)
    : notifications;

  const unreadCount = notifications.filter(n => !n.isRead).length;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end">
      <div className={`w-full bg-white rounded-t-2xl transform transition-transform duration-300 ${
        isOpen ? 'translate-y-0' : 'translate-y-full'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <h2 className="text-lg font-semibold text-gray-900">Notifications</h2>
            {unreadCount > 0 && (
              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                {unreadCount}
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="text-sm text-blue-600 hover:text-blue-700 font-medium"
              >
                Mark all read
              </button>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <XMarkIcon className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 p-4 bg-gray-50">
          <button
            onClick={() => setActiveTab('all')}
            className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
              activeTab === 'all'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            All ({notifications.length})
          </button>
          <button
            onClick={() => setActiveTab('unread')}
            className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
              activeTab === 'unread'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Unread ({unreadCount})
          </button>
        </div>

        {/* Notifications List */}
        <div className="max-h-96 overflow-y-auto">
          {filteredNotifications.length === 0 ? (
            <div className="text-center py-12">
              <BellIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {activeTab === 'unread' ? 'No unread notifications' : 'No notifications'}
              </h3>
              <p className="text-gray-600">
                {activeTab === 'unread' 
                  ? 'All caught up! Check back later for updates.'
                  : 'You\'ll see job updates, messages, and payments here.'
                }
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredNotifications.map((notification) => {
                const Icon = getNotificationIcon(notification.type);
                const colorClass = getNotificationColor(notification.type, notification.isUrgent);
                
                return (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 transition-colors ${
                      !notification.isRead ? 'bg-blue-50' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${colorClass}`}>
                        <Icon className="w-5 h-5" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className={`text-sm font-medium ${
                              !notification.isRead ? 'text-gray-900' : 'text-gray-700'
                            }`}>
                              {notification.title}
                              {notification.isUrgent && (
                                <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                                  Urgent
                                </span>
                              )}
                            </h4>
                            <p className="text-sm text-gray-600 mt-1">
                              {notification.message}
                            </p>
                            <div className="flex items-center space-x-1 mt-2">
                              <ClockIcon className="w-3 h-3 text-gray-400" />
                              <span className="text-xs text-gray-500">
                                {new Date(notification.timestamp).toLocaleDateString('en-IN', {
                                  day: 'numeric',
                                  month: 'short',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </span>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-2 ml-2">
                            {!notification.isRead && (
                              <button
                                onClick={() => markAsRead(notification.id)}
                                className="p-1 hover:bg-gray-200 rounded-full transition-colors"
                                title="Mark as read"
                              >
                                <CheckIcon className="w-4 h-4 text-gray-400" />
                              </button>
                            )}
                            <button
                              onClick={() => deleteNotification(notification.id)}
                              className="p-1 hover:bg-gray-200 rounded-full transition-colors"
                              title="Delete notification"
                            >
                              <TrashIcon className="w-4 h-4 text-gray-400" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 bg-gray-50 border-t border-gray-200">
          <p className="text-xs text-gray-600 text-center">
            We only send essential updates between 7 AM–9 PM
          </p>
        </div>
      </div>
    </div>
  );
}
