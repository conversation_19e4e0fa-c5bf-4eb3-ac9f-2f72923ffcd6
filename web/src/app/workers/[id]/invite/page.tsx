'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { 
  ArrowLeftIcon,
  PlusIcon,
  CheckCircleIcon,
  CurrencyRupeeIcon,
  MapPinIcon,
  ClockIcon,
  BoltIcon,
  UserIcon
} from '@heroicons/react/24/outline';

// Mock worker data
const MOCK_WORKER_DATA = {
  '1': {
    id: '1',
    name: '<PERSON><PERSON>',
    avatar: '👨‍🔧',
    skills: ['Plumber', 'Electrician'],
    rating: 4.8,
    distance: 1.2
  }
};

// Mock open jobs data
const MOCK_OPEN_JOBS = [
  {
    id: '1',
    title: 'Kitchen Sink Repair Required',
    category: 'Plumber',
    rate: 150,
    rateType: 'hourly',
    location: 'Powai, Mumbai',
    duration: '2-3 hours',
    isUrgent: true,
    postedAt: '2025-08-25T08:00:00Z',
    applications: 8
  },
  {
    id: '4',
    title: 'Painting Work - 2BHK Apartment',
    category: 'Painter',
    rate: 1200,
    rateType: 'daily',
    location: 'Malad, Mumbai',
    duration: '3-4 days',
    isUrgent: false,
    postedAt: '2025-08-22T16:45:00Z',
    applications: 3
  }
];

// Message templates
const MESSAGE_TEMPLATES = [
  "Hi! I found your profile and would like to invite you to work on this job. Please let me know if you're interested.",
  "Hello! Your skills match perfectly with this job requirement. Would you be available to start soon?",
  "Hi! I'm impressed by your ratings and experience. I'd love to have you work on this project.",
  "Hello! This job seems perfect for your expertise. Please review the details and let me know your availability."
];

export default function InviteWorkerPage() {
  const params = useParams();
  const router = useRouter();
  const workerId = params.id as string;
  const [worker, setWorker] = useState<any>(null);
  const [openJobs, setOpenJobs] = useState(MOCK_OPEN_JOBS);
  const [selectedJob, setSelectedJob] = useState<string | null>(null);
  const [inviteType, setInviteType] = useState<'existing' | 'new'>('existing');
  const [customMessage, setCustomMessage] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState(0);
  const [isInviting, setIsInviting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // New job form state
  const [newJobData, setNewJobData] = useState({
    title: '',
    category: '',
    rate: '',
    rateType: 'hourly',
    location: '',
    duration: '',
    description: ''
  });

  useEffect(() => {
    // Load worker data
    const workerData = MOCK_WORKER_DATA[workerId as keyof typeof MOCK_WORKER_DATA];
    if (workerData) {
      setWorker(workerData);
      // Pre-select message template
      setCustomMessage(MESSAGE_TEMPLATES[selectedTemplate]);
    }
  }, [workerId, selectedTemplate]);

  const handleSendInvite = async () => {
    if (inviteType === 'existing' && !selectedJob) return;
    if (inviteType === 'new' && (!newJobData.title || !newJobData.category)) return;
    
    setIsInviting(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsInviting(false);
      setShowSuccess(true);
      
      // Auto-close success and navigate back
      setTimeout(() => {
        router.back();
      }, 2000);
    }, 1500);
  };

  const handleNewJobChange = (field: string, value: string) => {
    setNewJobData(prev => ({ ...prev, [field]: value }));
  };

  if (!worker) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading worker details...</p>
        </div>
      </div>
    );
  }

  if (showSuccess) {
    return (
      <div className="min-h-screen bg-green-50 flex items-center justify-center p-4">
        <div className="text-center space-y-6 max-w-sm">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <CheckCircleIcon className="w-12 h-12 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Invite Sent! 🎉</h1>
            <p className="text-gray-600">
              Your invitation has been sent to <span className="font-medium">{worker.name}</span>. 
              They'll be notified and can respond directly.
            </p>
          </div>
          <div className="bg-white rounded-lg p-4 border border-green-200">
            <h3 className="font-medium text-gray-900 mb-2">What happens next?</h3>
            <ul className="text-sm text-gray-600 space-y-1 text-left">
              <li>• Worker receives notification about your invite</li>
              <li>• They can accept, decline, or negotiate terms</li>
              <li>• You'll get notified of their response</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Invite Worker</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6 pb-24">
        {/* Worker Summary */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <span className="text-3xl">{worker.avatar}</span>
            <div>
              <h2 className="font-semibold text-blue-900">{worker.name}</h2>
              <p className="text-sm text-blue-700">
                {worker.skills.join(', ')} • ⭐ {worker.rating} • {worker.distance} km away
              </p>
            </div>
          </div>
        </div>

        {/* Job Selection Type - Following UI/UX spec: Choose existing or Create new */}
        <div className="space-y-4">
          <h3 className="font-semibold text-gray-900">Select Job</h3>
          
          <div className="flex space-x-3">
            <button
              onClick={() => setInviteType('existing')}
              className={`flex-1 p-4 rounded-lg border-2 text-left transition-colors ${
                inviteType === 'existing'
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="font-medium text-gray-900">Existing Job</div>
              <div className="text-sm text-gray-600">Choose from your open jobs</div>
            </button>
            <button
              onClick={() => setInviteType('new')}
              className={`flex-1 p-4 rounded-lg border-2 text-left transition-colors ${
                inviteType === 'new'
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="font-medium text-gray-900">Create New Job</div>
              <div className="text-sm text-gray-600">Post a new job inline</div>
            </button>
          </div>
        </div>

        {/* Existing Jobs Selection */}
        {inviteType === 'existing' && (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Your Open Jobs</h4>
            {openJobs.length === 0 ? (
              <div className="text-center py-8 bg-gray-50 rounded-lg">
                <p className="text-gray-600 mb-4">You don't have any open jobs.</p>
                <button
                  onClick={() => setInviteType('new')}
                  className="text-green-600 hover:text-green-700 font-medium"
                >
                  Create a new job instead
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                {openJobs.map((job) => (
                  <button
                    key={job.id}
                    onClick={() => setSelectedJob(job.id)}
                    className={`w-full p-4 rounded-lg border-2 text-left transition-colors ${
                      selectedJob === job.id
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          {job.isUrgent && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              <BoltIcon className="w-3 h-3 mr-1" />
                              Urgent
                            </span>
                          )}
                        </div>
                        <h5 className="font-medium text-gray-900">{job.title}</h5>
                        <p className="text-sm text-gray-600">{job.category}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900">₹{job.rate}</p>
                        <p className="text-xs text-gray-500">per {job.rateType === 'hourly' ? 'hr' : 'day'}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <MapPinIcon className="w-4 h-4" />
                        <span>{job.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <ClockIcon className="w-4 h-4" />
                        <span>{job.duration}</span>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        )}

        {/* New Job Creation */}
        {inviteType === 'new' && (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Create New Job</h4>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Job Title *</label>
                <input
                  type="text"
                  value={newJobData.title}
                  onChange={(e) => handleNewJobChange('title', e.target.value)}
                  placeholder="e.g., Kitchen Plumbing Repair"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                <select
                  value={newJobData.category}
                  onChange={(e) => handleNewJobChange('category', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="">Select category</option>
                  {worker.skills.map((skill: string) => (
                    <option key={skill} value={skill}>{skill}</option>
                  ))}
                  <option value="Other">Other</option>
                </select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Rate (₹)</label>
                  <input
                    type="number"
                    value={newJobData.rate}
                    onChange={(e) => handleNewJobChange('rate', e.target.value)}
                    placeholder="150"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Rate Type</label>
                  <select
                    value={newJobData.rateType}
                    onChange={(e) => handleNewJobChange('rateType', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  >
                    <option value="hourly">Per Hour</option>
                    <option value="daily">Per Day</option>
                    <option value="fixed">Fixed Price</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Message Templates - Following UI/UX spec: Optional message template */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Invitation Message</h4>
          
          <div className="space-y-3">
            <div className="flex space-x-2">
              {MESSAGE_TEMPLATES.map((_, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setSelectedTemplate(index);
                    setCustomMessage(MESSAGE_TEMPLATES[index]);
                  }}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    selectedTemplate === index
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Template {index + 1}
                </button>
              ))}
            </div>
            
            <textarea
              value={customMessage}
              onChange={(e) => setCustomMessage(e.target.value)}
              placeholder="Write a personal message to the worker..."
              rows={4}
              maxLength={300}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
            />
            <p className="text-xs text-gray-500">{customMessage.length}/300 characters</p>
          </div>
        </div>
      </div>

      {/* Bottom Send Button */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 safe-area-bottom">
        <div className="mobile-container">
          <button
            onClick={handleSendInvite}
            disabled={
              isInviting || 
              (inviteType === 'existing' && !selectedJob) ||
              (inviteType === 'new' && (!newJobData.title || !newJobData.category))
            }
            className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors ${
              !isInviting && 
              ((inviteType === 'existing' && selectedJob) || 
               (inviteType === 'new' && newJobData.title && newJobData.category))
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isInviting ? (
              <div className="flex items-center justify-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Sending Invite...
              </div>
            ) : (
              'Send Invite'
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
