'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  ArrowLeftIcon,
  StarIcon,
  MapPinIcon,
  ClockIcon,
  CheckBadgeIcon,
  ChatBubbleLeftIcon,
  PlusIcon,
  CurrencyRupeeIcon,
  CalendarIcon,
  PhoneIcon,
  ShareIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';

// Mock worker data following UI/UX specs
const MOCK_WORKER_DATA = {
  '1': {
    id: '1',
    name: '<PERSON><PERSON>',
    avatar: '👨‍🔧',
    isVerified: true,
    distance: 1.2,
    availability: 'Available now',
    phone: '+91 98765 43210',
    joinedDate: '2023-03-15',
    responseTime: '< 1 hour',
    personas: [
      {
        id: 'plumber',
        skill: 'Plumber',
        rate: 140,
        rateType: 'hourly',
        experience: '8+ years',
        completedJobs: 156,
        rating: 4.8,
        reviewCount: 127,
        description: 'Expert in residential and commercial plumbing. Specialized in kitchen and bathroom installations.',
        portfolio: [
          { id: '1', image: '🚿', title: 'Bathroom Installation', description: 'Complete bathroom plumbing setup' },
          { id: '2', image: '🔧', title: 'Kitchen Sink Repair', description: 'Fixed leaking faucet and drain' },
          { id: '3', image: '🚰', title: 'Water Heater Installation', description: 'Installed new electric water heater' }
        ]
      },
      {
        id: 'electrician',
        skill: 'Electrician',
        rate: 160,
        rateType: 'hourly',
        experience: '5+ years',
        completedJobs: 89,
        rating: 4.7,
        reviewCount: 76,
        description: 'Licensed electrician with expertise in home wiring and appliance installation.',
        portfolio: [
          { id: '4', image: '💡', title: 'LED Light Installation', description: 'Installed smart LED lighting system' },
          { id: '5', image: '🔌', title: 'AC Wiring', description: 'Complete electrical setup for split AC' }
        ]
      }
    ],
    reviews: [
      {
        id: '1',
        posterName: 'Amit Singh',
        rating: 5,
        comment: 'Excellent work! Fixed my kitchen sink perfectly. Very professional and brought all necessary tools.',
        jobTitle: 'Kitchen Sink Repair',
        completedAt: '2025-08-20T14:30:00Z',
        persona: 'Plumber'
      },
      {
        id: '2',
        posterName: 'Sunita Patel',
        rating: 5,
        comment: 'Quick and efficient. Installed the water heater exactly as requested. Highly recommended!',
        jobTitle: 'Water Heater Installation',
        completedAt: '2025-08-18T10:15:00Z',
        persona: 'Plumber'
      },
      {
        id: '3',
        posterName: 'Priya Sharma',
        rating: 4,
        comment: 'Good work on the electrical wiring. Completed on time and cleaned up after work.',
        jobTitle: 'AC Installation Wiring',
        completedAt: '2025-08-15T16:45:00Z',
        persona: 'Electrician'
      }
    ]
  }
};

export default function WorkerProfilePage() {
  const params = useParams();
  const router = useRouter();
  const workerId = params.id as string;
  const [worker, setWorker] = useState<any>(null);
  const [activePersona, setActivePersona] = useState(0);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    // Load worker data
    const workerData = MOCK_WORKER_DATA[workerId as keyof typeof MOCK_WORKER_DATA];
    if (workerData) {
      setWorker(workerData);
    }
  }, [workerId]);

  if (!worker) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading worker profile...</p>
        </div>
      </div>
    );
  }

  const currentPersona = worker.personas[activePersona];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Worker Profile</h1>
          <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
            <ShareIcon className="w-6 h-6 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6">
        {/* Worker Header - Following UI/UX spec: Photo, Name, Verified badge, Distance, Availability */}
        <div className="text-center space-y-4">
          <div className="relative inline-block">
            <div className="w-24 h-24 bg-gray-300 rounded-full flex items-center justify-center text-4xl">
              {worker.avatar}
            </div>
            {worker.isVerified && (
              <div className="absolute -bottom-1 -right-1 w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                <CheckBadgeIcon className="w-5 h-5 text-white" />
              </div>
            )}
          </div>
          
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{worker.name}</h2>
            <div className="flex items-center justify-center space-x-4 mt-2 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <MapPinIcon className="w-4 h-4" />
                <span>{worker.distance} km away</span>
              </div>
              <div className="flex items-center space-x-1">
                <ClockIcon className="w-4 h-4" />
                <span className="text-green-600 font-medium">{worker.availability}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Persona Tabs - Following UI/UX spec: if multiple personas */}
        {worker.personas.length > 1 && (
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900">Skills</h3>
            <div className="flex space-x-2">
              {worker.personas.map((persona: any, index: number) => (
                <button
                  key={persona.id}
                  onClick={() => setActivePersona(index)}
                  className={`flex-1 p-3 rounded-lg border-2 text-center transition-colors ${
                    activePersona === index
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium text-gray-900">{persona.skill}</div>
                  <div className="text-sm text-gray-600">₹{persona.rate}/{persona.rateType === 'hourly' ? 'hr' : 'day'}</div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Current Persona Details */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-green-900">{currentPersona.skill}</h3>
            <div className="flex items-center space-x-1">
              <StarSolidIcon className="w-5 h-5 text-yellow-400" />
              <span className="font-semibold text-green-900">{currentPersona.rating}</span>
              <span className="text-sm text-green-700">({currentPersona.reviewCount})</span>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 mb-3">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-900">₹{currentPersona.rate}</p>
              <p className="text-sm text-green-700">per {currentPersona.rateType === 'hourly' ? 'hour' : 'day'}</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-900">{currentPersona.completedJobs}</p>
              <p className="text-sm text-green-700">jobs completed</p>
            </div>
          </div>
          
          <div className="text-center mb-3">
            <p className="text-sm text-green-700">{currentPersona.experience} experience</p>
          </div>
          
          <p className="text-sm text-green-800">{currentPersona.description}</p>
        </div>

        {/* Action Buttons - Following UI/UX spec: Invite to Job, Message */}
        <div className="flex space-x-3">
          <Link
            href={`/workers/${worker.id}/invite`}
            className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg text-center font-medium hover:bg-green-700 transition-colors flex items-center justify-center"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            Invite to Job
          </Link>
          <Link
            href={`/messages/${worker.id}`}
            className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg text-center font-medium hover:bg-blue-700 transition-colors flex items-center justify-center"
          >
            <ChatBubbleLeftIcon className="w-5 h-5 mr-2" />
            Message
          </Link>
        </div>

        {/* Tabs */}
        <div className="space-y-4">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'portfolio', label: 'Portfolio' },
              { id: 'reviews', label: 'Reviews' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-white text-green-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          {activeTab === 'overview' && (
            <OverviewTab worker={worker} currentPersona={currentPersona} />
          )}

          {activeTab === 'portfolio' && (
            <PortfolioTab portfolio={currentPersona.portfolio} />
          )}

          {activeTab === 'reviews' && (
            <ReviewsTab reviews={worker.reviews.filter((r: any) => r.persona === currentPersona.skill)} />
          )}
        </div>
      </div>
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ worker, currentPersona }: { worker: any; currentPersona: any }) {
  return (
    <div className="space-y-4">
      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
          <CalendarIcon className="w-6 h-6 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600">Joined</p>
          <p className="font-semibold text-gray-900">
            {new Date(worker.joinedDate).toLocaleDateString('en-IN', {
              month: 'short',
              year: 'numeric'
            })}
          </p>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
          <ClockIcon className="w-6 h-6 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600">Response Time</p>
          <p className="font-semibold text-gray-900">{worker.responseTime}</p>
        </div>
      </div>

      {/* Contact Info */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-3">Contact Information</h4>
        <div className="flex items-center space-x-3">
          <PhoneIcon className="w-5 h-5 text-gray-400" />
          <span className="text-gray-700">{worker.phone}</span>
          <button className="text-blue-600 text-sm hover:text-blue-700">Call</button>
        </div>
      </div>

      {/* All Skills Summary */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">All Skills</h4>
        {worker.personas.map((persona: any, index: number) => (
          <div key={persona.id} className="bg-white border border-gray-200 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div>
                <h5 className="font-medium text-gray-900">{persona.skill}</h5>
                <p className="text-sm text-gray-600">{persona.experience} • {persona.completedJobs} jobs</p>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-1">
                  <StarSolidIcon className="w-4 h-4 text-yellow-400" />
                  <span className="font-medium text-gray-900">{persona.rating}</span>
                </div>
                <p className="text-sm text-gray-600">₹{persona.rate}/{persona.rateType === 'hourly' ? 'hr' : 'day'}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Portfolio Tab Component
function PortfolioTab({ portfolio }: { portfolio: any[] }) {
  return (
    <div className="space-y-4">
      {portfolio.length === 0 ? (
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">📸</span>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Portfolio Yet</h3>
          <p className="text-gray-600">This worker hasn't added any portfolio items.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {portfolio.map((item: any) => (
            <div key={item.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-start space-x-4">
                <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center text-2xl">
                  {item.image}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">{item.title}</h4>
                  <p className="text-sm text-gray-600">{item.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Reviews Tab Component
function ReviewsTab({ reviews }: { reviews: any[] }) {
  return (
    <div className="space-y-4">
      {reviews.length === 0 ? (
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <StarIcon className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Reviews Yet</h3>
          <p className="text-gray-600">This worker hasn't received any reviews for this skill.</p>
        </div>
      ) : (
        reviews.map((review: any) => (
          <div key={review.id} className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-start justify-between mb-3">
              <div>
                <h4 className="font-medium text-gray-900">{review.posterName}</h4>
                <p className="text-sm text-gray-600">{review.jobTitle}</p>
              </div>
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <StarSolidIcon
                    key={i}
                    className={`w-4 h-4 ${
                      i < review.rating ? 'text-yellow-400' : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
            <p className="text-gray-700 mb-2">"{review.comment}"</p>
            <p className="text-xs text-gray-500">
              {new Date(review.completedAt).toLocaleDateString('en-IN', {
                day: 'numeric',
                month: 'short',
                year: 'numeric'
              })}
            </p>
          </div>
        ))
      )}
    </div>
  );
}
