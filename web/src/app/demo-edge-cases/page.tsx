'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

// Import all the edge case components
import EmptyState, { 
  NoJobsFound, 
  NoSearchResults, 
  ConnectionError, 
  NoApplications 
} from '@/components/shared/EmptyState';
import { 
  JobCardSkeleton, 
  MessageSkeleton, 
  ProfileSkeleton, 
  ListSkeleton,
  SearchResultsSkeleton,
  NotificationSkeleton,
  LoadingScreen
} from '@/components/shared/LoadingSkeleton';
import { 
  NetworkError, 
  APIError, 
  UserUnavailable 
} from '@/components/shared/ErrorBoundary';
import PermissionEducation, { 
  usePermissionManager,
  PermissionType 
} from '@/components/shared/PermissionEducation';

export default function DemoEdgeCasesPage() {
  const router = useRouter();
  const [activeDemo, setActiveDemo] = useState<string | null>(null);
  const [showLoadingScreen, setShowLoadingScreen] = useState(false);
  const permissionManager = usePermissionManager();

  const demoSections = [
    {
      title: 'Empty States',
      items: [
        { id: 'no_jobs', label: 'No Jobs Available', component: <NoJobsFound onRefresh={() => alert('Refreshing jobs...')} /> },
        { id: 'no_applications', label: 'No Applications', component: <NoApplications onFindJobs={() => alert('Finding jobs...')} /> },
        { id: 'no_search_results', label: 'No Search Results', component: <NoSearchResults onClearFilters={() => alert('Clearing filters...')} /> },
        { id: 'connection_error', label: 'Connection Error', component: <ConnectionError onRetry={() => alert('Retrying...')} /> },
        { id: 'no_messages', label: 'No Messages', component: <EmptyState type="no_messages" /> },
        { id: 'no_earnings', label: 'No Earnings', component: <EmptyState type="no_earnings" onAction={() => alert('Finding jobs...')} /> },
        { id: 'no_workers', label: 'No Workers Found', component: <EmptyState type="no_workers" onAction={() => alert('Posting job...')} /> },
        { id: 'no_notifications', label: 'No Notifications', component: <EmptyState type="no_notifications" /> }
      ]
    },
    {
      title: 'Loading Skeletons',
      items: [
        { id: 'job_cards', label: 'Job Cards Loading', component: <div className="space-y-4"><JobCardSkeleton /><JobCardSkeleton /><JobCardSkeleton /></div> },
        { id: 'messages', label: 'Messages Loading', component: <div><MessageSkeleton /><MessageSkeleton isOwn /><MessageSkeleton /></div> },
        { id: 'profile', label: 'Profile Loading', component: <ProfileSkeleton /> },
        { id: 'list', label: 'List Loading', component: <ListSkeleton count={4} /> },
        { id: 'search_results', label: 'Search Results Loading', component: <SearchResultsSkeleton /> },
        { id: 'notifications', label: 'Notifications Loading', component: <NotificationSkeleton /> }
      ]
    },
    {
      title: 'Error States',
      items: [
        { id: 'network_error', label: 'Network Error', component: <NetworkError onRetry={() => alert('Retrying...')} onGoOffline={() => alert('Going offline...')} /> },
        { id: 'api_error', label: 'API Error', component: <APIError message="Failed to load jobs. Please try again." onRetry={() => alert('Retrying...')} onDismiss={() => setActiveDemo(null)} /> },
        { id: 'user_unavailable', label: 'User Unavailable', component: <UserUnavailable userName="Rajesh Kumar" onTryAgain={() => alert('Trying again...')} onFindAlternative={() => alert('Finding alternative...')} /> }
      ]
    },
    {
      title: 'Permission Education',
      items: [
        { id: 'location_permission', label: 'Location Permission', action: () => permissionManager.showPermissionDialog('location') },
        { id: 'camera_permission', label: 'Camera Permission', action: () => permissionManager.showPermissionDialog('camera') },
        { id: 'notification_permission', label: 'Notification Permission', action: () => permissionManager.showPermissionDialog('notifications') },
        { id: 'storage_permission', label: 'Storage Permission', action: () => permissionManager.showPermissionDialog('storage') }
      ]
    },
    {
      title: 'Loading Screens',
      items: [
        { id: 'loading_screen', label: 'Full Loading Screen', action: () => {
          setShowLoadingScreen(true);
          setTimeout(() => setShowLoadingScreen(false), 3000);
        }}
      ]
    }
  ];

  if (showLoadingScreen) {
    return <LoadingScreen message="Loading your jobs..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Edge Cases Demo</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {activeDemo ? (
          <div className="space-y-4">
            <button
              onClick={() => setActiveDemo(null)}
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              ← Back to menu
            </button>
            
            <div className="bg-white rounded-lg min-h-[400px]">
              {demoSections
                .flatMap(section => section.items)
                .find(item => item.id === activeDemo)?.component}
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h2 className="text-2xl font-bold text-gray-900">Edge Cases & Error Handling</h2>
              <p className="text-gray-600">
                Comprehensive demo of all error states, loading states, and edge cases
              </p>
            </div>

            {demoSections.map((section) => (
              <div key={section.title} className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {section.items.map((item) => (
                    <button
                      key={item.id}
                      onClick={() => {
                        if ('action' in item && item.action) {
                          item.action();
                        } else {
                          setActiveDemo(item.id);
                        }
                      }}
                      className="p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all text-left"
                    >
                      <h4 className="font-medium text-gray-900">{item.label}</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {section.title === 'Permission Education' ? 'Show permission dialog' : 'View demo'}
                      </p>
                    </button>
                  ))}
                </div>
              </div>
            ))}

            {/* Test Error Boundary */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900">Error Boundary Test</h3>
              <button
                onClick={() => {
                  throw new Error('Test error for error boundary');
                }}
                className="p-4 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors text-left w-full"
              >
                <h4 className="font-medium text-red-900">Trigger Error Boundary</h4>
                <p className="text-sm text-red-700 mt-1">
                  Click to test the global error boundary
                </p>
              </button>
            </div>

            {/* Offline Test */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900">Offline State Test</h3>
              <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-2">Test Offline Banner</h4>
                <p className="text-sm text-orange-800 mb-3">
                  To test the offline banner, open your browser's developer tools and go to the Network tab. 
                  Set the network to "Offline" to see the banner appear.
                </p>
                <p className="text-xs text-orange-700">
                  The banner will show queued actions and automatically disappear when you go back online.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Permission Education Dialogs */}
      {permissionManager.currentPermission && (
        <PermissionEducation
          permissionType={permissionManager.currentPermission}
          isOpen={true}
          onClose={permissionManager.closePermissionDialog}
          onAllow={async () => {
            await permissionManager.requestPermission(permissionManager.currentPermission!);
            permissionManager.closePermissionDialog();
          }}
          onDeny={() => {
            permissionManager.closePermissionDialog();
          }}
        />
      )}
    </div>
  );
}
