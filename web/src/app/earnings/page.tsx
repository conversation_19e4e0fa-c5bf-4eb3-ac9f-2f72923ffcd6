'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import {
  CurrencyRupeeIcon,
  CalendarIcon,
  ArrowTrendingUpIcon,
  DocumentArrowDownIcon,
  ChartBarIcon,
  ClockIcon,
  StarIcon
} from '@heroicons/react/24/outline';

// Mock earnings data
const MOCK_EARNINGS_DATA = {
  summary: {
    today: 1500,
    week: 8500,
    month: 32000,
    total: 125000
  },
  monthlyGoal: {
    target: 40000,
    current: 32000,
    percentage: 80
  },
  recentJobs: [
    {
      id: '3',
      title: 'Electrical Wiring - New AC Installation',
      category: 'Electrician',
      amount: 1500,
      completedAt: '2025-08-25T10:30:00Z',
      rating: 5,
      posterName: '<PERSON><PERSON>'
    },
    {
      id: '4',
      title: 'Painting Work - 2BHK Apartment',
      category: 'Painter',
      amount: 3600,
      completedAt: '2025-08-24T16:45:00Z',
      rating: 5,
      posterName: '<PERSON><PERSON>'
    },
    {
      id: '2',
      title: 'House Cleaning - Deep Clean Required',
      category: 'Cleaner',
      amount: 900,
      completedAt: '2025-08-23T14:20:00Z',
      rating: 4,
      posterName: '<PERSON><PERSON>'
    }
  ],
  byPersona: [
    { skill: 'Electrician', earnings: 15000, jobs: 8, avgRating: 4.8 },
    { skill: 'Painter', earnings: 12000, jobs: 6, avgRating: 4.9 },
    { skill: 'Cleaner', earnings: 5000, jobs: 10, avgRating: 4.7 }
  ],
  weeklyTrend: [
    { day: 'Mon', amount: 1200 },
    { day: 'Tue', amount: 800 },
    { day: 'Wed', amount: 1500 },
    { day: 'Thu', amount: 2000 },
    { day: 'Fri', amount: 1800 },
    { day: 'Sat', amount: 1200 },
    { day: 'Sun', amount: 0 }
  ]
};

export default function EarningsPage() {
  const { mode } = useTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const [earningsData, setEarningsData] = useState(MOCK_EARNINGS_DATA);
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // Show empty state for poster mode
  if (mode === 'poster') {
    return (
      <div className="px-4 py-6">
        <div className="text-center space-y-4">
          <div className="w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
            <span className="text-4xl">💰</span>
          </div>
          <h2 className="text-xl font-semibold text-gray-900">No Payments Yet</h2>
          <p className="text-gray-600 max-w-sm mx-auto">
            Your payment history will appear here
          </p>
        </div>
      </div>
    );
  }

  const handleExportCSV = () => {
    // Mock CSV export functionality
    const csvData = earningsData.recentJobs.map(job => ({
      Date: new Date(job.completedAt).toLocaleDateString('en-IN'),
      Job: job.title,
      Category: job.category,
      Amount: job.amount,
      Rating: job.rating,
      Employer: job.posterName
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `earnings_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getGoalProgress = () => {
    const { current, target } = earningsData.monthlyGoal;
    const percentage = Math.min((current / target) * 100, 100);
    return { percentage, remaining: Math.max(target - current, 0) };
  };

  const goalProgress = getGoalProgress();

  return (
    <div className="px-4 py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Earnings</h1>
          <p className="text-gray-600">Track your income and performance</p>
        </div>
        <button
          onClick={handleExportCSV}
          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <DocumentArrowDownIcon className="w-4 h-4" />
          <span className="text-sm">Export</span>
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 gap-4">
        <EarningsSummaryCard
          title="Today"
          amount={earningsData.summary.today}
          icon={<ClockIcon className="w-5 h-5" />}
          color="text-blue-600 bg-blue-50"
        />
        <EarningsSummaryCard
          title="This Week"
          amount={earningsData.summary.week}
          icon={<CalendarIcon className="w-5 h-5" />}
          color="text-green-600 bg-green-50"
        />
        <EarningsSummaryCard
          title="This Month"
          amount={earningsData.summary.month}
          icon={<ArrowTrendingUpIcon className="w-5 h-5" />}
          color="text-purple-600 bg-purple-50"
        />
        <EarningsSummaryCard
          title="Total Earned"
          amount={earningsData.summary.total}
          icon={<CurrencyRupeeIcon className="w-5 h-5" />}
          color="text-orange-600 bg-orange-50"
        />
      </div>

      {/* Monthly Goal Progress */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-gray-900">Monthly Goal</h3>
          <span className="text-sm text-gray-600">
            {Math.round(goalProgress.percentage)}% Complete
          </span>
        </div>

        <div className="space-y-3">
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500"
              style={{ width: `${goalProgress.percentage}%` }}
            />
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              {formatCurrency(earningsData.monthlyGoal.current)} earned
            </span>
            <span className="font-medium text-gray-900">
              Goal: {formatCurrency(earningsData.monthlyGoal.target)}
            </span>
          </div>

          {goalProgress.remaining > 0 && (
            <p className="text-sm text-blue-600">
              {formatCurrency(goalProgress.remaining)} remaining to reach your goal
            </p>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {[
          { id: 'overview', label: 'Overview' },
          { id: 'history', label: 'History' },
          { id: 'breakdown', label: 'Breakdown' }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <WeeklyTrendChart weeklyTrend={earningsData.weeklyTrend} />
      )}

      {activeTab === 'history' && (
        <EarningsHistory recentJobs={earningsData.recentJobs} />
      )}

      {activeTab === 'breakdown' && (
        <PersonaBreakdown byPersona={earningsData.byPersona} />
      )}
    </div>
  );
}

// Earnings Summary Card Component
function EarningsSummaryCard({
  title,
  amount,
  icon,
  color
}: {
  title: string;
  amount: number;
  icon: React.ReactNode;
  color: string;
}) {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm text-gray-600">{title}</span>
        <div className={`p-2 rounded-lg ${color}`}>
          {icon}
        </div>
      </div>
      <p className="text-xl font-bold text-gray-900">
        ₹{amount.toLocaleString('en-IN')}
      </p>
    </div>
  );
}

// Weekly Trend Chart Component
function WeeklyTrendChart({ weeklyTrend }: { weeklyTrend: any[] }) {
  const maxAmount = Math.max(...weeklyTrend.map(d => d.amount));

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <h3 className="font-semibold text-gray-900 mb-4">This Week's Earnings</h3>
      <div className="space-y-3">
        {weeklyTrend.map((day, index) => (
          <div key={day.day} className="flex items-center space-x-3">
            <span className="text-sm font-medium text-gray-600 w-8">
              {day.day}
            </span>
            <div className="flex-1 bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                style={{
                  width: `${maxAmount > 0 ? (day.amount / maxAmount) * 100 : 0}%`,
                  transitionDelay: `${index * 100}ms`
                }}
              />
            </div>
            <span className="text-sm font-medium text-gray-900 w-16 text-right">
              ₹{day.amount}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}

// Earnings History Component
function EarningsHistory({ recentJobs }: { recentJobs: any[] }) {
  return (
    <div className="space-y-3">
      <h3 className="font-semibold text-gray-900">Recent Jobs</h3>
      {recentJobs.map((job) => (
        <div key={job.id} className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1">
              <h4 className="font-medium text-gray-900 mb-1">{job.title}</h4>
              <p className="text-sm text-gray-600">{job.category} • {job.posterName}</p>
            </div>
            <div className="text-right">
              <p className="font-semibold text-green-600">₹{job.amount}</p>
              <div className="flex items-center space-x-1">
                <StarIcon className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-600">{job.rating}</span>
              </div>
            </div>
          </div>
          <p className="text-xs text-gray-500">
            {new Date(job.completedAt).toLocaleDateString('en-IN', {
              day: 'numeric',
              month: 'short',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>
        </div>
      ))}
    </div>
  );
}

// Persona Breakdown Component
function PersonaBreakdown({ byPersona }: { byPersona: any[] }) {
  const totalEarnings = byPersona.reduce((sum, persona) => sum + persona.earnings, 0);

  return (
    <div className="space-y-3">
      <h3 className="font-semibold text-gray-900">Earnings by Skill</h3>
      {byPersona.map((persona) => (
        <div key={persona.skill} className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-gray-900">{persona.skill}</h4>
            <span className="font-semibold text-green-600">
              ₹{persona.earnings.toLocaleString('en-IN')}
            </span>
          </div>

          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                style={{ width: `${(persona.earnings / totalEarnings) * 100}%` }}
              />
            </div>

            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>{persona.jobs} jobs completed</span>
              <div className="flex items-center space-x-1">
                <StarIcon className="w-4 h-4 text-yellow-400 fill-current" />
                <span>{persona.avgRating} avg rating</span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
