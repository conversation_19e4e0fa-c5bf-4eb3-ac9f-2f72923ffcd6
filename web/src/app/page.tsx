'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/contexts/ThemeContext';
import {
  MagnifyingGlassIcon,
  MapPinIcon,
  FunnelIcon,
  ClockIcon,
  CurrencyRupeeIcon,
  BookmarkIcon,
  ArrowPathIcon,
  StarIcon,
  MapIcon
} from '@heroicons/react/24/outline';
import { BookmarkIcon as BookmarkSolidIcon, StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';
import Link from 'next/link';

// Mock job data with Indian context
const MOCK_JOBS = [
  {
    id: '1',
    title: 'Plumbing Work Required - Kitchen & Bathroom',
    description: 'Need experienced plumber to fix kitchen sink leak and install new bathroom faucet. Urgent work required.',
    category: 'Plumber',
    rate: 800,
    rateType: 'daily',
    distance: 1.2,
    urgency: 'urgent',
    postedTime: '2 hours ago',
    location: 'Andheri West, Mumbai',
    poster: '<PERSON><PERSON>',
    requirements: ['Experience with modern fixtures', 'Own tools required'],
    duration: '4-6 hours',
    isBookmarked: false,
    matchScore: 95
  },
  {
    id: '2',
    title: 'House Cleaning - Deep Clean Required',
    description: '3BHK apartment needs thorough deep cleaning. All rooms, kitchen, and bathrooms.',
    category: 'Cleaner',
    rate: 150,
    rateType: 'hourly',
    distance: 0.8,
    urgency: 'normal',
    postedTime: '4 hours ago',
    location: 'Bandra East, Mumbai',
    poster: 'Rajesh Kumar',
    requirements: ['Bring cleaning supplies', 'Experience with deep cleaning'],
    duration: '6-8 hours',
    isBookmarked: true,
    matchScore: 78
  },
  {
    id: '3',
    title: 'Electrical Wiring - New AC Installation',
    description: 'Install electrical wiring for 2 new split ACs in bedroom and living room. Safety compliance required.',
    category: 'Electrician',
    rate: 1500,
    rateType: 'daily',
    distance: 2.1,
    urgency: 'normal',
    postedTime: '1 day ago',
    location: 'Powai, Mumbai',
    poster: 'Amit Singh',
    requirements: ['Licensed electrician', 'AC installation experience'],
    duration: '1-2 days',
    isBookmarked: false,
    matchScore: 88
  },
  {
    id: '4',
    title: 'Carpenter - Kitchen Cabinet Repair',
    description: 'Fix broken kitchen cabinet doors and drawers. Some replacement parts may be needed.',
    category: 'Carpenter',
    rate: 300,
    rateType: 'hourly',
    distance: 3.2,
    urgency: 'normal',
    postedTime: '6 hours ago',
    location: 'Malad West, Mumbai',
    poster: 'Sunita Patel',
    requirements: ['Wood working tools', 'Cabinet repair experience'],
    duration: '3-4 hours',
    isBookmarked: false,
    matchScore: 72
  },
  {
    id: '5',
    title: 'Painting Work - 2BHK Apartment',
    description: 'Complete interior painting of 2BHK apartment. Walls and ceiling. Paint will be provided.',
    category: 'Painter',
    rate: 1200,
    rateType: 'daily',
    distance: 1.8,
    urgency: 'urgent',
    postedTime: '3 hours ago',
    location: 'Goregaon West, Mumbai',
    poster: 'Vikram Joshi',
    requirements: ['Professional painting tools', 'Interior painting experience'],
    duration: '2-3 days',
    isBookmarked: true,
    matchScore: 85
  }
];

const FILTER_OPTIONS = [
  { id: 'my-skills', label: 'My Skills', active: false },
  { id: 'urgent', label: 'Urgent', active: false },
  { id: 'nearby', label: 'Nearby (2km)', active: false },
  { id: 'high-pay', label: 'High Pay', active: false }
];

export default function Home() {
  const { mode } = useTheme();
  const router = useRouter();
  const [jobs, setJobs] = useState(MOCK_JOBS);
  const [filters, setFilters] = useState(FILTER_OPTIONS);
  const [searchQuery, setSearchQuery] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [sortBy, setSortBy] = useState('relevance');

  // Get user's personas for skill matching
  const [userPersonas, setUserPersonas] = useState<any[]>([]);

  useEffect(() => {
    // Redirect poster mode users to poster-home
    if (mode === 'poster') {
      router.push('/poster-home');
      return;
    }

    const personas = JSON.parse(localStorage.getItem('workerPersonas') || '[]');
    setUserPersonas(personas);
  }, [mode, router]);

  const handleFilterToggle = (filterId: string) => {
    setFilters(prev => prev.map(filter =>
      filter.id === filterId
        ? { ...filter, active: !filter.active }
        : filter
    ));
  };

  const handleBookmarkToggle = (jobId: string) => {
    setJobs(prev => prev.map(job =>
      job.id === jobId
        ? { ...job, isBookmarked: !job.isBookmarked }
        : job
    ));
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-50';
    if (score >= 80) return 'text-blue-600 bg-blue-50';
    return 'text-gray-600 bg-gray-50';
  };

  return (
    <div className="px-4 py-6 space-y-6">
      {/* Welcome Section */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold text-gray-900">
          {mode === 'worker' ? 'Find Jobs Near You' : 'Find Trusted Workers'}
        </h1>
        <p className="text-gray-600">
          {mode === 'worker'
            ? 'Discover opportunities that match your skills'
            : 'Hire reliable workers for your tasks'
          }
        </p>
      </div>

      {/* Search Bar */}
      <div className="relative">
        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder={mode === 'worker' ? 'Search for jobs...' : 'Search for workers...'}
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Location */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 text-gray-600">
          <MapPinIcon className="w-5 h-5" />
          <span className="text-sm">Mumbai, Maharashtra</span>
        </div>
        <button
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="flex items-center space-x-1 text-blue-600 text-sm hover:text-blue-700"
        >
          <ArrowPathIcon className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Mode-specific Content */}
      {mode === 'worker' ? (
        <WorkerJobsFeed
          jobs={jobs}
          filters={filters}
          onFilterToggle={handleFilterToggle}
          onBookmarkToggle={handleBookmarkToggle}
          userPersonas={userPersonas}
        />
      ) : (
        <PosterHomeContent />
      )}
    </div>
  );
}

// Worker Jobs Feed Component
function WorkerJobsFeed({
  jobs,
  filters,
  onFilterToggle,
  onBookmarkToggle,
  userPersonas
}: {
  jobs: any[];
  filters: any[];
  onFilterToggle: (filterId: string) => void;
  onBookmarkToggle: (jobId: string) => void;
  userPersonas: any[];
}) {
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-50';
    if (score >= 80) return 'text-blue-600 bg-blue-50';
    return 'text-gray-600 bg-gray-50';
  };

  return (
    <>
      {/* Filter Chips */}
      <div className="flex items-center space-x-2 overflow-x-auto pb-2">
        <FunnelIcon className="w-5 h-5 text-gray-400 flex-shrink-0" />
        {filters.map((filter) => (
          <button
            key={filter.id}
            onClick={() => onFilterToggle(filter.id)}
            className={`px-3 py-1.5 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${
              filter.active
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {filter.label}
          </button>
        ))}
      </div>

      {/* Jobs Count & Sort */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">{jobs.length} jobs found</p>
        <select className="text-sm border border-gray-300 rounded-lg px-3 py-1.5 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
          <option value="relevance">Sort by Relevance</option>
          <option value="distance">Sort by Distance</option>
          <option value="pay">Sort by Pay</option>
          <option value="posted">Sort by Posted Time</option>
        </select>
      </div>

      {/* Jobs List */}
      <div className="space-y-4">
        {jobs.map((job) => (
          <JobCard
            key={job.id}
            job={job}
            onBookmarkToggle={onBookmarkToggle}
            userPersonas={userPersonas}
          />
        ))}
      </div>

      {/* Load More */}
      <div className="text-center pt-4">
        <button className="px-6 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
          Load More Jobs
        </button>
      </div>
    </>
  );
}

// Job Card Component
function JobCard({ job, onBookmarkToggle, userPersonas }: {
  job: any;
  onBookmarkToggle: (jobId: string) => void;
  userPersonas: any[];
}) {
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-50';
    if (score >= 80) return 'text-blue-600 bg-blue-50';
    return 'text-gray-600 bg-gray-50';
  };

  return (
    <Link href={`/jobs/${job.id}`} className="block">
      <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2">
              {job.title}
            </h3>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>{job.category}</span>
              <span>•</span>
              <span>{job.location}</span>
            </div>
          </div>
          <button
            onClick={(e) => {
              e.preventDefault();
              onBookmarkToggle(job.id);
            }}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            {job.isBookmarked ? (
              <BookmarkSolidIcon className="w-5 h-5 text-blue-600" />
            ) : (
              <BookmarkIcon className="w-5 h-5 text-gray-400" />
            )}
          </button>
        </div>

        {/* Description */}
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {job.description}
        </p>

        {/* Tags */}
        <div className="flex items-center space-x-2 mb-3">
          {job.urgency === 'urgent' && (
            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getUrgencyColor(job.urgency)}`}>
              Urgent
            </span>
          )}
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getMatchScoreColor(job.matchScore)}`}>
            {job.matchScore}% match
          </span>
          <span className="text-xs text-gray-500">
            <ClockIcon className="w-3 h-3 inline mr-1" />
            {job.postedTime}
          </span>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="text-lg font-bold text-green-600">
              ₹{job.rate}/{job.rateType === 'hourly' ? 'hr' : 'day'}
            </div>
            <div className="text-sm text-gray-500">
              📍 {job.distance} km away
            </div>
          </div>
          <div className="text-sm text-gray-500">
            {job.duration}
          </div>
        </div>
      </div>
    </Link>
  );
}

function WorkerHomeContent() {
  return (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-blue-600">12</div>
          <div className="text-sm text-blue-800">New Jobs Today</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-green-600">₹2,400</div>
          <div className="text-sm text-green-800">This Week</div>
        </div>
      </div>

      {/* Recent Jobs */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900">Recent Jobs</h2>
        <div className="space-y-3">
          {[1, 2, 3].map((job) => (
            <div key={job} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-medium text-gray-900">Plumbing Work Required</h3>
                <span className="text-sm text-green-600 font-medium">₹800/day</span>
              </div>
              <p className="text-sm text-gray-600 mb-2">Fix kitchen sink and bathroom tap</p>
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>📍 2.3 km away</span>
                <span>⏰ Posted 2 hours ago</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

function PosterHomeContent() {
  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="bg-green-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold text-green-800 mb-2">Post a Job</h2>
        <p className="text-sm text-green-700 mb-3">Get your work done by trusted professionals</p>
        <button className="w-full bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors">
          Post New Job
        </button>
      </div>

      {/* Nearby Workers */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900">Nearby Workers</h2>
        <div className="space-y-3">
          {[1, 2, 3].map((worker) => (
            <div key={worker} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-lg">👨‍🔧</span>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">Rajesh Kumar</h3>
                  <p className="text-sm text-gray-600">Plumber • 4.8 ⭐ (127 reviews)</p>
                  <div className="flex items-center justify-between mt-1">
                    <span className="text-sm text-gray-500">📍 1.2 km away</span>
                    <span className="text-sm font-medium text-green-600">₹600/day</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
