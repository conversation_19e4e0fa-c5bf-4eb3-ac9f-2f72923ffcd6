'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/contexts/ThemeContext';
import { 
  MagnifyingGlassIcon, 
  MapPinIcon, 
  XMarkIcon,
  ArrowRightIcon 
} from '@heroicons/react/24/outline';

interface CoachMark {
  id: string;
  title: string;
  description: string;
  position: 'top' | 'bottom' | 'center';
  targetId?: string;
}

const COACH_MARKS: CoachMark[] = [
  {
    id: 'welcome',
    title: 'Welcome to Ozgaar! 🎉',
    description: 'You\'re all set up as a Worker. Let\'s take a quick tour to help you find your first job.',
    position: 'center'
  },
  {
    id: 'mode-switch',
    title: 'Switch Modes Anytime',
    description: 'Tap here to switch between Worker and Poster modes. You can both find work and hire others!',
    position: 'top',
    targetId: 'mode-switcher'
  },
  {
    id: 'search',
    title: 'Find Jobs Near You',
    description: 'Search for jobs that match your skills. We\'ll show you opportunities in your area first.',
    position: 'bottom',
    targetId: 'search-bar'
  },
  {
    id: 'jobs-tab',
    title: 'Track Your Applications',
    description: 'Check your job applications and their status in the My Jobs tab.',
    position: 'top',
    targetId: 'jobs-tab'
  },
  {
    id: 'ready',
    title: 'You\'re Ready! 🚀',
    description: 'Start browsing jobs below or use the search to find specific opportunities. Good luck!',
    position: 'center'
  }
];

export default function FirstRunHomePage() {
  const router = useRouter();
  const { setMode } = useTheme();
  const [currentCoachMark, setCurrentCoachMark] = useState(0);
  const [showCoachMarks, setShowCoachMarks] = useState(true);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [userPersonas, setUserPersonas] = useState<any[]>([]);

  useEffect(() => {
    // Set mode to worker
    setMode('worker');
    
    // Load user data
    const profile = JSON.parse(localStorage.getItem('workerProfile') || '{}');
    const personas = JSON.parse(localStorage.getItem('workerPersonas') || '[]');
    setUserProfile(profile);
    setUserPersonas(personas);
  }, [setMode]);

  const handleNextCoachMark = () => {
    if (currentCoachMark < COACH_MARKS.length - 1) {
      setCurrentCoachMark(currentCoachMark + 1);
    } else {
      handleFinishTour();
    }
  };

  const handleSkipTour = () => {
    setShowCoachMarks(false);
    localStorage.setItem('hasSeenWorkerTour', 'true');
    router.push('/');
  };

  const handleFinishTour = () => {
    setShowCoachMarks(false);
    localStorage.setItem('hasSeenWorkerTour', 'true');
    router.push('/');
  };

  const currentMark = COACH_MARKS[currentCoachMark];
  const activePersona = userPersonas.find(p => p.isActive) || userPersonas[0];

  return (
    <div className="min-h-screen bg-white relative">
      {/* Main Content */}
      <div className="px-4 py-6 space-y-6">
        {/* Welcome Header */}
        <div className="text-center space-y-2">
          <h1 className="text-2xl font-bold text-gray-900">
            Welcome, {userProfile?.fullName?.split(' ')[0] || 'Worker'}! 👋
          </h1>
          <p className="text-gray-600">
            Your {activePersona?.skill || 'Worker'} persona is ready. Let's find you some work!
          </p>
        </div>

        {/* Search Bar */}
        <div className="relative" id="search-bar">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search for jobs..."
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Location */}
        <div className="flex items-center space-x-2 text-gray-600">
          <MapPinIcon className="w-5 h-5" />
          <span className="text-sm">Showing results near Mumbai, Maharashtra</span>
        </div>

        {/* Persona Status */}
        {activePersona && (
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-blue-900">
                  Active Persona: {activePersona.skill}
                </h3>
                <p className="text-sm text-blue-700">
                  {activePersona.hourlyRate && `₹${activePersona.hourlyRate}/hour`}
                  {activePersona.hourlyRate && activePersona.dailyRate && ' • '}
                  {activePersona.dailyRate && `₹${activePersona.dailyRate}/day`}
                </p>
              </div>
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            </div>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-blue-600">0</div>
            <div className="text-sm text-blue-800">Applications</div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-green-600">₹0</div>
            <div className="text-sm text-green-800">Earnings</div>
          </div>
        </div>

        {/* Sample Jobs */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Jobs Near You</h2>
          <div className="space-y-3">
            {[1, 2, 3].map((job) => (
              <div key={job} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium text-gray-900">
                    {activePersona?.skill || 'Plumber'} Work Required
                  </h3>
                  <span className="text-sm text-green-600 font-medium">
                    ₹{activePersona?.hourlyRate || '200'}/hour
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  Need experienced {activePersona?.skill?.toLowerCase() || 'plumber'} for urgent work
                </p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>📍 {job * 0.5 + 1} km away</span>
                  <span>⏰ Posted {job} hours ago</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Coach Mark Overlay */}
      {showCoachMarks && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl">
            <div className="text-center space-y-4">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                <span className="text-2xl">
                  {currentCoachMark === 0 ? '👋' : 
                   currentCoachMark === 1 ? '🔄' :
                   currentCoachMark === 2 ? '🔍' :
                   currentCoachMark === 3 ? '📋' : '🚀'}
                </span>
              </div>
              
              <div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">
                  {currentMark.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {currentMark.description}
                </p>
              </div>

              <div className="flex items-center justify-center space-x-2 py-2">
                {COACH_MARKS.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentCoachMark ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleSkipTour}
                  className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Skip Tour
                </button>
                <button
                  onClick={handleNextCoachMark}
                  className="flex-1 py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                >
                  {currentCoachMark === COACH_MARKS.length - 1 ? (
                    'Get Started!'
                  ) : (
                    <>
                      Next
                      <ArrowRightIcon className="w-4 h-4 ml-1" />
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Skip Button */}
      <button
        onClick={handleSkipTour}
        className="fixed top-4 right-4 p-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-shadow z-40"
      >
        <XMarkIcon className="w-6 h-6 text-gray-600" />
      </button>
    </div>
  );
}
