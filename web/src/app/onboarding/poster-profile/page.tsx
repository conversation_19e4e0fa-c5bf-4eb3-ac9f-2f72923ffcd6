'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon, CameraIcon, UserIcon, BuildingOfficeIcon } from '@heroicons/react/24/outline';

const BUSINESS_CATEGORIES = [
  'Home Services',
  'Construction',
  'Retail/Shop',
  'Restaurant/Food',
  'Office/Corporate',
  'Event Management',
  'Real Estate',
  'Healthcare',
  'Education',
  'Manufacturing',
  'Agriculture',
  'Transportation',
  'Technology',
  'Beauty/Salon',
  'Fitness/Gym',
  'Other'
];

const HIRING_FREQUENCY = [
  { value: 'first-time', label: 'First time hiring', description: 'New to hiring workers' },
  { value: 'occasional', label: 'Occasionally', description: '1-2 times per month' },
  { value: 'regular', label: 'Regularly', description: 'Weekly or more often' },
  { value: 'business', label: 'For my business', description: 'Ongoing business needs' }
];

export default function PosterProfilePage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    photo: null as File | null,
    fullName: '',
    phone: '',
    businessName: '',
    businessCategory: '',
    hiringFrequency: '',
    location: '',
    about: ''
  });
  const [completeness, setCompleteness] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Calculate profile completeness
  React.useEffect(() => {
    const requiredFields = [
      formData.fullName,
      formData.phone,
      formData.businessCategory,
      formData.hiringFrequency,
      formData.location
    ];
    const filledFields = requiredFields.filter(field => field.trim() !== '').length;
    const photoBonus = formData.photo ? 1 : 0;
    const businessNameBonus = formData.businessName.trim() !== '' ? 1 : 0;
    const aboutBonus = formData.about.trim() !== '' ? 1 : 0;

    const total = filledFields + photoBonus + businessNameBonus + aboutBonus;
    setCompleteness(Math.round((total / 8) * 100));
  }, [formData]);

  const handleInputChange = (field: string, value: string | File | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleInputChange('photo', file);
    }
  };

  const isFormValid = () => {
    return formData.fullName.trim() !== '' &&
           formData.phone.trim() !== '' &&
           formData.businessCategory !== '' &&
           formData.hiringFrequency !== '' &&
           formData.location.trim() !== '';
  };

  const handleSubmit = async () => {
    if (!isFormValid()) return;

    setIsLoading(true);

    // Store profile data
    localStorage.setItem('posterProfile', JSON.stringify(formData));

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      router.push('/onboarding/poster-first-run-home');
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <Link href="/onboarding/mode-selection" className="p-2 hover:bg-gray-100 rounded-full">
          <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
        </Link>
        <h1 className="text-lg font-semibold text-gray-900">Poster Profile</h1>
        <div className="w-10" />
      </div>

      {/* Progress Bar */}
      <div className="px-6 py-4 bg-green-50">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-green-800">Profile Completeness</span>
          <span className="text-sm font-bold text-green-800">{completeness}%</span>
        </div>
        <div className="w-full bg-green-200 rounded-full h-2">
          <div
            className="bg-green-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${completeness}%` }}
          />
        </div>
      </div>

      {/* Form */}
      <div className="flex-1 px-6 py-6 space-y-6 overflow-y-auto">
        {/* Photo Upload */}
        <div className="text-center">
          <div className="relative inline-block">
            <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
              {formData.photo ? (
                <img
                  src={URL.createObjectURL(formData.photo)}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : (
                <UserIcon className="w-12 h-12 text-gray-400" />
              )}
            </div>
            <label className="absolute -bottom-1 -right-1 w-8 h-8 bg-green-600 rounded-full flex items-center justify-center cursor-pointer hover:bg-green-700 transition-colors">
              <CameraIcon className="w-4 h-4 text-white" />
              <input
                type="file"
                accept="image/*"
                onChange={handlePhotoUpload}
                className="hidden"
              />
            </label>
          </div>
          <p className="text-sm text-gray-600 mt-2">Add your photo</p>
        </div>

        {/* Full Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Full Name *
          </label>
          <input
            type="text"
            value={formData.fullName}
            onChange={(e) => handleInputChange('fullName', e.target.value)}
            placeholder="Enter your full name"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>

        {/* Phone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Phone Number *
          </label>
          <div className="flex">
            <div className="flex items-center px-3 py-3 bg-gray-50 border border-r-0 border-gray-300 rounded-l-lg">
              <span className="text-gray-900 font-medium">🇮🇳 +91</span>
            </div>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="98765-43210"
              className="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Business Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Business Name (Optional)
          </label>
          <div className="relative">
            <BuildingOfficeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={formData.businessName}
              onChange={(e) => handleInputChange('businessName', e.target.value)}
              placeholder="Your business or company name"
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Business Category */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            What type of work do you need help with? *
          </label>
          <select
            value={formData.businessCategory}
            onChange={(e) => handleInputChange('businessCategory', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          >
            <option value="">Select category</option>
            {BUSINESS_CATEGORIES.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>

        {/* Hiring Frequency */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            How often do you hire workers? *
          </label>
          <div className="space-y-3">
            {HIRING_FREQUENCY.map(freq => (
              <button
                key={freq.value}
                onClick={() => handleInputChange('hiringFrequency', freq.value)}
                className={`w-full p-4 rounded-lg border-2 text-left transition-colors ${
                  formData.hiringFrequency === freq.value
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-200 hover:border-green-300'
                }`}
              >
                <div className="font-medium text-gray-900">{freq.label}</div>
                <div className="text-sm text-gray-600">{freq.description}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Location */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Location *
          </label>
          <input
            type="text"
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            placeholder="e.g., Andheri West, Mumbai"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>

        {/* About */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            About Your Needs (Optional)
          </label>
          <textarea
            value={formData.about}
            onChange={(e) => handleInputChange('about', e.target.value)}
            placeholder="Tell workers about the type of help you typically need..."
            rows={3}
            maxLength={150}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
          />
          <p className="text-xs text-gray-500 mt-1">{formData.about.length}/150 characters</p>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="px-6 pb-8 pt-4 border-t border-gray-100">
        <button
          onClick={handleSubmit}
          disabled={!isFormValid() || isLoading}
          className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors ${
            isFormValid() && !isLoading
              ? 'bg-green-600 text-white hover:bg-green-700'
              : 'bg-gray-200 text-gray-500 cursor-not-allowed'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              Saving Profile...
            </div>
          ) : (
            'Save & Continue'
          )}
        </button>
      </div>
    </div>
  );
}
