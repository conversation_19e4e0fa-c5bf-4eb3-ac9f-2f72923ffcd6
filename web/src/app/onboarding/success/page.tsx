'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { CheckCircleIcon } from '@heroicons/react/24/solid';

export default function SuccessPage() {
  const router = useRouter();
  const [showAnimation, setShowAnimation] = useState(false);

  useEffect(() => {
    // Trigger animation after component mounts
    setTimeout(() => setShowAnimation(true), 100);
    
    // Auto-redirect after 2 seconds
    const timer = setTimeout(() => {
      router.push('/onboarding/mode-selection');
    }, 2000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex flex-col items-center justify-center px-6">
      {/* Success Animation */}
      <div className="text-center">
        <div 
          className={`transition-all duration-1000 ${
            showAnimation 
              ? 'scale-100 opacity-100' 
              : 'scale-50 opacity-0'
          }`}
        >
          <CheckCircleIcon className="w-32 h-32 text-green-500 mx-auto mb-6" />
        </div>

        <div 
          className={`transition-all duration-1000 delay-300 ${
            showAnimation 
              ? 'translate-y-0 opacity-100' 
              : 'translate-y-4 opacity-0'
          }`}
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-3">
            Verified!
          </h1>
          <p className="text-xl text-gray-600 mb-2">
            Welcome to Ozgaar
          </p>
          <p className="text-gray-500">
            Setting up your account...
          </p>
        </div>

        {/* Loading dots */}
        <div 
          className={`flex justify-center space-x-2 mt-8 transition-all duration-1000 delay-500 ${
            showAnimation 
              ? 'opacity-100' 
              : 'opacity-0'
          }`}
        >
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
        </div>
      </div>

      {/* Skip button (hidden, but available for manual navigation) */}
      <button
        onClick={() => router.push('/onboarding/mode-selection')}
        className="absolute bottom-8 text-gray-400 text-sm hover:text-gray-600 opacity-0"
      >
        Continue
      </button>
    </div>
  );
}
