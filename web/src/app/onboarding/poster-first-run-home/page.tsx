'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/contexts/ThemeContext';
import { 
  MagnifyingGlassIcon, 
  MapPinIcon, 
  XMarkIcon,
  ArrowRightIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

interface CoachMark {
  id: string;
  title: string;
  description: string;
  position: 'top' | 'bottom' | 'center';
  targetId?: string;
}

const COACH_MARKS: CoachMark[] = [
  {
    id: 'welcome',
    title: 'Welcome to Ozgaar! 🎉',
    description: 'You\'re all set up as a Poster. Let\'s take a quick tour to help you find trusted workers.',
    position: 'center'
  },
  {
    id: 'post-job',
    title: 'Post Your First Job',
    description: 'Tap the "Post a Job" button to create your first job posting and find workers quickly.',
    position: 'bottom',
    targetId: 'post-job-button'
  },
  {
    id: 'search',
    title: 'Search for Workers',
    description: 'Use the search to find specific types of workers in your area.',
    position: 'bottom',
    targetId: 'search-bar'
  },
  {
    id: 'worker-cards',
    title: 'Browse Nearby Workers',
    description: 'See worker profiles with their skills, ratings, and rates. Tap to view full profiles.',
    position: 'top',
    targetId: 'worker-cards'
  },
  {
    id: 'ready',
    title: 'You\'re Ready! 🚀',
    description: 'Start by posting a job or browse workers below. We\'ll help you find the right person for your needs!',
    position: 'center'
  }
];

// Mock worker data
const MOCK_WORKERS = [
  {
    id: '1',
    name: 'Rajesh Kumar',
    persona: 'Plumber',
    rating: 4.8,
    reviewCount: 127,
    hourlyRate: 200,
    dailyRate: 1200,
    distance: 1.2,
    avatar: '👨‍🔧',
    specialties: ['Kitchen repairs', 'Bathroom fitting'],
    isOnline: true
  },
  {
    id: '2',
    name: 'Priya Sharma',
    persona: 'Cleaner',
    rating: 4.9,
    reviewCount: 89,
    hourlyRate: 120,
    dailyRate: 600,
    distance: 0.8,
    avatar: '👩‍💼',
    specialties: ['Deep cleaning', 'Office cleaning'],
    isOnline: true
  },
  {
    id: '3',
    name: 'Amit Singh',
    persona: 'Electrician',
    rating: 4.7,
    reviewCount: 156,
    hourlyRate: 250,
    dailyRate: 1500,
    distance: 2.1,
    avatar: '👨‍🔬',
    specialties: ['Wiring', 'AC installation'],
    isOnline: false
  }
];

export default function PosterFirstRunHomePage() {
  const router = useRouter();
  const { setMode } = useTheme();
  const [currentCoachMark, setCurrentCoachMark] = useState(0);
  const [showCoachMarks, setShowCoachMarks] = useState(true);
  const [userProfile, setUserProfile] = useState<any>(null);

  useEffect(() => {
    // Set mode to poster
    setMode('poster');
    
    // Load user data
    const profile = JSON.parse(localStorage.getItem('posterProfile') || '{}');
    setUserProfile(profile);
  }, [setMode]);

  const handleNextCoachMark = () => {
    if (currentCoachMark < COACH_MARKS.length - 1) {
      setCurrentCoachMark(currentCoachMark + 1);
    } else {
      handleFinishTour();
    }
  };

  const handleSkipTour = () => {
    setShowCoachMarks(false);
    localStorage.setItem('hasSeenPosterTour', 'true');
    router.push('/');
  };

  const handleFinishTour = () => {
    setShowCoachMarks(false);
    localStorage.setItem('hasSeenPosterTour', 'true');
    router.push('/');
  };

  const currentMark = COACH_MARKS[currentCoachMark];

  return (
    <div className="min-h-screen bg-white relative">
      {/* Main Content */}
      <div className="px-4 py-6 space-y-6">
        {/* Welcome Header */}
        <div className="text-center space-y-2">
          <h1 className="text-2xl font-bold text-gray-900">
            Welcome, {userProfile?.fullName?.split(' ')[0] || 'Poster'}! 👋
          </h1>
          <p className="text-gray-600">
            Ready to find trusted workers for your {userProfile?.businessCategory?.toLowerCase() || 'business'} needs?
          </p>
        </div>

        {/* Post Job CTA */}
        <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-2xl text-white" id="post-job-button">
          <h2 className="text-xl font-bold mb-2">Post Your First Job</h2>
          <p className="text-green-100 mb-4">Get your work done by trusted professionals</p>
          <button className="w-full bg-white text-green-600 py-3 px-6 rounded-xl font-semibold hover:bg-green-50 transition-colors flex items-center justify-center">
            <PlusIcon className="w-5 h-5 mr-2" />
            Post a Job
          </button>
        </div>

        {/* Search Bar */}
        <div className="relative" id="search-bar">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search for workers..."
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>

        {/* Location */}
        <div className="flex items-center space-x-2 text-gray-600">
          <MapPinIcon className="w-5 h-5" />
          <span className="text-sm">Showing workers near {userProfile?.location || 'Mumbai, Maharashtra'}</span>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-green-50 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-green-600">0</div>
            <div className="text-sm text-green-800">Jobs Posted</div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-blue-600">{MOCK_WORKERS.filter(w => w.isOnline).length}</div>
            <div className="text-sm text-blue-800">Workers Online</div>
          </div>
        </div>

        {/* Nearby Workers */}
        <div className="space-y-4" id="worker-cards">
          <h2 className="text-lg font-semibold text-gray-900">Nearby Workers</h2>
          <div className="space-y-3">
            {MOCK_WORKERS.map((worker) => (
              <div key={worker.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                      <span className="text-lg">{worker.avatar}</span>
                    </div>
                    {worker.isOnline && (
                      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-gray-900">{worker.name}</h3>
                      <div className="text-right">
                        <div className="text-sm font-medium text-green-600">
                          ₹{worker.hourlyRate}/hr
                        </div>
                        <div className="text-xs text-gray-500">
                          ₹{worker.dailyRate}/day
                        </div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">
                      {worker.persona} • {worker.rating} ⭐ ({worker.reviewCount} reviews)
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <div className="flex flex-wrap gap-1">
                        {worker.specialties.slice(0, 2).map((specialty, index) => (
                          <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                            {specialty}
                          </span>
                        ))}
                      </div>
                      <span className="text-sm text-gray-500">📍 {worker.distance} km away</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Tips */}
        <div className="bg-yellow-50 p-4 rounded-xl border border-yellow-200">
          <div className="flex items-start space-x-3">
            <span className="text-yellow-600 text-lg">💡</span>
            <div>
              <h4 className="font-medium text-yellow-800 mb-1">Pro Tip</h4>
              <p className="text-yellow-700 text-sm">
                Post detailed job descriptions to attract the best workers. Include photos if possible!
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Coach Mark Overlay */}
      {showCoachMarks && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl">
            <div className="text-center space-y-4">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <span className="text-2xl">
                  {currentCoachMark === 0 ? '👋' : 
                   currentCoachMark === 1 ? '📝' :
                   currentCoachMark === 2 ? '🔍' :
                   currentCoachMark === 3 ? '👥' : '🚀'}
                </span>
              </div>
              
              <div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">
                  {currentMark.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {currentMark.description}
                </p>
              </div>

              <div className="flex items-center justify-center space-x-2 py-2">
                {COACH_MARKS.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentCoachMark ? 'bg-green-600' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleSkipTour}
                  className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Skip Tour
                </button>
                <button
                  onClick={handleNextCoachMark}
                  className="flex-1 py-3 px-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center"
                >
                  {currentCoachMark === COACH_MARKS.length - 1 ? (
                    'Get Started!'
                  ) : (
                    <>
                      Next
                      <ArrowRightIcon className="w-4 h-4 ml-1" />
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Skip Button */}
      <button
        onClick={handleSkipTour}
        className="fixed top-4 right-4 p-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-shadow z-40"
      >
        <XMarkIcon className="w-6 h-6 text-gray-600" />
      </button>
    </div>
  );
}
