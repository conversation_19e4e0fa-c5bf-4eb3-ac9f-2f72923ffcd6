'use client';

import React from 'react';
import Link from 'next/link';
import { ChevronRightIcon } from '@heroicons/react/24/outline';

export default function WelcomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex flex-col">
      {/* Header */}
      <div className="flex-1 flex flex-col justify-center items-center px-6 py-12">
        {/* Logo */}
        <div className="mb-8">
          <div className="w-24 h-24 bg-blue-600 rounded-2xl flex items-center justify-center mb-4">
            <span className="text-3xl font-bold text-white">R</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 text-center">Ozgaar</h1>
        </div>

        {/* Tagline */}
        <div className="text-center mb-12">
          <h2 className="text-2xl font-semibold text-gray-900 mb-3">
            Find work. Hire fast. Nearby.
          </h2>
          <p className="text-gray-600 text-lg leading-relaxed max-w-sm">
            Connect with trusted workers or find jobs that match your skills
          </p>
        </div>

        {/* Features */}
        <div className="space-y-4 mb-12 w-full max-w-sm">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600">🔍</span>
            </div>
            <span className="text-gray-700">Find jobs near you</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600">⚡</span>
            </div>
            <span className="text-gray-700">Quick hiring process</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600">🛡️</span>
            </div>
            <span className="text-gray-700">Verified profiles</span>
          </div>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="px-6 pb-8 space-y-4">
        {/* Primary CTA */}
        <Link
          href="/onboarding/phone"
          className="w-full bg-blue-600 text-white py-4 px-6 rounded-xl font-semibold text-lg flex items-center justify-center hover:bg-blue-700 transition-colors shadow-lg"
        >
          Get Started
          <ChevronRightIcon className="w-5 h-5 ml-2" />
        </Link>

        {/* Secondary CTA */}
        <button className="w-full bg-white border-2 border-orange-300 text-orange-700 py-4 px-6 rounded-xl font-semibold text-lg hover:bg-orange-50 transition-colors">
          Continue in हिंदी
        </button>

        {/* Legal Links */}
        <div className="flex justify-center space-x-6 pt-4">
          <Link href="/terms" className="text-sm text-gray-500 hover:text-gray-700">
            Terms
          </Link>
          <Link href="/privacy" className="text-sm text-gray-500 hover:text-gray-700">
            Privacy
          </Link>
        </div>
      </div>
    </div>
  );
}
