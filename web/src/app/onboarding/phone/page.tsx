'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon, PhoneIcon } from '@heroicons/react/24/outline';

export default function PhoneRegistrationPage() {
  const router = useRouter();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isValid, setIsValid] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const formatPhoneNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    
    // Limit to 10 digits
    const limited = digits.slice(0, 10);
    
    // Format as 98765-43210
    if (limited.length > 5) {
      return `${limited.slice(0, 5)}-${limited.slice(5)}`;
    }
    return limited;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    setPhoneNumber(formatted);
    
    // Validate phone number (should be 10 digits)
    const digits = formatted.replace(/\D/g, '');
    const valid = digits.length === 10 && /^[6-9]/.test(digits);
    setIsValid(valid);
    
    if (error) setError('');
  };

  const handleSendOTP = async () => {
    if (!isValid) {
      setError('Please enter a valid 10-digit mobile number');
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      // Store phone number for OTP verification
      localStorage.setItem('phoneNumber', phoneNumber);
      router.push('/onboarding/otp');
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <Link href="/onboarding/welcome" className="p-2 hover:bg-gray-100 rounded-full">
          <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
        </Link>
        <h1 className="text-lg font-semibold text-gray-900">Phone Verification</h1>
        <div className="w-10" /> {/* Spacer */}
      </div>

      {/* Content */}
      <div className="flex-1 px-6 py-8">
        {/* Icon */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <PhoneIcon className="w-10 h-10 text-blue-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Enter your mobile number
          </h2>
          <p className="text-gray-600 leading-relaxed">
            We'll send you a verification code to confirm your number
          </p>
        </div>

        {/* Phone Input */}
        <div className="space-y-4">
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mobile Number
            </label>
            <div className="flex">
              <div className="flex items-center px-3 py-3 bg-gray-50 border border-r-0 border-gray-300 rounded-l-lg">
                <span className="text-gray-900 font-medium">🇮🇳 +91</span>
              </div>
              <input
                type="tel"
                value={phoneNumber}
                onChange={handlePhoneChange}
                placeholder="98765-43210"
                className={`flex-1 px-4 py-3 border rounded-r-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg ${
                  error ? 'border-red-300' : 'border-gray-300'
                }`}
                maxLength={11} // 5 digits + hyphen + 5 digits
              />
            </div>
            {error && (
              <p className="mt-2 text-sm text-red-600">{error}</p>
            )}
            {isValid && (
              <p className="mt-2 text-sm text-green-600">✓ Valid mobile number</p>
            )}
          </div>

          {/* Info Text */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm text-blue-800">
              <span className="font-medium">Note:</span> Standard SMS rates may apply. 
              No charges by app.
            </p>
          </div>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="px-6 pb-8">
        <button
          onClick={handleSendOTP}
          disabled={!isValid || isLoading}
          className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors ${
            isValid && !isLoading
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-200 text-gray-500 cursor-not-allowed'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              Sending OTP...
            </div>
          ) : (
            'Send OTP'
          )}
        </button>
      </div>
    </div>
  );
}
