'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon, ArrowRightIcon, CheckIcon } from '@heroicons/react/24/outline';

const INDIAN_SKILLS = [
  'Plumber', 'Electrician', '<PERSON>', '<PERSON>', '<PERSON>', 'Cleaner', 
  '<PERSON>', '<PERSON>', '<PERSON>er', 'Security Guard', 'Delivery Person', 
  'Mechanic', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Baby<PERSON>tter',
  'Elder Care', 'Pet Care', 'Home Repair', 'AC Technician', 'Computer Repair',
  'Mobile Repair', 'Photographer', 'Event Helper'
];

const EXPERIENCE_LEVELS = [
  { value: 'beginner', label: 'Beginner (0-1 years)', description: 'Learning the basics' },
  { value: 'intermediate', label: 'Intermediate (1-3 years)', description: 'Can handle most tasks' },
  { value: 'experienced', label: 'Experienced (3-5 years)', description: 'Skilled professional' },
  { value: 'expert', label: 'Expert (5+ years)', description: 'Master of the craft' }
];

export default function PersonaWizardPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [personaData, setPersonaData] = useState({
    skill: '',
    experience: '',
    hourlyRate: '',
    dailyRate: '',
    portfolio: [] as string[],
    specialties: [] as string[]
  });
  const [isLoading, setIsLoading] = useState(false);

  const totalSteps = 3;

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    setIsLoading(true);
    
    // Store persona data
    const existingPersonas = JSON.parse(localStorage.getItem('workerPersonas') || '[]');
    const newPersona = {
      id: Date.now().toString(),
      ...personaData,
      createdAt: new Date().toISOString(),
      isActive: true
    };
    existingPersonas.push(newPersona);
    localStorage.setItem('workerPersonas', JSON.stringify(existingPersonas));
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      router.push('/onboarding/first-run-home');
    }, 1500);
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return personaData.skill !== '' && personaData.experience !== '';
      case 2:
        return personaData.hourlyRate !== '' || personaData.dailyRate !== '';
      case 3:
        return true; // Portfolio is optional
      default:
        return false;
    }
  };

  const getMarketRateHint = (skill: string) => {
    const rates: { [key: string]: { hourly: string, daily: string } } = {
      'Plumber': { hourly: '₹150-300', daily: '₹800-1500' },
      'Electrician': { hourly: '₹200-400', daily: '₹1000-2000' },
      'Carpenter': { hourly: '₹180-350', daily: '₹900-1800' },
      'Painter': { hourly: '₹120-250', daily: '₹600-1200' },
      'Mason': { hourly: '₹200-400', daily: '₹1000-2000' },
      'Cleaner': { hourly: '₹80-150', daily: '₹400-800' },
      'Cook': { hourly: '₹100-200', daily: '₹500-1000' },
      'Driver': { hourly: '₹100-200', daily: '₹800-1500' }
    };
    return rates[skill] || { hourly: '₹100-300', daily: '₹500-1500' };
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <button 
          onClick={currentStep === 1 ? () => router.back() : handleBack}
          className="p-2 hover:bg-gray-100 rounded-full"
        >
          <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
        </button>
        <h1 className="text-lg font-semibold text-gray-900">Create Persona</h1>
        <div className="w-10" />
      </div>

      {/* Progress */}
      <div className="px-6 py-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-600">Step {currentStep} of {totalSteps}</span>
          <span className="text-sm text-gray-500">{Math.round((currentStep / totalSteps) * 100)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 px-6 py-6">
        {currentStep === 1 && (
          <Step1
            data={personaData}
            onChange={(data) => setPersonaData({ ...personaData, ...data })}
          />
        )}
        {currentStep === 2 && (
          <Step2
            data={personaData}
            onChange={(data) => setPersonaData({ ...personaData, ...data })}
            marketRates={getMarketRateHint(personaData.skill)}
          />
        )}
        {currentStep === 3 && (
          <Step3
            data={personaData}
            onChange={(data) => setPersonaData({ ...personaData, ...data })}
          />
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="px-6 pb-8 pt-4 border-t border-gray-100">
        <button
          onClick={handleNext}
          disabled={!isStepValid() || isLoading}
          className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors flex items-center justify-center ${
            isStepValid() && !isLoading
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-200 text-gray-500 cursor-not-allowed'
          }`}
        >
          {isLoading ? (
            <>
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              Creating Persona...
            </>
          ) : currentStep === totalSteps ? (
            <>
              <CheckIcon className="w-5 h-5 mr-2" />
              Complete Persona
            </>
          ) : (
            <>
              Next Step
              <ArrowRightIcon className="w-5 h-5 ml-2" />
            </>
          )}
        </button>
      </div>
    </div>
  );
}

// Step 1: Skill & Experience
function Step1({ data, onChange }: { data: any, onChange: (data: any) => void }) {
  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Choose Your Skill
        </h2>
        <p className="text-gray-600">
          What service will this persona offer?
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Primary Skill *
        </label>
        <select
          value={data.skill}
          onChange={(e) => onChange({ skill: e.target.value })}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">Select a skill</option>
          {INDIAN_SKILLS.map(skill => (
            <option key={skill} value={skill}>{skill}</option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Experience Level *
        </label>
        <div className="space-y-3">
          {EXPERIENCE_LEVELS.map(level => (
            <button
              key={level.value}
              onClick={() => onChange({ experience: level.value })}
              className={`w-full p-4 rounded-lg border-2 text-left transition-colors ${
                data.experience === level.value
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-blue-300'
              }`}
            >
              <div className="font-medium text-gray-900">{level.label}</div>
              <div className="text-sm text-gray-600">{level.description}</div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}

// Step 2: Rates
function Step2({ data, onChange, marketRates }: { data: any, onChange: (data: any) => void, marketRates: any }) {
  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Set Your Rates
        </h2>
        <p className="text-gray-600">
          How much do you charge for {data.skill.toLowerCase()} work?
        </p>
      </div>

      {/* Market Rate Hint */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="font-medium text-blue-800 mb-2">Market Rates in Mumbai</h3>
        <div className="text-sm text-blue-700">
          <div>Hourly: {marketRates.hourly}</div>
          <div>Daily: {marketRates.daily}</div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Hourly Rate (Optional)
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₹</span>
            <input
              type="number"
              value={data.hourlyRate}
              onChange={(e) => onChange({ hourlyRate: e.target.value })}
              placeholder="200"
              className="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Daily Rate (Optional)
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₹</span>
            <input
              type="number"
              value={data.dailyRate}
              onChange={(e) => onChange({ dailyRate: e.target.value })}
              placeholder="1000"
              className="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <p className="text-sm text-yellow-800">
          💡 <strong>Tip:</strong> You need to set at least one rate (hourly or daily). You can always adjust these later.
        </p>
      </div>
    </div>
  );
}

// Step 3: Portfolio (Optional)
function Step3({ data, onChange }: { data: any, onChange: (data: any) => void }) {
  const [newSpecialty, setNewSpecialty] = useState('');

  const addSpecialty = () => {
    if (newSpecialty.trim() && !data.specialties.includes(newSpecialty.trim())) {
      onChange({ specialties: [...data.specialties, newSpecialty.trim()] });
      setNewSpecialty('');
    }
  };

  const removeSpecialty = (specialty: string) => {
    onChange({ specialties: data.specialties.filter((s: string) => s !== specialty) });
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Add Specialties
        </h2>
        <p className="text-gray-600">
          What specific things are you great at? (Optional)
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Add a specialty
        </label>
        <div className="flex space-x-2">
          <input
            type="text"
            value={newSpecialty}
            onChange={(e) => setNewSpecialty(e.target.value)}
            placeholder="e.g., Kitchen sink repair"
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            onKeyPress={(e) => e.key === 'Enter' && addSpecialty()}
          />
          <button
            onClick={addSpecialty}
            className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Add
          </button>
        </div>
      </div>

      {data.specialties.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Your Specialties
          </label>
          <div className="flex flex-wrap gap-2">
            {data.specialties.map((specialty: string, index: number) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
              >
                {specialty}
                <button
                  onClick={() => removeSpecialty(specialty)}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        </div>
      )}

      <div className="bg-green-50 p-4 rounded-lg border border-green-200">
        <p className="text-sm text-green-800">
          ✨ <strong>Great!</strong> Your {data.skill} persona is almost ready. You can always add more specialties later.
        </p>
      </div>
    </div>
  );
}
