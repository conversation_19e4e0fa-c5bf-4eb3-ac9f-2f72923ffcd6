'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/contexts/ThemeContext';

export default function ModeSelectionPage() {
  const router = useRouter();
  const { setMode } = useTheme();
  const [selectedMode, setSelectedMode] = useState<'worker' | 'poster' | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleModeSelect = (mode: 'worker' | 'poster') => {
    setSelectedMode(mode);
  };

  const handleContinue = async () => {
    if (!selectedMode) return;
    
    setIsLoading(true);
    
    // Set the theme mode
    setMode(selectedMode);
    
    // Store selection in localStorage
    localStorage.setItem('userMode', selectedMode);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      
      // Navigate based on selected mode
      if (selectedMode === 'worker') {
        router.push('/onboarding/worker-profile');
      } else {
        router.push('/onboarding/poster-profile');
      }
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="text-center pt-12 pb-8 px-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-3">
          How do you want to use Ozgaar?
        </h1>
        <p className="text-gray-600 leading-relaxed">
          Choose your primary role. You can switch anytime.
        </p>
      </div>

      {/* Mode Cards */}
      <div className="flex-1 px-6 py-8 space-y-6">
        {/* Worker Card */}
        <button
          onClick={() => handleModeSelect('worker')}
          className={`w-full p-6 rounded-2xl border-2 transition-all duration-200 ${
            selectedMode === 'worker'
              ? 'border-blue-500 bg-blue-50 shadow-lg scale-105'
              : 'border-gray-200 bg-white hover:border-blue-300 hover:shadow-md'
          }`}
        >
          <div className="flex items-center space-x-4">
            <div className={`w-16 h-16 rounded-2xl flex items-center justify-center ${
              selectedMode === 'worker' ? 'bg-blue-500' : 'bg-blue-100'
            }`}>
              <span className={`text-3xl ${
                selectedMode === 'worker' ? 'text-white' : 'text-blue-600'
              }`}>
                🔧
              </span>
            </div>
            <div className="flex-1 text-left">
              <h3 className="text-xl font-bold text-gray-900 mb-1">
                I'm a Worker
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Find jobs near you that match your skills and earn money
              </p>
              <div className="flex items-center mt-2 space-x-4 text-xs text-gray-500">
                <span>• Browse jobs</span>
                <span>• Apply instantly</span>
                <span>• Track earnings</span>
              </div>
            </div>
            {selectedMode === 'worker' && (
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">✓</span>
              </div>
            )}
          </div>
        </button>

        {/* Poster Card */}
        <button
          onClick={() => handleModeSelect('poster')}
          className={`w-full p-6 rounded-2xl border-2 transition-all duration-200 ${
            selectedMode === 'poster'
              ? 'border-green-500 bg-green-50 shadow-lg scale-105'
              : 'border-gray-200 bg-white hover:border-green-300 hover:shadow-md'
          }`}
        >
          <div className="flex items-center space-x-4">
            <div className={`w-16 h-16 rounded-2xl flex items-center justify-center ${
              selectedMode === 'poster' ? 'bg-green-500' : 'bg-green-100'
            }`}>
              <span className={`text-3xl ${
                selectedMode === 'poster' ? 'text-white' : 'text-green-600'
              }`}>
                💼
              </span>
            </div>
            <div className="flex-1 text-left">
              <h3 className="text-xl font-bold text-gray-900 mb-1">
                I'm a Poster
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Hire trusted workers for your tasks and projects
              </p>
              <div className="flex items-center mt-2 space-x-4 text-xs text-gray-500">
                <span>• Post jobs</span>
                <span>• Find workers</span>
                <span>• Manage projects</span>
              </div>
            </div>
            {selectedMode === 'poster' && (
              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">✓</span>
              </div>
            )}
          </div>
        </button>

        {/* Info Note */}
        <div className="bg-gray-50 p-4 rounded-xl">
          <p className="text-sm text-gray-600 text-center">
            💡 <span className="font-medium">Good to know:</span> You can switch between Worker and Poster modes anytime from the top menu
          </p>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="px-6 pb-8">
        <button
          onClick={handleContinue}
          disabled={!selectedMode || isLoading}
          className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors ${
            selectedMode && !isLoading
              ? selectedMode === 'worker'
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-green-600 text-white hover:bg-green-700'
              : 'bg-gray-200 text-gray-500 cursor-not-allowed'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              Setting up...
            </div>
          ) : (
            'Continue'
          )}
        </button>
      </div>
    </div>
  );
}
