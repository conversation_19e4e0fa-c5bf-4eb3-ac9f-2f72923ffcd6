'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon, CameraIcon, UserIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

const INDIAN_SKILLS = [
  'Plumber',
  '<PERSON>ian',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Cleaner',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>er',
  'Security Guard',
  'Delivery Person',
  'Mechanic',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  'Baby<PERSON>tter',
  'Elder Care',
  'Pet Care',
  'Home Repair',
  'AC Technician',
  'Computer Repair',
  'Mobile Repair',
  'Photographer',
  'Event Helper'
];

export default function WorkerProfilePage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    photo: null as File | null,
    fullName: '',
    phone: '',
    primarySkill: '',
    experience: '',
    hourlyRate: '',
    dailyRate: '',
    about: '',
    phoneVisible: true
  });
  const [completeness, setCompleteness] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Calculate profile completeness
  React.useEffect(() => {
    const fields = [
      formData.fullName,
      formData.phone,
      formData.primarySkill,
      formData.experience,
      formData.hourlyRate || formData.dailyRate
    ];
    const filledFields = fields.filter(field => field.trim() !== '').length;
    const photoBonus = formData.photo ? 1 : 0;
    const aboutBonus = formData.about.trim() !== '' ? 1 : 0;

    const total = filledFields + photoBonus + aboutBonus;
    setCompleteness(Math.round((total / 7) * 100));
  }, [formData]);

  const handleInputChange = (field: string, value: string | boolean | File | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleInputChange('photo', file);
    }
  };

  const getMarketRateHint = (skill: string) => {
    const rates: { [key: string]: { hourly: string, daily: string } } = {
      'Plumber': { hourly: '₹150-300', daily: '₹800-1500' },
      'Electrician': { hourly: '₹200-400', daily: '₹1000-2000' },
      'Carpenter': { hourly: '₹180-350', daily: '₹900-1800' },
      'Painter': { hourly: '₹120-250', daily: '₹600-1200' },
      'Mason': { hourly: '₹200-400', daily: '₹1000-2000' },
      'Cleaner': { hourly: '₹80-150', daily: '₹400-800' },
      'Cook': { hourly: '₹100-200', daily: '₹500-1000' },
      'Driver': { hourly: '₹100-200', daily: '₹800-1500' }
    };
    return rates[skill] || { hourly: '₹100-300', daily: '₹500-1500' };
  };

  const isFormValid = () => {
    return formData.fullName.trim() !== '' &&
           formData.phone.trim() !== '' &&
           formData.primarySkill !== '' &&
           formData.experience !== '' &&
           (formData.hourlyRate !== '' || formData.dailyRate !== '');
  };

  const handleSubmit = async () => {
    if (!isFormValid()) return;

    setIsLoading(true);

    // Store profile data
    localStorage.setItem('workerProfile', JSON.stringify(formData));

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      router.push('/onboarding/persona-intro');
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <Link href="/onboarding/mode-selection" className="p-2 hover:bg-gray-100 rounded-full">
          <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
        </Link>
        <h1 className="text-lg font-semibold text-gray-900">Worker Profile</h1>
        <div className="w-10" />
      </div>

      {/* Progress Bar */}
      <div className="px-6 py-4 bg-blue-50">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-blue-800">Profile Completeness</span>
          <span className="text-sm font-bold text-blue-800">{completeness}%</span>
        </div>
        <div className="w-full bg-blue-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${completeness}%` }}
          />
        </div>
      </div>

      {/* Form */}
      <div className="flex-1 px-6 py-6 space-y-6 overflow-y-auto">
        {/* Photo Upload */}
        <div className="text-center">
          <div className="relative inline-block">
            <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
              {formData.photo ? (
                <img
                  src={URL.createObjectURL(formData.photo)}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : (
                <UserIcon className="w-12 h-12 text-gray-400" />
              )}
            </div>
            <label className="absolute -bottom-1 -right-1 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-colors">
              <CameraIcon className="w-4 h-4 text-white" />
              <input
                type="file"
                accept="image/*"
                onChange={handlePhotoUpload}
                className="hidden"
              />
            </label>
          </div>
          <p className="text-sm text-gray-600 mt-2">Add your photo</p>
        </div>

        {/* Full Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Full Name *
          </label>
          <input
            type="text"
            value={formData.fullName}
            onChange={(e) => handleInputChange('fullName', e.target.value)}
            placeholder="Enter your full name"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Phone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Phone Number *
          </label>
          <div className="space-y-2">
            <div className="flex">
              <div className="flex items-center px-3 py-3 bg-gray-50 border border-r-0 border-gray-300 rounded-l-lg">
                <span className="text-gray-900 font-medium">🇮🇳 +91</span>
              </div>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="98765-43210"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-center space-x-2">
              <button
                type="button"
                onClick={() => handleInputChange('phoneVisible', !formData.phoneVisible)}
                className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-800"
              >
                {formData.phoneVisible ? (
                  <EyeIcon className="w-4 h-4" />
                ) : (
                  <EyeSlashIcon className="w-4 h-4" />
                )}
                <span>{formData.phoneVisible ? 'Visible to employers' : 'Hidden from employers'}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Primary Skill */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Primary Skill *
          </label>
          <select
            value={formData.primarySkill}
            onChange={(e) => handleInputChange('primarySkill', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select your primary skill</option>
            {INDIAN_SKILLS.map(skill => (
              <option key={skill} value={skill}>{skill}</option>
            ))}
          </select>
        </div>

        {/* Experience */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Experience *
          </label>
          <select
            value={formData.experience}
            onChange={(e) => handleInputChange('experience', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select experience</option>
            <option value="0-1">0-1 years (Beginner)</option>
            <option value="1-3">1-3 years</option>
            <option value="3-5">3-5 years</option>
            <option value="5-10">5-10 years</option>
            <option value="10+">10+ years (Expert)</option>
          </select>
        </div>

        {/* Rates */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Your Rates * (Choose one or both)
          </label>
          {formData.primarySkill && (
            <div className="bg-blue-50 p-3 rounded-lg mb-3">
              <p className="text-sm text-blue-800">
                <span className="font-medium">Market rates for {formData.primarySkill} in Mumbai:</span>
                <br />
                Hourly: {getMarketRateHint(formData.primarySkill).hourly} |
                Daily: {getMarketRateHint(formData.primarySkill).daily}
              </p>
            </div>
          )}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-xs text-gray-600 mb-1">Hourly Rate</label>
              <input
                type="number"
                value={formData.hourlyRate}
                onChange={(e) => handleInputChange('hourlyRate', e.target.value)}
                placeholder="₹150"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-600 mb-1">Daily Rate</label>
              <input
                type="number"
                value={formData.dailyRate}
                onChange={(e) => handleInputChange('dailyRate', e.target.value)}
                placeholder="₹800"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* About */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            About You (Optional)
          </label>
          <textarea
            value={formData.about}
            onChange={(e) => handleInputChange('about', e.target.value)}
            placeholder="Tell employers about your skills and experience..."
            rows={3}
            maxLength={100}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          />
          <p className="text-xs text-gray-500 mt-1">{formData.about.length}/100 characters</p>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="px-6 pb-8 pt-4 border-t border-gray-100">
        <button
          onClick={handleSubmit}
          disabled={!isFormValid() || isLoading}
          className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors ${
            isFormValid() && !isLoading
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-200 text-gray-500 cursor-not-allowed'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              Saving Profile...
            </div>
          ) : (
            'Save & Continue'
          )}
        </button>
      </div>
    </div>
  );
}
