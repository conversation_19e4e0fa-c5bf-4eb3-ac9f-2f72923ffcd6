'use client';

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon, SparklesIcon } from '@heroicons/react/24/outline';

export default function PersonaIntroPage() {
  const router = useRouter();

  const handleCreatePersona = () => {
    router.push('/onboarding/persona-wizard');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4">
        <Link href="/onboarding/worker-profile" className="p-2 hover:bg-white/50 rounded-full">
          <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
        </Link>
        <h1 className="text-lg font-semibold text-gray-900">Create Your Persona</h1>
        <div className="w-10" />
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col justify-center px-6 py-8">
        {/* Icon */}
        <div className="text-center mb-8">
          <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <SparklesIcon className="w-12 h-12 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-3">
            What is a Persona?
          </h2>
        </div>

        {/* Explanation Cards */}
        <div className="space-y-4 mb-8">
          <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 text-lg">🎯</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-1">Targeted Applications</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Apply to jobs with the right skills and rates for each specific role
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-green-600 text-lg">💰</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-1">Better Rates</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Set different rates for different skills - charge more for specialized work
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-purple-600 text-lg">⭐</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-1">Build Reputation</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Each persona builds its own rating and review history
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Example */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-6 rounded-2xl text-white mb-8">
          <h3 className="font-semibold mb-3">Example:</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-white rounded-full"></span>
              <span><strong>Rajesh the Plumber:</strong> ₹200/hour for basic plumbing</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-white rounded-full"></span>
              <span><strong>Rajesh the AC Expert:</strong> ₹400/hour for AC repair</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-white rounded-full"></span>
              <span><strong>Rajesh the Handyman:</strong> ₹150/hour for general fixes</span>
            </div>
          </div>
        </div>

        {/* Benefits */}
        <div className="bg-yellow-50 p-4 rounded-xl border border-yellow-200 mb-8">
          <div className="flex items-start space-x-3">
            <span className="text-yellow-600 text-lg">💡</span>
            <div>
              <h4 className="font-medium text-yellow-800 mb-1">Pro Tip</h4>
              <p className="text-yellow-700 text-sm">
                Workers with multiple personas earn 40% more on average!
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="px-6 pb-8">
        <button
          onClick={handleCreatePersona}
          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-6 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg"
        >
          Create My First Persona
        </button>
        
        <div className="text-center mt-4">
          <Link
            href="/"
            className="text-gray-500 text-sm hover:text-gray-700"
          >
            Skip for now
          </Link>
        </div>
      </div>
    </div>
  );
}
