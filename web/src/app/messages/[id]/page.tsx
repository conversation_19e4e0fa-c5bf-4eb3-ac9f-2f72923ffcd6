'use client';

import React, { useState, useEffect, useRef } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { 
  ArrowLeftIcon, 
  PaperAirplaneIcon,
  PhoneIcon,
  EllipsisVerticalIcon,
  UserIcon,
  ClockIcon,
  CheckIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

// Mock chat data
const MOCK_CHAT_DATA = {
  '1': {
    id: '1',
    jobId: '1',
    jobTitle: 'Plumbing Work Required - Kitchen & Bathroom',
    jobRate: '₹800/day',
    posterName: '<PERSON><PERSON>',
    posterAvatar: '👩‍💼',
    posterPhone: '+91 98765 43210',
    isOnline: true,
    lastSeen: '2024-01-15T16:30:00Z',
    messages: [
      {
        id: '1',
        text: 'Hi! I saw your application for the plumbing work. Your profile looks great!',
        timestamp: '2024-01-15T10:30:00Z',
        isFromMe: false,
        status: 'read'
      },
      {
        id: '2',
        text: 'Thank you! I have 5 years of experience and can start immediately.',
        timestamp: '2024-01-15T10:35:00Z',
        isFromMe: true,
        status: 'read'
      },
      {
        id: '3',
        text: 'Perfect! The work involves fixing a kitchen sink leak and installing a new bathroom faucet.',
        timestamp: '2024-01-15T10:40:00Z',
        isFromMe: false,
        status: 'read'
      },
      {
        id: '4',
        text: 'I can handle both tasks. Do you have the new faucet ready or should I bring one?',
        timestamp: '2024-01-15T10:45:00Z',
        isFromMe: true,
        status: 'read'
      },
      {
        id: '5',
        text: 'I have the faucet ready. Can you start tomorrow morning at 9 AM?',
        timestamp: '2024-01-15T16:30:00Z',
        isFromMe: false,
        status: 'delivered'
      }
    ]
  }
};

const QUICK_REPLIES = [
  'On my way',
  'Running 15 min late',
  'Work completed',
  'Need more details',
  'Available now',
  'Will call you'
];

export default function ChatPage() {
  const params = useParams();
  const router = useRouter();
  const chatId = params.id as string;
  const [chatData, setChatData] = useState<any>(null);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showQuickReplies, setShowQuickReplies] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Load chat data
    const data = MOCK_CHAT_DATA[chatId as keyof typeof MOCK_CHAT_DATA];
    if (data) {
      setChatData(data);
    }
  }, [chatId]);

  useEffect(() => {
    // Scroll to bottom when messages change
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatData?.messages]);

  const handleSendMessage = () => {
    if (!newMessage.trim() || !chatData) return;

    const message = {
      id: Date.now().toString(),
      text: newMessage.trim(),
      timestamp: new Date().toISOString(),
      isFromMe: true,
      status: 'sent'
    };

    setChatData((prev: any) => ({
      ...prev,
      messages: [...prev.messages, message]
    }));

    setNewMessage('');
    setShowQuickReplies(false);

    // Simulate message delivery
    setTimeout(() => {
      setChatData((prev: any) => ({
        ...prev,
        messages: prev.messages.map((msg: any) => 
          msg.id === message.id ? { ...msg, status: 'delivered' } : msg
        )
      }));
    }, 1000);
  };

  const handleQuickReply = (reply: string) => {
    setNewMessage(reply);
    setShowQuickReplies(false);
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-IN', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const getMessageStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckIcon className="w-3 h-3 text-gray-400" />;
      case 'delivered':
        return <CheckCircleIcon className="w-3 h-3 text-gray-400" />;
      case 'read':
        return <CheckCircleIcon className="w-3 h-3 text-blue-500" />;
      default:
        return <ClockIcon className="w-3 h-3 text-gray-400" />;
    }
  };

  if (!chatData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading chat...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button 
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeftIcon className="w-5 h-5 text-gray-600" />
            </button>
            
            <div className="relative">
              <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-lg">{chatData.posterAvatar}</span>
              </div>
              {chatData.isOnline && (
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <h1 className="font-medium text-gray-900">{chatData.posterName}</h1>
              <p className="text-xs text-gray-500">
                {chatData.isOnline ? 'Online' : `Last seen ${formatTime(chatData.lastSeen)}`}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
              <PhoneIcon className="w-5 h-5 text-gray-600" />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
              <EllipsisVerticalIcon className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>
        
        {/* Job Context Chip */}
        <div className="mt-3 bg-blue-50 border border-blue-200 rounded-lg p-2">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-blue-900 truncate">
                {chatData.jobTitle}
              </p>
              <p className="text-xs text-blue-700">{chatData.jobRate}</p>
            </div>
            <button className="text-xs text-blue-600 hover:text-blue-800 font-medium">
              View Job
            </button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto px-4 py-4 space-y-4">
        {chatData.messages.map((message: any) => (
          <MessageBubble 
            key={message.id}
            message={message}
            formatTime={formatTime}
            getMessageStatusIcon={getMessageStatusIcon}
          />
        ))}
        
        {isTyping && (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-sm">{chatData.posterAvatar}</span>
            </div>
            <div className="bg-white rounded-2xl px-4 py-2 shadow-sm">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Replies */}
      {showQuickReplies && (
        <div className="px-4 py-2 bg-white border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {QUICK_REPLIES.map((reply) => (
              <button
                key={reply}
                onClick={() => handleQuickReply(reply)}
                className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
              >
                {reply}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="bg-white border-t border-gray-200 px-4 py-3">
        <div className="flex items-end space-x-3">
          <button
            onClick={() => setShowQuickReplies(!showQuickReplies)}
            className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
          >
            <span className="text-lg">⚡</span>
          </button>
          
          <div className="flex-1 relative">
            <textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type a message..."
              rows={1}
              maxLength={500}
              className="w-full px-4 py-2 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              style={{ minHeight: '40px', maxHeight: '120px' }}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
            />
            <div className="absolute bottom-1 right-2 text-xs text-gray-400">
              {newMessage.length}/500
            </div>
          </div>
          
          <button
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            className={`p-2 rounded-full transition-colors ${
              newMessage.trim()
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            }`}
          >
            <PaperAirplaneIcon className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
}

// Message Bubble Component
function MessageBubble({ 
  message, 
  formatTime, 
  getMessageStatusIcon 
}: { 
  message: any;
  formatTime: (timestamp: string) => string;
  getMessageStatusIcon: (status: string) => React.ReactNode;
}) {
  return (
    <div className={`flex ${message.isFromMe ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
        message.isFromMe
          ? 'bg-blue-600 text-white'
          : 'bg-white text-gray-900 shadow-sm'
      }`}>
        <p className="text-sm">{message.text}</p>
        <div className={`flex items-center justify-end space-x-1 mt-1 ${
          message.isFromMe ? 'text-blue-100' : 'text-gray-500'
        }`}>
          <span className="text-xs">{formatTime(message.timestamp)}</span>
          {message.isFromMe && getMessageStatusIcon(message.status)}
        </div>
      </div>
    </div>
  );
}
