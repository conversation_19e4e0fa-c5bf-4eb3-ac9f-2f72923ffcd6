'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTheme } from '@/contexts/ThemeContext';
import {
  UserIcon,
  ClockIcon,
  CheckIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

// Mock conversations data
const MOCK_CONVERSATIONS = [
  {
    id: '1',
    jobId: '1',
    jobTitle: 'Plumbing Work Required - Kitchen & Bathroom',
    posterName: '<PERSON><PERSON>',
    posterAvatar: '👩‍💼',
    lastMessage: 'Can you start tomorrow morning at 9 AM?',
    lastMessageTime: '2024-01-15T16:30:00Z',
    unreadCount: 2,
    isOnline: true,
    messageStatus: 'delivered', // sent, delivered, read
    isFromMe: false
  },
  {
    id: '2',
    jobId: '2',
    jobTitle: 'House Cleaning - Deep Clean Required',
    posterName: '<PERSON><PERSON>',
    posterAvatar: '👨‍💼',
    lastMessage: 'Perfect! See you on Monday.',
    lastMessageTime: '2024-01-14T18:45:00Z',
    unreadCount: 0,
    isOnline: false,
    messageStatus: 'read',
    isFromMe: true
  },
  {
    id: '3',
    jobId: '3',
    jobTitle: 'Electrical Wiring - New AC Installation',
    posterName: 'Amit Singh',
    posterAvatar: '👨‍🔧',
    lastMessage: 'Work completed! Please check and confirm.',
    lastMessageTime: '2024-01-13T14:20:00Z',
    unreadCount: 0,
    isOnline: true,
    messageStatus: 'delivered',
    isFromMe: true
  }
];

export default function MessagesPage() {
  const { mode } = useTheme();
  const [conversations, setConversations] = useState(MOCK_CONVERSATIONS);

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-IN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString('en-IN', { weekday: 'short' });
    } else {
      return date.toLocaleDateString('en-IN', {
        day: 'numeric',
        month: 'short'
      });
    }
  };

  const getMessageStatusIcon = (status: string, isFromMe: boolean) => {
    if (!isFromMe) return null;

    switch (status) {
      case 'sent':
        return <CheckIcon className="w-4 h-4 text-gray-400" />;
      case 'delivered':
        return <CheckCircleIcon className="w-4 h-4 text-gray-400" />;
      case 'read':
        return <CheckCircleIcon className="w-4 h-4 text-blue-500" />;
      default:
        return <ClockIcon className="w-4 h-4 text-gray-400" />;
    }
  };

  const totalUnread = conversations.reduce((sum, conv) => sum + conv.unreadCount, 0);

  return (
    <div className="px-4 py-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
        <p className="text-gray-600">
          {mode === 'worker'
            ? 'Chat with employers about your jobs'
            : 'Communicate with workers you\'ve hired'
          }
        </p>
        {totalUnread > 0 && (
          <div className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
            {totalUnread} unread message{totalUnread > 1 ? 's' : ''}
          </div>
        )}
      </div>

      {/* Conversations List */}
      <div className="space-y-2">
        {conversations.length > 0 ? (
          conversations.map((conversation) => (
            <ConversationCard
              key={conversation.id}
              conversation={conversation}
              formatTime={formatTime}
              getMessageStatusIcon={getMessageStatusIcon}
            />
          ))
        ) : (
          <EmptyState mode={mode} />
        )}
      </div>
    </div>
  );
}

// Conversation Card Component
function ConversationCard({
  conversation,
  formatTime,
  getMessageStatusIcon
}: {
  conversation: any;
  formatTime: (timestamp: string) => string;
  getMessageStatusIcon: (status: string, isFromMe: boolean) => React.ReactNode;
}) {
  return (
    <Link href={`/messages/${conversation.id}`} className="block">
      <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
        <div className="flex items-center space-x-3">
          {/* Avatar */}
          <div className="relative flex-shrink-0">
            <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-lg">{conversation.posterAvatar}</span>
            </div>
            {conversation.isOnline && (
              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
            )}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <h3 className="font-medium text-gray-900 truncate">
                {conversation.posterName}
              </h3>
              <div className="flex items-center space-x-1">
                {getMessageStatusIcon(conversation.messageStatus, conversation.isFromMe)}
                <span className="text-xs text-gray-500">
                  {formatTime(conversation.lastMessageTime)}
                </span>
              </div>
            </div>

            <p className="text-sm text-gray-600 mb-1 truncate">
              {conversation.jobTitle}
            </p>

            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-800 truncate flex-1">
                {conversation.isFromMe && (
                  <span className="text-gray-500 mr-1">You:</span>
                )}
                {conversation.lastMessage}
              </p>
              {conversation.unreadCount > 0 && (
                <div className="ml-2 w-5 h-5 bg-blue-600 text-white text-xs rounded-full flex items-center justify-center flex-shrink-0">
                  {conversation.unreadCount}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}

// Empty State Component
function EmptyState({ mode }: { mode: string }) {
  return (
    <div className="text-center py-12">
      <div className="w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <span className="text-4xl">💬</span>
      </div>
      <h2 className="text-xl font-semibold text-gray-900 mb-2">No Messages Yet</h2>
      <p className="text-gray-600 max-w-sm mx-auto mb-6">
        {mode === 'worker'
          ? 'Start applying to jobs to connect with employers'
          : 'Post a job or invite workers to start conversations'
        }
      </p>
      <Link
        href="/"
        className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
      >
        {mode === 'worker' ? 'Browse Jobs' : 'Post a Job'}
      </Link>
    </div>
  );
}
