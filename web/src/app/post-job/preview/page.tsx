'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeftIcon, 
  EyeIcon,
  CheckCircleIcon,
  MapPinIcon,
  CurrencyRupeeIcon,
  ClockIcon,
  BoltIcon,
  UserIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

export default function JobPreviewPage() {
  const router = useRouter();
  const [jobData, setJobData] = useState<any>(null);
  const [isPublishing, setIsPublishing] = useState(false);
  const [showPublishConfirmation, setShowPublishConfirmation] = useState(false);

  useEffect(() => {
    // Load job data from localStorage
    const savedData = localStorage.getItem('jobDraft');
    if (savedData) {
      setJobData(JSON.parse(savedData));
    } else {
      // Redirect back if no data
      router.push('/post-job');
    }
  }, [router]);

  const handlePublish = async () => {
    setIsPublishing(true);
    
    // Simulate API call
    setTimeout(() => {
      // Generate job ID and save to localStorage
      const jobId = Date.now().toString();
      const publishedJob = {
        ...jobData,
        id: jobId,
        status: 'open',
        postedAt: new Date().toISOString(),
        views: 0,
        applications: 0
      };
      
      // Save published job
      const existingJobs = JSON.parse(localStorage.getItem('postedJobs') || '[]');
      existingJobs.push(publishedJob);
      localStorage.setItem('postedJobs', JSON.stringify(existingJobs));
      
      // Clear draft
      localStorage.removeItem('jobDraft');
      
      setIsPublishing(false);
      setShowPublishConfirmation(true);
      
      // Navigate to job management after 3 seconds
      setTimeout(() => {
        router.push('/jobs/posted');
      }, 3000);
    }, 2000);
  };

  if (!jobData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading preview...</p>
        </div>
      </div>
    );
  }

  if (showPublishConfirmation) {
    return (
      <div className="min-h-screen bg-green-50 flex items-center justify-center p-4">
        <div className="text-center space-y-6 max-w-sm">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <CheckCircleIcon className="w-12 h-12 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Job Posted Successfully! 🎉</h1>
            <p className="text-gray-600">Your job is now live and workers can start applying.</p>
          </div>
          <div className="bg-white rounded-lg p-4 border border-green-200">
            <h3 className="font-medium text-gray-900 mb-2">What happens next?</h3>
            <ul className="text-sm text-gray-600 space-y-1 text-left">
              <li>• Workers will see your job in their feed</li>
              <li>• You'll get notifications for applications</li>
              <li>• Review and select the best candidates</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Job Preview</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Preview Notice */}
      <div className="bg-blue-50 border-b border-blue-200 p-4">
        <div className="flex items-center space-x-3">
          <EyeIcon className="w-5 h-5 text-blue-600" />
          <div>
            <p className="font-medium text-blue-900">Worker View Preview</p>
            <p className="text-sm text-blue-700">This is exactly how workers will see your job</p>
          </div>
        </div>
      </div>

      {/* Job Card Preview */}
      <div className="p-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
          {/* Job Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                {jobData.isUrgent && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    <BoltIcon className="w-3 h-3 mr-1" />
                    Urgent
                  </span>
                )}
                <span className="text-xs text-gray-500">
                  Posted {new Date().toLocaleDateString('en-IN')}
                </span>
              </div>
              <h2 className="text-lg font-semibold text-gray-900 mb-1">{jobData.title}</h2>
              <p className="text-sm text-gray-600">{jobData.category}</p>
            </div>
            <div className="text-right">
              <p className="text-lg font-bold text-green-600">
                ₹{jobData.amount}
              </p>
              <p className="text-xs text-gray-500">
                per {jobData.rateType === 'fixed' ? 'job' : jobData.rateType.replace('ly', '')}
              </p>
            </div>
          </div>

          {/* Job Details */}
          <div className="space-y-3 mb-4">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <MapPinIcon className="w-4 h-4" />
              <span>{jobData.location}</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <ClockIcon className="w-4 h-4" />
              <span>{jobData.duration}</span>
            </div>
          </div>

          {/* Description */}
          <div className="mb-4">
            <h3 className="font-medium text-gray-900 mb-2">Job Description</h3>
            <p className="text-gray-700 text-sm leading-relaxed">{jobData.description}</p>
          </div>

          {/* Requirements */}
          {jobData.requirements && jobData.requirements.length > 0 && (
            <div className="mb-4">
              <h3 className="font-medium text-gray-900 mb-2">Requirements</h3>
              <ul className="space-y-1">
                {jobData.requirements.map((req: string, index: number) => (
                  <li key={index} className="text-sm text-gray-700 flex items-center space-x-2">
                    <span className="w-1.5 h-1.5 bg-gray-400 rounded-full"></span>
                    <span>{req}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Poster Info */}
          <div className="border-t border-gray-200 pt-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                <UserIcon className="w-5 h-5 text-gray-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">You</p>
                <p className="text-sm text-gray-600">Job Poster</p>
              </div>
            </div>
          </div>

          {/* Action Buttons (Worker View) */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex space-x-3">
              <button className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg font-medium">
                Apply Now
              </button>
              <button className="px-4 py-3 border border-gray-300 rounded-lg">
                <span className="sr-only">Save job</span>
                ❤️
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Estimated Metrics */}
      <div className="p-4">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-3">📊 Estimated Performance</h3>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-blue-600">12-18</p>
              <p className="text-xs text-gray-600">Expected Views</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-green-600">3-5</p>
              <p className="text-xs text-gray-600">Likely Applications</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-purple-600">2-4h</p>
              <p className="text-xs text-gray-600">Response Time</p>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-3 text-center">
            Based on similar jobs in your area
          </p>
        </div>
      </div>

      {/* Tips */}
      <div className="p-4">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-medium text-yellow-900 mb-2">💡 Tips to get more applications</h3>
          <ul className="space-y-1 text-sm text-yellow-800">
            <li>• Add specific details about the work needed</li>
            <li>• Include photos if helpful</li>
            <li>• Respond quickly to applications</li>
            <li>• Be clear about timing and expectations</li>
          </ul>
        </div>
      </div>

      {/* Bottom Publish Button */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 safe-area-bottom">
        <div className="mobile-container">
          <button
            onClick={handlePublish}
            disabled={isPublishing}
            className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors ${
              isPublishing
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            {isPublishing ? (
              <div className="flex items-center justify-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Publishing Job...
              </div>
            ) : (
              'Publish Job'
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
