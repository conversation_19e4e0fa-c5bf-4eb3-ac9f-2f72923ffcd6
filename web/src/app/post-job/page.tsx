'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeftIcon, 
  ArrowRightIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  MapPinIcon,
  CurrencyRupeeIcon,
  ClockIcon,
  BoltIcon
} from '@heroicons/react/24/outline';

const JOB_CATEGORIES = [
  'Plumber', 'Electrician', 'Cleaner', '<PERSON>', '<PERSON>', 'Cook', 
  'Driver', 'Security Guard', 'Gardener', 'Mechanic', 'Tailor', 'Other'
];

const JOB_TITLE_SUGGESTIONS = {
  'Plumber': [
    'Kitchen Sink Repair Required',
    'Bathroom Plumbing Installation',
    'Water Pipe Leak Fixing',
    'Toilet Installation Work'
  ],
  'Electrician': [
    'AC Installation Wiring',
    'Electrical Panel Repair',
    'Fan Installation Work',
    'Light Fixture Setup'
  ],
  'Cleaner': [
    'Deep House Cleaning',
    'Office Cleaning Required',
    'Post-Construction Cleanup',
    'Regular Home Cleaning'
  ],
  'Carpenter': [
    'Kitchen Cabinet Repair',
    'Furniture Assembly Work',
    'Door Installation Required',
    'Custom Shelving Work'
  ]
};

const RATE_TYPES = [
  { id: 'hourly', label: 'Per Hour', description: 'Best for short tasks' },
  { id: 'daily', label: 'Per Day', description: 'Best for full day work' },
  { id: 'fixed', label: 'Fixed Price', description: 'Best for project work' }
];

export default function PostJobPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    rateType: 'hourly',
    amount: '',
    duration: '',
    location: '',
    isUrgent: false,
    requirements: [] as string[]
  });
  const [titleSuggestions, setTitleSuggestions] = useState<string[]>([]);
  const [showBudgetWarning, setShowBudgetWarning] = useState(false);

  useEffect(() => {
    // Update title suggestions based on category
    if (formData.category && JOB_TITLE_SUGGESTIONS[formData.category as keyof typeof JOB_TITLE_SUGGESTIONS]) {
      setTitleSuggestions(JOB_TITLE_SUGGESTIONS[formData.category as keyof typeof JOB_TITLE_SUGGESTIONS]);
    } else {
      setTitleSuggestions([]);
    }
  }, [formData.category]);

  useEffect(() => {
    // Check for budget warnings
    const amount = parseFloat(formData.amount);
    if (amount > 0) {
      if (formData.rateType === 'hourly' && amount < 100) {
        setShowBudgetWarning(true);
      } else if (formData.rateType === 'daily' && amount < 800) {
        setShowBudgetWarning(true);
      } else {
        setShowBudgetWarning(false);
      }
    }
  }, [formData.amount, formData.rateType]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleTitleSuggestionClick = (suggestion: string) => {
    handleInputChange('title', suggestion);
  };

  const isStepValid = (step: number) => {
    switch (step) {
      case 1:
        return formData.title.trim() !== '' && 
               formData.description.trim() !== '' && 
               formData.category !== '';
      case 2:
        return formData.rateType !== '' && 
               formData.amount.trim() !== '' && 
               formData.duration.trim() !== '';
      case 3:
        return formData.location.trim() !== '';
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < 3 && isStepValid(currentStep)) {
      setCurrentStep(currentStep + 1);
    } else if (currentStep === 3 && isStepValid(currentStep)) {
      // Navigate to preview
      localStorage.setItem('jobDraft', JSON.stringify(formData));
      router.push('/post-job/preview');
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return 'Job Details';
      case 2: return 'Budget & Duration';
      case 3: return 'Location & Preferences';
      default: return 'Post Job';
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={handleBack}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">{getStepTitle()}</h1>
          <div className="w-10" />
        </div>
        
        {/* Progress Bar */}
        <div className="px-4 pb-4">
          <div className="flex items-center space-x-2">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center flex-1">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep 
                    ? 'bg-green-600 text-white' 
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step < currentStep ? (
                    <CheckCircleIcon className="w-5 h-5" />
                  ) : (
                    step
                  )}
                </div>
                {step < 3 && (
                  <div className={`flex-1 h-1 mx-2 rounded ${
                    step < currentStep ? 'bg-green-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-2 text-xs text-gray-600">
            <span>Details</span>
            <span>Budget</span>
            <span>Location</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6 pb-24">
        {currentStep === 1 && (
          <Step1JobDetails 
            formData={formData}
            onInputChange={handleInputChange}
            titleSuggestions={titleSuggestions}
            onTitleSuggestionClick={handleTitleSuggestionClick}
          />
        )}

        {currentStep === 2 && (
          <Step2BudgetDuration 
            formData={formData}
            onInputChange={handleInputChange}
            showBudgetWarning={showBudgetWarning}
          />
        )}

        {currentStep === 3 && (
          <Step3LocationPreferences 
            formData={formData}
            onInputChange={handleInputChange}
          />
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 safe-area-bottom">
        <div className="mobile-container">
          <button
            onClick={handleNext}
            disabled={!isStepValid(currentStep)}
            className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors flex items-center justify-center ${
              isStepValid(currentStep)
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            {currentStep === 3 ? 'Preview Job' : 'Next Step'}
            <ArrowRightIcon className="w-5 h-5 ml-2" />
          </button>
        </div>
      </div>
    </div>
  );
}

// Step 1: Job Details Component
function Step1JobDetails({ 
  formData, 
  onInputChange, 
  titleSuggestions, 
  onTitleSuggestionClick 
}: {
  formData: any;
  onInputChange: (field: string, value: any) => void;
  titleSuggestions: string[];
  onTitleSuggestionClick: (suggestion: string) => void;
}) {
  return (
    <div className="space-y-6">
      {/* Job Title */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Job Title *
        </label>
        <input
          type="text"
          value={formData.title}
          onChange={(e) => onInputChange('title', e.target.value)}
          placeholder="e.g., Kitchen Plumbing Repair Required"
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          maxLength={100}
        />
        <p className="text-xs text-gray-500 mt-1">{formData.title.length}/100 characters</p>
      </div>

      {/* Category */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Category *
        </label>
        <select
          value={formData.category}
          onChange={(e) => onInputChange('category', e.target.value)}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
        >
          <option value="">Select category</option>
          {JOB_CATEGORIES.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>
      </div>

      {/* Title Suggestions */}
      {titleSuggestions.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Suggested Titles
          </label>
          <div className="space-y-2">
            {titleSuggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => onTitleSuggestionClick(suggestion)}
                className="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors"
              >
                <span className="text-gray-900">{suggestion}</span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Job Description *
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => onInputChange('description', e.target.value)}
          placeholder="Describe the work you need done, including specific requirements..."
          rows={4}
          maxLength={500}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
        />
        <p className="text-xs text-gray-500 mt-1">{formData.description.length}/500 characters</p>
      </div>
    </div>
  );
}

// Step 2: Budget & Duration Component
function Step2BudgetDuration({
  formData,
  onInputChange,
  showBudgetWarning
}: {
  formData: any;
  onInputChange: (field: string, value: any) => void;
  showBudgetWarning: boolean;
}) {
  return (
    <div className="space-y-6">
      {/* Rate Type */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Payment Type *
        </label>
        <div className="space-y-3">
          {RATE_TYPES.map((type) => (
            <button
              key={type.id}
              onClick={() => onInputChange('rateType', type.id)}
              className={`w-full p-4 rounded-lg border-2 text-left transition-colors ${
                formData.rateType === type.id
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">{type.label}</div>
                  <div className="text-sm text-gray-600">{type.description}</div>
                </div>
                {formData.rateType === type.id && (
                  <CheckCircleIcon className="w-5 h-5 text-green-600" />
                )}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Amount */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Amount (₹) *
        </label>
        <div className="relative">
          <CurrencyRupeeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="number"
            value={formData.amount}
            onChange={(e) => onInputChange('amount', e.target.value)}
            placeholder="Enter amount"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            min="0"
          />
        </div>

        {showBudgetWarning && (
          <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">Low budget alert</p>
                <p>This rate might be below market average. Consider increasing to attract more workers.</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Duration */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Expected Duration *
        </label>
        <div className="relative">
          <ClockIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={formData.duration}
            onChange={(e) => onInputChange('duration', e.target.value)}
            placeholder="e.g., 2-3 hours, Half day, 1 week"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>
        <p className="text-xs text-gray-500 mt-1">Help workers understand the time commitment</p>
      </div>

      {/* Budget Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">💡 Budget Tips</h3>
        <ul className="space-y-1 text-sm text-blue-800">
          <li>• Competitive rates attract more skilled workers</li>
          <li>• Consider including materials cost separately</li>
          <li>• Higher rates often mean faster completion</li>
        </ul>
      </div>
    </div>
  );
}

// Step 3: Location & Preferences Component
function Step3LocationPreferences({
  formData,
  onInputChange
}: {
  formData: any;
  onInputChange: (field: string, value: any) => void;
}) {
  return (
    <div className="space-y-6">
      {/* Location */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Job Location *
        </label>
        <div className="relative">
          <MapPinIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={formData.location}
            onChange={(e) => onInputChange('location', e.target.value)}
            placeholder="Enter full address or area name"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>
        <p className="text-xs text-gray-500 mt-1">Workers will see this location to decide if they can reach</p>
      </div>

      {/* Urgency Toggle */}
      <div>
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <BoltIcon className="w-6 h-6 text-orange-500" />
            <div>
              <h3 className="font-medium text-gray-900">Urgent Job</h3>
              <p className="text-sm text-gray-600">Need this done today or tomorrow</p>
            </div>
          </div>
          <button
            onClick={() => onInputChange('isUrgent', !formData.isUrgent)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              formData.isUrgent ? 'bg-orange-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                formData.isUrgent ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
        {formData.isUrgent && (
          <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <p className="text-sm text-orange-800">
              <span className="font-medium">Urgent jobs</span> are shown first to workers and may attract higher rates.
            </p>
          </div>
        )}
      </div>

      {/* Additional Requirements */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Additional Requirements (Optional)
        </label>
        <div className="space-y-2">
          {[
            'Must bring own tools',
            'Experience certificate required',
            'Available on weekends',
            'Speaks Hindi/English'
          ].map((requirement, index) => (
            <label key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <input
                type="checkbox"
                checked={formData.requirements.includes(requirement)}
                onChange={(e) => {
                  const requirements = e.target.checked
                    ? [...formData.requirements, requirement]
                    : formData.requirements.filter((r: string) => r !== requirement);
                  onInputChange('requirements', requirements);
                }}
                className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
              />
              <span className="text-gray-900">{requirement}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Summary Preview */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="font-medium text-gray-900 mb-3">Job Summary</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Category:</span>
            <span className="text-gray-900">{formData.category || 'Not selected'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Budget:</span>
            <span className="text-gray-900">
              ₹{formData.amount || '0'} {formData.rateType ? `per ${formData.rateType === 'fixed' ? 'job' : formData.rateType.replace('ly', '')}` : ''}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Duration:</span>
            <span className="text-gray-900">{formData.duration || 'Not specified'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Priority:</span>
            <span className={`${formData.isUrgent ? 'text-orange-600 font-medium' : 'text-gray-900'}`}>
              {formData.isUrgent ? 'Urgent' : 'Normal'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
