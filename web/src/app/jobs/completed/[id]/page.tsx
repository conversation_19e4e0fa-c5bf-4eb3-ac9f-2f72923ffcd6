'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  CheckCircleIcon,
  StarIcon,
  CurrencyRupeeIcon,
  CalendarIcon,
  ClockIcon,
  ShareIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';

// Mock completed job data
const MOCK_COMPLETED_JOB = {
  '3': {
    id: '3',
    title: 'Electrical Wiring - New AC Installation',
    category: 'Electrician',
    location: 'Powai, Mumbai',
    posterName: 'Amit Singh',
    completedAt: new Date().toISOString(),
    duration: '6 hours',
    photosCount: 4,
    finalAmount: 1500,
    paymentMethod: 'Cash',
    status: 'completed'
  }
};

export default function JobCompletedPage() {
  const params = useParams();
  const router = useRouter();
  const jobId = params.id as string;
  const [job, setJob] = useState<any>(null);
  const [showCelebration, setShowCelebration] = useState(true);

  useEffect(() => {
    // Load job data
    const jobData = MOCK_COMPLETED_JOB[jobId as keyof typeof MOCK_COMPLETED_JOB];
    if (jobData) {
      setJob(jobData);
    }

    // Hide celebration after 3 seconds
    const timer = setTimeout(() => {
      setShowCelebration(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, [jobId]);

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Job Completed Successfully!',
        text: `I just completed "${job?.title}" and earned ₹${job?.finalAmount}!`,
        url: window.location.href,
      });
    }
  };

  if (!job) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading completion details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Celebration Animation */}
      {showCelebration && (
        <div className="fixed inset-0 pointer-events-none z-50 flex items-center justify-center">
          <div className="text-6xl animate-bounce">🎉</div>
        </div>
      )}

      {/* Content */}
      <div className="px-4 py-8 space-y-8">
        {/* Success Header */}
        <div className="text-center space-y-4">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <CheckCircleIcon className="w-12 h-12 text-green-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900">
            Congratulations! 🎉
          </h1>
          <p className="text-gray-600 max-w-sm mx-auto">
            You've successfully completed your work and earned your payment!
          </p>
        </div>

        {/* Earnings Card */}
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="text-center space-y-3">
            <div className="flex items-center justify-center space-x-2">
              <CurrencyRupeeIcon className="w-8 h-8 text-green-600" />
              <span className="text-3xl font-bold text-green-600">
                ₹{job.finalAmount}
              </span>
            </div>
            <p className="text-gray-600">Earned from this job</p>
            <div className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
              Payment via {job.paymentMethod}
            </div>
          </div>
        </div>

        {/* Job Summary */}
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <h2 className="font-semibold text-gray-900 mb-4">Job Summary</h2>
          <div className="space-y-3">
            <div>
              <h3 className="font-medium text-gray-900 mb-1">{job.title}</h3>
              <p className="text-sm text-gray-600">{job.category} • {job.location}</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4 pt-3 border-t border-gray-100">
              <div className="text-center">
                <ClockIcon className="w-5 h-5 text-gray-400 mx-auto mb-1" />
                <p className="text-sm font-medium text-gray-900">{job.duration}</p>
                <p className="text-xs text-gray-600">Duration</p>
              </div>
              <div className="text-center">
                <CalendarIcon className="w-5 h-5 text-gray-400 mx-auto mb-1" />
                <p className="text-sm font-medium text-gray-900">
                  {new Date(job.completedAt).toLocaleDateString('en-IN', {
                    day: 'numeric',
                    month: 'short'
                  })}
                </p>
                <p className="text-xs text-gray-600">Completed</p>
              </div>
            </div>
          </div>
        </div>

        {/* Work Evidence */}
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <h2 className="font-semibold text-gray-900 mb-4">Work Evidence</h2>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">📸</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">{job.photosCount} Photos Uploaded</p>
                <p className="text-sm text-gray-600">Work completion proof</p>
              </div>
            </div>
            <CheckCircleIcon className="w-6 h-6 text-green-600" />
          </div>
        </div>

        {/* Rating Prompt */}
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <h2 className="font-semibold text-gray-900 mb-4">Rate Your Experience</h2>
          <p className="text-gray-600 mb-4">
            How was working with {job.posterName}?
          </p>
          <div className="flex items-center justify-center space-x-2 mb-4">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                className="p-1 hover:scale-110 transition-transform"
              >
                <StarIcon className="w-8 h-8 text-gray-300 hover:text-yellow-400" />
              </button>
            ))}
          </div>
          <Link
            href={`/jobs/rating/${jobId}`}
            className="block w-full text-center bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            Rate & Review
          </Link>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleShare}
            className="w-full flex items-center justify-center space-x-2 bg-white text-gray-700 py-3 px-4 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
          >
            <ShareIcon className="w-5 h-5" />
            <span>Share Achievement</span>
          </button>
          
          <Link
            href="/earnings"
            className="block w-full text-center bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
          >
            View Earnings Dashboard
          </Link>
          
          <Link
            href="/"
            className="block w-full text-center bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            Find More Jobs
          </Link>
        </div>

        {/* Achievement Badge */}
        <div className="text-center py-6">
          <div className="inline-flex items-center space-x-2 bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full">
            <span className="text-lg">🏆</span>
            <span className="font-medium">Job Completed Successfully!</span>
          </div>
        </div>
      </div>
    </div>
  );
}
