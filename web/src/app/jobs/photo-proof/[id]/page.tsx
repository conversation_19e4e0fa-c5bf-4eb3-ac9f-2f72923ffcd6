'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { 
  ArrowLeftIcon, 
  CameraIcon,
  XMarkIcon,
  CheckCircleIcon,
  MapPinIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

// Mock job data for photo proof
const MOCK_JOB_DATA = {
  '3': {
    id: '3',
    title: 'Electrical Wiring - New AC Installation',
    category: 'Electrician',
    rate: 1500,
    rateType: 'daily',
    location: 'Powai, Mumbai',
    posterName: 'Amit Singh',
    description: 'Install electrical wiring for 2 new split ACs in bedroom and living room.',
    requirements: [
      'Show both AC units properly installed',
      'Display electrical connections clearly',
      'Include safety compliance indicators',
      'Capture before/after comparison if possible'
    ]
  }
};

export default function PhotoProofPage() {
  const params = useParams();
  const router = useRouter();
  const jobId = params.id as string;
  const [job, setJob] = useState<any>(null);
  const [capturedPhotos, setCapturedPhotos] = useState<File[]>([]);
  const [showCamera, setShowCamera] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Load job data
    const jobData = MOCK_JOB_DATA[jobId as keyof typeof MOCK_JOB_DATA];
    if (jobData) {
      setJob(jobData);
    }

    // Mock location detection
    setCurrentLocation('Powai, Mumbai - 400076');
  }, [jobId]);

  const handlePhotoCapture = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      setCapturedPhotos(prev => [...prev, ...files]);
    }
  };

  const removePhoto = (index: number) => {
    setCapturedPhotos(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmitProof = async () => {
    if (capturedPhotos.length === 0) return;
    
    setIsUploading(true);
    
    // Simulate upload process
    setTimeout(() => {
      setIsUploading(false);
      // Navigate to payment confirmation
      router.push(`/jobs/payment/${jobId}`);
    }, 3000);
  };

  const getCurrentTimestamp = () => {
    return new Date().toLocaleString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  if (!job) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading job details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Work Completion Proof</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6">
        {/* Job Summary */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="font-semibold text-blue-900 mb-2">{job.title}</h2>
          <div className="space-y-1 text-sm text-blue-800">
            <div className="flex items-center justify-between">
              <span>Category:</span>
              <span>{job.category}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Location:</span>
              <span>{job.location}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Rate:</span>
              <span className="font-semibold">₹{job.rate}/{job.rateType === 'hourly' ? 'hr' : 'day'}</span>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-900 mb-2">Photo Requirements</h3>
              <ul className="space-y-1 text-sm text-yellow-800">
                {job.requirements.map((req: string, index: number) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-yellow-600 mt-1">•</span>
                    <span>{req}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Location & Time Info */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2 text-gray-600">
              <MapPinIcon className="w-4 h-4" />
              <span>Location: {currentLocation}</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-600">
              <ClockIcon className="w-4 h-4" />
              <span>{getCurrentTimestamp()}</span>
            </div>
          </div>
        </div>

        {/* Camera Interface */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Capture Work Photos</h3>
          
          {/* Capture Button */}
          <div className="text-center">
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              capture="environment"
              multiple
              onChange={handlePhotoCapture}
              className="hidden"
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              className="inline-flex items-center space-x-3 bg-blue-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-700 transition-colors"
            >
              <CameraIcon className="w-6 h-6" />
              <span>Take Photos</span>
            </button>
            <p className="text-sm text-gray-600 mt-2">
              Tap to open camera and capture work completion photos
            </p>
          </div>

          {/* Photo Gallery */}
          {capturedPhotos.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">
                Captured Photos ({capturedPhotos.length})
              </h4>
              <div className="grid grid-cols-2 gap-3">
                {capturedPhotos.map((photo, index) => (
                  <PhotoPreview 
                    key={index}
                    photo={photo}
                    index={index}
                    onRemove={removePhoto}
                    timestamp={getCurrentTimestamp()}
                    location={currentLocation}
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Quality Tips */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-medium text-green-900 mb-2">📸 Photo Quality Tips</h3>
          <ul className="space-y-1 text-sm text-green-800">
            <li>• Ensure good lighting for clear visibility</li>
            <li>• Capture multiple angles of completed work</li>
            <li>• Include close-up details and overall view</li>
            <li>• Make sure all work areas are visible</li>
            <li>• Avoid blurry or dark photos</li>
          </ul>
        </div>
      </div>

      {/* Bottom Submit Button */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 safe-area-bottom">
        <div className="mobile-container">
          <button
            onClick={handleSubmitProof}
            disabled={capturedPhotos.length === 0 || isUploading}
            className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors ${
              capturedPhotos.length > 0 && !isUploading
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isUploading ? (
              <div className="flex items-center justify-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Uploading Photos...
              </div>
            ) : (
              `Submit Work Proof (${capturedPhotos.length} photos)`
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

// Photo Preview Component
function PhotoPreview({ 
  photo, 
  index, 
  onRemove, 
  timestamp, 
  location 
}: { 
  photo: File;
  index: number;
  onRemove: (index: number) => void;
  timestamp: string;
  location: string;
}) {
  const [imageUrl, setImageUrl] = useState<string>('');

  useEffect(() => {
    const url = URL.createObjectURL(photo);
    setImageUrl(url);
    return () => URL.revokeObjectURL(url);
  }, [photo]);

  return (
    <div className="relative bg-gray-100 rounded-lg overflow-hidden">
      <img 
        src={imageUrl} 
        alt={`Work proof ${index + 1}`}
        className="w-full h-32 object-cover"
      />
      
      {/* Remove Button */}
      <button
        onClick={() => onRemove(index)}
        className="absolute top-2 right-2 w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center hover:bg-red-700 transition-colors"
      >
        <XMarkIcon className="w-4 h-4" />
      </button>
      
      {/* Metadata Overlay */}
      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-2">
        <div className="text-xs space-y-1">
          <div className="flex items-center space-x-1">
            <ClockIcon className="w-3 h-3" />
            <span>{timestamp}</span>
          </div>
          <div className="flex items-center space-x-1">
            <MapPinIcon className="w-3 h-3" />
            <span className="truncate">{location}</span>
          </div>
        </div>
      </div>
      
      {/* Quality Indicator */}
      <div className="absolute top-2 left-2">
        <div className="flex items-center space-x-1 bg-green-600 text-white px-2 py-1 rounded-full text-xs">
          <CheckCircleIcon className="w-3 h-3" />
          <span>Good</span>
        </div>
      </div>
    </div>
  );
}
