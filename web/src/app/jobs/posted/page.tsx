'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  PlusIcon,
  EyeIcon,
  UserGroupIcon,
  ClockIcon,
  MapPinIcon,
  CurrencyRupeeIcon,
  BoltIcon,
  EllipsisVerticalIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

// <PERSON><PERSON> posted jobs data following UI/UX specs
const MOCK_POSTED_JOBS = [
  {
    id: '1',
    title: 'Kitchen Sink Repair Required',
    category: 'Plumber',
    status: 'open',
    rate: 150,
    rateType: 'hourly',
    location: 'Powai, Mumbai',
    duration: '2-3 hours',
    isUrgent: true,
    postedAt: '2025-08-25T08:00:00Z',
    views: 24,
    applications: 8,
    description: 'Need urgent repair of kitchen sink. Water is leaking from the faucet and the drain is blocked.'
  },
  {
    id: '2',
    title: 'House Cleaning - Deep Clean Required',
    category: 'Cleaner',
    status: 'in_progress',
    rate: 800,
    rateType: 'daily',
    location: 'Bandra, Mumbai',
    duration: '6-8 hours',
    isUrgent: false,
    postedAt: '2025-08-24T14:30:00Z',
    views: 18,
    applications: 5,
    acceptedWorker: {
      name: '<PERSON><PERSON>',
      avatar: '👩‍🧹',
      rating: 4.8
    },
    description: '3BHK apartment needs thorough deep cleaning. All rooms, kitchen, and bathrooms.'
  },
  {
    id: '3',
    title: 'AC Installation Work',
    category: 'Electrician',
    status: 'completed',
    rate: 2000,
    rateType: 'fixed',
    location: 'Andheri, Mumbai',
    duration: '1-2 days',
    isUrgent: false,
    postedAt: '2025-08-23T10:15:00Z',
    views: 32,
    applications: 12,
    completedWorker: {
      name: 'Rajesh Kumar',
      avatar: '👨‍🔧',
      rating: 5.0
    },
    description: 'Install 2 split AC units in bedroom and living room with proper electrical connections.'
  },
  {
    id: '4',
    title: 'Painting Work - 2BHK Apartment',
    category: 'Painter',
    status: 'open',
    rate: 1200,
    rateType: 'daily',
    location: 'Malad, Mumbai',
    duration: '3-4 days',
    isUrgent: false,
    postedAt: '2025-08-22T16:45:00Z',
    views: 15,
    applications: 3,
    description: 'Interior painting for 2BHK apartment. Walls and ceiling. Materials will be provided.'
  }
];

export default function PostedJobsPage() {
  const [jobs, setJobs] = useState(MOCK_POSTED_JOBS);
  const [activeTab, setActiveTab] = useState('all');
  const [sortBy, setSortBy] = useState('recent');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'in_progress': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'completed': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'open': return 'Open';
      case 'in_progress': return 'In Progress';
      case 'completed': return 'Completed';
      default: return status;
    }
  };

  const filteredJobs = activeTab === 'all' 
    ? jobs 
    : jobs.filter(job => job.status === activeTab);

  const sortedJobs = [...filteredJobs].sort((a, b) => {
    switch (sortBy) {
      case 'recent':
        return new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime();
      case 'applications':
        return b.applications - a.applications;
      case 'views':
        return b.views - a.views;
      default:
        return 0;
    }
  });

  const getTabCount = (status: string) => {
    if (status === 'all') return jobs.length;
    return jobs.filter(job => job.status === status).length;
  };

  return (
    <div className="px-4 py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Jobs</h1>
          <p className="text-gray-600">Manage your posted jobs and applications</p>
        </div>
        <Link
          href="/post-job"
          className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          <PlusIcon className="w-4 h-4" />
          <span className="text-sm">Post Job</span>
        </Link>
      </div>

      {/* Tabs - Following UI/UX spec: Open | In Progress | Completed */}
      <div className="space-y-4">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {[
            { id: 'all', label: 'All', count: getTabCount('all') },
            { id: 'open', label: 'Open', count: getTabCount('open') },
            { id: 'in_progress', label: 'In Progress', count: getTabCount('in_progress') },
            { id: 'completed', label: 'Completed', count: getTabCount('completed') }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-white text-green-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>

        {/* Sort & Filter */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FunnelIcon className="w-4 h-4 text-gray-400" />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="recent">Most Recent</option>
              <option value="applications">Most Applications</option>
              <option value="views">Most Views</option>
            </select>
          </div>
          <p className="text-sm text-gray-500">
            {sortedJobs.length} job{sortedJobs.length !== 1 ? 's' : ''}
          </p>
        </div>
      </div>

      {/* Job Cards */}
      <div className="space-y-4">
        {sortedJobs.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <UserGroupIcon className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No jobs found</h3>
            <p className="text-gray-600 mb-4">
              {activeTab === 'all' 
                ? "You haven't posted any jobs yet."
                : `No ${activeTab.replace('_', ' ')} jobs found.`
              }
            </p>
            <Link
              href="/post-job"
              className="inline-flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
              <span>Post Your First Job</span>
            </Link>
          </div>
        ) : (
          sortedJobs.map((job) => (
            <JobCard key={job.id} job={job} />
          ))
        )}
      </div>
    </div>
  );
}

// Job Card Component following UI/UX specs
function JobCard({ job }: { job: any }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'in_progress': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'completed': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'open': return 'Open';
      case 'in_progress': return 'In Progress';
      case 'completed': return 'Completed';
      default: return status;
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(job.status)}`}>
              {getStatusLabel(job.status)}
            </span>
            {job.isUrgent && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                <BoltIcon className="w-3 h-3 mr-1" />
                Urgent
              </span>
            )}
          </div>
          <h3 className="font-semibold text-gray-900 mb-1">{job.title}</h3>
          <p className="text-sm text-gray-600">{job.category}</p>
        </div>
        <button className="p-1 hover:bg-gray-100 rounded-full transition-colors">
          <EllipsisVerticalIcon className="w-5 h-5 text-gray-400" />
        </button>
      </div>

      {/* Job Details */}
      <div className="space-y-2 mb-4">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4 text-gray-600">
            <div className="flex items-center space-x-1">
              <CurrencyRupeeIcon className="w-4 h-4" />
              <span>₹{job.rate} {job.rateType === 'fixed' ? '' : `/${job.rateType === 'hourly' ? 'hr' : 'day'}`}</span>
            </div>
            <div className="flex items-center space-x-1">
              <ClockIcon className="w-4 h-4" />
              <span>{job.duration}</span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-1 text-sm text-gray-600">
          <MapPinIcon className="w-4 h-4" />
          <span>{job.location}</span>
        </div>
      </div>

      {/* Metrics - Following UI/UX spec: Views, Applications, Time since posting */}
      <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-1 text-gray-600">
            <EyeIcon className="w-4 h-4" />
            <span>{job.views} views</span>
          </div>
          <div className="flex items-center space-x-1 text-gray-600">
            <UserGroupIcon className="w-4 h-4" />
            <span>{job.applications} applications</span>
          </div>
        </div>
        <span className="text-xs text-gray-500">
          {new Date(job.postedAt).toLocaleDateString('en-IN', { 
            day: 'numeric', 
            month: 'short',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </span>
      </div>

      {/* Worker Info for In Progress/Completed */}
      {job.status === 'in_progress' && job.acceptedWorker && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">{job.acceptedWorker.avatar}</span>
            <div>
              <p className="font-medium text-orange-900">{job.acceptedWorker.name}</p>
              <p className="text-sm text-orange-700">Working on this job</p>
            </div>
          </div>
        </div>
      )}

      {job.status === 'completed' && job.completedWorker && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{job.completedWorker.avatar}</span>
              <div>
                <p className="font-medium text-green-900">{job.completedWorker.name}</p>
                <p className="text-sm text-green-700">Completed this job</p>
              </div>
            </div>
            <div className="flex items-center space-x-1">
              <span className="text-yellow-400">⭐</span>
              <span className="text-sm font-medium text-green-900">{job.completedWorker.rating}</span>
            </div>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex space-x-3">
        <Link
          href={`/jobs/${job.id}/applicants`}
          className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg text-center text-sm font-medium hover:bg-green-700 transition-colors"
        >
          {job.status === 'open' ? `View Applications (${job.applications})` : 'View Details'}
        </Link>
        {job.status === 'open' && (
          <button className="px-4 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors">
            Edit
          </button>
        )}
      </div>
    </div>
  );
}
