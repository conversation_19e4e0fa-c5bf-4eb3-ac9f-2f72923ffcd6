'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { 
  ArrowLeftIcon, 
  ClockIcon,
  CheckCircleIcon,
  PlayIcon,
  MapPinIcon,
  CalendarIcon,
  UserIcon,
  ChatBubbleLeftRightIcon,
  CameraIcon,
  CurrencyRupeeIcon
} from '@heroicons/react/24/outline';
import { CheckCircleIcon as CheckCircleSolidIcon } from '@heroicons/react/24/solid';

// Mock application data (in real app, this would come from API)
const MOCK_APPLICATION_STATUS = {
  '1': {
    id: '1',
    jobId: '1',
    jobTitle: 'Plumbing Work Required - Kitchen & Bathroom',
    jobCategory: 'Plumber',
    jobRate: 800,
    jobRateType: 'daily',
    jobLocation: 'Andheri West, Mumbai',
    posterName: 'Priya Sharma',
    posterPhone: '+91 98765 43210',
    appliedWith: 'Plumber',
    appliedAt: '2024-01-15T10:30:00Z',
    status: 'applied',
    message: 'I have 5 years of experience in plumbing work and can start immediately.',
    timeline: [
      {
        status: 'applied',
        timestamp: '2024-01-15T10:30:00Z',
        title: 'Application Submitted',
        description: 'Your application has been sent to the poster',
        completed: true
      },
      {
        status: 'accepted',
        timestamp: null,
        title: 'Application Accepted',
        description: 'Waiting for poster to accept your application',
        completed: false
      },
      {
        status: 'started',
        timestamp: null,
        title: 'Work Started',
        description: 'Begin working on the job',
        completed: false
      },
      {
        status: 'completed',
        timestamp: null,
        title: 'Work Completed',
        description: 'Submit proof of completed work',
        completed: false
      }
    ]
  },
  '2': {
    id: '2',
    jobId: '2',
    jobTitle: 'House Cleaning - Deep Clean Required',
    jobCategory: 'Cleaner',
    jobRate: 150,
    jobRateType: 'hourly',
    jobLocation: 'Bandra East, Mumbai',
    posterName: 'Rajesh Kumar',
    posterPhone: '+91 87654 32109',
    appliedWith: 'Cleaner',
    appliedAt: '2024-01-14T14:20:00Z',
    status: 'accepted',
    message: 'Professional cleaning service with own supplies.',
    acceptedAt: '2024-01-14T16:45:00Z',
    startDate: '2024-01-16T09:00:00Z',
    timeline: [
      {
        status: 'applied',
        timestamp: '2024-01-14T14:20:00Z',
        title: 'Application Submitted',
        description: 'Your application has been sent to the poster',
        completed: true
      },
      {
        status: 'accepted',
        timestamp: '2024-01-14T16:45:00Z',
        title: 'Application Accepted',
        description: 'Congratulations! Your application has been accepted',
        completed: true
      },
      {
        status: 'started',
        timestamp: null,
        title: 'Work Started',
        description: 'Start work on Jan 16, 2024 at 9:00 AM',
        completed: false
      },
      {
        status: 'completed',
        timestamp: null,
        title: 'Work Completed',
        description: 'Submit proof of completed work',
        completed: false
      }
    ]
  }
};

export default function JobStatusPage() {
  const params = useParams();
  const router = useRouter();
  const applicationId = params.id as string;
  const [application, setApplication] = useState<any>(null);

  useEffect(() => {
    // In real app, fetch application status from API
    const appData = MOCK_APPLICATION_STATUS[applicationId as keyof typeof MOCK_APPLICATION_STATUS];
    if (appData) {
      setApplication(appData);
    }
  }, [applicationId]);

  const getStatusColor = (status: string, completed: boolean = false) => {
    if (completed) return 'text-green-600 bg-green-50 border-green-200';
    
    switch (status) {
      case 'applied': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'accepted': return 'text-green-600 bg-green-50 border-green-200';
      case 'started': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'completed': return 'text-purple-600 bg-purple-50 border-purple-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string, completed: boolean = false) => {
    if (completed) return <CheckCircleSolidIcon className="w-5 h-5 text-green-600" />;
    
    switch (status) {
      case 'applied': return <ClockIcon className="w-5 h-5" />;
      case 'accepted': return <CheckCircleIcon className="w-5 h-5" />;
      case 'started': return <PlayIcon className="w-5 h-5" />;
      case 'completed': return <CheckCircleIcon className="w-5 h-5" />;
      default: return <ClockIcon className="w-5 h-5" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getNextAction = () => {
    switch (application?.status) {
      case 'applied': return 'Contact poster if needed';
      case 'accepted': return 'Prepare for work start';
      case 'started': return 'Complete work and take photos';
      case 'completed': return 'View earnings and rating';
      default: return '';
    }
  };

  if (!application) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading application status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Job Status</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6">
        {/* Job Summary */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h2 className="font-semibold text-gray-900 mb-2">{application.jobTitle}</h2>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Category:</span>
              <span className="text-gray-900">{application.jobCategory}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Location:</span>
              <span className="text-gray-900">{application.jobLocation}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Rate:</span>
              <span className="font-semibold text-green-600">
                ₹{application.jobRate}/{application.jobRateType === 'hourly' ? 'hr' : 'day'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Applied as:</span>
              <span className="text-gray-900">{application.appliedWith}</span>
            </div>
          </div>
        </div>

        {/* Current Status */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-3">Current Status</h3>
          <div className={`p-3 rounded-lg border ${getStatusColor(application.status)}`}>
            <div className="flex items-center space-x-3">
              {getStatusIcon(application.status)}
              <div>
                <div className="font-medium capitalize">{application.status}</div>
                <div className="text-sm opacity-75">{getNextAction()}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Progress Timeline */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-4">Progress Timeline</h3>
          <div className="space-y-4">
            {application.timeline.map((step: any, index: number) => (
              <div key={step.status} className="flex items-start space-x-3">
                {/* Icon */}
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                  step.completed ? 'bg-green-100' : 'bg-gray-100'
                }`}>
                  {getStatusIcon(step.status, step.completed)}
                </div>
                
                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className={`font-medium ${step.completed ? 'text-green-900' : 'text-gray-900'}`}>
                      {step.title}
                    </h4>
                    {step.timestamp && (
                      <span className="text-xs text-gray-500">
                        {formatDate(step.timestamp)}
                      </span>
                    )}
                  </div>
                  <p className={`text-sm ${step.completed ? 'text-green-700' : 'text-gray-600'}`}>
                    {step.description}
                  </p>
                </div>
                
                {/* Connector Line */}
                {index < application.timeline.length - 1 && (
                  <div className={`absolute left-7 mt-8 w-0.5 h-6 ${
                    step.completed ? 'bg-green-200' : 'bg-gray-200'
                  }`} style={{ marginLeft: '-1px' }} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Poster Contact */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-3">Poster Contact</h3>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
              <UserIcon className="w-5 h-5 text-gray-600" />
            </div>
            <div className="flex-1">
              <div className="font-medium text-gray-900">{application.posterName}</div>
              <div className="text-sm text-gray-600">{application.posterPhone}</div>
            </div>
            <button className="p-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors">
              <ChatBubbleLeftRightIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Your Application Message */}
        {application.message && (
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Your Application Message</h3>
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-gray-700 text-sm">{application.message}</p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          {application.status === 'started' && (
            <button className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center space-x-2">
              <CameraIcon className="w-5 h-5" />
              <span>Upload Work Completion Photos</span>
            </button>
          )}
          
          {application.status === 'accepted' && (
            <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors">
              Mark as Started
            </button>
          )}
          
          <button className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2">
            <ChatBubbleLeftRightIcon className="w-5 h-5" />
            <span>Message Poster</span>
          </button>
        </div>
      </div>
    </div>
  );
}
