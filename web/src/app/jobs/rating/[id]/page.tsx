'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { 
  ArrowLeftIcon, 
  StarIcon,
  UserIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';

// Mock job data for rating
const MOCK_JOB_DATA = {
  '3': {
    id: '3',
    title: 'Electrical Wiring - New AC Installation',
    category: 'Electrician',
    location: 'Powai, Mumbai',
    posterName: 'Amit Singh',
    posterAvatar: '👨‍💼',
    completedAt: '2025-08-25T10:30:00Z',
    finalAmount: 1500,
    duration: '6 hours'
  }
};

const RATING_LABELS = [
  { value: 1, label: 'Poor', description: 'Very unsatisfied with the experience' },
  { value: 2, label: 'Fair', description: 'Below expectations' },
  { value: 3, label: 'Good', description: 'Met expectations' },
  { value: 4, label: 'Very Good', description: 'Exceeded expectations' },
  { value: 5, label: 'Excellent', description: 'Outstanding experience' }
];

export default function RatingPage() {
  const params = useParams();
  const router = useRouter();
  const jobId = params.id as string;
  const [job, setJob] = useState<any>(null);
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Load job data
    const jobData = MOCK_JOB_DATA[jobId as keyof typeof MOCK_JOB_DATA];
    if (jobData) {
      setJob(jobData);
    }
  }, [jobId]);

  const handleStarClick = (value: number) => {
    setRating(value);
  };

  const handleStarHover = (value: number) => {
    setHoverRating(value);
  };

  const handleStarLeave = () => {
    setHoverRating(0);
  };

  const getCurrentRatingLabel = () => {
    const currentRating = hoverRating || rating;
    const ratingData = RATING_LABELS.find(r => r.value === currentRating);
    return ratingData || null;
  };

  const handleSubmitRating = async () => {
    if (rating === 0) return;
    
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      // Store rating data
      const ratingData = {
        jobId: job.id,
        posterName: job.posterName,
        rating: rating,
        comment: comment.trim(),
        submittedAt: new Date().toISOString()
      };
      
      localStorage.setItem(`rating_${job.id}`, JSON.stringify(ratingData));
      
      setIsSubmitting(false);
      
      // Navigate to earnings dashboard
      router.push('/earnings');
    }, 2000);
  };

  if (!job) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading rating form...</p>
        </div>
      </div>
    );
  }

  const currentRatingLabel = getCurrentRatingLabel();

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Rate Your Experience</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-8">
        {/* Job Summary */}
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center mx-auto">
            <span className="text-2xl">{job.posterAvatar}</span>
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">{job.posterName}</h2>
            <p className="text-gray-600">{job.title}</p>
            <p className="text-sm text-gray-500">{job.location} • {job.duration}</p>
          </div>
        </div>

        {/* Rating Stars */}
        <div className="text-center space-y-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              How was your experience?
            </h3>
            <p className="text-gray-600 text-sm">
              Your feedback helps improve the platform for everyone
            </p>
          </div>

          {/* Star Rating */}
          <div className="flex items-center justify-center space-x-2">
            {[1, 2, 3, 4, 5].map((star) => {
              const isActive = star <= (hoverRating || rating);
              return (
                <button
                  key={star}
                  onClick={() => handleStarClick(star)}
                  onMouseEnter={() => handleStarHover(star)}
                  onMouseLeave={handleStarLeave}
                  className="p-2 hover:scale-110 transition-transform"
                >
                  {isActive ? (
                    <StarSolidIcon className="w-12 h-12 text-yellow-400" />
                  ) : (
                    <StarIcon className="w-12 h-12 text-gray-300" />
                  )}
                </button>
              );
            })}
          </div>

          {/* Rating Label */}
          {currentRatingLabel && (
            <div className="text-center space-y-1">
              <p className="text-xl font-semibold text-gray-900">
                {currentRatingLabel.label}
              </p>
              <p className="text-sm text-gray-600">
                {currentRatingLabel.description}
              </p>
            </div>
          )}
        </div>

        {/* Comment Section */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Comments (Optional)
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Share more details about your experience..."
              rows={4}
              maxLength={200}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            />
            <div className="flex items-center justify-between mt-2">
              <p className="text-xs text-gray-500">
                Help others by sharing specific details
              </p>
              <p className="text-xs text-gray-500">
                {comment.length}/200
              </p>
            </div>
          </div>
        </div>

        {/* Job Details Card */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-3">Job Details</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Completed:</span>
              <span className="text-gray-900">
                {new Date(job.completedAt).toLocaleDateString('en-IN', {
                  day: 'numeric',
                  month: 'short',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Duration:</span>
              <span className="text-gray-900">{job.duration}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Earnings:</span>
              <span className="font-semibold text-green-600">₹{job.finalAmount}</span>
            </div>
          </div>
        </div>

        {/* Privacy Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <CheckCircleIcon className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Your privacy is protected</p>
              <p>Ratings are anonymous and help maintain quality standards on the platform.</p>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Submit Button */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 safe-area-bottom">
        <div className="mobile-container">
          <button
            onClick={handleSubmitRating}
            disabled={rating === 0 || isSubmitting}
            className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors ${
              rating > 0 && !isSubmitting
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Submitting Rating...
              </div>
            ) : rating === 0 ? (
              'Select a Rating to Continue'
            ) : (
              `Submit ${rating}-Star Rating`
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
