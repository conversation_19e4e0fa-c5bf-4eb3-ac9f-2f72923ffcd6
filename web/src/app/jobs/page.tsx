'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTheme } from '@/contexts/ThemeContext';
import {
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlayIcon,
  MapPinIcon,
  CurrencyRupeeIcon,
  CalendarIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

const TABS = [
  { id: 'applications', label: 'Applications', count: 0 },
  { id: 'active', label: 'Active', count: 0 },
  { id: 'completed', label: 'Completed', count: 0 }
];

// Mock data for different job statuses
const MOCK_APPLICATIONS = [
  {
    id: '1',
    jobId: '1',
    jobTitle: 'Plumbing Work Required - Kitchen & Bathroom',
    jobCategory: 'Plumber',
    jobRate: 800,
    jobRateType: 'daily',
    jobLocation: 'Andheri West, Mumbai',
    posterName: '<PERSON><PERSON>',
    appliedWith: 'Plumber',
    appliedAt: '2024-01-15T10:30:00Z',
    status: 'applied',
    message: 'I have 5 years of experience in plumbing work and can start immediately.'
  },
  {
    id: '2',
    jobId: '2',
    jobTitle: 'House Cleaning - Deep Clean Required',
    jobCategory: 'Cleaner',
    jobRate: 150,
    jobRateType: 'hourly',
    jobLocation: 'Bandra East, Mumbai',
    posterName: 'Rajesh Kumar',
    appliedWith: 'Cleaner',
    appliedAt: '2024-01-14T14:20:00Z',
    status: 'accepted',
    message: 'Professional cleaning service with own supplies.',
    acceptedAt: '2024-01-14T16:45:00Z',
    startDate: '2024-01-16T09:00:00Z'
  },
  {
    id: '3',
    jobId: '3',
    jobTitle: 'Electrical Wiring - New AC Installation',
    jobCategory: 'Electrician',
    jobRate: 1500,
    jobRateType: 'daily',
    jobLocation: 'Powai, Mumbai',
    posterName: 'Amit Singh',
    appliedWith: 'Electrician',
    appliedAt: '2024-01-13T11:15:00Z',
    status: 'started',
    message: 'Licensed electrician with AC installation experience.',
    acceptedAt: '2024-01-13T15:30:00Z',
    startedAt: '2024-01-15T08:00:00Z'
  },
  {
    id: '4',
    jobId: '4',
    jobTitle: 'Painting Work - 2BHK Apartment',
    jobCategory: 'Painter',
    jobRate: 1200,
    jobRateType: 'daily',
    jobLocation: 'Goregaon West, Mumbai',
    posterName: 'Sunita Patel',
    appliedWith: 'Painter',
    appliedAt: '2024-01-10T09:45:00Z',
    status: 'completed',
    message: 'Professional painter with 8 years experience.',
    acceptedAt: '2024-01-10T12:00:00Z',
    startedAt: '2024-01-12T08:30:00Z',
    completedAt: '2024-01-14T17:00:00Z',
    rating: 5,
    earnings: 3600
  }
];

export default function JobsPage() {
  const { mode } = useTheme();
  const [activeTab, setActiveTab] = useState('applications');
  const [applications, setApplications] = useState(MOCK_APPLICATIONS);
  const [tabs, setTabs] = useState(TABS);

  useEffect(() => {
    // Load applications from localStorage
    const storedApplications = JSON.parse(localStorage.getItem('myApplications') || '[]');
    const allApplications = [...MOCK_APPLICATIONS, ...storedApplications];
    setApplications(allApplications);

    // Update tab counts
    const updatedTabs = tabs.map(tab => ({
      ...tab,
      count: allApplications.filter(app => {
        switch (tab.id) {
          case 'applications': return app.status === 'applied';
          case 'active': return ['accepted', 'started'].includes(app.status);
          case 'completed': return app.status === 'completed';
          default: return false;
        }
      }).length
    }));
    setTabs(updatedTabs);
  }, []);

  // Show different content based on mode
  if (mode === 'poster') {
    return (
      <div className="px-4 py-6">
        <div className="text-center space-y-4">
          <div className="w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
            <span className="text-4xl">💼</span>
          </div>
          <h2 className="text-xl font-semibold text-gray-900">No Posted Jobs Yet</h2>
          <p className="text-gray-600 max-w-sm mx-auto">
            Create your first job posting to find workers
          </p>
        </div>
      </div>
    );
  }

  const getFilteredApplications = () => {
    return applications.filter(app => {
      switch (activeTab) {
        case 'applications': return app.status === 'applied';
        case 'active': return ['accepted', 'started'].includes(app.status);
        case 'completed': return app.status === 'completed';
        default: return false;
      }
    });
  };

  const filteredApplications = getFilteredApplications();

  return (
    <div className="px-4 py-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold text-gray-900">My Jobs</h1>
        <p className="text-gray-600">Track your applications and active work</p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {tab.label}
            {tab.count > 0 && (
              <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${
                activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-200 text-gray-600'
              }`}>
                {tab.count}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="space-y-4">
        {filteredApplications.length > 0 ? (
          filteredApplications.map((app) => (
            <JobApplicationCard key={app.id} application={app} />
          ))
        ) : (
          <EmptyState activeTab={activeTab} />
        )}
      </div>
    </div>
  );
}

// Job Application Card Component
function JobApplicationCard({ application }: { application: any }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'applied': return 'text-blue-600 bg-blue-50';
      case 'accepted': return 'text-green-600 bg-green-50';
      case 'started': return 'text-orange-600 bg-orange-50';
      case 'completed': return 'text-purple-600 bg-purple-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'applied': return <ClockIcon className="w-4 h-4" />;
      case 'accepted': return <CheckCircleIcon className="w-4 h-4" />;
      case 'started': return <PlayIcon className="w-4 h-4" />;
      case 'completed': return <CheckCircleIcon className="w-4 h-4" />;
      default: return <ClockIcon className="w-4 h-4" />;
    }
  };

  const getNextAction = (app: any) => {
    switch (app.status) {
      case 'applied': return 'Waiting for response';
      case 'accepted': return `Start on ${new Date(app.startDate).toLocaleDateString()}`;
      case 'started': return 'Complete work and upload proof';
      case 'completed': return 'View earnings';
      default: return '';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Link href={`/jobs/status/${application.id}`} className="block">
      <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2">
              {application.jobTitle}
            </h3>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>{application.jobCategory}</span>
              <span>•</span>
              <span>{application.jobLocation}</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getStatusColor(application.status)}`}>
              {getStatusIcon(application.status)}
              <span className="capitalize">{application.status}</span>
            </span>
          </div>
        </div>

        {/* Details */}
        <div className="space-y-2 mb-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Applied as:</span>
            <span className="font-medium text-gray-900">{application.appliedWith}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Rate:</span>
            <span className="font-semibold text-green-600">
              ₹{application.jobRate}/{application.jobRateType === 'hourly' ? 'hr' : 'day'}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Applied:</span>
            <span className="text-gray-900">{formatDate(application.appliedAt)}</span>
          </div>
        </div>

        {/* Next Action */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <span className="text-sm text-gray-600">{getNextAction(application)}</span>
          <ArrowRightIcon className="w-4 h-4 text-gray-400" />
        </div>

        {/* Earnings (for completed jobs) */}
        {application.status === 'completed' && application.earnings && (
          <div className="mt-3 p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-green-800">Earnings</span>
              <span className="text-lg font-bold text-green-600">₹{application.earnings}</span>
            </div>
            {application.rating && (
              <div className="text-sm text-green-700 mt-1">
                Rating: {'⭐'.repeat(application.rating)} ({application.rating}/5)
              </div>
            )}
          </div>
        )}
      </div>
    </Link>
  );
}

// Empty State Component
function EmptyState({ activeTab }: { activeTab: string }) {
  const getEmptyMessage = () => {
    switch (activeTab) {
      case 'applications': return {
        title: 'No Applications Yet',
        description: 'Start applying for jobs to see them here',
        action: 'Browse Jobs'
      };
      case 'active': return {
        title: 'No Active Jobs',
        description: 'Your accepted and ongoing jobs will appear here',
        action: 'Apply for Jobs'
      };
      case 'completed': return {
        title: 'No Completed Jobs',
        description: 'Your finished jobs and earnings will be shown here',
        action: 'Find Work'
      };
      default: return {
        title: 'No Jobs',
        description: 'Start your journey by applying for jobs',
        action: 'Browse Jobs'
      };
    }
  };

  const message = getEmptyMessage();

  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <ClockIcon className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{message.title}</h3>
      <p className="text-gray-600 mb-6">{message.description}</p>
      <Link
        href="/"
        className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
      >
        {message.action}
      </Link>
    </div>
  );
}
