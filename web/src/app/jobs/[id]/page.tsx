'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import {
  ArrowLeftIcon,
  BookmarkIcon,
  ShareIcon,
  MapPinIcon,
  ClockIcon,
  CurrencyRupeeIcon,
  UserIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { BookmarkIcon as BookmarkSolidIcon } from '@heroicons/react/24/solid';

// Mock job data (in real app, this would come from API)
const MOCK_JOB_DETAILS = {
  '1': {
    id: '1',
    title: 'Plumbing Work Required - Kitchen & Bathroom',
    description: 'Need experienced plumber to fix kitchen sink leak and install new bathroom faucet. The kitchen sink has been leaking for 2 days and needs immediate attention. The bathroom faucet is old and needs complete replacement with a modern fixture.',
    category: 'Plumber',
    rate: 800,
    rateType: 'daily',
    distance: 1.2,
    urgency: 'urgent',
    postedTime: '2 hours ago',
    location: 'Andheri West, Mumbai',
    fullAddress: '123 Linking Road, Andheri West, Mumbai 400058',
    poster: {
      name: '<PERSON><PERSON>',
      rating: 4.8,
      reviewCount: 23,
      verified: true,
      joinedDate: 'March 2023'
    },
    requirements: [
      'Experience with modern fixtures',
      'Own tools required',
      'Available for immediate start',
      'Knowledge of pipe fitting'
    ],
    duration: '4-6 hours',
    isBookmarked: false,
    matchScore: 95,
    photos: [
      '/api/placeholder/300/200',
      '/api/placeholder/300/200'
    ],
    additionalDetails: {
      workType: 'Repair & Installation',
      paymentMethod: 'Cash on completion',
      materialsProvided: 'New faucet provided, other materials by worker',
      accessInfo: 'Building has lift, parking available',
      contactPreference: 'Call between 9 AM - 6 PM'
    }
  }
};

export default function JobDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const jobId = params.id as string;
  const [job, setJob] = useState<any>(null);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [showApplyDrawer, setShowApplyDrawer] = useState(false);
  const [userPersonas, setUserPersonas] = useState<any[]>([]);
  const [selectedPersona, setSelectedPersona] = useState<any>(null);
  const [applicationMessage, setApplicationMessage] = useState('');
  const [dailyApplications, setDailyApplications] = useState(3);
  const [isApplying, setIsApplying] = useState(false);

  useEffect(() => {
    // In real app, fetch job details from API
    const jobData = MOCK_JOB_DETAILS[jobId as keyof typeof MOCK_JOB_DETAILS];
    if (jobData) {
      setJob(jobData);
      setIsBookmarked(jobData.isBookmarked);
    }

    // Load user personas
    const personas = JSON.parse(localStorage.getItem('workerPersonas') || '[]');
    setUserPersonas(personas);
    if (personas.length > 0) {
      setSelectedPersona(personas[0]);
    }

    // Load daily applications count
    const today = new Date().toDateString();
    const storedApplications = localStorage.getItem(`applications_${today}`);
    if (storedApplications) {
      setDailyApplications(parseInt(storedApplications));
    }
  }, [jobId]);

  const handleBookmarkToggle = () => {
    setIsBookmarked(!isBookmarked);
  };

  const handleApply = () => {
    setShowApplyDrawer(true);
  };

  const handleSubmitApplication = async () => {
    if (!selectedPersona || dailyApplications >= 10) return;

    setIsApplying(true);

    // Simulate API call
    setTimeout(() => {
      // Update daily applications count
      const today = new Date().toDateString();
      const newCount = dailyApplications + 1;
      setDailyApplications(newCount);
      localStorage.setItem(`applications_${today}`, newCount.toString());

      // Store application
      const applications = JSON.parse(localStorage.getItem('myApplications') || '[]');
      const newApplication = {
        id: Date.now().toString(),
        jobId: job.id,
        jobTitle: job.title,
        jobCategory: job.category,
        jobRate: job.rate,
        jobRateType: job.rateType,
        jobLocation: job.location,
        posterName: job.poster.name,
        appliedWith: selectedPersona.skill,
        message: applicationMessage,
        appliedAt: new Date().toISOString(),
        status: 'applied'
      };
      applications.push(newApplication);
      localStorage.setItem('myApplications', JSON.stringify(applications));

      setIsApplying(false);
      setShowApplyDrawer(false);

      // Show success message or redirect
      alert('Application submitted successfully!');
    }, 2000);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: job?.title,
        text: job?.description,
        url: window.location.href,
      });
    }
  };

  if (!job) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading job details...</p>
        </div>
      </div>
    );
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-50';
    if (score >= 80) return 'text-blue-600 bg-blue-50';
    return 'text-gray-600 bg-gray-50';
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Job Details</h1>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleShare}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ShareIcon className="w-6 h-6 text-gray-600" />
            </button>
            <button
              onClick={handleBookmarkToggle}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              {isBookmarked ? (
                <BookmarkSolidIcon className="w-6 h-6 text-blue-600" />
              ) : (
                <BookmarkIcon className="w-6 h-6 text-gray-600" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6 pb-24">
        {/* Job Header */}
        <div className="space-y-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              {job.title}
            </h2>
            <div className="flex items-center space-x-2 text-sm text-gray-600 mb-3">
              <span>{job.category}</span>
              <span>•</span>
              <span>{job.location}</span>
              <span>•</span>
              <span>{job.postedTime}</span>
            </div>
          </div>

          {/* Tags */}
          <div className="flex items-center space-x-2 flex-wrap">
            {job.urgency === 'urgent' && (
              <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getUrgencyColor(job.urgency)}`}>
                <ExclamationTriangleIcon className="w-4 h-4 inline mr-1" />
                Urgent
              </span>
            )}
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getMatchScoreColor(job.matchScore)}`}>
              {job.matchScore}% match for your skills
            </span>
          </div>

          {/* Rate & Duration */}
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-green-600">
                  ₹{job.rate}/{job.rateType === 'hourly' ? 'hour' : 'day'}
                </div>
                <div className="text-sm text-green-700">
                  Duration: {job.duration}
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-600">
                  <MapPinIcon className="w-4 h-4 inline mr-1" />
                  {job.distance} km away
                </div>
                <div className="text-sm text-gray-600">
                  <ClockIcon className="w-4 h-4 inline mr-1" />
                  {job.duration}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900">Job Description</h3>
          <p className="text-gray-700 leading-relaxed">
            {job.description}
          </p>
        </div>

        {/* Requirements */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900">Requirements</h3>
          <ul className="space-y-2">
            {job.requirements.map((req: string, index: number) => (
              <li key={index} className="flex items-start space-x-2">
                <CheckCircleIcon className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700">{req}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Additional Details */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900">Additional Details</h3>
          <div className="bg-gray-50 p-4 rounded-lg space-y-3">
            <div className="grid grid-cols-1 gap-3">
              <div>
                <span className="text-sm font-medium text-gray-600">Work Type:</span>
                <span className="text-sm text-gray-900 ml-2">{job.additionalDetails.workType}</span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-600">Payment:</span>
                <span className="text-sm text-gray-900 ml-2">{job.additionalDetails.paymentMethod}</span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-600">Materials:</span>
                <span className="text-sm text-gray-900 ml-2">{job.additionalDetails.materialsProvided}</span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-600">Access:</span>
                <span className="text-sm text-gray-900 ml-2">{job.additionalDetails.accessInfo}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Poster Info */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900">Posted By</h3>
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                <UserIcon className="w-6 h-6 text-gray-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h4 className="font-medium text-gray-900">{job.poster.name}</h4>
                  {job.poster.verified && (
                    <CheckCircleIcon className="w-4 h-4 text-blue-600" />
                  )}
                </div>
                <div className="text-sm text-gray-600">
                  ⭐ {job.poster.rating} ({job.poster.reviewCount} reviews)
                </div>
                <div className="text-xs text-gray-500">
                  Member since {job.poster.joinedDate}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Map Preview */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900">Location</h3>
          <div className="bg-gray-200 rounded-lg h-48 flex items-center justify-center">
            <div className="text-center text-gray-600">
              <MapPinIcon className="w-8 h-8 mx-auto mb-2" />
              <p className="text-sm">Map preview</p>
              <p className="text-xs">{job.fullAddress}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Apply Button */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 safe-area-bottom">
        <div className="mobile-container">
          <button
            onClick={handleApply}
            className="w-full bg-blue-600 text-white py-4 px-6 rounded-xl font-semibold text-lg hover:bg-blue-700 transition-colors"
          >
            Apply for this Job
          </button>
        </div>
      </div>

      {/* Apply Drawer */}
      {showApplyDrawer && (
        <ApplyDrawer
          job={job}
          userPersonas={userPersonas}
          selectedPersona={selectedPersona}
          setSelectedPersona={setSelectedPersona}
          applicationMessage={applicationMessage}
          setApplicationMessage={setApplicationMessage}
          dailyApplications={dailyApplications}
          isApplying={isApplying}
          onSubmit={handleSubmitApplication}
          onClose={() => setShowApplyDrawer(false)}
        />
      )}
    </div>
  );
}

// Apply Drawer Component
function ApplyDrawer({
  job,
  userPersonas,
  selectedPersona,
  setSelectedPersona,
  applicationMessage,
  setApplicationMessage,
  dailyApplications,
  isApplying,
  onSubmit,
  onClose
}: {
  job: any;
  userPersonas: any[];
  selectedPersona: any;
  setSelectedPersona: (persona: any) => void;
  applicationMessage: string;
  setApplicationMessage: (message: string) => void;
  dailyApplications: number;
  isApplying: boolean;
  onSubmit: () => void;
  onClose: () => void;
}) {
  const canApply = selectedPersona && dailyApplications < 10 && !isApplying;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end">
      <div className="bg-white rounded-t-2xl w-full max-h-[80vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Apply for Job</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-6 h-6 text-gray-600" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 space-y-6">
          {/* Daily Limit */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-blue-900">Daily Applications</h3>
                <p className="text-sm text-blue-700">
                  {dailyApplications} of 10 applications used today
                </p>
              </div>
              <div className="text-2xl font-bold text-blue-600">
                {10 - dailyApplications}
              </div>
            </div>
            <div className="mt-2 w-full bg-blue-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(dailyApplications / 10) * 100}%` }}
              />
            </div>
          </div>

          {/* Job Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">{job.title}</h3>
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>{job.category} • {job.location}</span>
              <span className="font-semibold text-green-600">
                ₹{job.rate}/{job.rateType === 'hourly' ? 'hr' : 'day'}
              </span>
            </div>
          </div>

          {/* Persona Selection */}
          <div className="space-y-3">
            <h3 className="font-medium text-gray-900">Apply as:</h3>
            {userPersonas.length > 0 ? (
              <div className="space-y-2">
                {userPersonas.map((persona) => (
                  <button
                    key={persona.id}
                    onClick={() => setSelectedPersona(persona)}
                    className={`w-full p-3 rounded-lg border-2 text-left transition-colors ${
                      selectedPersona?.id === persona.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-blue-300'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-gray-900">{persona.skill}</div>
                        <div className="text-sm text-gray-600">
                          ₹{persona.dailyRate}/day • {persona.specialties?.length || 0} specialties
                        </div>
                      </div>
                      {selectedPersona?.id === persona.id && (
                        <CheckCircleIcon className="w-5 h-5 text-blue-600" />
                      )}
                    </div>
                  </button>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <UserIcon className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                <p>No personas created yet</p>
                <p className="text-sm">Create a persona to apply for jobs</p>
              </div>
            )}
          </div>

          {/* Custom Message */}
          <div className="space-y-3">
            <h3 className="font-medium text-gray-900">Message to Poster (Optional)</h3>
            <textarea
              value={applicationMessage}
              onChange={(e) => setApplicationMessage(e.target.value)}
              placeholder="Tell the poster why you're the right person for this job..."
              rows={4}
              maxLength={300}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            />
            <p className="text-xs text-gray-500 text-right">
              {applicationMessage.length}/300 characters
            </p>
          </div>

          {/* Submit Button */}
          <button
            onClick={onSubmit}
            disabled={!canApply}
            className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors ${
              canApply
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isApplying ? (
              <div className="flex items-center justify-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Submitting Application...
              </div>
            ) : dailyApplications >= 10 ? (
              'Daily Limit Reached'
            ) : !selectedPersona ? (
              'Select a Persona to Apply'
            ) : (
              'Submit Application'
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
