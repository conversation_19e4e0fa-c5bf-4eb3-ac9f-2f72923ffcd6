'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  ArrowLeftIcon,
  EyeIcon,
  UserGroupIcon,
  ClockIcon,
  MapPinIcon,
  CurrencyRupeeIcon,
  BoltIcon,
  StarIcon,
  ChatBubbleLeftIcon,
  CheckCircleIcon,
  XMarkIcon,
  BookmarkIcon,
  FunnelIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';

// Mock job data
const MOCK_JOB_DATA = {
  '1': {
    id: '1',
    title: 'Kitchen Sink Repair Required',
    category: 'Plumber',
    status: 'open',
    rate: 150,
    rateType: 'hourly',
    location: 'Powai, Mumbai',
    duration: '2-3 hours',
    isUrgent: true,
    postedAt: '2025-08-25T08:00:00Z',
    views: 24,
    applications: 8,
    description: 'Need urgent repair of kitchen sink. Water is leaking from the faucet and the drain is blocked. Must bring own tools and materials.',
    requirements: ['Must bring own tools', 'Experience certificate required']
  }
};

// Mock applicants data following UI/UX specs
const MOCK_APPLICANTS = [
  {
    id: '1',
    workerName: 'Rajesh Kumar',
    avatar: '👨‍🔧',
    persona: 'Plumber',
    rate: 140,
    rateType: 'hourly',
    availability: 'Available now',
    rating: 4.8,
    reviewCount: 127,
    distance: 1.2,
    matchScore: 95,
    introMessage: 'I have 8+ years experience in plumbing. Can start immediately and bring all necessary tools.',
    appliedAt: '2025-08-25T09:30:00Z',
    status: 'pending', // pending, shortlisted, rejected, accepted
    experience: '8+ years',
    completedJobs: 156,
    responseTime: '< 1 hour'
  },
  {
    id: '2',
    workerName: 'Amit Singh',
    avatar: '👨‍🔧',
    persona: 'Plumber',
    rate: 160,
    rateType: 'hourly',
    availability: 'Available today',
    rating: 4.9,
    reviewCount: 89,
    distance: 2.1,
    matchScore: 88,
    introMessage: 'Specialized in kitchen and bathroom plumbing. Licensed plumber with modern tools.',
    appliedAt: '2025-08-25T10:15:00Z',
    status: 'shortlisted',
    experience: '6+ years',
    completedJobs: 98,
    responseTime: '< 30 min'
  },
  {
    id: '3',
    workerName: 'Suresh Patel',
    avatar: '👨‍🔧',
    persona: 'Plumber',
    rate: 120,
    rateType: 'hourly',
    availability: 'Available tomorrow',
    rating: 4.6,
    reviewCount: 203,
    distance: 3.5,
    matchScore: 72,
    introMessage: 'Experienced plumber. Can fix any plumbing issue. Reasonable rates.',
    appliedAt: '2025-08-25T11:45:00Z',
    status: 'pending',
    experience: '10+ years',
    completedJobs: 234,
    responseTime: '< 2 hours'
  }
];

export default function JobApplicantsPage() {
  const params = useParams();
  const router = useRouter();
  const jobId = params.id as string;
  const [job, setJob] = useState<any>(null);
  const [applicants, setApplicants] = useState(MOCK_APPLICANTS);
  const [sortBy, setSortBy] = useState('match_score');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showAcceptDialog, setShowAcceptDialog] = useState<string | null>(null);

  useEffect(() => {
    // Load job data
    const jobData = MOCK_JOB_DATA[jobId as keyof typeof MOCK_JOB_DATA];
    if (jobData) {
      setJob(jobData);
    }
  }, [jobId]);

  const handleStatusChange = (applicantId: string, newStatus: string) => {
    setApplicants(prev => prev.map(applicant => 
      applicant.id === applicantId 
        ? { ...applicant, status: newStatus }
        : applicant
    ));
  };

  const handleAccept = (applicantId: string) => {
    handleStatusChange(applicantId, 'accepted');
    setShowAcceptDialog(null);
    // In real app, this would update job status to 'in_progress'
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 80) return 'text-blue-600 bg-blue-50 border-blue-200';
    if (score >= 70) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-gray-600 bg-gray-50 border-gray-200';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'shortlisted': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'accepted': return 'text-green-600 bg-green-50 border-green-200';
      case 'rejected': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const filteredApplicants = filterStatus === 'all' 
    ? applicants 
    : applicants.filter(applicant => applicant.status === filterStatus);

  const sortedApplicants = [...filteredApplicants].sort((a, b) => {
    switch (sortBy) {
      case 'match_score':
        return b.matchScore - a.matchScore;
      case 'rating':
        return b.rating - a.rating;
      case 'time':
        return new Date(a.appliedAt).getTime() - new Date(b.appliedAt).getTime();
      case 'rate':
        return a.rate - b.rate;
      default:
        return 0;
    }
  });

  if (!job) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading job details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Job Applications</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6">
        {/* Job Summary - Following UI/UX spec */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                  Open
                </span>
                {job.isUrgent && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                    <BoltIcon className="w-3 h-3 mr-1" />
                    Urgent
                  </span>
                )}
              </div>
              <h2 className="font-semibold text-green-900 mb-1">{job.title}</h2>
              <p className="text-sm text-green-700">{job.category}</p>
            </div>
            <div className="text-right">
              <p className="font-semibold text-green-900">₹{job.rate}/{job.rateType === 'hourly' ? 'hr' : 'day'}</p>
              <p className="text-xs text-green-700">{job.duration}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-green-700 mb-3">
            <div className="flex items-center space-x-1">
              <MapPinIcon className="w-4 h-4" />
              <span>{job.location}</span>
            </div>
          </div>

          {/* Metrics - Following UI/UX spec: Views, Applications, Time since posting */}
          <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-green-200">
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-1 text-gray-600">
                <EyeIcon className="w-4 h-4" />
                <span>{job.views} views</span>
              </div>
              <div className="flex items-center space-x-1 text-gray-600">
                <UserGroupIcon className="w-4 h-4" />
                <span>{job.applications} applications</span>
              </div>
            </div>
            <span className="text-xs text-gray-500">
              Posted {new Date(job.postedAt).toLocaleDateString('en-IN', { 
                day: 'numeric', 
                month: 'short',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </span>
          </div>
        </div>

        {/* Sorting & Filtering - Following UI/UX spec */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <FunnelIcon className="w-4 h-4 text-gray-400" />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="match_score">Match Score</option>
                <option value="rating">Rating</option>
                <option value="time">Applied Time</option>
                <option value="rate">Rate (Low to High)</option>
              </select>
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="all">All Applications</option>
              <option value="pending">Pending</option>
              <option value="shortlisted">Shortlisted</option>
              <option value="accepted">Accepted</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
          <p className="text-sm text-gray-500">
            {sortedApplicants.length} applicant{sortedApplicants.length !== 1 ? 's' : ''}
          </p>
        </div>

        {/* Applicants List */}
        <div className="space-y-4">
          {sortedApplicants.length === 0 ? (
            <NoApplicantsState job={job} />
          ) : (
            sortedApplicants.map((applicant) => (
              <ApplicantCard 
                key={applicant.id} 
                applicant={applicant} 
                onStatusChange={handleStatusChange}
                onAccept={() => setShowAcceptDialog(applicant.id)}
              />
            ))
          )}
        </div>
      </div>

      {/* Accept Confirmation Dialog */}
      {showAcceptDialog && (
        <AcceptDialog 
          applicant={sortedApplicants.find(a => a.id === showAcceptDialog)!}
          onConfirm={() => handleAccept(showAcceptDialog)}
          onCancel={() => setShowAcceptDialog(null)}
        />
      )}
    </div>
  );
}

// Applicant Card Component - Following UI/UX specs
function ApplicantCard({
  applicant,
  onStatusChange,
  onAccept
}: {
  applicant: any;
  onStatusChange: (id: string, status: string) => void;
  onAccept: () => void;
}) {
  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 80) return 'text-blue-600 bg-blue-50 border-blue-200';
    if (score >= 70) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-gray-600 bg-gray-50 border-gray-200';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'shortlisted': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'accepted': return 'text-green-600 bg-green-50 border-green-200';
      case 'rejected': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <span className="text-3xl">{applicant.avatar}</span>
          <div>
            <h3 className="font-semibold text-gray-900">{applicant.workerName}</h3>
            <p className="text-sm text-gray-600">{applicant.persona}</p>
            <div className="flex items-center space-x-2 mt-1">
              <div className="flex items-center space-x-1">
                <StarSolidIcon className="w-4 h-4 text-yellow-400" />
                <span className="text-sm font-medium text-gray-900">{applicant.rating}</span>
                <span className="text-sm text-gray-600">({applicant.reviewCount})</span>
              </div>
              <span className="text-gray-300">•</span>
              <span className="text-sm text-gray-600">{applicant.distance} km away</span>
            </div>
          </div>
        </div>
        <div className="text-right space-y-1">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getMatchScoreColor(applicant.matchScore)}`}>
            {applicant.matchScore}% match
          </span>
          {applicant.status !== 'pending' && (
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(applicant.status)}`}>
              {applicant.status.charAt(0).toUpperCase() + applicant.status.slice(1)}
            </span>
          )}
        </div>
      </div>

      {/* Rate & Availability */}
      <div className="flex items-center justify-between mb-3 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-1 text-gray-600">
            <CurrencyRupeeIcon className="w-4 h-4" />
            <span className="font-medium">₹{applicant.rate}/{applicant.rateType === 'hourly' ? 'hr' : 'day'}</span>
          </div>
          <div className="flex items-center space-x-1 text-gray-600">
            <ClockIcon className="w-4 h-4" />
            <span>{applicant.availability}</span>
          </div>
        </div>
        <span className="text-xs text-gray-500">
          Applied {new Date(applicant.appliedAt).toLocaleDateString('en-IN', {
            day: 'numeric',
            month: 'short',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </span>
      </div>

      {/* Stats */}
      <div className="flex items-center space-x-4 mb-3 text-sm text-gray-600">
        <span>{applicant.experience} experience</span>
        <span>•</span>
        <span>{applicant.completedJobs} jobs completed</span>
        <span>•</span>
        <span>Responds in {applicant.responseTime}</span>
      </div>

      {/* Intro Message */}
      <div className="mb-4">
        <p className="text-sm text-gray-700 bg-blue-50 border border-blue-200 rounded-lg p-3">
          "{applicant.introMessage}"
        </p>
      </div>

      {/* Actions */}
      <div className="flex space-x-2">
        <Link
          href={`/workers/${applicant.id}/profile`}
          className="flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-center text-sm font-medium hover:bg-gray-200 transition-colors"
        >
          View Profile
        </Link>
        <Link
          href={`/messages/${applicant.id}`}
          className="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg text-center text-sm font-medium hover:bg-blue-700 transition-colors flex items-center justify-center"
        >
          <ChatBubbleLeftIcon className="w-4 h-4 mr-1" />
          Message
        </Link>

        {applicant.status === 'pending' && (
          <>
            <button
              onClick={() => onStatusChange(applicant.id, 'shortlisted')}
              className="bg-yellow-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-colors flex items-center"
            >
              <BookmarkIcon className="w-4 h-4 mr-1" />
              Shortlist
            </button>
            <button
              onClick={onAccept}
              className="bg-green-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors flex items-center"
            >
              <CheckCircleIcon className="w-4 h-4 mr-1" />
              Accept
            </button>
            <button
              onClick={() => onStatusChange(applicant.id, 'rejected')}
              className="bg-red-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors flex items-center"
            >
              <XMarkIcon className="w-4 h-4 mr-1" />
              Reject
            </button>
          </>
        )}

        {applicant.status === 'shortlisted' && (
          <>
            <button
              onClick={onAccept}
              className="bg-green-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors flex items-center"
            >
              <CheckCircleIcon className="w-4 h-4 mr-1" />
              Accept
            </button>
            <button
              onClick={() => onStatusChange(applicant.id, 'rejected')}
              className="bg-red-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors flex items-center"
            >
              <XMarkIcon className="w-4 h-4 mr-1" />
              Reject
            </button>
          </>
        )}
      </div>
    </div>
  );
}

// No Applicants State - Following UI/UX spec: tips to optimize job
function NoApplicantsState({ job }: { job: any }) {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <UserGroupIcon className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">No applications yet</h3>
      <p className="text-gray-600 mb-6">
        Your job hasn't received any applications. Here are some tips to get more responses:
      </p>

      {/* Optimization Tips */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 text-left max-w-md mx-auto">
        <div className="flex items-start space-x-3">
          <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="font-medium text-yellow-900 mb-2">Optimization Tips</h4>
            <ul className="space-y-1 text-sm text-yellow-800">
              <li>• Make your title more specific and clear</li>
              <li>• Consider increasing your budget rate</li>
              <li>• Add more details about the work needed</li>
              <li>• Mark as urgent if time-sensitive</li>
              <li>• Check if your location is easily accessible</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex space-x-3 justify-center">
        <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
          Edit Job
        </button>
        <button className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
          Renew Job
        </button>
      </div>
    </div>
  );
}

// Accept Confirmation Dialog
function AcceptDialog({
  applicant,
  onConfirm,
  onCancel
}: {
  applicant: any;
  onConfirm: () => void;
  onCancel: () => void;
}) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl p-6 max-w-sm w-full">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <CheckCircleIcon className="w-8 h-8 text-green-600" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-900 mb-2">Accept Application?</h3>
            <p className="text-gray-600">
              You're about to accept <span className="font-medium">{applicant.workerName}</span> for this job.
              This will move the job to "In Progress" and notify the worker.
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Worker Rate:</span>
              <span className="font-medium text-gray-900">₹{applicant.rate}/{applicant.rateType === 'hourly' ? 'hr' : 'day'}</span>
            </div>
            <div className="flex items-center justify-between text-sm mt-1">
              <span className="text-gray-600">Availability:</span>
              <span className="font-medium text-gray-900">{applicant.availability}</span>
            </div>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={onCancel}
              className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={onConfirm}
              className="flex-1 py-3 px-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Accept Worker
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
