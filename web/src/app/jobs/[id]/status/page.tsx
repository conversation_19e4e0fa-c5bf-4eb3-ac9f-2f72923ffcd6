'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  ArrowLeftIcon,
  CheckCircleIcon,
  ClockIcon,
  PlayIcon,
  PhotoIcon,
  ChatBubbleLeftIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  EyeIcon,
  MapPinIcon,
  CurrencyRupeeIcon,
  UserIcon
} from '@heroicons/react/24/outline';

// Mock job status data following UI/UX specs
const MOCK_JOB_STATUS = {
  '2': {
    id: '2',
    title: 'House Cleaning - Deep Clean Required',
    category: 'Cleaner',
    rate: 800,
    rateType: 'daily',
    location: 'Bandra, Mumbai',
    duration: '6-8 hours',
    status: 'started', // applied → accepted → started → completed
    worker: {
      id: '2',
      name: '<PERSON><PERSON>',
      avatar: '👩‍🧹',
      phone: '+91 98765 43211',
      rating: 4.8
    },
    timeline: [
      {
        stage: 'applied',
        title: 'Job Posted',
        description: 'Job was posted and received applications',
        completedAt: '2025-08-24T14:30:00Z',
        status: 'completed'
      },
      {
        stage: 'accepted',
        title: 'Worker Accepted',
        description: '<PERSON><PERSON> was selected for this job',
        completedAt: '2025-08-24T16:45:00Z',
        status: 'completed'
      },
      {
        stage: 'started',
        title: 'Work Started',
        description: 'Worker confirmed work has begun',
        completedAt: '2025-08-25T09:00:00Z',
        status: 'completed'
      },
      {
        stage: 'photo_proof',
        title: 'Photo Proof Submitted',
        description: 'Worker submitted completion photos',
        completedAt: null,
        status: 'pending',
        photos: [
          { id: '1', url: '🏠', description: 'Living room cleaned' },
          { id: '2', url: '🛏️', description: 'Bedroom organized' },
          { id: '3', url: '🚿', description: 'Bathroom sanitized' },
          { id: '4', url: '🍽️', description: 'Kitchen deep cleaned' }
        ]
      },
      {
        stage: 'completed',
        title: 'Job Completed',
        description: 'Work approved and payment processed',
        completedAt: null,
        status: 'pending'
      }
    ],
    disputeStatus: null, // null, 'raised', 'resolved'
    estimatedCompletion: '2025-08-25T17:00:00Z'
  }
};

export default function JobStatusPage() {
  const params = useParams();
  const router = useRouter();
  const jobId = params.id as string;
  const [jobStatus, setJobStatus] = useState<any>(null);
  const [showPhotoProof, setShowPhotoProof] = useState(false);
  const [photoApprovalAction, setPhotoApprovalAction] = useState<'approve' | 'reject' | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');

  useEffect(() => {
    // Load job status data
    const statusData = MOCK_JOB_STATUS[jobId as keyof typeof MOCK_JOB_STATUS];
    if (statusData) {
      setJobStatus(statusData);
    }
  }, [jobId]);

  const handlePhotoApproval = (action: 'approve' | 'reject') => {
    if (action === 'reject' && !rejectionReason.trim()) return;
    
    // Update timeline
    const updatedTimeline = [...jobStatus.timeline];
    const photoProofIndex = updatedTimeline.findIndex(t => t.stage === 'photo_proof');
    const completedIndex = updatedTimeline.findIndex(t => t.stage === 'completed');
    
    if (action === 'approve') {
      updatedTimeline[photoProofIndex].status = 'completed';
      updatedTimeline[photoProofIndex].completedAt = new Date().toISOString();
      updatedTimeline[completedIndex].status = 'current';
      
      setJobStatus(prev => ({
        ...prev,
        status: 'photo_approved',
        timeline: updatedTimeline
      }));
      
      // Navigate to payment confirmation
      setTimeout(() => {
        router.push(`/jobs/${jobId}/payment-confirm`);
      }, 1500);
    } else {
      // Handle rejection
      updatedTimeline[photoProofIndex].status = 'rejected';
      updatedTimeline[photoProofIndex].rejectionReason = rejectionReason;
      
      setJobStatus(prev => ({
        ...prev,
        status: 'photo_rejected',
        timeline: updatedTimeline
      }));
    }
    
    setPhotoApprovalAction(null);
    setRejectionReason('');
    setShowPhotoProof(false);
  };

  const getStageStatus = (stage: any) => {
    if (stage.status === 'completed') return 'text-green-600 bg-green-50 border-green-200';
    if (stage.status === 'current') return 'text-blue-600 bg-blue-50 border-blue-200';
    if (stage.status === 'rejected') return 'text-red-600 bg-red-50 border-red-200';
    return 'text-gray-400 bg-gray-50 border-gray-200';
  };

  const getStageIcon = (stage: any) => {
    if (stage.status === 'completed') return <CheckCircleIcon className="w-5 h-5" />;
    if (stage.status === 'current') return <ClockIcon className="w-5 h-5" />;
    if (stage.status === 'rejected') return <XMarkIcon className="w-5 h-5" />;
    return <div className="w-5 h-5 rounded-full border-2 border-current" />;
  };

  if (!jobStatus) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading job status...</p>
        </div>
      </div>
    );
  }

  const photoProofStage = jobStatus.timeline.find((t: any) => t.stage === 'photo_proof');
  const hasPhotoProof = photoProofStage && photoProofStage.photos && photoProofStage.photos.length > 0;

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Job Status</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6">
        {/* Job Summary */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="font-semibold text-blue-900 mb-2">{jobStatus.title}</h2>
          <div className="space-y-1 text-sm text-blue-800">
            <div className="flex items-center justify-between">
              <span>Category:</span>
              <span>{jobStatus.category}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Rate:</span>
              <span className="font-semibold">₹{jobStatus.rate}/{jobStatus.rateType === 'hourly' ? 'hr' : 'day'}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Location:</span>
              <span>{jobStatus.location}</span>
            </div>
          </div>
        </div>

        {/* Worker Info */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-3 mb-3">
            <span className="text-2xl">{jobStatus.worker.avatar}</span>
            <div>
              <h3 className="font-semibold text-green-900">{jobStatus.worker.name}</h3>
              <p className="text-sm text-green-700">⭐ {jobStatus.worker.rating} rating</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Link
              href={`/messages/${jobStatus.worker.id}`}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg text-center text-sm font-medium hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              <ChatBubbleLeftIcon className="w-4 h-4 mr-2" />
              Message Worker
            </Link>
            <button className="px-4 py-2 border border-green-300 text-green-700 rounded-lg text-sm hover:bg-green-100 transition-colors">
              Call
            </button>
          </div>
        </div>

        {/* Progress Timeline - Following UI/UX spec: Applied → Accepted → Started → Completed */}
        <div className="space-y-4">
          <h3 className="font-semibold text-gray-900">Progress Timeline</h3>
          
          <div className="space-y-4">
            {jobStatus.timeline.map((stage: any, index: number) => (
              <div key={stage.stage} className="flex items-start space-x-4">
                {/* Timeline Line */}
                <div className="flex flex-col items-center">
                  <div className={`w-10 h-10 rounded-full border-2 flex items-center justify-center ${getStageStatus(stage)}`}>
                    {getStageIcon(stage)}
                  </div>
                  {index < jobStatus.timeline.length - 1 && (
                    <div className={`w-0.5 h-8 mt-2 ${
                      stage.status === 'completed' ? 'bg-green-300' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
                
                {/* Stage Content */}
                <div className="flex-1 pb-8">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-gray-900">{stage.title}</h4>
                    {stage.completedAt && (
                      <span className="text-xs text-gray-500">
                        {new Date(stage.completedAt).toLocaleDateString('en-IN', {
                          day: 'numeric',
                          month: 'short',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{stage.description}</p>
                  
                  {/* Photo Proof Section */}
                  {stage.stage === 'photo_proof' && hasPhotoProof && (
                    <div className="space-y-3">
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-yellow-900">
                            📸 {stage.photos.length} photos submitted
                          </span>
                          <button
                            onClick={() => setShowPhotoProof(true)}
                            className="text-blue-600 text-sm hover:text-blue-700 flex items-center"
                          >
                            <EyeIcon className="w-4 h-4 mr-1" />
                            View Photos
                          </button>
                        </div>
                        
                        {stage.status === 'pending' && (
                          <div className="flex space-x-2 mt-3">
                            <button
                              onClick={() => setPhotoApprovalAction('approve')}
                              className="flex-1 bg-green-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors flex items-center justify-center"
                            >
                              <CheckCircleIcon className="w-4 h-4 mr-1" />
                              Approve Work
                            </button>
                            <button
                              onClick={() => setPhotoApprovalAction('reject')}
                              className="flex-1 bg-red-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors flex items-center justify-center"
                            >
                              <XMarkIcon className="w-4 h-4 mr-1" />
                              Request Changes
                            </button>
                          </div>
                        )}
                        
                        {stage.status === 'rejected' && stage.rejectionReason && (
                          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                            <p className="text-sm text-red-800">
                              <span className="font-medium">Feedback:</span> {stage.rejectionReason}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Action CTAs based on stage - Following UI/UX spec */}
                  {stage.status === 'current' && stage.stage === 'started' && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <p className="text-sm text-blue-800 mb-2">
                        Work is in progress. Estimated completion: {' '}
                        {new Date(jobStatus.estimatedCompletion).toLocaleDateString('en-IN', {
                          day: 'numeric',
                          month: 'short',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Dispute Section */}
        {jobStatus.disputeStatus && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <ExclamationTriangleIcon className="w-6 h-6 text-red-600 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-red-900">Dispute Raised</h3>
                <p className="text-sm text-red-700 mt-1">
                  There's an ongoing dispute for this job. Our support team will help resolve it.
                </p>
                <button className="mt-2 text-red-600 text-sm hover:text-red-700 font-medium">
                  View Dispute Details
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Photo Proof Modal */}
      {showPhotoProof && photoProofStage && (
        <PhotoProofModal 
          photos={photoProofStage.photos}
          onClose={() => setShowPhotoProof(false)}
        />
      )}

      {/* Photo Approval Action Modal */}
      {photoApprovalAction && (
        <PhotoApprovalModal 
          action={photoApprovalAction}
          rejectionReason={rejectionReason}
          onReasonChange={setRejectionReason}
          onConfirm={() => handlePhotoApproval(photoApprovalAction)}
          onCancel={() => {
            setPhotoApprovalAction(null);
            setRejectionReason('');
          }}
        />
      )}
    </div>
  );
}

// Photo Proof Modal Component
function PhotoProofModal({
  photos,
  onClose
}: {
  photos: any[];
  onClose: () => void;
}) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl max-w-md w-full max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Work Completion Photos</h3>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        <div className="p-4 space-y-4 max-h-96 overflow-y-auto">
          {photos.map((photo: any) => (
            <div key={photo.id} className="bg-gray-50 rounded-lg p-4">
              <div className="text-center mb-3">
                <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center mx-auto text-3xl">
                  {photo.url}
                </div>
              </div>
              <p className="text-sm text-gray-700 text-center">{photo.description}</p>
            </div>
          ))}
        </div>

        <div className="p-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="w-full bg-gray-600 text-white py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}

// Photo Approval Modal Component
function PhotoApprovalModal({
  action,
  rejectionReason,
  onReasonChange,
  onConfirm,
  onCancel
}: {
  action: 'approve' | 'reject';
  rejectionReason: string;
  onReasonChange: (reason: string) => void;
  onConfirm: () => void;
  onCancel: () => void;
}) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl p-6 max-w-sm w-full">
        <div className="text-center space-y-4">
          <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto ${
            action === 'approve' ? 'bg-green-100' : 'bg-red-100'
          }`}>
            {action === 'approve' ? (
              <CheckCircleIcon className="w-8 h-8 text-green-600" />
            ) : (
              <XMarkIcon className="w-8 h-8 text-red-600" />
            )}
          </div>

          <div>
            <h3 className="text-lg font-bold text-gray-900 mb-2">
              {action === 'approve' ? 'Approve Work?' : 'Request Changes?'}
            </h3>
            <p className="text-gray-600">
              {action === 'approve'
                ? 'Confirming will mark the job as completed and proceed to payment.'
                : 'The worker will be notified about required changes and can resubmit photos.'
              }
            </p>
          </div>

          {action === 'reject' && (
            <div className="text-left">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Feedback for worker (required)
              </label>
              <textarea
                value={rejectionReason}
                onChange={(e) => onReasonChange(e.target.value)}
                placeholder="Please specify what needs to be changed or improved..."
                rows={3}
                maxLength={200}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
              />
              <p className="text-xs text-gray-500 mt-1">{rejectionReason.length}/200 characters</p>
            </div>
          )}

          <div className="flex space-x-3">
            <button
              onClick={onCancel}
              className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={onConfirm}
              disabled={action === 'reject' && !rejectionReason.trim()}
              className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${
                action === 'approve'
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : rejectionReason.trim()
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : 'bg-gray-200 text-gray-500 cursor-not-allowed'
              }`}
            >
              {action === 'approve' ? 'Approve & Continue' : 'Send Feedback'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
