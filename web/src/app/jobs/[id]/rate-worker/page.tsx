'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { 
  ArrowLeftIcon,
  StarIcon,
  CheckCircleIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';

// Mock job data for rating
const MOCK_JOB_DATA = {
  '2': {
    id: '2',
    title: 'House Cleaning - Deep Clean Required',
    category: 'Cleaner',
    rate: 800,
    rateType: 'daily',
    location: 'Bandra, Mumbai',
    completedAt: '2025-08-25T16:30:00Z',
    worker: {
      id: '2',
      name: '<PERSON><PERSON>',
      avatar: '👩‍🧹',
      phone: '+91 98765 43211',
      rating: 4.8
    }
  }
};

// Rating labels following UI/UX spec: 1-5 stars with labels
const RATING_LABELS = {
  1: 'Poor',
  2: 'Below Average',
  3: 'Average',
  4: 'Good',
  5: 'Excellent'
};

// Reason options for low ratings (< 2 stars) following UI/UX spec
const LOW_RATING_REASONS = [
  'Work quality was poor',
  'Worker was unprofessional',
  'Did not complete the job properly',
  'Was late or did not show up',
  'Damaged property',
  'Did not follow instructions',
  'Other'
];

export default function RateWorkerPage() {
  const params = useParams();
  const router = useRouter();
  const jobId = params.id as string;
  const [job, setJob] = useState<any>(null);
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [comment, setComment] = useState('');
  const [selectedReasons, setSelectedReasons] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  useEffect(() => {
    // Load job data
    const jobData = MOCK_JOB_DATA[jobId as keyof typeof MOCK_JOB_DATA];
    if (jobData) {
      setJob(jobData);
    }
  }, [jobId]);

  const handleReasonToggle = (reason: string) => {
    setSelectedReasons(prev => 
      prev.includes(reason) 
        ? prev.filter(r => r !== reason)
        : [...prev, reason]
    );
  };

  const handleSubmitRating = async () => {
    if (rating === 0) return;
    if (rating < 3 && selectedReasons.length === 0) return;

    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      // Store rating data
      const ratingData = {
        jobId: job.id,
        workerId: job.worker.id,
        rating,
        comment: comment.trim(),
        reasons: rating < 3 ? selectedReasons : [],
        submittedAt: new Date().toISOString()
      };
      
      localStorage.setItem(`rating_${job.id}`, JSON.stringify(ratingData));
      
      setIsSubmitting(false);
      setShowSuccess(true);
      
      // Auto-close and navigate back
      setTimeout(() => {
        router.push('/jobs/posted');
      }, 2500);
    }, 1500);
  };

  const handleSkip = () => {
    // Show reminder toast and navigate back
    router.push('/jobs/posted');
  };

  if (!job) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading job details...</p>
        </div>
      </div>
    );
  }

  if (showSuccess) {
    return (
      <div className="min-h-screen bg-green-50 flex items-center justify-center p-4">
        <div className="text-center space-y-6 max-w-sm">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <CheckCircleIcon className="w-12 h-12 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Rating Submitted! ⭐</h1>
            <p className="text-gray-600">
              Thank you for rating <span className="font-medium">{job.worker.name}</span>. 
              Your feedback helps other posters make better decisions.
            </p>
          </div>
          <div className="bg-white rounded-lg p-4 border border-green-200">
            <div className="flex items-center justify-center space-x-2 mb-2">
              {[...Array(5)].map((_, i) => (
                <StarSolidIcon
                  key={i}
                  className={`w-6 h-6 ${
                    i < rating ? 'text-yellow-400' : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <p className="text-sm text-gray-600">
              {rating} star{rating !== 1 ? 's' : ''} - {RATING_LABELS[rating as keyof typeof RATING_LABELS]}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Rate Worker</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6 pb-24">
        {/* Job Summary */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="font-semibold text-blue-900 mb-2">{job.title}</h2>
          <div className="space-y-1 text-sm text-blue-800">
            <div className="flex items-center justify-between">
              <span>Category:</span>
              <span>{job.category}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Amount Paid:</span>
              <span className="font-semibold">₹{job.rate}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Completed:</span>
              <span>{new Date(job.completedAt).toLocaleDateString('en-IN', {
                day: 'numeric',
                month: 'short',
                hour: '2-digit',
                minute: '2-digit'
              })}</span>
            </div>
          </div>
        </div>

        {/* Worker Info */}
        <div className="text-center space-y-4">
          <div className="inline-block">
            <div className="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center text-3xl">
              {job.worker.avatar}
            </div>
          </div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900">{job.worker.name}</h3>
            <p className="text-gray-600">How was your experience?</p>
          </div>
        </div>

        {/* Rating Stars - Following UI/UX spec: 1-5 stars with labels */}
        <div className="space-y-4">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoverRating(star)}
                  onMouseLeave={() => setHoverRating(0)}
                  className="p-1 transition-transform hover:scale-110"
                >
                  <StarSolidIcon
                    className={`w-12 h-12 ${
                      star <= (hoverRating || rating)
                        ? 'text-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
            </div>
            
            {(rating > 0 || hoverRating > 0) && (
              <div className="text-center">
                <p className="text-lg font-medium text-gray-900">
                  {RATING_LABELS[(hoverRating || rating) as keyof typeof RATING_LABELS]}
                </p>
                <p className="text-sm text-gray-600">
                  {hoverRating || rating} star{(hoverRating || rating) !== 1 ? 's' : ''}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Reason Selection for Low Ratings - Following UI/UX spec: Reason selection for <3 stars */}
        {rating > 0 && rating < 3 && (
          <div className="space-y-4">
            <h3 className="font-medium text-gray-900">What went wrong? (Select all that apply)</h3>
            <div className="space-y-2">
              {LOW_RATING_REASONS.map((reason) => (
                <button
                  key={reason}
                  onClick={() => handleReasonToggle(reason)}
                  className={`w-full p-3 rounded-lg border-2 text-left transition-colors ${
                    selectedReasons.includes(reason)
                      ? 'border-red-500 bg-red-50 text-red-900'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{reason}</span>
                    {selectedReasons.includes(reason) && (
                      <CheckCircleIcon className="w-5 h-5 text-red-600" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Comment Section - Following UI/UX spec: optional comment (<=200 chars) */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">
            Additional Comments {rating > 0 && '(Optional)'}
          </h3>
          <div>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder={
                rating >= 4 
                  ? "What did you like about their work?"
                  : rating >= 3
                  ? "Any additional feedback?"
                  : "Please provide more details about the issues..."
              }
              rows={4}
              maxLength={200}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
            />
            <p className="text-xs text-gray-500 mt-1">{comment.length}/200 characters</p>
          </div>
        </div>

        {/* Rating Guidelines */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">Rating Guidelines</h4>
          <div className="space-y-1 text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <span className="text-yellow-400">⭐⭐⭐⭐⭐</span>
              <span>Excellent - Exceeded expectations</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-yellow-400">⭐⭐⭐⭐</span>
              <span>Good - Met expectations well</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-yellow-400">⭐⭐⭐</span>
              <span>Average - Met basic expectations</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-yellow-400">⭐⭐</span>
              <span>Below Average - Some issues</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-yellow-400">⭐</span>
              <span>Poor - Major problems</span>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Action Buttons */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 safe-area-bottom">
        <div className="mobile-container space-y-3">
          <button
            onClick={handleSubmitRating}
            disabled={
              rating === 0 || 
              (rating < 3 && selectedReasons.length === 0) ||
              isSubmitting
            }
            className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors ${
              rating > 0 && (rating >= 3 || selectedReasons.length > 0) && !isSubmitting
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Submitting Rating...
              </div>
            ) : (
              'Submit Rating'
            )}
          </button>
          
          <button
            onClick={handleSkip}
            className="w-full py-3 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Skip for Now
          </button>
        </div>
      </div>
    </div>
  );
}
