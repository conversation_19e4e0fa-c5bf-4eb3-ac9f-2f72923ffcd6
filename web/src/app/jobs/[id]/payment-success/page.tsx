'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  CheckCircleIcon,
  StarIcon,
  HomeIcon,
  DocumentTextIcon,
  ChatBubbleLeftIcon
} from '@heroicons/react/24/outline';

export default function PaymentSuccessPage() {
  const params = useParams();
  const router = useRouter();
  const jobId = params.id as string;
  const [paymentData, setPaymentData] = useState<any>(null);

  useEffect(() => {
    // Load payment data from localStorage
    const storedPayment = localStorage.getItem(`payment_${jobId}`);
    if (storedPayment) {
      setPaymentData(JSON.parse(storedPayment));
    }
  }, [jobId]);

  const handleGoHome = () => {
    router.push('/');
  };

  const handleRateWorker = () => {
    router.push(`/jobs/${jobId}/rate-worker`);
  };

  if (!paymentData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading payment confirmation...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-green-50 flex items-center justify-center p-4">
      <div className="text-center space-y-8 max-w-md w-full">
        {/* Success Icon */}
        <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto">
          <CheckCircleIcon className="w-16 h-16 text-green-600" />
        </div>

        {/* Success Message */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-3">Payment Confirmed! 🎉</h1>
          <p className="text-gray-600 text-lg">
            Your payment has been successfully recorded. The job is now complete!
          </p>
        </div>

        {/* Payment Summary */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-green-200">
          <h2 className="font-semibold text-gray-900 mb-4">Payment Summary</h2>
          <div className="space-y-3 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Amount Paid:</span>
              <span className="font-semibold text-gray-900">₹{paymentData.amount}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Payment Method:</span>
              <span className="capitalize">{paymentData.method}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Confirmed At:</span>
              <span>{new Date(paymentData.confirmedAt).toLocaleString('en-IN', {
                day: '2-digit',
                month: 'short',
                hour: '2-digit',
                minute: '2-digit'
              })}</span>
            </div>
            {paymentData.note && (
              <div className="flex items-start justify-between pt-3 border-t border-gray-200">
                <span className="text-gray-600">Note:</span>
                <span className="text-right max-w-xs">{paymentData.note}</span>
              </div>
            )}
          </div>
        </div>

        {/* What's Next */}
        <div className="bg-white rounded-2xl p-6 shadow-sm">
          <h3 className="font-semibold text-gray-900 mb-4">What's Next?</h3>
          <div className="space-y-3 text-sm text-left">
            <div className="flex items-start space-x-3">
              <StarIcon className="w-5 h-5 text-yellow-500 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">Rate the Worker</p>
                <p className="text-gray-600">Help others by sharing your experience</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <DocumentTextIcon className="w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">Payment Receipt</p>
                <p className="text-gray-600">Available in your payment history</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <ChatBubbleLeftIcon className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">Stay Connected</p>
                <p className="text-gray-600">Message the worker anytime for future jobs</p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleRateWorker}
            className="w-full bg-yellow-500 text-white py-4 px-6 rounded-xl font-semibold text-lg hover:bg-yellow-600 transition-colors flex items-center justify-center"
          >
            <StarIcon className="w-6 h-6 mr-2" />
            Rate Worker
          </button>
          
          <div className="flex space-x-3">
            <Link
              href="/jobs/posted"
              className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center"
            >
              My Jobs
            </Link>
            <button
              onClick={handleGoHome}
              className="flex-1 bg-gray-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors flex items-center justify-center"
            >
              <HomeIcon className="w-5 h-5 mr-2" />
              Home
            </button>
          </div>
        </div>

        {/* Footer Note */}
        <div className="text-center">
          <p className="text-sm text-gray-500">
            Thank you for using Ozgaar! 🙏
          </p>
        </div>
      </div>
    </div>
  );
}
