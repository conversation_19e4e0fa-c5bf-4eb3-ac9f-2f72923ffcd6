'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { 
  ArrowLeftIcon, 
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CurrencyRupeeIcon,
  CreditCardIcon,
  BanknotesIcon,
  DevicePhoneMobileIcon,
  BuildingLibraryIcon
} from '@heroicons/react/24/outline';

// Mock job data for payment
const MOCK_JOB_DATA = {
  '3': {
    id: '3',
    title: 'Electrical Wiring - New AC Installation',
    category: 'Electrician',
    originalRate: 1500,
    rateType: 'daily',
    location: 'Powai, Mumbai',
    posterName: 'Amit Singh',
    posterPhone: '+91 98765 43210',
    workDuration: '6 hours',
    completedAt: new Date().toISOString(),
    photosUploaded: 4
  }
};

const PAYMENT_METHODS = [
  {
    id: 'cash',
    name: 'Cash',
    icon: BanknotesIcon,
    description: 'Receive payment in cash',
    processingTime: 'Immediate',
    color: 'text-green-600 bg-green-50 border-green-200'
  },
  {
    id: 'upi',
    name: 'UPI',
    icon: DevicePhoneMobileIcon,
    description: 'Google Pay, PhonePe, Paytm',
    processingTime: 'Instant',
    color: 'text-blue-600 bg-blue-50 border-blue-200'
  },
  {
    id: 'bank',
    name: 'Bank Transfer',
    icon: BuildingLibraryIcon,
    description: 'Direct bank account transfer',
    processingTime: '1-2 business days',
    color: 'text-purple-600 bg-purple-50 border-purple-200'
  }
];

export default function PaymentConfirmationPage() {
  const params = useParams();
  const router = useRouter();
  const jobId = params.id as string;
  const [job, setJob] = useState<any>(null);
  const [selectedMethod, setSelectedMethod] = useState('cash');
  const [finalAmount, setFinalAmount] = useState(0);
  const [amountMismatch, setAmountMismatch] = useState(false);
  const [customAmount, setCustomAmount] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showMismatchDialog, setShowMismatchDialog] = useState(false);

  useEffect(() => {
    // Load job data
    const jobData = MOCK_JOB_DATA[jobId as keyof typeof MOCK_JOB_DATA];
    if (jobData) {
      setJob(jobData);
      setFinalAmount(jobData.originalRate);
    }
  }, [jobId]);

  const handleAmountChange = (amount: string) => {
    const numAmount = parseFloat(amount);
    setCustomAmount(amount);
    
    if (numAmount !== job?.originalRate) {
      setAmountMismatch(true);
      setFinalAmount(numAmount || 0);
    } else {
      setAmountMismatch(false);
      setFinalAmount(numAmount);
    }
  };

  const handleConfirmPayment = async () => {
    if (amountMismatch && !showMismatchDialog) {
      setShowMismatchDialog(true);
      return;
    }

    setIsProcessing(true);
    
    // Simulate payment processing
    setTimeout(() => {
      // Store payment data
      const paymentData = {
        jobId: job.id,
        amount: finalAmount,
        method: selectedMethod,
        completedAt: new Date().toISOString(),
        status: 'completed'
      };
      
      localStorage.setItem(`payment_${job.id}`, JSON.stringify(paymentData));
      
      setIsProcessing(false);
      
      // Navigate to success or earnings page
      router.push(`/jobs/completed/${jobId}`);
    }, 2000);
  };

  const getSelectedMethodInfo = () => {
    return PAYMENT_METHODS.find(method => method.id === selectedMethod);
  };

  if (!job) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading payment details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Payment Confirmation</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6 pb-24">
        {/* Work Completion Summary */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-3 mb-3">
            <CheckCircleIcon className="w-6 h-6 text-green-600" />
            <h2 className="font-semibold text-green-900">Work Completed Successfully!</h2>
          </div>
          <div className="space-y-2 text-sm text-green-800">
            <div className="flex items-center justify-between">
              <span>Job:</span>
              <span className="font-medium">{job.title}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Duration:</span>
              <span>{job.workDuration}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Photos uploaded:</span>
              <span>{job.photosUploaded} photos</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Completed at:</span>
              <span>{new Date(job.completedAt).toLocaleString('en-IN', {
                day: '2-digit',
                month: 'short',
                hour: '2-digit',
                minute: '2-digit'
              })}</span>
            </div>
          </div>
        </div>

        {/* Payment Amount */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Payment Amount</h3>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-gray-600">Original Rate:</span>
              <span className="text-lg font-semibold text-gray-900">
                ₹{job.originalRate}/{job.rateType === 'hourly' ? 'hr' : 'day'}
              </span>
            </div>
            
            <div className="space-y-3">
              <label className="block text-sm font-medium text-gray-700">
                Final Amount (₹)
              </label>
              <input
                type="number"
                value={customAmount || job.originalRate}
                onChange={(e) => handleAmountChange(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg font-semibold"
                placeholder="Enter final amount"
              />
              
              {amountMismatch && (
                <div className="flex items-start space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium">Amount differs from original rate</p>
                    <p>Difference: ₹{Math.abs(finalAmount - job.originalRate)} {finalAmount > job.originalRate ? 'extra' : 'less'}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Payment Method Selection */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Payment Method</h3>
          
          <div className="space-y-3">
            {PAYMENT_METHODS.map((method) => {
              const Icon = method.icon;
              return (
                <button
                  key={method.id}
                  onClick={() => setSelectedMethod(method.id)}
                  className={`w-full p-4 rounded-lg border-2 text-left transition-colors ${
                    selectedMethod === method.id
                      ? method.color
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <Icon className="w-6 h-6" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{method.name}</div>
                      <div className="text-sm text-gray-600">{method.description}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        Processing: {method.processingTime}
                      </div>
                    </div>
                    {selectedMethod === method.id && (
                      <CheckCircleIcon className="w-5 h-5 text-current" />
                    )}
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Payment Summary */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-3">Payment Summary</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between text-blue-800">
              <span>Amount:</span>
              <span className="font-semibold">₹{finalAmount}</span>
            </div>
            <div className="flex items-center justify-between text-blue-800">
              <span>Method:</span>
              <span>{getSelectedMethodInfo()?.name}</span>
            </div>
            <div className="flex items-center justify-between text-blue-800">
              <span>Processing:</span>
              <span>{getSelectedMethodInfo()?.processingTime}</span>
            </div>
          </div>
        </div>

        {/* Poster Contact */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-2">Employer Contact</h3>
          <div className="text-sm text-gray-600">
            <p>{job.posterName}</p>
            <p>{job.posterPhone}</p>
          </div>
        </div>
      </div>

      {/* Bottom Confirm Button */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 safe-area-bottom">
        <div className="mobile-container">
          <button
            onClick={handleConfirmPayment}
            disabled={!finalAmount || isProcessing}
            className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors ${
              finalAmount && !isProcessing
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isProcessing ? (
              <div className="flex items-center justify-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Processing Payment...
              </div>
            ) : (
              `Confirm Payment - ₹${finalAmount}`
            )}
          </button>
        </div>
      </div>

      {/* Amount Mismatch Dialog */}
      {showMismatchDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl p-6 max-w-sm w-full">
            <div className="text-center space-y-4">
              <ExclamationTriangleIcon className="w-12 h-12 text-yellow-600 mx-auto" />
              <h3 className="text-lg font-bold text-gray-900">Amount Mismatch</h3>
              <p className="text-gray-600">
                The final amount (₹{finalAmount}) differs from the original rate (₹{job.originalRate}). 
                This may require approval from the employer.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowMismatchDialog(false)}
                  className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    setShowMismatchDialog(false);
                    handleConfirmPayment();
                  }}
                  className="flex-1 py-3 px-4 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                >
                  Proceed Anyway
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
