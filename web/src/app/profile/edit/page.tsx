'use client';

import React, { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/contexts/ThemeContext';
import { 
  ArrowLeftIcon,
  CameraIcon,
  UserIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

// Mock user data
const MOCK_USER_DATA = {
  worker: {
    name: '<PERSON><PERSON>',
    phone: '+91 98765-43210',
    email: 'raj<PERSON>.<EMAIL>',
    avatar: '👨‍🔧',
    location: 'Powai, Mumbai',
    bio: 'Experienced plumber with 8+ years in residential and commercial work. Specialized in kitchen and bathroom installations.',
    skills: ['Plumber', 'Electrician'],
    experience: '8+ years',
    completedJobs: 156,
    rating: 4.8,
    isVerified: true
  },
  poster: {
    name: '<PERSON><PERSON>',
    phone: '+91 98765-43210',
    email: '<EMAIL>',
    avatar: '👩‍💼',
    location: 'Bandra, Mumbai',
    bio: 'Property manager looking for reliable workers for maintenance and renovation projects.',
    companyName: 'Sharma Properties',
    jobsPosted: 12,
    rating: 4.6,
    isVerified: false
  }
};

export default function ProfileEditPage() {
  const router = useRouter();
  const { mode } = useTheme();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const userData = MOCK_USER_DATA[mode];
  const [formData, setFormData] = useState({
    name: userData.name,
    email: userData.email,
    location: userData.location,
    bio: userData.bio,
    companyName: mode === 'poster' ? userData.companyName : '',
    avatar: userData.avatar
  });
  
  const [isUploading, setIsUploading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePhotoUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setIsUploading(true);
      
      // Simulate upload process
      setTimeout(() => {
        // In real app, this would upload to cloud storage
        const reader = new FileReader();
        reader.onload = (e) => {
          setFormData(prev => ({ ...prev, avatar: e.target?.result as string }));
          setIsUploading(false);
        };
        reader.readAsDataURL(file);
      }, 1500);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    
    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }
    
    if (formData.bio.length > 200) {
      newErrors.bio = 'Bio must be 200 characters or less';
    }
    
    if (mode === 'poster' && !formData.companyName.trim()) {
      newErrors.companyName = 'Company name is required for posters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    
    setIsSaving(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      setShowSuccess(true);
      
      // Auto-hide success message and go back
      setTimeout(() => {
        router.back();
      }, 2000);
    }, 1500);
  };

  if (showSuccess) {
    return (
      <div className="min-h-screen bg-green-50 flex items-center justify-center p-4">
        <div className="text-center space-y-6 max-w-sm">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <CheckCircleIcon className="w-12 h-12 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Profile Updated! ✨</h1>
            <p className="text-gray-600">
              Your profile has been successfully updated. Changes are now live.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Edit Profile</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6 pb-24">
        {/* Profile Photo */}
        <div className="text-center space-y-4">
          <div className="relative inline-block">
            <div className="w-24 h-24 bg-gray-300 rounded-full flex items-center justify-center text-4xl overflow-hidden">
              {typeof formData.avatar === 'string' && formData.avatar.startsWith('data:') ? (
                <img src={formData.avatar} alt="Profile" className="w-full h-full object-cover" />
              ) : (
                <span>{formData.avatar}</span>
              )}
            </div>
            
            <button
              onClick={handlePhotoUpload}
              disabled={isUploading}
              className="absolute -bottom-1 -right-1 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {isUploading ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <CameraIcon className="w-4 h-4" />
              )}
            </button>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
          />
          
          <p className="text-sm text-gray-600">
            Tap the camera icon to update your photo
          </p>
        </div>

        {/* Form Fields */}
        <div className="space-y-4">
          {/* Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                errors.name ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter your full name"
            />
            {errors.name && (
              <p className="text-red-600 text-sm mt-1 flex items-center">
                <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                {errors.name}
              </p>
            )}
          </div>

          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address *
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                errors.email ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter your email address"
            />
            {errors.email && (
              <p className="text-red-600 text-sm mt-1 flex items-center">
                <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                {errors.email}
              </p>
            )}
          </div>

          {/* Phone (Read-only) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <input
              type="text"
              value={userData.phone}
              disabled
              className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
            />
            <p className="text-xs text-gray-500 mt-1">
              Phone number cannot be changed. Contact support if needed.
            </p>
          </div>

          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Location *
            </label>
            <input
              type="text"
              value={formData.location}
              onChange={(e) => handleInputChange('location', e.target.value)}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                errors.location ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter your location"
            />
            {errors.location && (
              <p className="text-red-600 text-sm mt-1 flex items-center">
                <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                {errors.location}
              </p>
            )}
          </div>

          {/* Company Name (Poster only) */}
          {mode === 'poster' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Name *
              </label>
              <input
                type="text"
                value={formData.companyName}
                onChange={(e) => handleInputChange('companyName', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.companyName ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter your company name"
              />
              {errors.companyName && (
                <p className="text-red-600 text-sm mt-1 flex items-center">
                  <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                  {errors.companyName}
                </p>
              )}
            </div>
          )}

          {/* Bio */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bio {mode === 'worker' ? '(Optional)' : '(Optional)'}
            </label>
            <textarea
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              rows={4}
              maxLength={200}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${
                errors.bio ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder={
                mode === 'worker' 
                  ? "Tell others about your experience and specializations..."
                  : "Tell workers about your company and project types..."
              }
            />
            <div className="flex items-center justify-between mt-1">
              {errors.bio ? (
                <p className="text-red-600 text-sm flex items-center">
                  <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                  {errors.bio}
                </p>
              ) : (
                <div />
              )}
              <p className="text-xs text-gray-500">{formData.bio.length}/200</p>
            </div>
          </div>
        </div>

        {/* Profile Stats (Read-only) */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-3">Profile Stats</h3>
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {mode === 'worker' ? userData.completedJobs : userData.jobsPosted}
              </p>
              <p className="text-sm text-gray-600">
                {mode === 'worker' ? 'Jobs Completed' : 'Jobs Posted'}
              </p>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">{userData.rating}</p>
              <p className="text-sm text-gray-600">Rating</p>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Save Button */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 safe-area-bottom">
        <button
          onClick={handleSave}
          disabled={isSaving}
          className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-colors ${
            !isSaving
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-200 text-gray-500 cursor-not-allowed'
          }`}
        >
          {isSaving ? (
            <div className="flex items-center justify-center">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              Saving Changes...
            </div>
          ) : (
            'Save Changes'
          )}
        </button>
      </div>
    </div>
  );
}
