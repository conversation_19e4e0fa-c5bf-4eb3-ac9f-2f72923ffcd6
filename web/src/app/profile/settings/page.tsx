'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/contexts/ThemeContext';
import { 
  ArrowLeftIcon,
  BellIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  DevicePhoneMobileIcon,
  EnvelopeIcon,
  CheckIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';

// Mock settings data
const MOCK_SETTINGS = {
  notifications: {
    jobAlerts: true,
    messages: true,
    payments: true,
    marketing: false,
    pushEnabled: true,
    emailEnabled: true,
    smsEnabled: false
  },
  privacy: {
    profileVisibility: 'public', // public, private, contacts
    showPhone: false,
    showEmail: false,
    showLocation: true
  },
  language: 'english', // english, hindi
  preferences: {
    autoApply: false,
    jobRadius: 10, // km
    workingHours: 'flexible' // flexible, morning, evening
  }
};

export default function SettingsPage() {
  const router = useRouter();
  const { mode } = useTheme();
  const [settings, setSettings] = useState(MOCK_SETTINGS);
  const [showLanguageSelector, setShowLanguageSelector] = useState(false);

  const updateSetting = (category: string, key: string, value: unknown) => {
    setSettings(prev => {
      const categoryData = prev[category as keyof typeof prev];
      return {
        ...prev,
        [category]: {
          ...(typeof categoryData === 'object' && categoryData !== null ? categoryData : {}),
          [key]: value
        }
      };
    });
  };

  const languages = [
    { id: 'english', name: 'English', native: 'English' },
    { id: 'hindi', name: 'Hindi', native: 'हिंदी' }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Settings</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6">
        {/* Notifications */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <BellIcon className="w-5 h-5 mr-2" />
            Notifications
          </h2>
          
          <div className="bg-white border border-gray-200 rounded-lg divide-y divide-gray-200">
            {/* Job Alerts */}
            <div className="p-4 flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">
                  {mode === 'worker' ? 'Job Alerts' : 'Application Alerts'}
                </h3>
                <p className="text-sm text-gray-600">
                  {mode === 'worker' 
                    ? 'Get notified about new jobs matching your skills'
                    : 'Get notified when workers apply to your jobs'
                  }
                </p>
              </div>
              <button
                onClick={() => updateSetting('notifications', 'jobAlerts', !settings.notifications.jobAlerts)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.notifications.jobAlerts ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.notifications.jobAlerts ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            {/* Messages */}
            <div className="p-4 flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">Messages</h3>
                <p className="text-sm text-gray-600">Get notified about new messages</p>
              </div>
              <button
                onClick={() => updateSetting('notifications', 'messages', !settings.notifications.messages)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.notifications.messages ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.notifications.messages ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            {/* Payments */}
            <div className="p-4 flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">Payments</h3>
                <p className="text-sm text-gray-600">Get notified about payment updates</p>
              </div>
              <button
                onClick={() => updateSetting('notifications', 'payments', !settings.notifications.payments)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.notifications.payments ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.notifications.payments ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Notification Methods */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-3">Notification Methods</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <DevicePhoneMobileIcon className="w-4 h-4 text-gray-600" />
                  <span className="text-sm text-gray-700">Push Notifications</span>
                </div>
                <button
                  onClick={() => updateSetting('notifications', 'pushEnabled', !settings.notifications.pushEnabled)}
                  className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                    settings.notifications.pushEnabled ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                      settings.notifications.pushEnabled ? 'translate-x-5' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <EnvelopeIcon className="w-4 h-4 text-gray-600" />
                  <span className="text-sm text-gray-700">Email</span>
                </div>
                <button
                  onClick={() => updateSetting('notifications', 'emailEnabled', !settings.notifications.emailEnabled)}
                  className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                    settings.notifications.emailEnabled ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                      settings.notifications.emailEnabled ? 'translate-x-5' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Language */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <GlobeAltIcon className="w-5 h-5 mr-2" />
            Language
          </h2>
          
          <button
            onClick={() => setShowLanguageSelector(true)}
            className="w-full bg-white border border-gray-200 rounded-lg p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <GlobeAltIcon className="w-4 h-4 text-blue-600" />
              </div>
              <div className="text-left">
                <p className="font-medium text-gray-900">
                  {languages.find(l => l.id === settings.language)?.name}
                </p>
                <p className="text-sm text-gray-600">
                  {languages.find(l => l.id === settings.language)?.native}
                </p>
              </div>
            </div>
            <ChevronRightIcon className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* Privacy */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <ShieldCheckIcon className="w-5 h-5 mr-2" />
            Privacy
          </h2>
          
          <div className="bg-white border border-gray-200 rounded-lg divide-y divide-gray-200">
            <div className="p-4 flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">Show Phone Number</h3>
                <p className="text-sm text-gray-600">
                  {mode === 'worker' ? 'Let posters see your phone number' : 'Let workers see your phone number'}
                </p>
              </div>
              <button
                onClick={() => updateSetting('privacy', 'showPhone', !settings.privacy.showPhone)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.privacy.showPhone ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.privacy.showPhone ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div className="p-4 flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">Show Location</h3>
                <p className="text-sm text-gray-600">Show your general location to others</p>
              </div>
              <button
                onClick={() => updateSetting('privacy', 'showLocation', !settings.privacy.showLocation)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.privacy.showLocation ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.privacy.showLocation ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>

        {/* Worker-specific preferences */}
        {mode === 'worker' && (
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900">Job Preferences</h2>
            
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Job Search Radius: {settings.preferences.jobRadius} km
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="50"
                    value={settings.preferences.jobRadius}
                    onChange={(e) => updateSetting('preferences', 'jobRadius', parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>1 km</span>
                    <span>50 km</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Account Actions */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Account</h2>
          
          <div className="space-y-2">
            <button className="w-full bg-white border border-gray-200 rounded-lg p-4 text-left hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-900">Download My Data</span>
                <ChevronRightIcon className="w-5 h-5 text-gray-400" />
              </div>
              <p className="text-sm text-gray-600 mt-1">Get a copy of your account data</p>
            </button>
            
            <button className="w-full bg-white border border-red-200 rounded-lg p-4 text-left hover:bg-red-50 transition-colors">
              <div className="flex items-center justify-between">
                <span className="font-medium text-red-600">Delete Account</span>
                <ChevronRightIcon className="w-5 h-5 text-red-400" />
              </div>
              <p className="text-sm text-red-500 mt-1">Permanently delete your account and data</p>
            </button>
          </div>
        </div>
      </div>

      {/* Language Selector Modal */}
      {showLanguageSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end">
          <div className="w-full bg-white rounded-t-2xl">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Select Language</h3>
              <button
                onClick={() => setShowLanguageSelector(false)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5 text-gray-600" />
              </button>
            </div>
            
            <div className="p-4 space-y-2">
              {languages.map((language) => (
                <button
                  key={language.id}
                  onClick={() => {
                    updateSetting('language', '', language.id);
                    setShowLanguageSelector(false);
                  }}
                  className="w-full p-4 rounded-lg border border-gray-200 flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <div className="text-left">
                    <p className="font-medium text-gray-900">{language.name}</p>
                    <p className="text-sm text-gray-600">{language.native}</p>
                  </div>
                  {settings.language === language.id && (
                    <CheckIcon className="w-5 h-5 text-blue-600" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
