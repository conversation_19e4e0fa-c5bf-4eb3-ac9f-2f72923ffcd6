'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/contexts/ThemeContext';
import { 
  ArrowLeftIcon,
  QuestionMarkCircleIcon,
  ChatBubbleLeftRightIcon,
  PhoneIcon,
  EnvelopeIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  ChevronRightIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';

// Mock FAQ data
const FAQ_DATA = [
  {
    id: '1',
    question: 'How do I get paid for completed jobs?',
    answer: 'Payments are processed after job completion and poster approval. You can choose from cash, UPI, or bank transfer. Payments typically arrive within 1-2 business days for digital methods.',
    category: 'payments'
  },
  {
    id: '2',
    question: 'What if there\'s a dispute with a job?',
    answer: 'You can report disputes through the job status page or contact our support team. We review all disputes fairly and work to resolve them within 24-48 hours.',
    category: 'disputes'
  },
  {
    id: '3',
    question: 'How do I increase my job visibility?',
    answer: 'Complete your profile, add portfolio photos, maintain good ratings, and respond quickly to job applications. Verified profiles get higher visibility.',
    category: 'jobs'
  },
  {
    id: '4',
    question: 'Can I work in multiple cities?',
    answer: 'Yes! You can update your location and search radius in settings. You can also temporarily change your location when traveling.',
    category: 'location'
  },
  {
    id: '5',
    question: 'How do ratings work?',
    answer: 'Both workers and posters can rate each other after job completion. Ratings are based on work quality, communication, and professionalism. Maintain a 4+ rating for best results.',
    category: 'ratings'
  }
];

const CONTACT_OPTIONS = [
  {
    id: 'chat',
    title: 'Live Chat',
    description: 'Chat with our support team',
    icon: ChatBubbleLeftRightIcon,
    availability: 'Available 9 AM - 9 PM',
    action: () => console.log('Open live chat')
  },
  {
    id: 'phone',
    title: 'Phone Support',
    description: '+91 1800-123-4567',
    icon: PhoneIcon,
    availability: 'Available 9 AM - 6 PM',
    action: () => window.open('tel:+911800123456')
  },
  {
    id: 'email',
    title: 'Email Support',
    description: '<EMAIL>',
    icon: EnvelopeIcon,
    availability: 'Response within 24 hours',
    action: () => window.open('mailto:<EMAIL>')
  }
];

const LEGAL_PAGES = [
  {
    id: 'terms',
    title: 'Terms of Service',
    description: 'Our terms and conditions',
    icon: DocumentTextIcon
  },
  {
    id: 'privacy',
    title: 'Privacy Policy',
    description: 'How we protect your data',
    icon: ShieldCheckIcon
  },
  {
    id: 'safety',
    title: 'Safety Guidelines',
    description: 'Stay safe while working',
    icon: ExclamationTriangleIcon
  }
];

export default function HelpPage() {
  const router = useRouter();
  const { mode } = useTheme();
  const [expandedFaq, setExpandedFaq] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredFaqs = FAQ_DATA.filter(faq =>
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const toggleFaq = (faqId: string) => {
    setExpandedFaq(expandedFaq === faqId ? null : faqId);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Help & Support</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6">
        {/* Contact Support */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Contact Support</h2>
          
          <div className="space-y-3">
            {CONTACT_OPTIONS.map((option) => {
              const Icon = option.icon;
              return (
                <button
                  key={option.id}
                  onClick={option.action}
                  className="w-full bg-white border border-gray-200 rounded-lg p-4 flex items-center space-x-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="flex-1 text-left">
                    <h3 className="font-medium text-gray-900">{option.title}</h3>
                    <p className="text-sm text-gray-600">{option.description}</p>
                    <p className="text-xs text-green-600 mt-1">{option.availability}</p>
                  </div>
                  <ChevronRightIcon className="w-5 h-5 text-gray-400" />
                </button>
              );
            })}
          </div>
        </div>

        {/* FAQ Search */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Frequently Asked Questions</h2>
          
          <div className="relative">
            <QuestionMarkCircleIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search for help..."
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* FAQ List */}
        <div className="space-y-3">
          {filteredFaqs.length === 0 ? (
            <div className="text-center py-8">
              <QuestionMarkCircleIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
              <p className="text-gray-600">
                Try different keywords or contact our support team for help.
              </p>
            </div>
          ) : (
            filteredFaqs.map((faq) => (
              <div key={faq.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleFaq(faq.id)}
                  className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <h3 className="font-medium text-gray-900 pr-4">{faq.question}</h3>
                  <ChevronDownIcon 
                    className={`w-5 h-5 text-gray-400 transition-transform ${
                      expandedFaq === faq.id ? 'rotate-180' : ''
                    }`} 
                  />
                </button>
                
                {expandedFaq === faq.id && (
                  <div className="px-4 pb-4 border-t border-gray-100">
                    <p className="text-gray-700 leading-relaxed pt-3">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))
          )}
        </div>

        {/* Quick Actions */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Quick Actions</h2>
          
          <div className="grid grid-cols-2 gap-3">
            <button className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center hover:bg-blue-100 transition-colors">
              <ChatBubbleLeftRightIcon className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <p className="text-sm font-medium text-blue-900">Report Issue</p>
            </button>
            
            <button className="bg-green-50 border border-green-200 rounded-lg p-4 text-center hover:bg-green-100 transition-colors">
              <QuestionMarkCircleIcon className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <p className="text-sm font-medium text-green-900">Feature Request</p>
            </button>
          </div>
        </div>

        {/* Legal & Policies */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Legal & Policies</h2>
          
          <div className="space-y-2">
            {LEGAL_PAGES.map((page) => {
              const Icon = page.icon;
              return (
                <button
                  key={page.id}
                  className="w-full bg-white border border-gray-200 rounded-lg p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Icon className="w-5 h-5 text-gray-600" />
                    <div className="text-left">
                      <h3 className="font-medium text-gray-900">{page.title}</h3>
                      <p className="text-sm text-gray-600">{page.description}</p>
                    </div>
                  </div>
                  <ChevronRightIcon className="w-5 h-5 text-gray-400" />
                </button>
              );
            })}
          </div>
        </div>

        {/* App Info */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-3">App Information</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Version</span>
              <span className="text-gray-900">1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Build</span>
              <span className="text-gray-900">2025.08.25</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Mode</span>
              <span className="text-gray-900 capitalize">{mode}</span>
            </div>
          </div>
        </div>

        {/* Emergency Contact */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <ExclamationTriangleIcon className="w-6 h-6 text-red-600 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="font-medium text-red-900 mb-1">Emergency Support</h3>
              <p className="text-sm text-red-800 mb-3">
                For urgent safety issues or emergencies while working, contact us immediately.
              </p>
              <button 
                onClick={() => window.open('tel:+911800123456')}
                className="bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
              >
                Call Emergency Line
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
