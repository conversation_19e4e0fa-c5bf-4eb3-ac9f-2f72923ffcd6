'use client';

import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { 
  UserIcon, 
  CogIcon, 
  QuestionMarkCircleIcon,
  ArrowRightOnRectangleIcon 
} from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';

export default function ProfilePage() {
  const router = useRouter();
  const { mode } = useTheme();

  const menuItems = [
    { icon: UserIcon, label: 'Edit Profile', href: '/profile/edit' },
    { icon: CogIcon, label: 'Settings', href: '/profile/settings' },
    { icon: QuestionMarkCircleIcon, label: 'Help & Support', href: '/profile/help' },
    { icon: ArrowRightOnRectangleIcon, label: 'Sign Out', href: '/signout' },
  ];
  const handleMenuItemClick = (href: string) => {
    router.push(href);
  };
  return (
    <div className="px-4 py-6 space-y-6">
      {/* Profile Header */}
      <div className="text-center space-y-4">
        <div className="w-24 h-24 mx-auto bg-gray-300 rounded-full flex items-center justify-center">
          <UserIcon className="w-12 h-12 text-gray-600" />
        </div>
        <div>
          <h2 className="text-xl font-semibold text-gray-900">John Doe</h2>
          <p className="text-gray-600">
            {mode === 'worker' ? 'Plumber • 4.8 ⭐' : 'Verified Poster'}
          </p>
          <p className="text-sm text-gray-500">+91 98765-43210</p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-gray-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-gray-900">
            {mode === 'worker' ? '23' : '8'}
          </div>
          <div className="text-sm text-gray-600">
            {mode === 'worker' ? 'Jobs Completed' : 'Jobs Posted'}
          </div>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-gray-900">4.8</div>
          <div className="text-sm text-gray-600">Rating</div>
        </div>
      </div>

      {/* Menu Items */}
      <div className="space-y-2">
        {menuItems.map((item, index) => (
          <button
            key={index}
            className="w-full flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            onClick={() => handleMenuItemClick(item.href)}
          >
            <div className="flex items-center space-x-3">
              <item.icon className="w-5 h-5 text-gray-600" />
              <span className="text-gray-900">{item.label}</span>
            </div>
            <span className="text-gray-400">›</span>
          </button>
        ))}
      </div>
    </div>
  );
}
