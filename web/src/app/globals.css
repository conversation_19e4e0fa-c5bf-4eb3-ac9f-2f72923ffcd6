@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Worker Theme (Blue) */
  --worker-primary: #2563eb;
  --worker-primary-dark: #1d4ed8;
  --worker-primary-light: #3b82f6;
  --worker-secondary: #e0f2fe;
  --worker-accent: #0ea5e9;

  /* Poster Theme (Green) */
  --poster-primary: #16a34a;
  --poster-primary-dark: #15803d;
  --poster-primary-light: #22c55e;
  --poster-secondary: #f0fdf4;
  --poster-accent: #10b981;

  /* Status Colors */
  --status-urgent: #dc2626;
  --status-applied: #6b7280;
  --status-accepted: #2563eb;
  --status-started: #ea580c;
  --status-completed: #16a34a;

  /* Common Colors */
  --border: #e5e7eb;
  --border-dark: #374151;
  --surface: #f9fafb;
  --surface-dark: #1f2937;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
  --background: #ffffff;
  --foreground: #171717;

  /* Worker Theme (Blue) */
  --worker-primary: #2563eb;
  --worker-primary-dark: #1d4ed8;
  --worker-primary-light: #3b82f6;
  --worker-secondary: #e0f2fe;
  --worker-accent: #0ea5e9;

  /* Poster Theme (Green) */
  --poster-primary: #16a34a;
  --poster-primary-dark: #15803d;
  --poster-primary-light: #22c55e;
  --poster-secondary: #f0fdf4;
  --poster-accent: #10b981;

  /* Status Colors */
  --status-urgent: #dc2626;
  --status-applied: #6b7280;
  --status-accepted: #2563eb;
  --status-started: #ea580c;
  --status-completed: #16a34a;

  /* Common Colors */
  --border: #e5e7eb;
  --border-dark: #374151;
  --surface: #f9fafb;
  --surface-dark: #1f2937;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* Mobile-first responsive design */
.mobile-container {
  max-width: 428px;
  margin: 0 auto;
  background: var(--background);
  position: relative;
}

/* Theme classes */
.theme-worker {
  --primary: var(--worker-primary);
  --primary-dark: var(--worker-primary-dark);
  --primary-light: var(--worker-primary-light);
  --secondary: var(--worker-secondary);
  --accent: var(--worker-accent);
}

.theme-poster {
  --primary: var(--poster-primary);
  --primary-dark: var(--poster-primary-dark);
  --primary-light: var(--poster-primary-light);
  --secondary: var(--poster-secondary);
  --accent: var(--poster-accent);
}

/* Utility classes */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
