'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeftIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  ExclamationTriangleIcon,
  CurrencyRupeeIcon,
  CalendarIcon,
  UserIcon,
  BanknotesIcon,
  DevicePhoneMobileIcon,
  BuildingLibraryIcon
} from '@heroicons/react/24/outline';

// Mock payment history data following UI/UX specs
const MOCK_PAYMENT_HISTORY = [
  {
    id: '1',
    date: '2025-08-25T16:30:00Z',
    workerId: '2',
    workerName: '<PERSON><PERSON>',
    workerAvatar: '👩‍🧹',
    jobTitle: 'House Cleaning - Deep Clean Required',
    amount: 800,
    method: 'upi',
    status: 'completed',
    hasDispute: false,
    jobId: '2'
  },
  {
    id: '2',
    date: '2025-08-23T18:15:00Z',
    workerId: '1',
    workerName: '<PERSON><PERSON>',
    workerAvatar: '👨‍🔧',
    jobTitle: 'AC Installation Work',
    amount: 2000,
    method: 'cash',
    status: 'completed',
    hasDispute: false,
    jobId: '3'
  },
  {
    id: '3',
    date: '2025-08-20T14:45:00Z',
    workerId: '3',
    workerName: 'Amit Singh',
    workerAvatar: '👨‍🔧',
    jobTitle: 'Kitchen Sink Repair Required',
    amount: 420,
    method: 'bank',
    status: 'completed',
    hasDispute: true,
    jobId: '1'
  },
  {
    id: '4',
    date: '2025-08-18T11:30:00Z',
    workerId: '4',
    workerName: 'Priya Sharma',
    workerAvatar: '👩‍🎨',
    jobTitle: 'Painting Work - 2BHK Apartment',
    amount: 3600,
    method: 'upi',
    status: 'completed',
    hasDispute: false,
    jobId: '4'
  },
  {
    id: '5',
    date: '2025-07-28T09:20:00Z',
    workerId: '5',
    workerName: 'Vikram Patel',
    workerAvatar: '👨‍🔧',
    jobTitle: 'Electrical Wiring - New Connection',
    amount: 1500,
    method: 'cash',
    status: 'completed',
    hasDispute: false,
    jobId: '5'
  }
];

// Filter options following UI/UX spec: 7/30/90/All
const FILTER_OPTIONS = [
  { id: 'all', label: 'All Time', days: null },
  { id: '7', label: 'Last 7 Days', days: 7 },
  { id: '30', label: 'Last 30 Days', days: 30 },
  { id: '90', label: 'Last 90 Days', days: 90 }
];

export default function PaymentHistoryPage() {
  const router = useRouter();
  const [payments, setPayments] = useState(MOCK_PAYMENT_HISTORY);
  const [activeFilter, setActiveFilter] = useState('all');
  const [isExporting, setIsExporting] = useState(false);

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash': return BanknotesIcon;
      case 'upi': return DevicePhoneMobileIcon;
      case 'bank': return BuildingLibraryIcon;
      default: return CurrencyRupeeIcon;
    }
  };

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case 'cash': return 'Cash';
      case 'upi': return 'UPI';
      case 'bank': return 'Bank Transfer';
      default: return method;
    }
  };

  const filteredPayments = payments.filter(payment => {
    if (activeFilter === 'all') return true;
    
    const filterDays = FILTER_OPTIONS.find(f => f.id === activeFilter)?.days;
    if (!filterDays) return true;
    
    const paymentDate = new Date(payment.date);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - filterDays);
    
    return paymentDate >= cutoffDate;
  });

  const totalAmount = filteredPayments.reduce((sum, payment) => sum + payment.amount, 0);
  const disputeCount = filteredPayments.filter(p => p.hasDispute).length;

  const handleExportCSV = async () => {
    setIsExporting(true);
    
    // Simulate export process
    setTimeout(() => {
      // Create CSV content
      const headers = ['Date', 'Worker', 'Job Title', 'Amount', 'Method', 'Status', 'Dispute'];
      const csvContent = [
        headers.join(','),
        ...filteredPayments.map(payment => [
          new Date(payment.date).toLocaleDateString('en-IN'),
          payment.workerName,
          `"${payment.jobTitle}"`,
          payment.amount,
          getPaymentMethodLabel(payment.method),
          payment.status,
          payment.hasDispute ? 'Yes' : 'No'
        ].join(','))
      ].join('\n');
      
      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `payment-history-${activeFilter}-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      setIsExporting(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button 
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Payment History</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6 space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <p className="text-2xl font-bold text-green-900">₹{totalAmount.toLocaleString('en-IN')}</p>
            <p className="text-sm text-green-700">Total Paid</p>
          </div>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
            <p className="text-2xl font-bold text-blue-900">{filteredPayments.length}</p>
            <p className="text-sm text-blue-700">Transactions</p>
          </div>
        </div>

        {/* Filters - Following UI/UX spec: 7/30/90/All */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="font-semibold text-gray-900">Filter by Period</h2>
            <button
              onClick={handleExportCSV}
              disabled={isExporting || filteredPayments.length === 0}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                !isExporting && filteredPayments.length > 0
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-200 text-gray-500 cursor-not-allowed'
              }`}
            >
              <ArrowDownTrayIcon className="w-4 h-4" />
              <span>{isExporting ? 'Exporting...' : 'Export CSV'}</span>
            </button>
          </div>
          
          <div className="flex space-x-2">
            {FILTER_OPTIONS.map((filter) => (
              <button
                key={filter.id}
                onClick={() => setActiveFilter(filter.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeFilter === filter.id
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {filter.label}
              </button>
            ))}
          </div>
        </div>

        {/* Dispute Alert */}
        {disputeCount > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="font-medium text-yellow-900">
                  {disputeCount} payment{disputeCount !== 1 ? 's' : ''} with dispute{disputeCount !== 1 ? 's' : ''}
                </h3>
                <p className="text-sm text-yellow-800">
                  Some payments have dispute markers. Contact support if you need assistance.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Payment List - Following UI/UX spec: Date, Worker, Job title, Amount, Method; dispute markers */}
        <div className="space-y-4">
          <h2 className="font-semibold text-gray-900">
            Payment History ({filteredPayments.length} transactions)
          </h2>
          
          {filteredPayments.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CurrencyRupeeIcon className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Payments Found</h3>
              <p className="text-gray-600">
                No payments found for the selected time period.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredPayments.map((payment) => {
                const PaymentIcon = getPaymentMethodIcon(payment.method);
                return (
                  <div key={payment.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{payment.workerAvatar}</span>
                        <div>
                          <h3 className="font-medium text-gray-900">{payment.workerName}</h3>
                          <p className="text-sm text-gray-600">{payment.jobTitle}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900">₹{payment.amount.toLocaleString('en-IN')}</p>
                        <div className="flex items-center space-x-1 text-xs text-gray-500">
                          <PaymentIcon className="w-3 h-3" />
                          <span>{getPaymentMethodLabel(payment.method)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1 text-gray-600">
                          <CalendarIcon className="w-4 h-4" />
                          <span>{new Date(payment.date).toLocaleDateString('en-IN', {
                            day: 'numeric',
                            month: 'short',
                            year: 'numeric'
                          })}</span>
                        </div>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {payment.status}
                        </span>
                      </div>
                      
                      {payment.hasDispute && (
                        <div className="flex items-center space-x-1">
                          <ExclamationTriangleIcon className="w-4 h-4 text-red-500" />
                          <span className="text-xs text-red-600 font-medium">Dispute</span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Summary Footer */}
        {filteredPayments.length > 0 && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">
                Showing {filteredPayments.length} transaction{filteredPayments.length !== 1 ? 's' : ''}
                {activeFilter !== 'all' && ` from ${FILTER_OPTIONS.find(f => f.id === activeFilter)?.label.toLowerCase()}`}
              </span>
              <span className="font-semibold text-gray-900">
                Total: ₹{totalAmount.toLocaleString('en-IN')}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
