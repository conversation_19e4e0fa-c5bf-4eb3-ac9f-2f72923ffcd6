'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  PlusIcon,
  EyeIcon,
  UserGroupIcon,
  ClockIcon,
  CheckCircleIcon,
  CurrencyRupeeIcon,
  BellIcon,
  ChartBarIcon,
  MapPinIcon,
  StarIcon
} from '@heroicons/react/24/outline';

// Mock data for poster dashboard
const MOCK_POSTER_DATA = {
  stats: {
    activeJobs: 3,
    totalApplications: 24,
    completedJobs: 12,
    totalSpent: 45000
  },
  recentJobs: [
    {
      id: '1',
      title: 'Kitchen Sink Repair Required',
      category: 'Plumber',
      status: 'active',
      applications: 8,
      postedAt: '2025-08-25T08:00:00Z',
      budget: 150,
      budgetType: 'hourly',
      location: 'Powai, Mumbai',
      isUrgent: true
    },
    {
      id: '2',
      title: 'House Cleaning - Deep Clean',
      category: 'Cleaner',
      status: 'in_progress',
      applications: 5,
      postedAt: '2025-08-24T14:30:00Z',
      budget: 800,
      budgetType: 'daily',
      location: 'Band<PERSON>, Mumbai',
      workerName: '<PERSON><PERSON>'
    },
    {
      id: '3',
      title: 'AC Installation Work',
      category: 'Electrician',
      status: 'completed',
      applications: 12,
      postedAt: '2025-08-23T10:15:00Z',
      budget: 2000,
      budgetType: 'fixed',
      location: 'Andheri, Mumbai',
      workerName: 'Rajesh Kumar',
      rating: 5
    }
  ],
  notifications: [
    {
      id: '1',
      type: 'application',
      message: 'New application for Kitchen Sink Repair',
      time: '5 min ago',
      unread: true
    },
    {
      id: '2',
      type: 'completion',
      message: 'AC Installation Work marked as completed',
      time: '2 hours ago',
      unread: true
    },
    {
      id: '3',
      type: 'message',
      message: 'Message from Sunita Patel',
      time: '1 day ago',
      unread: false
    }
  ]
};

export default function PosterHomePage() {
  const [posterData, setPosterData] = useState(MOCK_POSTER_DATA);
  const [selectedTab, setSelectedTab] = useState('all');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'in_progress': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'completed': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Open';
      case 'in_progress': return 'In Progress';
      case 'completed': return 'Completed';
      default: return status;
    }
  };

  const filteredJobs = selectedTab === 'all' 
    ? posterData.recentJobs 
    : posterData.recentJobs.filter(job => job.status === selectedTab);

  return (
    <div className="px-4 py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Welcome back! 👋</h1>
          <p className="text-gray-600">Manage your jobs and find great workers</p>
        </div>
        <div className="relative">
          <button className="p-2 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors">
            <BellIcon className="w-6 h-6 text-gray-600" />
          </button>
          {posterData.notifications.some(n => n.unread) && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-4">
        <Link
          href="/post-job"
          className="flex items-center space-x-3 bg-green-600 text-white p-4 rounded-xl hover:bg-green-700 transition-colors"
        >
          <PlusIcon className="w-6 h-6" />
          <div>
            <p className="font-semibold">Post New Job</p>
            <p className="text-sm text-green-100">Find workers quickly</p>
          </div>
        </Link>
        <Link
          href="/jobs/posted"
          className="flex items-center space-x-3 bg-blue-600 text-white p-4 rounded-xl hover:bg-blue-700 transition-colors"
        >
          <UserGroupIcon className="w-6 h-6" />
          <div>
            <p className="font-semibold">View Applications</p>
            <p className="text-sm text-blue-100">Review candidates</p>
          </div>
        </Link>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">Active Jobs</span>
            <ChartBarIcon className="w-5 h-5 text-blue-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">{posterData.stats.activeJobs}</p>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">Applications</span>
            <UserGroupIcon className="w-5 h-5 text-green-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">{posterData.stats.totalApplications}</p>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">Completed</span>
            <CheckCircleIcon className="w-5 h-5 text-purple-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">{posterData.stats.completedJobs}</p>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">Total Spent</span>
            <CurrencyRupeeIcon className="w-5 h-5 text-orange-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">₹{posterData.stats.totalSpent.toLocaleString('en-IN')}</p>
        </div>
      </div>

      {/* Recent Jobs */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Your Jobs</h2>
          <Link href="/jobs/posted" className="text-blue-600 text-sm hover:text-blue-700">
            View All
          </Link>
        </div>

        {/* Job Status Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {[
            { id: 'all', label: 'All' },
            { id: 'active', label: 'Open' },
            { id: 'in_progress', label: 'In Progress' },
            { id: 'completed', label: 'Completed' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSelectedTab(tab.id)}
              className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                selectedTab === tab.id
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Job Cards */}
        <div className="space-y-3">
          {filteredJobs.map((job) => (
            <div key={job.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(job.status)}`}>
                      {getStatusLabel(job.status)}
                    </span>
                    {job.isUrgent && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                        Urgent
                      </span>
                    )}
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">{job.title}</h3>
                  <p className="text-sm text-gray-600">{job.category}</p>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">
                    ₹{job.budget} {job.budgetType === 'fixed' ? '' : `/${job.budgetType === 'hourly' ? 'hr' : 'day'}`}
                  </p>
                  <p className="text-xs text-gray-500">{job.applications} applications</p>
                </div>
              </div>

              <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                <div className="flex items-center space-x-1">
                  <MapPinIcon className="w-4 h-4" />
                  <span>{job.location}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <ClockIcon className="w-4 h-4" />
                  <span>{new Date(job.postedAt).toLocaleDateString('en-IN', { day: 'numeric', month: 'short' })}</span>
                </div>
              </div>

              {job.status === 'in_progress' && job.workerName && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-3">
                  <p className="text-sm text-orange-800">
                    <span className="font-medium">In progress with:</span> {job.workerName}
                  </p>
                </div>
              )}

              {job.status === 'completed' && job.workerName && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-green-800">
                      <span className="font-medium">Completed by:</span> {job.workerName}
                    </p>
                    {job.rating && (
                      <div className="flex items-center space-x-1">
                        <StarIcon className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-green-800">{job.rating}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="flex space-x-3">
                {job.status === 'active' && (
                  <>
                    <Link
                      href={`/jobs/${job.id}/applicants`}
                      className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg text-center text-sm font-medium hover:bg-blue-700 transition-colors"
                    >
                      View Applications ({job.applications})
                    </Link>
                    <button className="px-4 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                      Edit
                    </button>
                  </>
                )}
                {job.status === 'in_progress' && (
                  <>
                    <Link
                      href={`/jobs/${job.id}/status`}
                      className="flex-1 bg-orange-600 text-white py-2 px-4 rounded-lg text-center text-sm font-medium hover:bg-orange-700 transition-colors"
                    >
                      Track Progress
                    </Link>
                    <Link
                      href={`/messages/${job.id}`}
                      className="px-4 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Message
                    </Link>
                  </>
                )}
                {job.status === 'completed' && (
                  <Link
                    href={`/jobs/${job.id}/details`}
                    className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg text-center text-sm font-medium hover:bg-gray-700 transition-colors"
                  >
                    View Details
                  </Link>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Notifications */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
        <div className="space-y-2">
          {posterData.notifications.slice(0, 3).map((notification) => (
            <div key={notification.id} className={`p-3 rounded-lg border ${
              notification.unread ? 'bg-blue-50 border-blue-200' : 'bg-white border-gray-200'
            }`}>
              <div className="flex items-center justify-between">
                <p className={`text-sm ${notification.unread ? 'text-blue-900 font-medium' : 'text-gray-700'}`}>
                  {notification.message}
                </p>
                <span className="text-xs text-gray-500">{notification.time}</span>
              </div>
            </div>
          ))}
        </div>
        <Link href="/notifications" className="block text-center text-blue-600 text-sm hover:text-blue-700">
          View All Notifications
        </Link>
      </div>
    </div>
  );
}
