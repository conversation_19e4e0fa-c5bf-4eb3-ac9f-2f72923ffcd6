import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/ThemeContext";
import LayoutContent from "@/components/LayoutContent";
import { AccessibilityProvider, SkipLink } from "@/components/shared/AccessibilityProvider";
import { ThemeConsistencyChecker, FinalPolish } from "@/components/shared/ThemeConsistency";
import { BundleSizeMonitor, PerformanceMetrics, ServiceWorkerRegistration, CriticalCSS } from "@/components/shared/PerformanceOptimizer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Ozgaar - Find work. Hire fast. Nearby.",
  description: "Worker-Poster marketplace for India. Find jobs or hire trusted workers nearby.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SkipLink />
        <AccessibilityProvider>
          <ThemeProvider>
            <LayoutContent>
              {children}
            </LayoutContent>
            <FinalPolish />
            <ThemeConsistencyChecker />
            {/* <BundleSizeMonitor /> */}
            {/* <PerformanceMetrics /> */}
          </ThemeProvider>
        </AccessibilityProvider>
        <ServiceWorkerRegistration />
        <CriticalCSS />
      </body>
    </html>
  );
}
