'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

export type UserMode = 'worker' | 'poster';

interface ThemeContextType {
  mode: UserMode;
  setMode: (mode: UserMode) => void;
  toggleMode: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [mode, setMode] = useState<UserMode>('worker');

  // Apply theme class to document body
  useEffect(() => {
    document.body.className = document.body.className
      .replace(/theme-\w+/g, '')
      .concat(` theme-${mode}`)
      .trim();
  }, [mode]);

  const toggleMode = () => {
    setMode(mode === 'worker' ? 'poster' : 'worker');
  };

  return (
    <ThemeContext.Provider value={{ mode, setMode, toggleMode }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
