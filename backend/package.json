{"name": "<PERSON><PERSON><PERSON>-backend", "version": "1.0.0", "description": "Node.js backend server for Ozgaar job platform - intermediary layer between mobile app and Supabase", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "test": "jest", "test:watch": "jest --watch", "type-check": "tsc --noEmit"}, "keywords": ["nodejs", "express", "typescript", "supabase", "job-platform", "api", "backend"], "author": "Ozgaar Team", "license": "MIT", "dependencies": {"@ozgaar/types": "workspace:*", "@supabase/supabase-js": "^2.39.0", "@types/multer": "^2.0.0", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.2", "sharp": "^0.34.3", "uuid": "^11.1.0", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.0", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.55.0", "jest": "^29.7.0", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}