# Server Configuration
NODE_ENV=development
PORT=3000
HOST=localhost

# Supabase Configuration (Server-side only)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
SUPABASE_ANON_KEY=your-anon-key-here

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 2Factor.in SMS Configuration (Primary Provider)
TWOFACTOR_API_KEY=your-2factor-api-key-here

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
OTP_RATE_LIMIT_ATTEMPTS=3
OTP_RATE_LIMIT_WINDOW_HOURS=1

# Regional Configuration
DEFAULT_COUNTRY_CODE=+91
SUPPORTED_LANGUAGES=hindi,english,tamil,telugu,bengali,marathi,gujarati,kannada

# Security Configuration
BCRYPT_ROUNDS=12
CORS_ORIGIN=*
TRUST_PROXY=false

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined

# Database Configuration
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_TIMEOUT=30000
