import multer from 'multer'
import sharp from 'sharp'
import { Request, Response, NextFunction } from 'express'
import logger from '../utils/logger'

// Configure multer for memory storage
const storage = multer.memoryStorage()

// File filter for images only
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Accept only image files
  if (file.mimetype.startsWith('image/')) {
    cb(null, true)
  } else {
    cb(new Error('Only image files are allowed'))
  }
}

// Multer configuration
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 1 // Only one file at a time
  }
})

// Image processing middleware
export const processProfileImage = async (req: any, res: Response, next: NextFunction) => {
  try {
    if (!req.file) {
      return next()
    }

    const { buffer, mimetype } = req.file

    // Validate image format
    const allowedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedFormats.includes(mimetype)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid image format. Only JPEG, PNG, and WebP are allowed.',
        code: 'INVALID_IMAGE_FORMAT'
      })
    }

    // Process image with Sharp
    const processedImageBuffer = await sharp(buffer)
      .resize(400, 400, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({
        quality: 85,
        progressive: true
      })
      .toBuffer()

    // Get image metadata
    const metadata = await sharp(processedImageBuffer).metadata()

    // Attach processed image to request
    req.processedImage = {
      buffer: processedImageBuffer,
      mimetype: 'image/jpeg',
      size: processedImageBuffer.length,
      width: metadata.width,
      height: metadata.height
    }

    logger.info(`Image processed: ${metadata.width}x${metadata.height}, size: ${processedImageBuffer.length} bytes`)
    next()

  } catch (error) {
    logger.error('Image processing error:', error)
    res.status(400).json({
      success: false,
      error: 'Failed to process image. Please try with a different image.',
      code: 'IMAGE_PROCESSING_FAILED'
    })
  }
}

// Error handler for multer
export const handleUploadError = (error: any, req: Request, res: Response, next: NextFunction) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File too large. Maximum size is 10MB.',
        code: 'FILE_TOO_LARGE'
      })
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: 'Too many files. Only one file is allowed.',
        code: 'TOO_MANY_FILES'
      })
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        error: 'Unexpected field name. Use "profileImage" as the field name.',
        code: 'UNEXPECTED_FIELD'
      })
    }
  }

  if (error.message === 'Only image files are allowed') {
    return res.status(400).json({
      success: false,
      error: 'Only image files are allowed.',
      code: 'INVALID_FILE_TYPE'
    })
  }

  logger.error('Upload error:', error)
  return res.status(500).json({
    success: false,
    error: 'File upload failed.',
    code: 'UPLOAD_FAILED'
  })
}

// Type declaration for processed image
declare global {
  namespace Express {
    interface Request {
      processedImage?: {
        buffer: Buffer
        mimetype: string
        size: number
        width?: number
        height?: number
      }
    }
  }
}
