import jwtService, { JwtPayload } from '../utils/jwt'
import database from '../config/database'
import logger from '../utils/logger'

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string
        phone: string
        userType: 'worker' | 'poster'
        user_type: 'worker' | 'poster'
        is_worker: boolean
        is_poster: boolean
      }
    }
  }
}

// Extract token from Authorization header
const extractToken = (req: any): string | null => {
  const authHeader = req.headers.authorization
  
  if (!authHeader) {
    return null
  }

  // Check for Bearer token format
  if (authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }

  return null
}

// Authentication middleware
export const authenticate = async (req: any, res: any, next: any): Promise<void> => {
  try {
    const token = extractToken(req)

    if (!token) {
      res.status(401).json({
        success: false,
        error: 'Access token required',
        code: 'TOKEN_MISSING'
      })
      return
    }

    // Verify the token
    let decoded: JwtPayload
    try {
      decoded = jwtService.verifyAccessToken(token)
    } catch (error: any) {
      logger.warn('Token verification failed:', error.message)
      
      if (error.message === 'Access token expired') {
        res.status(401).json({
          success: false,
          error: 'Access token expired',
          code: 'TOKEN_EXPIRED'
        })
      } else {
        res.status(401).json({
          success: false,
          error: 'Invalid access token',
          code: 'TOKEN_INVALID'
        })
      }
      return
    }

    // Verify user still exists in database
    const { data: user, error: userError } = await database.getUserById(decoded.userId)

    if (userError || !user) {
      logger.warn(`User not found for token: ${decoded.userId}`)
      res.status(401).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      })
      return
    }

    // Attach user info to request
    req.user = {
      id: user.id,
      phone: user.phone,
      userType: user.user_type,
      user_type: user.user_type,
      is_worker: user.is_worker,
      is_poster: user.is_poster
    }

    next()
  } catch (error) {
    logger.error('Authentication middleware error:', error)
    res.status(500).json({
      success: false,
      error: 'Authentication failed',
      code: 'AUTH_ERROR'
    })
  }
}

// Optional authentication middleware (doesn't fail if no token)
export const optionalAuth = async (req: any, res: any, next: any): Promise<void> => {
  try {
    const token = extractToken(req)

    if (!token) {
      // No token provided, continue without authentication
      next()
      return
    }

    // Try to verify the token
    try {
      const decoded = jwtService.verifyAccessToken(token)
      
      // Verify user still exists
      const { data: user, error: userError } = await database.getUserById(decoded.userId)

      if (!userError && user) {
        req.user = {
          id: user.id,
          phone: user.phone,
          userType: user.user_type,
          user_type: user.user_type,
          is_worker: user.is_worker,
          is_poster: user.is_poster
        }
      }
    } catch (error) {
      // Token verification failed, but we continue without authentication
      logger.debug('Optional auth token verification failed:', error)
    }

    next()
  } catch (error) {
    logger.error('Optional authentication middleware error:', error)
    next() // Continue even if there's an error
  }
}

// Role-based authorization middleware
export const authorize = (allowedRoles: Array<'worker' | 'poster'>) => {
  return (req: any, res: any, next: any): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      })
      return
    }

    if (!allowedRoles.includes(req.user.userType)) {
      res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS'
      })
      return
    }

    next()
  }
}

// Middleware to check if user owns the resource
export const checkResourceOwnership = (userIdParam: string = 'userId') => {
  return (req: any, res: any, next: any): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      })
      return
    }

    const resourceUserId = req.params[userIdParam]
    
    if (req.user.id !== resourceUserId) {
      res.status(403).json({
        success: false,
        error: 'Access denied - resource ownership required',
        code: 'RESOURCE_ACCESS_DENIED'
      })
      return
    }

    next()
  }
}
