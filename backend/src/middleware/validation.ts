import { Request, Response, NextFunction } from 'express'
import <PERSON><PERSON> from 'joi'
import logger from '../utils/logger'

// Generic validation middleware factory
export const validate = (schema: Joi.ObjectSchema, property: 'body' | 'query' | 'params' = 'body') => {
  return (req: any, res: any, next: NextFunction): void => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true,
      allowUnknown: false
    })

    if (error) {
      const errorMessage = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }))

      logger.warn('Validation error:', { 
        endpoint: req.path, 
        method: req.method, 
        errors: errorMessage 
      })

      res.status(400).json({
        success: false,
        error: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: errorMessage
      })
      return
    }

    // Replace the original property with the validated and sanitized value
    req[property] = value
    next()
  }
}

// Middleware to validate request body
export const validateBody = (schema: Joi.ObjectSchema) => validate(schema, 'body')

// Middleware to validate query parameters
export const validateQuery = (schema: Joi.ObjectSchema) => validate(schema, 'query')

// Middleware to validate route parameters
export const validateParams = (schema: Joi.ObjectSchema) => validate(schema, 'params')

// Common validation schemas
export const commonSchemas = {
  // UUID parameter validation
  uuidParam: Joi.object({
    id: Joi.string().uuid().required().messages({
      'string.uuid': 'Invalid ID format',
      'any.required': 'ID is required'
    })
  }),

  // Pagination query validation
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
    limit: Joi.number().integer().min(1).max(100).default(20).messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
    sort: Joi.string().valid('asc', 'desc').default('desc').messages({
      'any.only': 'Sort must be either "asc" or "desc"'
    }),
    sortBy: Joi.string().default('created_at').messages({
      'string.base': 'Sort by must be a string'
    })
  }),

  // Search query validation
  search: Joi.object({
    q: Joi.string().min(1).max(100).messages({
      'string.min': 'Search query must be at least 1 character',
      'string.max': 'Search query cannot exceed 100 characters'
    }),
    category: Joi.string().valid(
      'electrical', 'plumbing', 'carpentry', 'cooking', 'cleaning',
      'driving', 'delivery', 'security', 'gardening', 'tutoring'
    ).messages({
      'any.only': 'Invalid category'
    }),
    location: Joi.object({
      lat: Joi.number().min(-90).max(90).required().messages({
        'number.base': 'Latitude must be a number',
        'number.min': 'Latitude must be between -90 and 90',
        'number.max': 'Latitude must be between -90 and 90',
        'any.required': 'Latitude is required'
      }),
      lng: Joi.number().min(-180).max(180).required().messages({
        'number.base': 'Longitude must be a number',
        'number.min': 'Longitude must be between -180 and 180',
        'number.max': 'Longitude must be between -180 and 180',
        'any.required': 'Longitude is required'
      }),
      radius: Joi.number().min(1).max(100).default(10).messages({
        'number.base': 'Radius must be a number',
        'number.min': 'Radius must be at least 1 km',
        'number.max': 'Radius cannot exceed 100 km'
      })
    })
  })
}

// Error handling middleware for validation errors
export const handleValidationError = (error: any, req: any, res: any, next: NextFunction): void => {
  if (error.isJoi) {
    const errorMessage = error.details.map((detail: any) => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }))

    logger.warn('Joi validation error:', { 
      endpoint: req.path, 
      method: req.method, 
      errors: errorMessage 
    })

    res.status(400).json({
      success: false,
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errorMessage
    })
    return
  }

  next(error)
}

// Sanitization middleware
export const sanitizeInput = (req: any, res: any, next: NextFunction): void => {
  // Remove any potential XSS attempts from string inputs
  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      // Basic XSS prevention - remove script tags and javascript: protocols
      return obj
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .trim()
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject)
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized: any = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          sanitized[key] = sanitizeObject(obj[key])
        }
      }
      return sanitized
    }
    
    return obj
  }

  if (req.body) {
    req.body = sanitizeObject(req.body)
  }
  
  if (req.query) {
    req.query = sanitizeObject(req.query)
  }
  
  if (req.params) {
    req.params = sanitizeObject(req.params)
  }

  next()
}
