import rateLimit from 'express-rate-limit'
import { Request, Response } from 'express'
import config from '../config'
import logger from '../utils/logger'

// General API rate limiter
export const generalRateLimit = rateLimit({
  windowMs: config.rateLimit.windowMs, // 15 minutes
  max: config.rateLimit.maxRequests, // Limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: 'Too many requests from this IP, please try again later',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  handler: (req: any, res: any) => {
    logger.warn(`Rate limit exceeded for IP: ${req.ip}`)
    res.status(429).json({
      success: false,
      error: 'Too many requests from this IP, please try again later',
      code: 'RATE_LIMIT_EXCEEDED'
    })
  }
})

// Strict rate limiter for authentication endpoints
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 auth requests per windowMs
  message: {
    success: false,
    error: 'Too many authentication attempts, please try again later',
    code: 'AUTH_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: any, res: any) => {
    logger.warn(`Auth rate limit exceeded for IP: ${req.ip}`)
    res.status(429).json({
      success: false,
      error: 'Too many authentication attempts, please try again later',
      code: 'AUTH_RATE_LIMIT_EXCEEDED'
    })
  }
})

// Very strict rate limiter for OTP endpoints
export const otpRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Limit each IP to 5 OTP requests per hour
  message: {
    success: false,
    error: 'Too many OTP requests, please try again later',
    code: 'OTP_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: any) => {
    // Use phone number if available, otherwise fall back to IP
    const phone = req.body?.phone || req.ip
    return `otp_${phone}`
  },
  handler: (req: any, res: any) => {
    const phone = req.body?.phone || 'unknown'
    logger.warn(`OTP rate limit exceeded for phone/IP: ${phone}/${req.ip}`)
    res.status(429).json({
      success: false,
      error: 'Too many OTP requests, please try again later',
      code: 'OTP_RATE_LIMIT_EXCEEDED'
    })
  }
})

// Rate limiter for password reset attempts
export const passwordResetRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 password reset requests per hour
  message: {
    success: false,
    error: 'Too many password reset attempts, please try again later',
    code: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: any, res: any) => {
    logger.warn(`Password reset rate limit exceeded for IP: ${req.ip}`)
    res.status(429).json({
      success: false,
      error: 'Too many password reset attempts, please try again later',
      code: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED'
    })
  }
})

// Rate limiter for user registration
export const registrationRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 registration attempts per hour
  message: {
    success: false,
    error: 'Too many registration attempts, please try again later',
    code: 'REGISTRATION_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: any, res: any) => {
    logger.warn(`Registration rate limit exceeded for IP: ${req.ip}`)
    res.status(429).json({
      success: false,
      error: 'Too many registration attempts, please try again later',
      code: 'REGISTRATION_RATE_LIMIT_EXCEEDED'
    })
  }
})

// Custom rate limiter for specific phone numbers (used in OTP service)
export const createPhoneRateLimit = (maxAttempts: number = 3, windowMs: number = 60 * 60 * 1000) => {
  const phoneAttempts = new Map<string, { count: number; resetTime: number }>()

  return (req: any, res: any, next: Function) => {
    const phone = req.body?.phone
    
    if (!phone) {
      next()
      return
    }

    const now = Date.now()
    const phoneData = phoneAttempts.get(phone)

    // Clean up expired entries
    if (phoneData && now > phoneData.resetTime) {
      phoneAttempts.delete(phone)
    }

    const currentData = phoneAttempts.get(phone) || { count: 0, resetTime: now + windowMs }

    if (currentData.count >= maxAttempts) {
      logger.warn(`Phone rate limit exceeded for: ${phone}`)
      res.status(429).json({
        success: false,
        error: 'Too many attempts for this phone number, please try again later',
        code: 'PHONE_RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil((currentData.resetTime - now) / 1000)
      })
      return
    }

    // Increment counter
    currentData.count++
    phoneAttempts.set(phone, currentData)

    next()
  }
}
