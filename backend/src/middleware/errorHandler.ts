import { Request, Response, NextFunction } from 'express'
import logger from '../utils/logger'

// Custom error class
export class AppError extends Error {
  public statusCode: number
  public code: string
  public isOperational: boolean

  constructor(message: string, statusCode: number = 500, code: string = 'INTERNAL_ERROR') {
    super(message)
    this.statusCode = statusCode
    this.code = code
    this.isOperational = true

    Error.captureStackTrace(this, this.constructor)
  }
}

// Global error handler middleware
export const errorHandler = (
  error: Error | AppError,
  req: any,
  res: any,
  next: NextFunction
): void => {
  let statusCode = 500
  let code = 'INTERNAL_ERROR'
  let message = 'Internal server error'

  // Handle custom AppError
  if (error instanceof AppError) {
    statusCode = error.statusCode
    code = error.code
    message = error.message
  }
  // Handle JWT errors
  else if (error.name === 'JsonWebTokenError') {
    statusCode = 401
    code = 'INVALID_TOKEN'
    message = 'Invalid token'
  }
  else if (error.name === 'TokenExpiredError') {
    statusCode = 401
    code = 'TOKEN_EXPIRED'
    message = 'Token expired'
  }
  // Handle validation errors
  else if (error.name === 'ValidationError') {
    statusCode = 400
    code = 'VALIDATION_ERROR'
    message = error.message
  }
  // Handle database errors
  else if (error.message?.includes('duplicate key')) {
    statusCode = 409
    code = 'DUPLICATE_RESOURCE'
    message = 'Resource already exists'
  }
  else if (error.message?.includes('foreign key')) {
    statusCode = 400
    code = 'INVALID_REFERENCE'
    message = 'Invalid resource reference'
  }
  // Handle other known errors
  else if (error.name === 'CastError') {
    statusCode = 400
    code = 'INVALID_ID'
    message = 'Invalid resource ID'
  }

  // Log error details
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    statusCode,
    code,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  })

  // Send error response
  res.status(statusCode).json({
    success: false,
    error: message,
    code,
    ...(process.env["NODE_ENV"] === 'development' && {
      stack: error.stack,
      details: error.message
    })
  })
}

// 404 handler
export const notFoundHandler = (req: any, res: any): void => {
  res.status(404).json({
    success: false,
    error: 'Resource not found',
    code: 'NOT_FOUND'
  })
}

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// Unhandled promise rejection handler
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logger.error('Unhandled Promise Rejection:', reason)
  // Close server gracefully
  process.exit(1)
})

// Uncaught exception handler
process.on('uncaughtException', (error: Error) => {
  logger.error('Uncaught Exception:', error)
  // Close server gracefully
  process.exit(1)
})
