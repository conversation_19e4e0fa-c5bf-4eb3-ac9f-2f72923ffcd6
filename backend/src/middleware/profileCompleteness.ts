import { Request, Response, NextFunction } from 'express'
import { profileValidationService } from '../services/profileValidationService'
import logger from '../utils/logger'

interface AuthenticatedRequest extends Request {
  user?: any
}

/**
 * Middleware to check if user has minimum profile completeness for specific actions
 */
export const requireProfileCompleteness = (
  minLevel: 'basic' | 'good' | 'complete' = 'basic'
) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      // Validate current profile
      const validation = profileValidationService.validateProfile(req.user)
      
      // Check if user meets minimum completeness level
      const meetsRequirement = checkCompletenessLevel(validation.completenessLevel, minLevel)
      
      if (!meetsRequirement) {
        const requirements = profileValidationService.getCompletenessRequirements()
        const requiredLevel = requirements[minLevel]

        res.status(403).json({
          success: false,
          error: 'Profile completeness requirement not met',
          code: 'PROFILE_INCOMPLETE',
          data: {
            currentLevel: validation.completenessLevel,
            currentScore: validation.completenessScore,
            requiredLevel: minLevel,
            requiredScore: requiredLevel?.minScore || 60,
            missingFields: validation.missingFields,
            suggestions: validation.suggestions,
            platformAccess: validation.platformAccess
          }
        })
        return
      }

      // Add profile validation to request for use in controllers
      req.profileValidation = validation
      next()

    } catch (error) {
      logger.error('Profile completeness check error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  }
}

/**
 * Middleware to check if user can apply to jobs
 */
export const requireJobApplicationAccess = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      })
      return
    }

    const validation = profileValidationService.validateProfile(req.user)
    
    if (!validation.platformAccess.canApplyToJobs) {
      res.status(403).json({
        success: false,
        error: 'Profile not complete enough to apply for jobs',
        code: 'INSUFFICIENT_PROFILE_COMPLETENESS',
        data: {
          currentLevel: validation.completenessLevel,
          currentScore: validation.completenessScore,
          missingFields: validation.missingFields,
          suggestions: validation.suggestions,
          requirements: 'Complete at least 60% of your profile to apply for jobs'
        }
      })
      return
    }

    req.profileValidation = validation
    next()

  } catch (error) {
    logger.error('Job application access check error:', error)
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    })
  }
}

/**
 * Middleware to check if user can receive job invites
 */
export const requireJobInviteAccess = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      })
      return
    }

    const validation = profileValidationService.validateProfile(req.user)
    
    if (!validation.platformAccess.canReceiveJobInvites) {
      res.status(403).json({
        success: false,
        error: 'Profile not complete enough to receive job invites',
        code: 'INSUFFICIENT_PROFILE_COMPLETENESS',
        data: {
          currentLevel: validation.completenessLevel,
          currentScore: validation.completenessScore,
          missingFields: validation.missingFields,
          suggestions: validation.suggestions,
          requirements: 'Complete at least 80% of your profile to receive job invites'
        }
      })
      return
    }

    req.profileValidation = validation
    next()

  } catch (error) {
    logger.error('Job invite access check error:', error)
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    })
  }
}

/**
 * Helper function to check if current level meets minimum requirement
 */
function checkCompletenessLevel(
  currentLevel: 'draft' | 'basic' | 'good' | 'complete',
  minLevel: 'basic' | 'good' | 'complete'
): boolean {
  const levelHierarchy = {
    draft: 0,
    basic: 1,
    good: 2,
    complete: 3
  }

  return levelHierarchy[currentLevel] >= levelHierarchy[minLevel]
}

/**
 * Middleware to add profile completeness info to all authenticated requests
 */
export const addProfileCompletenessInfo = async (
  req: AuthenticatedRequest,
  _res: Response,
  next: NextFunction
) => {
  try {
    if (req.user) {
      const validation = profileValidationService.validateProfile(req.user)
      req.profileValidation = validation
    }
    next()
  } catch (error) {
    logger.error('Add profile completeness info error:', error)
    // Don't block the request, just log the error
    next()
  }
}

// Extend Request interface to include profile validation
declare global {
  namespace Express {
    interface Request {
      profileValidation?: any
    }
  }
}
