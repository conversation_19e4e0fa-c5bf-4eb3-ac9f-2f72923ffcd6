import database from '../config/database'
import logger from '../utils/logger'
import jobVisibilityService from '../services/jobVisibilityService'

// Job interface based on database schema
export interface Job {
  id: string
  poster_id: string
  title: string
  description: string
  skill_category: string
  location: {
    type: 'Point'
    coordinates: [number, number] // [longitude, latitude]
  }
  address: string
  landmark?: string
  job_type: 'one_time' | 'recurring' | 'permanent'
  urgency: 'low' | 'normal' | 'high' | 'urgent'
  budget_min: number
  budget_max: number
  estimated_duration_hours?: number
  requirements?: string
  preferred_gender: 'any' | 'male' | 'female'
  min_experience_years: number
  min_rating: number
  status: 'active' | 'paused' | 'filled' | 'cancelled' | 'expired'
  applications_count: number
  views_count: number
  expires_at: string
  created_at: string
  updated_at: string
  // New fields from enhanced job schema
  search_keywords?: string[] // JSON array of keywords for search indexing
  visibility_score?: number // Score used for ranking jobs in search results (higher = more visible)
  indexed_at?: string // Timestamp when job was indexed for search
  is_visible?: boolean // Whether job is visible to workers in search results
  filled_at?: string // Timestamp when job was marked as filled
}

export const jobController = {
  // =====================================================
  // CREATE JOB
  // =====================================================
  createJob: async (req: any, res: any): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      const {
        title,
        description,
        skill_category,
        latitude,
        longitude,
        address,
        landmark,
        job_type = 'one_time',
        urgency = 'normal',
        budget_min,
        budget_max,
        estimated_duration_hours,
        requirements,
        preferred_gender = 'any',
        min_experience_years = 0,
        min_rating = 0
      } = req.body

      // Validate required fields
      if (!title || !description || !skill_category || !latitude || !longitude || !address || !budget_min || !budget_max) {
        res.status(400).json({
          success: false,
          error: 'Missing required fields: title, description, skill_category, latitude, longitude, address, budget_min, budget_max',
          code: 'MISSING_REQUIRED_FIELDS'
        })
        return
      }

      // Validate budget range
      if (budget_min <= 0 || budget_max < budget_min) {
        res.status(400).json({
          success: false,
          error: 'Invalid budget range. budget_min must be > 0 and budget_max must be >= budget_min',
          code: 'INVALID_BUDGET_RANGE'
        })
        return
      }

      // Validate description length (max 500 chars as per US-003)
      if (description.length > 500) {
        res.status(400).json({
          success: false,
          error: 'Description must be 500 characters or less',
          code: 'DESCRIPTION_TOO_LONG'
        })
        return
      }

      // Validate title length
      if (title.length > 200) {
        res.status(400).json({
          success: false,
          error: 'Title must be 200 characters or less',
          code: 'TITLE_TOO_LONG'
        })
        return
      }

      // Set job expiry (7 days from now as per business rules)
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 7)

      // Prepare job data
      const jobData = {
        poster_id: req.user.id,
        title: title.trim(),
        description: description.trim(),
        skill_category,
        location: `POINT(${longitude} ${latitude})`, // PostGIS format
        address: address.trim(),
        landmark: landmark?.trim() || null,
        job_type,
        urgency,
        budget_min: parseFloat(budget_min),
        budget_max: parseFloat(budget_max),
        estimated_duration_hours: estimated_duration_hours ? parseInt(estimated_duration_hours) : null,
        requirements: requirements?.trim() || null,
        preferred_gender,
        min_experience_years: parseInt(min_experience_years) || 0,
        min_rating: parseFloat(min_rating) || 0,
        status: 'active',
        applications_count: 0,
        views_count: 0,
        expires_at: expiresAt.toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        // New fields for job visibility and search
        search_keywords: '[]', // Will be populated by job visibility service
        visibility_score: 100.0, // Initial visibility score
        indexed_at: null, // Will be set when indexed
        is_visible: false, // Will be set to true when indexed
        filled_at: null
      }

      // Create job in database
      const { data: job, error } = await database.createJob(jobData)

      if (error || !job) {
        logger.error('Failed to create job:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to create job posting',
          code: 'JOB_CREATE_FAILED'
        })
        return
      }

      // Process job for immediate visibility (async, don't wait)
      jobVisibilityService.processNewJob(job.id).catch(error => {
        logger.error(`Failed to process job visibility for ${job.id}:`, error)
        // Don't fail the request - visibility processing is background
      })

      res.status(201).json({
        success: true,
        message: 'Job posted successfully',
        data: {
          job: {
            id: job.id,
            title: job.title,
            description: job.description,
            skill_category: job.skill_category,
            location: {
              latitude: parseFloat(latitude),
              longitude: parseFloat(longitude),
              address: job.address,
              landmark: job.landmark
            },
            job_type: job.job_type,
            urgency: job.urgency,
            budget_min: job.budget_min,
            budget_max: job.budget_max,
            estimated_duration_hours: job.estimated_duration_hours,
            requirements: job.requirements,
            status: job.status,
            expires_at: job.expires_at,
            created_at: job.created_at
          }
        }
      })

    } catch (error) {
      logger.error('Create job error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // =====================================================
  // GET JOBS (LIST WITH FILTERING)
  // =====================================================
  getJobs: async (req: any, res: any): Promise<void> => {
    try {
      const {
        skill_category,
        urgency,
        status = 'active',
        latitude,
        longitude,
        radius_km = 25, // Default 25km radius as per US-004
        limit = 20,
        offset = 0,
        sort_by = 'created_at',
        sort_order = 'desc'
      } = req.query

      // Build filter options
      const filterOptions: any = {
        status,
        limit: parseInt(limit),
        offset: parseInt(offset),
        sort_by,
        sort_order
      }

      if (skill_category) {
        filterOptions.skill_category = skill_category
      }

      if (urgency) {
        filterOptions.urgency = urgency
      }

      // Add location-based filtering if coordinates provided
      if (latitude && longitude) {
        filterOptions.location = {
          latitude: parseFloat(latitude),
          longitude: parseFloat(longitude),
          radius_km: parseFloat(radius_km)
        }
      }

      // Get jobs from database
      const { data: jobs, error } = await database.getJobs(filterOptions)

      if (error) {
        logger.error('Failed to get jobs:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to retrieve jobs',
          code: 'JOBS_FETCH_FAILED'
        })
        return
      }

      // Optimize response for mobile consumption
      const optimizedJobs = jobs?.map((job: any) => {
        // Calculate urgency indicators
        const isUrgent = job.urgency === 'urgent' || job.urgency === 'high'
        const urgencyMultiplier = job.urgency === 'urgent' ? 1.5 : job.urgency === 'high' ? 1.2 : 1.0

        // Calculate time-based indicators
        const createdAt = new Date(job.created_at)
        const expiresAt = new Date(job.expires_at)
        const now = new Date()
        const hoursUntilExpiry = Math.max(0, Math.floor((expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60)))
        const hoursSincePosted = Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60 * 60))

        return {
          id: job.id,
          title: job.title,
          description: job.description.length > 200 ?
            job.description.substring(0, 200) + '...' : job.description,
          skill_category: job.skill_category,
          urgency: job.urgency,
          budget_min: job.budget_min,
          budget_max: job.budget_max,
          estimated_duration_hours: job.estimated_duration_hours,
          address: job.address,
          landmark: job.landmark,
          created_at: job.created_at,
          expires_at: job.expires_at,
          applications_count: job.applications_count || 0,
          views_count: job.views_count || 0,
          distance_km: job.distance_km, // Include distance for location-based searches

          // Enhanced mobile-optimized fields
          is_urgent: isUrgent,
          urgency_multiplier: urgencyMultiplier,
          hours_until_expiry: hoursUntilExpiry,
          hours_since_posted: hoursSincePosted,
          is_expiring_soon: hoursUntilExpiry <= 24,
          is_new: hoursSincePosted <= 2,
          budget_display: `₹${job.budget_min}${job.budget_max && job.budget_max !== job.budget_min ? ` - ₹${job.budget_max}` : '+'}`,

          poster: {
            full_name: job.users?.full_name || job.poster_full_name,
            profile_image_url: job.users?.profile_image_url || job.poster_profile_image_url
          }
        }
      }) || []

      // Calculate if this is a location-based search
      const isLocationSearch = !!filterOptions.location
      const searchRadius = filterOptions.location?.radius_km || 25

      res.status(200).json({
        success: true,
        data: {
          jobs: optimizedJobs,
          pagination: {
            limit: filterOptions.limit,
            offset: filterOptions.offset,
            total: optimizedJobs.length,
            has_more: optimizedJobs.length === filterOptions.limit
          },
          filters: {
            skill_category: filterOptions.skill_category,
            urgency: filterOptions.urgency,
            status: filterOptions.status,
            location: filterOptions.location
          },
          search_metadata: {
            is_location_search: isLocationSearch,
            search_radius_km: isLocationSearch ? searchRadius : null,
            sorted_by_distance: isLocationSearch,
            total_results: optimizedJobs.length
          }
        }
      })

    } catch (error) {
      logger.error('Get jobs error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // =====================================================
  // GET JOB BY ID
  // =====================================================
  getJobById: async (req: any, res: any): Promise<void> => {
    try {
      const { id } = req.params

      if (!id) {
        res.status(400).json({
          success: false,
          error: 'Job ID is required',
          code: 'MISSING_JOB_ID'
        })
        return
      }

      // Get job from database
      const { data: job, error } = await database.getJobById(id)

      if (error) {
        logger.error('Failed to get job by ID:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to retrieve job',
          code: 'JOB_FETCH_FAILED'
        })
        return
      }

      if (!job) {
        res.status(404).json({
          success: false,
          error: 'Job not found',
          code: 'JOB_NOT_FOUND'
        })
        return
      }

      // Increment view count if user is not the poster
      if (req.user && req.user.id !== job.poster_id) {
        await database.incrementJobViews(id)
      }

      res.status(200).json({
        success: true,
        data: {
          job
        }
      })

    } catch (error) {
      logger.error('Get job by ID error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // =====================================================
  // UPDATE JOB
  // =====================================================
  updateJob: async (req: any, res: any): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      const { id } = req.params
      const updateData = req.body

      if (!id) {
        res.status(400).json({
          success: false,
          error: 'Job ID is required',
          code: 'MISSING_JOB_ID'
        })
        return
      }

      // Get existing job to verify ownership
      const { data: existingJob, error: fetchError } = await database.getJobById(id)

      if (fetchError) {
        logger.error('Failed to fetch job for update:', fetchError)
        res.status(500).json({
          success: false,
          error: 'Failed to retrieve job',
          code: 'JOB_FETCH_FAILED'
        })
        return
      }

      if (!existingJob) {
        res.status(404).json({
          success: false,
          error: 'Job not found',
          code: 'JOB_NOT_FOUND'
        })
        return
      }

      // Verify ownership
      if (existingJob.poster_id !== req.user.id) {
        res.status(403).json({
          success: false,
          error: 'Access denied. You can only update your own job postings',
          code: 'ACCESS_DENIED'
        })
        return
      }

      // Update job in database
      const { data: updatedJob, error } = await database.updateJob(id, {
        ...updateData,
        updated_at: new Date().toISOString()
      })

      if (error || !updatedJob) {
        logger.error('Failed to update job:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to update job',
          code: 'JOB_UPDATE_FAILED'
        })
        return
      }

      res.status(200).json({
        success: true,
        message: 'Job updated successfully',
        data: {
          job: updatedJob
        }
      })

    } catch (error) {
      logger.error('Update job error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // =====================================================
  // DELETE JOB
  // =====================================================
  deleteJob: async (req: any, res: any): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      const { id } = req.params

      if (!id) {
        res.status(400).json({
          success: false,
          error: 'Job ID is required',
          code: 'MISSING_JOB_ID'
        })
        return
      }

      // Get existing job to verify ownership
      const { data: existingJob, error: fetchError } = await database.getJobById(id)

      if (fetchError) {
        logger.error('Failed to fetch job for deletion:', fetchError)
        res.status(500).json({
          success: false,
          error: 'Failed to retrieve job',
          code: 'JOB_FETCH_FAILED'
        })
        return
      }

      if (!existingJob) {
        res.status(404).json({
          success: false,
          error: 'Job not found',
          code: 'JOB_NOT_FOUND'
        })
        return
      }

      // Verify ownership
      if (existingJob.poster_id !== req.user.id) {
        res.status(403).json({
          success: false,
          error: 'Access denied. You can only delete your own job postings',
          code: 'ACCESS_DENIED'
        })
        return
      }

      // Delete job from database
      const { error } = await database.deleteJob(id)

      if (error) {
        logger.error('Failed to delete job:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to delete job',
          code: 'JOB_DELETE_FAILED'
        })
        return
      }

      res.status(200).json({
        success: true,
        message: 'Job deleted successfully'
      })

    } catch (error) {
      logger.error('Delete job error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  }
}