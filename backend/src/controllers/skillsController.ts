// =====================================================
// SKILLS CONTROLLER
// =====================================================
// Controller for skill categories and subcategories API endpoints
// Task: US-002.2 - Create Skill Categories Master Data System

import { Request, Response } from 'express'
import database from '../config/database'
import logger from '../utils/logger'
import { 
  SkillQueryOptions, 
  isValidSkillCategoryCode,
  GetSkillCategoriesResponse,
  GetSkillSubcategoriesResponse,
  SkillSearchResponse
} from '../types/skills'

export const skillsController = {
  // =====================================================
  // GET ALL SKILL CATEGORIES
  // =====================================================
  getCategories: async (req: Request, res: Response): Promise<void> => {
    try {
      const options: SkillQueryOptions = {
        include_inactive: req.query['include_inactive'] === 'true',
        order_by: (req.query['order_by'] as any) || 'display_order',
        order_direction: (req.query['order_direction'] as any) || 'ASC'
      }

      // Only add limit and offset if they exist
      if (req.query['limit']) {
        options.limit = parseInt(req.query['limit'] as string)
      }
      if (req.query['offset']) {
        options.offset = parseInt(req.query['offset'] as string)
      }

      const { data: categories, error } = await database.getSkillCategories(options)

      if (error) {
        logger.error('Failed to fetch skill categories:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to fetch skill categories',
          code: 'FETCH_CATEGORIES_FAILED'
        } as GetSkillCategoriesResponse)
        return
      }

      res.status(200).json({
        success: true,
        data: categories || [],
        message: 'Skill categories fetched successfully'
      } as GetSkillCategoriesResponse)

    } catch (error) {
      logger.error('Get skill categories error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      } as GetSkillCategoriesResponse)
    }
  },

  // =====================================================
  // GET SKILL CATEGORY BY CODE
  // =====================================================
  getCategoryByCode: async (req: Request, res: Response): Promise<void> => {
    try {
      const { code } = req.params

      if (!code || !isValidSkillCategoryCode(code)) {
        res.status(400).json({
          success: false,
          error: 'Invalid skill category code',
          code: 'INVALID_CATEGORY_CODE'
        })
        return
      }

      const { data: category, error } = await database.getSkillCategoryByCode(code)

      if (error) {
        logger.error('Failed to fetch skill category:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to fetch skill category',
          code: 'FETCH_CATEGORY_FAILED'
        })
        return
      }

      if (!category) {
        res.status(404).json({
          success: false,
          error: 'Skill category not found',
          code: 'CATEGORY_NOT_FOUND'
        })
        return
      }

      res.status(200).json({
        success: true,
        data: [category],
        message: 'Skill category fetched successfully'
      } as GetSkillCategoriesResponse)

    } catch (error) {
      logger.error('Get skill category by code error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // =====================================================
  // GET SUBCATEGORIES FOR A CATEGORY
  // =====================================================
  getSubcategories: async (req: Request, res: Response): Promise<void> => {
    try {
      const { categoryId } = req.params
      const { categoryCode } = req.query

      if (!categoryId && !categoryCode) {
        res.status(400).json({
          success: false,
          error: 'Category ID or category code is required',
          code: 'MISSING_CATEGORY_IDENTIFIER'
        })
        return
      }

      const options: SkillQueryOptions = {
        include_inactive: req.query['include_inactive'] === 'true',
        order_by: (req.query['order_by'] as any) || 'display_order',
        order_direction: (req.query['order_direction'] as any) || 'ASC'
      }

      // Only add limit and offset if they exist
      if (req.query['limit']) {
        options.limit = parseInt(req.query['limit'] as string)
      }
      if (req.query['offset']) {
        options.offset = parseInt(req.query['offset'] as string)
      }

      let subcategoriesResult: { data: any; error: any }

      if (categoryCode) {
        // Validate category code
        if (!isValidSkillCategoryCode(categoryCode as string)) {
          res.status(400).json({
            success: false,
            error: 'Invalid skill category code',
            code: 'INVALID_CATEGORY_CODE'
          })
          return
        }
        subcategoriesResult = await database.getSkillSubcategoriesByCode(categoryCode as string, options)
      } else {
        if (!categoryId) {
          res.status(400).json({
            success: false,
            error: 'Category ID is required',
            code: 'MISSING_CATEGORY_ID'
          })
          return
        }
        subcategoriesResult = await database.getSkillSubcategories(categoryId, options)
      }

      const { data: subcategories, error } = subcategoriesResult

      if (error) {
        logger.error('Failed to fetch skill subcategories:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to fetch skill subcategories',
          code: 'FETCH_SUBCATEGORIES_FAILED'
        } as GetSkillSubcategoriesResponse)
        return
      }

      res.status(200).json({
        success: true,
        data: subcategories || [],
        message: 'Skill subcategories fetched successfully'
      } as GetSkillSubcategoriesResponse)

    } catch (error) {
      logger.error('Get skill subcategories error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      } as GetSkillSubcategoriesResponse)
    }
  },

  // =====================================================
  // SEARCH SKILLS
  // =====================================================
  searchSkills: async (req: Request, res: Response): Promise<void> => {
    try {
      const { q: searchQuery, category } = req.query

      if (!searchQuery || typeof searchQuery !== 'string' || searchQuery.trim().length < 2) {
        res.status(400).json({
          success: false,
          error: 'Search query must be at least 2 characters long',
          code: 'INVALID_SEARCH_QUERY'
        })
        return
      }

      // Validate category code if provided
      if (category && !isValidSkillCategoryCode(category as string)) {
        res.status(400).json({
          success: false,
          error: 'Invalid skill category code',
          code: 'INVALID_CATEGORY_CODE'
        })
        return
      }

      const { data: searchResults, error } = await database.searchSkills(
        searchQuery.trim(),
        category as string
      )

      if (error) {
        logger.error('Failed to search skills:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to search skills',
          code: 'SEARCH_SKILLS_FAILED'
        } as SkillSearchResponse)
        return
      }

      res.status(200).json({
        success: true,
        data: searchResults || { categories: [], subcategories: [] },
        message: 'Skills search completed successfully'
      } as SkillSearchResponse)

    } catch (error) {
      logger.error('Search skills error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      } as SkillSearchResponse)
    }
  }
}
