// =====================================================
// SKILLS ROUTES
// =====================================================
// API routes for skill categories and subcategories
// Task: US-002.2 - Create Skill Categories Master Data System

import { Router, type Router as RouterType } from 'express'
import { skillsController } from '../controllers/skillsController'
// import { authenticateToken } from '../middleware/auth' // For future authenticated routes

const router: RouterType = Router()

// =====================================================
// PUBLIC ROUTES (No authentication required)
// =====================================================

/**
 * @route GET /api/skills/categories
 * @desc Get all skill categories
 * @access Public
 * @query {boolean} include_inactive - Include inactive categories (default: false)
 * @query {string} order_by - Order by field: display_order, name, created_at (default: display_order)
 * @query {string} order_direction - Order direction: ASC, DESC (default: ASC)
 * @query {number} limit - Limit number of results
 * @query {number} offset - Offset for pagination
 */
router.get('/categories', skillsController.getCategories)

/**
 * @route GET /api/skills/categories/:code
 * @desc Get skill category by code
 * @access Public
 * @param {string} code - Skill category code (electrical, plumbing, etc.)
 */
router.get('/categories/:code', skillsController.getCategoryByCode)

/**
 * @route GET /api/skills/subcategories
 * @desc Get skill subcategories for a category
 * @access Public
 * @query {string} categoryCode - Category code to filter by
 * @query {boolean} include_inactive - Include inactive subcategories (default: false)
 * @query {string} order_by - Order by field: display_order, name, created_at (default: display_order)
 * @query {string} order_direction - Order direction: ASC, DESC (default: ASC)
 * @query {number} limit - Limit number of results
 * @query {number} offset - Offset for pagination
 */
router.get('/subcategories', skillsController.getSubcategories)

/**
 * @route GET /api/skills/subcategories/:categoryId
 * @desc Get skill subcategories by category ID
 * @access Public
 * @param {string} categoryId - Category UUID
 * @query {boolean} include_inactive - Include inactive subcategories (default: false)
 * @query {string} order_by - Order by field: display_order, name, created_at (default: display_order)
 * @query {string} order_direction - Order direction: ASC, DESC (default: ASC)
 * @query {number} limit - Limit number of results
 * @query {number} offset - Offset for pagination
 */
router.get('/subcategories/:categoryId', skillsController.getSubcategories)

/**
 * @route GET /api/skills/search
 * @desc Search skills across categories and subcategories
 * @access Public
 * @query {string} q - Search query (minimum 2 characters)
 * @query {string} category - Optional category code to filter results
 */
router.get('/search', skillsController.searchSkills)

// =====================================================
// AUTHENTICATED ROUTES (Future expansion)
// =====================================================

// These routes can be added later for admin functionality:
// - POST /categories (create new category)
// - PUT /categories/:id (update category)
// - DELETE /categories/:id (deactivate category)
// - POST /subcategories (create new subcategory)
// - PUT /subcategories/:id (update subcategory)
// - DELETE /subcategories/:id (deactivate subcategory)

export default router
