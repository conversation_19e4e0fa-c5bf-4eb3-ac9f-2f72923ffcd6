import app from './app'
import config from './config'
import logger from './utils/logger'
import database from './config/database'
import { storageService } from './services/storageService'

// Start server
const startServer = async (): Promise<void> => {
  try {
    // Test database connection
    const dbHealthy = await database.healthCheck()
    if (!dbHealthy) {
      throw new Error('Database connection failed')
    }
    logger.info('Database connection established')

    // Initialize storage service
    await storageService.initializeBucket()
    logger.info('Storage service initialized')

    // Start HTTP server
    const server = app.listen(config.server.port, config.server.host, () => {
      logger.info(`🚀 Ozgaar Backend Server started successfully`)
      logger.info(`📍 Server running on http://${config.server.host}:${config.server.port}`)
      logger.info(`🌍 Environment: ${config.server.env}`)
      logger.info(`📊 Health check: http://${config.server.host}:${config.server.port}/health`)
      logger.info(`🔗 API base URL: http://${config.server.host}:${config.server.port}/api`)
    })

    // Graceful shutdown handling
    const gracefulShutdown = (signal: string) => {
      logger.info(`${signal} received. Starting graceful shutdown...`)
      
      server.close((err:any) => {
        if (err) {
          logger.error('Error during server shutdown:', err)
          process.exit(1)
        }
        
        logger.info('Server closed successfully')
        process.exit(0)
      })

      // Force shutdown after 30 seconds
      setTimeout(() => {
        logger.error('Forced shutdown after timeout')
        process.exit(1)
      }, 30000)
    }

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => gracefulShutdown('SIGINT'))

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error)
      process.exit(1)
    })

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason)
      process.exit(1)
    })

  } catch (error) {
    logger.error('Failed to start server:', error)
    process.exit(1)
  }
}

// Start the server
startServer()
