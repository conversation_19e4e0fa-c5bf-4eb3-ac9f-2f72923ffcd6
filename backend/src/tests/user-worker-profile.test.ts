/**
 * Test suite for enhanced User model with worker profile fields
 * Task: US-002.1 - Enhance Database Schema for Worker Profile Fields
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/globals'
import { createClient } from '@supabase/supabase-js'
import { User } from '../config/database'

// Test configuration
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:54321'
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'test-key'
const supabase = createClient(supabaseUrl, supabaseKey)

describe('User Model - Worker Profile Fields', () => {
  let testUserId: string

  beforeAll(async () => {
    // Clean up any existing test data
    await supabase
      .from('users')
      .delete()
      .like('phone', '+919999%')
  })

  afterAll(async () => {
    // Clean up test data
    if (testUserId) {
      await supabase
        .from('users')
        .delete()
        .eq('id', testUserId)
    }
  })

  it('should create a user with basic worker profile fields', async () => {
    const testUser: Partial<User> = {
      phone: '+919999888777',
      full_name: 'Test Worker',
      user_type: 'worker',
      primary_skill_category: 'electrical',
      currently_available: true,
      years_of_experience: 5,
      profile_completed: true
    }

    const { data, error } = await supabase
      .from('users')
      .insert(testUser)
      .select()
      .single()

    expect(error).toBeNull()
    expect(data).toBeDefined()
    expect(data.primary_skill_category).toBe('electrical')
    expect(data.currently_available).toBe(true)
    expect(data.years_of_experience).toBe(5)
    
    testUserId = data.id
  })

  it('should allow null primary_skill_category for incomplete profiles', async () => {
    const testUser: Partial<User> = {
      phone: '+919999888778',
      user_type: 'worker',
      profile_completed: false
      // primary_skill_category is null
    }

    const { data, error } = await supabase
      .from('users')
      .insert(testUser)
      .select()
      .single()

    expect(error).toBeNull()
    expect(data).toBeDefined()
    expect(data.primary_skill_category).toBeNull()
    expect(data.profile_completed).toBe(false)

    // Clean up
    await supabase.from('users').delete().eq('id', data.id)
  })

  it('should enforce primary_skill_category for completed worker profiles', async () => {
    const testUser: Partial<User> = {
      phone: '+919999888779',
      full_name: 'Test Worker 2',
      user_type: 'worker',
      profile_completed: true
      // Missing primary_skill_category - should fail constraint
    }

    const { data, error } = await supabase
      .from('users')
      .insert(testUser)
      .select()
      .single()

    expect(error).toBeDefined()
    expect(error?.message).toContain('check_profile_completion')
  })

  it('should allow poster users without primary_skill_category', async () => {
    const testUser: Partial<User> = {
      phone: '+919999888780',
      full_name: 'Test Poster',
      user_type: 'poster',
      profile_completed: true
      // No primary_skill_category needed for posters
    }

    const { data, error } = await supabase
      .from('users')
      .insert(testUser)
      .select()
      .single()

    expect(error).toBeNull()
    expect(data).toBeDefined()
    expect(data.user_type).toBe('poster')
    expect(data.primary_skill_category).toBeNull()

    // Clean up
    await supabase.from('users').delete().eq('id', data.id)
  })

  it('should validate years_of_experience is non-negative', async () => {
    const testUser: Partial<User> = {
      phone: '+919999888781',
      full_name: 'Test Worker 3',
      user_type: 'worker',
      primary_skill_category: 'plumbing',
      years_of_experience: -1, // Invalid negative value
      profile_completed: true
    }

    const { data, error } = await supabase
      .from('users')
      .insert(testUser)
      .select()
      .single()

    expect(error).toBeDefined()
    expect(error?.message).toContain('years_of_experience')
  })

  it('should query users by primary_skill_category efficiently', async () => {
    // This test verifies the index is working
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('primary_skill_category', 'electrical')
      .eq('currently_available', true)

    expect(error).toBeNull()
    expect(Array.isArray(data)).toBe(true)
  })
})
