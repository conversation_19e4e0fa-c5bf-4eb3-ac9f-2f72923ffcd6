/**
 * Test suite for OTP rate limiting functionality
 * Verifies the fix for the timestamp type mismatch issue
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/globals'
import { createClient } from '@supabase/supabase-js'

// Test configuration
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:54321'
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'test-key'
const supabase = createClient(supabaseUrl, supabaseKey)

describe('OTP Rate Limiting - Post Fix Verification', () => {
  const testPhone = '+919999888777'

  beforeAll(async () => {
    // Clean up any existing test data
    await supabase
      .from('otp_attempts')
      .delete()
      .eq('phone', testPhone)
  })

  afterAll(async () => {
    // Clean up test data
    await supabase
      .from('otp_attempts')
      .delete()
      .eq('phone', testPhone)
  })

  it('should handle OTP rate limiting without type errors', async () => {
    // Call the handle_otp_rate_limit function
    const { data, error } = await supabase
      .rpc('handle_otp_rate_limit', { phone_number: testPhone })

    // Should not have any type mismatch errors
    expect(error).toBeNull()
    expect(data).toBeDefined()
    
    // Should return a proper response object
    expect(data).toHaveProperty('allowed')
    expect(typeof data.allowed).toBe('boolean')
  })

  it('should allow first OTP attempt', async () => {
    const { data, error } = await supabase
      .rpc('handle_otp_rate_limit', { phone_number: testPhone })

    expect(error).toBeNull()
    expect(data.allowed).toBe(true)
    expect(data.remaining_attempts).toBeGreaterThanOrEqual(0)
  })

  it('should track OTP attempts correctly', async () => {
    // Second attempt
    const { data: secondAttempt, error: secondError } = await supabase
      .rpc('handle_otp_rate_limit', { phone_number: testPhone })

    expect(secondError).toBeNull()
    expect(secondAttempt.allowed).toBe(true)
    expect(secondAttempt.remaining_attempts).toBeGreaterThanOrEqual(0)
  })

  it('should block after exceeding rate limit', async () => {
    // Third attempt
    await supabase.rpc('handle_otp_rate_limit', { phone_number: testPhone })
    
    // Fourth attempt (should be blocked)
    const { data: fourthAttempt, error: fourthError } = await supabase
      .rpc('handle_otp_rate_limit', { phone_number: testPhone })

    expect(fourthError).toBeNull()
    expect(fourthAttempt.allowed).toBe(false)
    expect(fourthAttempt.reason).toBe('rate_limited')
    expect(fourthAttempt).toHaveProperty('blocked_until')
  })

  it('should store OTP attempts with correct timestamp types', async () => {
    const { data, error } = await supabase
      .from('otp_attempts')
      .select('*')
      .eq('phone', testPhone)
      .limit(1)

    expect(error).toBeNull()
    expect(data).toBeDefined()
    expect(data.length).toBeGreaterThan(0)
    
    const attempt = data[0]
    expect(attempt).toHaveProperty('phone', testPhone)
    expect(attempt).toHaveProperty('attempts_count')
    expect(attempt).toHaveProperty('last_attempt_at')
    expect(attempt).toHaveProperty('created_at')
    
    // Verify timestamp fields exist and are not null
    expect(attempt.last_attempt_at).not.toBeNull()
    expect(attempt.created_at).not.toBeNull()
  })
})