import axios from 'axios'
import config from '../config'
import logger from '../utils/logger'
import database from '../config/database'

interface SendOtpOptions {
  phone: string
  message?: string
}

interface SendOtpResult {
  success: boolean
  sessionId?: string
  error?: string
  remainingAttempts?: number
}

interface VerifyOtpResult {
  success: boolean
  error?: string
}

interface TwoFactorApiResponse {
  Status: 'Success' | 'Error'
  Details: string
}

class TwoFactorService {
  private baseUrl: string = 'https://2factor.in/API/V1'
  private isConfigured: boolean = false

  constructor() {
    this.initializeService()
  }

  private initializeService(): void {
    try {
      if (!config.twoFactor.apiKey) {
        logger.warn('2Factor.in API key not configured. SMS service will not be available.')
        return
      }

      this.isConfigured = true
      logger.info('2Factor.in service initialized successfully')
    } catch (error) {
      logger.error('Failed to initialize 2Factor.in service:', error)
      this.isConfigured = false
    }
  }

  // Check if 2Factor.in is properly configured
  public isAvailable(): boolean {
    return this.isConfigured && !!config.twoFactor.apiKey
  }

  // Format phone number for 2Factor.in (remove + symbol, ensure country code)
  private formatPhoneNumber(phone: string): string {
    // Remove + symbol and any spaces/dashes
    let formatted = phone.replace(/[\+\s\-]/g, '')
    
    // If phone starts with 91 (India), use as is
    // If phone starts with 0, replace with 91
    // If phone is 10 digits, prepend 91
    if (formatted.startsWith('91') && formatted.length === 12) {
      return formatted
    } else if (formatted.startsWith('0') && formatted.length === 11) {
      return '91' + formatted.substring(1)
    } else if (formatted.length === 10) {
      return '91' + formatted
    }
    
    return formatted
  }

  // Send OTP with rate limiting check
  public async sendOtp({ phone }: SendOtpOptions): Promise<SendOtpResult> {
    if (!this.isAvailable()) {
      logger.error('2Factor.in service not available')
      return {
        success: false,
        error: 'SMS service not configured'
      }
    }

    try {
      // Check rate limiting
      const { data: rateLimitResult, error: rateLimitError } = await database.handleOtpRateLimit(phone)

      if (rateLimitError) {
        logger.error('Rate limit check failed:', rateLimitError)
        return {
          success: false,
          error: 'Rate limit check failed'
        }
      }

      if (!rateLimitResult.allowed) {
        logger.warn(`SMS rate limit exceeded for phone: ${phone}`)
        return {
          success: false,
          error: 'Rate limit exceeded',
          remainingAttempts: rateLimitResult.remaining_attempts
        }
      }

      // Format phone number for 2Factor.in
      const formattedPhone = this.formatPhoneNumber(phone)

      // Send OTP via 2Factor.in API
      const url = `${this.baseUrl}/${config.twoFactor.apiKey}/SMS/${formattedPhone}/AUTOGEN`
      
      const response = await axios.get<TwoFactorApiResponse>(url, {
        timeout: 10000, // 10 second timeout
      })

      if (response.data.Status === 'Success') {
        const sessionId = response.data.Details

        // Log successful SMS
        await database.createSmsLog({
          phone: phone,
          session_id: sessionId,
          status: 'sent',
          provider: '2factor',
          created_at: new Date().toISOString()
        })

        logger.info(`OTP sent successfully to ${phone}, Session ID: ${sessionId}`)

        return {
          success: true,
          sessionId: sessionId,
          remainingAttempts: rateLimitResult.remaining_attempts
        }
      } else {
        // Handle API error response
        logger.error(`2Factor.in API error: ${response.data.Details}`)
        
        await database.createSmsLog({
          phone: phone,
          status: 'failed',
          provider: '2factor',
          error_message: response.data.Details,
          created_at: new Date().toISOString()
        })

        return {
          success: false,
          error: response.data.Details
        }
      }

    } catch (error: any) {
      logger.error('2Factor.in SMS send error:', error)

      // Log failed SMS attempt
      await database.createSmsLog({
        phone: phone,
        status: 'failed',
        provider: '2factor',
        error_message: error.message || 'Unknown error',
        created_at: new Date().toISOString()
      })

      return {
        success: false,
        error: error.message || 'Failed to send SMS'
      }
    }
  }

  // Verify OTP using session ID
  public async verifyOtp(sessionId: string, otpCode: string): Promise<VerifyOtpResult> {
    if (!this.isAvailable()) {
      logger.error('2Factor.in service not available')
      return {
        success: false,
        error: 'SMS service not configured'
      }
    }

    try {
      // Verify OTP via 2Factor.in API
      const url = `${this.baseUrl}/${config.twoFactor.apiKey}/SMS/VERIFY/${sessionId}/${otpCode}`
      
      const response = await axios.get<TwoFactorApiResponse>(url, {
        timeout: 10000, // 10 second timeout
      })

      if (response.data.Status === 'Success') {
        logger.info(`OTP verified successfully for session: ${sessionId}`)
        return {
          success: true
        }
      } else {
        logger.warn(`OTP verification failed for session ${sessionId}: ${response.data.Details}`)
        return {
          success: false,
          error: response.data.Details
        }
      }

    } catch (error: any) {
      logger.error('2Factor.in OTP verification error:', error)
      return {
        success: false,
        error: error.message || 'Failed to verify OTP'
      }
    }
  }

  // Validate phone number for 2Factor.in (Indian mobile numbers)
  public validatePhoneNumber(phone: string): boolean {
    // Basic validation for Indian mobile numbers
    // Accepts formats: +************, ************, 09876543210, 9876543210
    const phoneRegex = /^(\+91|91|0)?[6-9][0-9]{9}$/
    return phoneRegex.test(phone.replace(/[\s\-]/g, ''))
  }

  // Get SMS statistics
  public async getSmsStats(startDate?: Date, endDate?: Date) {
    try {
      const start = startDate || new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours ago
      const end = endDate || new Date()

      const { data, error } = await database.getClient()
        .rpc('get_sms_stats', {
          start_date: start.toISOString(),
          end_date: end.toISOString()
        })

      if (error) {
        logger.error('Failed to get SMS stats:', error)
        return null
      }

      return data
    } catch (error) {
      logger.error('Error getting SMS stats:', error)
      return null
    }
  }
}

export default new TwoFactorService()
