import logger from '../utils/logger'
import database from '../config/database'

interface JobVisibilityStatus {
  jobId: string
  isVisible: boolean
  indexedAt: Date | null
  visibilityScore: number
  searchKeywords: string[]
  lastUpdated: Date
}

interface VisibilityMetrics {
  totalJobs: number
  visibleJobs: number
  averageIndexingTime: number
  jobsVisibleWithin5Min: number
  visibilityRate: number
}

/**
 * Job Visibility Service
 * Ensures jobs are visible to workers within 5 minutes of posting
 * Handles job indexing, search optimization, and real-time updates
 */
class JobVisibilityService {
  private readonly VISIBILITY_TARGET_MINUTES = 5
  private readonly INDEXING_BATCH_SIZE = 50
  private readonly SEARCH_BOOST_FACTORS = {
    urgency: { urgent: 2.0, high: 1.5, normal: 1.0 },
    budget: { above_market: 1.3, market: 1.0, below_market: 0.8 },
    location: { metro: 1.2, tier1: 1.1, tier2: 1.0, tier3: 0.9 },
    recency: { last_hour: 1.5, last_day: 1.2, last_week: 1.0, older: 0.8 }
  }

  /**
   * Process a newly created job for immediate visibility
   */
  async processNewJob(jobId: string): Promise<JobVisibilityStatus> {
    try {
      logger.info(`Processing new job for visibility: ${jobId}`)

      // Get job details using the proper database method
      const { data: job, error } = await database.getJobById(jobId)
      if (error || !job) {
        throw new Error(`Job not found: ${jobId}`)
      }

      // Generate search keywords
      const searchKeywords = this.generateSearchKeywords(job)

      // Calculate visibility score
      const visibilityScore = this.calculateVisibilityScore(job)

      // Update job with visibility data
      await this.updateJobVisibilityData(jobId, {
        search_keywords: searchKeywords,
        visibility_score: visibilityScore,
        indexed_at: new Date(),
        is_visible: true
      })

      // Trigger real-time updates
      await this.triggerRealTimeUpdates(job)

      // Schedule visibility verification
      this.scheduleVisibilityVerification(jobId)

      const status: JobVisibilityStatus = {
        jobId,
        isVisible: true,
        indexedAt: new Date(),
        visibilityScore,
        searchKeywords,
        lastUpdated: new Date()
      }

      logger.info(`Job ${jobId} processed for visibility with score ${visibilityScore}`)
      return status

    } catch (error) {
      logger.error(`Failed to process job visibility for ${jobId}:`, error)
      throw error
    }
  }

  /**
   * Generate search keywords for a job
   */
  private generateSearchKeywords(job: any): string[] {
    const keywords = new Set<string>()

    // Add title words
    if (job.title) {
      job.title.toLowerCase().split(/\s+/).forEach((word: string) => {
        if (word.length > 2) keywords.add(word)
      })
    }

    // Add skill category
    if (job.skill_category) {
      keywords.add(job.skill_category.toLowerCase())
    }

    // Add location keywords
    if (job.address) {
      job.address.toLowerCase().split(/[,\s]+/).forEach((word: string) => {
        if (word.length > 2) keywords.add(word)
      })
    }

    // Add description keywords (important ones)
    if (job.description) {
      const importantWords = job.description.toLowerCase()
        .match(/\b(urgent|immediate|asap|today|tomorrow|weekend|experienced|professional|certified|licensed)\b/g)
      if (importantWords) {
        importantWords.forEach((word: string) => keywords.add(word))
      }
    }

    // Add budget-related keywords
    if (job.budget_type) {
      keywords.add(job.budget_type)
    }

    return Array.from(keywords)
  }

  /**
   * Calculate visibility score for job ranking
   */
  private calculateVisibilityScore(job: any): number {
    let score = 100 // Base score

    // Urgency boost
    const urgencyBoost = this.SEARCH_BOOST_FACTORS.urgency[job.urgency as keyof typeof this.SEARCH_BOOST_FACTORS.urgency] || 1.0
    score *= urgencyBoost

    // Budget competitiveness boost
    const budgetBoost = this.calculateBudgetBoost(job)
    score *= budgetBoost

    // Location boost
    const locationBoost = this.calculateLocationBoost(job)
    score *= locationBoost

    // Recency boost (new jobs get priority)
    const recencyBoost = this.SEARCH_BOOST_FACTORS.recency.last_hour
    score *= recencyBoost

    // Quality indicators
    if (job.description && job.description.length > 100) {
      score *= 1.1 // Detailed description boost
    }

    if (job.requirements && job.requirements.length > 20) {
      score *= 1.05 // Clear requirements boost
    }

    return Math.round(score * 100) / 100
  }

  /**
   * Calculate budget competitiveness boost
   */
  private calculateBudgetBoost(job: any): number {
    // This would integrate with market rate service
    // For now, use simple heuristics
    const avgBudget = (job.budget_min + job.budget_max) / 2

    if (avgBudget > 2000) return this.SEARCH_BOOST_FACTORS.budget.above_market
    if (avgBudget > 1000) return this.SEARCH_BOOST_FACTORS.budget.market
    return this.SEARCH_BOOST_FACTORS.budget.below_market
  }

  /**
   * Calculate location boost based on demand
   */
  private calculateLocationBoost(job: any): number {
    if (!job.address) return 1.0

    const address = job.address.toLowerCase()

    if (address.includes('delhi') || address.includes('mumbai') || address.includes('bangalore')) {
      return this.SEARCH_BOOST_FACTORS.location.metro
    }
    if (address.includes('gurgaon') || address.includes('pune') || address.includes('hyderabad')) {
      return this.SEARCH_BOOST_FACTORS.location.tier1
    }

    return this.SEARCH_BOOST_FACTORS.location.tier2
  }

  /**
   * Update job visibility data in database
   */
  private async updateJobVisibilityData(jobId: string, data: any): Promise<void> {
    try {
      const updateData = {
        search_keywords: JSON.stringify(data.search_keywords),
        visibility_score: data.visibility_score,
        indexed_at: data.indexed_at.toISOString(),
        is_visible: data.is_visible,
        updated_at: new Date().toISOString()
      }

      const { error } = await database.updateJob(jobId, updateData)
      if (error) {
        throw error
      }
    } catch (error) {
      logger.error(`Failed to update job visibility data for ${jobId}:`, error)
      throw error
    }
  }

  /**
   * Trigger real-time updates to job feeds
   */
  private async triggerRealTimeUpdates(job: any): Promise<void> {
    try {
      // In a production system, this would:
      // 1. Update search indexes (Elasticsearch, etc.)
      // 2. Invalidate relevant caches
      // 3. Send real-time notifications to workers
      // 4. Update job feeds and recommendations

      logger.info(`Triggering real-time updates for job ${job.id}`)

      // Simulate immediate indexing
      await this.simulateSearchIndexUpdate(job)

      // Simulate cache invalidation
      await this.simulateCacheInvalidation(job)

      // Simulate worker notifications
      await this.simulateWorkerNotifications(job)

    } catch (error) {
      logger.error(`Failed to trigger real-time updates for job ${job.id}:`, error)
      // Don't throw - this shouldn't block job creation
    }
  }

  /**
   * Schedule visibility verification after 5 minutes
   */
  private scheduleVisibilityVerification(jobId: string): void {
    setTimeout(async () => {
      try {
        await this.verifyJobVisibility(jobId)
      } catch (error) {
        logger.error(`Visibility verification failed for job ${jobId}:`, error)
      }
    }, this.VISIBILITY_TARGET_MINUTES * 60 * 1000)
  }

  /**
   * Verify that a job is visible within the target timeframe
   */
  async verifyJobVisibility(jobId: string): Promise<boolean> {
    try {
      const { data: job, error } = await database.getJobById(jobId)
      if (error || !job) {
        logger.warn(`Job ${jobId} not found during visibility verification`)
        return false
      }

      const createdAt = new Date(job.created_at)
      const now = new Date()
      const minutesSinceCreation = (now.getTime() - createdAt.getTime()) / (1000 * 60)

      if (minutesSinceCreation <= this.VISIBILITY_TARGET_MINUTES && job.is_visible) {
        logger.info(`✅ Job ${jobId} is visible within ${this.VISIBILITY_TARGET_MINUTES} minutes`)
        return true
      } else {
        logger.warn(`⚠️ Job ${jobId} visibility target missed. Minutes since creation: ${minutesSinceCreation}`)

        // Trigger emergency visibility boost
        await this.emergencyVisibilityBoost(jobId)
        return false
      }
    } catch (error) {
      logger.error(`Error verifying job visibility for ${jobId}:`, error)
      return false
    }
  }

  /**
   * Emergency visibility boost for jobs that missed the 5-minute target
   */
  private async emergencyVisibilityBoost(jobId: string): Promise<void> {
    try {
      logger.info(`Applying emergency visibility boost for job ${jobId}`)

      // Get current job data
      const { data: job, error: getError } = await database.getJobById(jobId)
      if (getError || !job) {
        throw new Error(`Job not found: ${jobId}`)
      }

      // Increase visibility score by 50%
      const newVisibilityScore = (job.visibility_score || 1.0) * 1.5
      const { error: updateError } = await database.updateJob(jobId, {
        visibility_score: newVisibilityScore,
        updated_at: new Date().toISOString()
      })

      if (updateError) {
        throw updateError
      }

      // Re-trigger real-time updates with updated job data
      const updatedJob = { ...job, visibility_score: newVisibilityScore }
      await this.triggerRealTimeUpdates(updatedJob)

    } catch (error) {
      logger.error(`Emergency visibility boost failed for job ${jobId}:`, error)
    }
  }

  /**
   * Get visibility metrics for monitoring
   */
  async getVisibilityMetrics(): Promise<VisibilityMetrics> {
    try {
      // Get jobs from the last 24 hours
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()

      const { data: jobs, error } = await database.getClient()
        .from('jobs')
        .select('*')
        .gte('created_at', twentyFourHoursAgo)

      if (error) {
        throw error
      }

      const totalJobs = jobs?.length || 0
      const visibleJobs = jobs?.filter(job => job.is_visible).length || 0

      // Calculate jobs visible within target time
      let jobsVisibleWithin5Min = 0
      let totalIndexingTime = 0
      let jobsWithIndexingTime = 0

      jobs?.forEach(job => {
        if (job.indexed_at && job.created_at) {
          const createdAt = new Date(job.created_at)
          const indexedAt = new Date(job.indexed_at)
          const indexingMinutes = (indexedAt.getTime() - createdAt.getTime()) / (1000 * 60)

          totalIndexingTime += indexingMinutes
          jobsWithIndexingTime++

          if (job.is_visible && indexingMinutes <= this.VISIBILITY_TARGET_MINUTES) {
            jobsVisibleWithin5Min++
          }
        }
      })

      const averageIndexingTime = jobsWithIndexingTime > 0 ? totalIndexingTime / jobsWithIndexingTime : 0

      return {
        totalJobs,
        visibleJobs,
        averageIndexingTime,
        jobsVisibleWithin5Min,
        visibilityRate: totalJobs > 0 ? (jobsVisibleWithin5Min / totalJobs) * 100 : 0
      }
    } catch (error) {
      logger.error('Failed to get visibility metrics:', error)
      return {
        totalJobs: 0,
        visibleJobs: 0,
        averageIndexingTime: 0,
        jobsVisibleWithin5Min: 0,
        visibilityRate: 0
      }
    }
  }

  /**
   * Simulate search index update (placeholder for real implementation)
   */
  private async simulateSearchIndexUpdate(job: any): Promise<void> {
    // In production, this would update Elasticsearch or similar
    await new Promise(resolve => setTimeout(resolve, 100))
    logger.debug(`Search index updated for job ${job.id}`)
  }

  /**
   * Simulate cache invalidation (placeholder for real implementation)
   */
  private async simulateCacheInvalidation(job: any): Promise<void> {
    // In production, this would invalidate Redis cache keys
    await new Promise(resolve => setTimeout(resolve, 50))
    logger.debug(`Cache invalidated for job ${job.id}`)
  }

  /**
   * Simulate worker notifications (placeholder for real implementation)
   */
  private async simulateWorkerNotifications(job: any): Promise<void> {
    // In production, this would send push notifications to relevant workers
    await new Promise(resolve => setTimeout(resolve, 200))
    logger.debug(`Worker notifications sent for job ${job.id}`)
  }

  /**
   * Batch process jobs for visibility (for background processing)
   */
  async batchProcessJobsForVisibility(): Promise<number> {
    try {
      // Get unprocessed jobs from the last hour
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()

      const { data: unprocessedJobs, error } = await database.getClient()
        .from('jobs')
        .select('id')
        .is('indexed_at', null)
        .gte('created_at', oneHourAgo)
        .order('created_at', { ascending: true })
        .limit(this.INDEXING_BATCH_SIZE)

      if (error) {
        throw error
      }

      let processedCount = 0
      for (const job of unprocessedJobs || []) {
        try {
          await this.processNewJob(job.id)
          processedCount++
        } catch (error) {
          logger.error(`Failed to process job ${job.id} in batch:`, error)
        }
      }

      if (processedCount > 0) {
        logger.info(`Batch processed ${processedCount} jobs for visibility`)
      }

      return processedCount
    } catch (error) {
      logger.error('Batch processing failed:', error)
      return 0
    }
  }
}

export default new JobVisibilityService()