import logger from '../utils/logger'
import database from '../config/database'

interface JobPerformanceMetrics {
  jobId: string
  views: number
  applications: number
  timeToFill?: number // in hours
  averageResponseTime: number // in hours
  conversionRate: number // applications/views ratio
  qualityScore: number // based on application quality
  lastUpdated: Date
}

interface JobAnalytics {
  totalViews: number
  totalApplications: number
  averageTimeToFill: number
  conversionRate: number
  topPerformingJobs: JobPerformanceMetrics[]
  underPerformingJobs: JobPerformanceMetrics[]
  insights: string[]
  recommendations: string[]
}

interface JobViewEvent {
  jobId: string
  viewerId?: string
  viewerType: 'worker' | 'anonymous'
  source: 'search' | 'direct' | 'recommendation' | 'notification'
  timestamp: Date
  location?: {
    latitude: number
    longitude: number
  }
  deviceInfo?: {
    platform: string
    userAgent: string
  }
}

interface JobApplicationEvent {
  jobId: string
  applicantId: string
  applicationId: string
  timestamp: Date
  applicationQuality: 'high' | 'medium' | 'low'
  responseTime: number // hours since job posting
}

/**
 * Job Performance Tracking Service
 * Tracks job views, applications, and performance metrics
 * Provides analytics and optimization insights for job posters
 */
class JobPerformanceService {
  private readonly PERFORMANCE_CACHE_TTL = 300 // 5 minutes
  private readonly TOP_JOBS_LIMIT = 10
  private readonly UNDERPERFORMING_THRESHOLD = 0.02 // 2% conversion rate

  /**
   * Track a job view event
   */
  async trackJobView(event: JobViewEvent): Promise<void> {
    try {
      logger.info(`Tracking job view: ${event.jobId} by ${event.viewerType}`)

      // Insert view event
      await (database as any).query(
        `INSERT INTO job_views (
          job_id, viewer_id, viewer_type, source, timestamp,
          location_lat, location_lng, platform, user_agent
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
        [
          event.jobId,
          event.viewerId,
          event.viewerType,
          event.source,
          event.timestamp,
          event.location?.latitude,
          event.location?.longitude,
          event.deviceInfo?.platform,
          event.deviceInfo?.userAgent
        ]
      )

      // Update job performance metrics
      await this.updateJobMetrics(event.jobId)

      logger.debug(`Job view tracked successfully for ${event.jobId}`)

    } catch (error) {
      logger.error(`Failed to track job view for ${event.jobId}:`, error)
      throw error
    }
  }

  /**
   * Track a job application event
   */
  async trackJobApplication(event: JobApplicationEvent): Promise<void> {
    try {
      logger.info(`Tracking job application: ${event.jobId} by ${event.applicantId}`)

      // Insert application event
      await (database as any).query(
        `INSERT INTO job_applications_tracking (
          job_id, applicant_id, application_id, timestamp,
          application_quality, response_time_hours
        ) VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          event.jobId,
          event.applicantId,
          event.applicationId,
          event.timestamp,
          event.applicationQuality,
          event.responseTime
        ]
      )

      // Update job performance metrics
      await this.updateJobMetrics(event.jobId)

      logger.debug(`Job application tracked successfully for ${event.jobId}`)

    } catch (error) {
      logger.error(`Failed to track job application for ${event.jobId}:`, error)
      throw error
    }
  }

  /**
   * Update job performance metrics
   */
  private async updateJobMetrics(jobId: string): Promise<void> {
    try {
      // Get view count
      const viewResult = await (database as any).query(
        'SELECT COUNT(*) as view_count FROM job_views WHERE job_id = $1',
        [jobId]
      )
      const views = parseInt(viewResult.rows[0].view_count) || 0

      // Get application count and metrics
      const appResult = await (database as any).query(
        `SELECT
          COUNT(*) as app_count,
          AVG(response_time_hours) as avg_response_time,
          AVG(CASE
            WHEN application_quality = 'high' THEN 3
            WHEN application_quality = 'medium' THEN 2
            WHEN application_quality = 'low' THEN 1
            ELSE 1
          END) as quality_score
        FROM job_applications_tracking
        WHERE job_id = $1`,
        [jobId]
      )

      const applications = parseInt(appResult.rows[0].app_count) || 0
      const averageResponseTime = parseFloat(appResult.rows[0].avg_response_time) || 0
      const qualityScore = parseFloat(appResult.rows[0].quality_score) || 1

      // Calculate conversion rate
      const conversionRate = views > 0 ? (applications / views) * 100 : 0

      // Update or insert performance metrics
      await (database as any).query(
        `INSERT INTO job_performance_metrics (
          job_id, views, applications, average_response_time,
          conversion_rate, quality_score, last_updated
        ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
        ON CONFLICT (job_id) DO UPDATE SET
          views = EXCLUDED.views,
          applications = EXCLUDED.applications,
          average_response_time = EXCLUDED.average_response_time,
          conversion_rate = EXCLUDED.conversion_rate,
          quality_score = EXCLUDED.quality_score,
          last_updated = NOW()`,
        [jobId, views, applications, averageResponseTime, conversionRate, qualityScore]
      )

    } catch (error) {
      logger.error(`Failed to update job metrics for ${jobId}:`, error)
      throw error
    }
  }

  /**
   * Get performance metrics for a specific job
   */
  async getJobMetrics(jobId: string): Promise<JobPerformanceMetrics | null> {
    try {
      const result = await (database as any).query(
        `SELECT
          job_id, views, applications, time_to_fill,
          average_response_time, conversion_rate, quality_score, last_updated
        FROM job_performance_metrics
        WHERE job_id = $1`,
        [jobId]
      )

      if (result.rows.length === 0) {
        return null
      }

      const row = result.rows[0]
      return {
        jobId: row.job_id,
        views: row.views,
        applications: row.applications,
        timeToFill: row.time_to_fill,
        averageResponseTime: row.average_response_time,
        conversionRate: row.conversion_rate,
        qualityScore: row.quality_score,
        lastUpdated: new Date(row.last_updated)
      }

    } catch (error) {
      logger.error(`Failed to get job metrics for ${jobId}:`, error)
      throw error
    }
  }

  /**
   * Get analytics dashboard data for a job poster
   */
  async getJobAnalytics(posterId: string, timeframe: 'day' | 'week' | 'month' = 'week'): Promise<JobAnalytics> {
    try {
      const timeCondition = this.getTimeCondition(timeframe)

      // Get overall metrics
      const overallResult = await (database as any).query(
        `SELECT
          COALESCE(SUM(jpm.views), 0) as total_views,
          COALESCE(SUM(jpm.applications), 0) as total_applications,
          COALESCE(AVG(jpm.time_to_fill), 0) as avg_time_to_fill,
          COALESCE(AVG(jpm.conversion_rate), 0) as avg_conversion_rate
        FROM job_performance_metrics jpm
        JOIN jobs j ON j.id = jpm.job_id
        WHERE j.poster_id = $1 AND j.created_at >= ${timeCondition}`,
        [posterId]
      )

      const overall = overallResult.rows[0]

      // Get top performing jobs
      const topJobsResult = await (database as any).query(
        `SELECT
          jpm.job_id, jpm.views, jpm.applications, jpm.time_to_fill,
          jpm.average_response_time, jpm.conversion_rate, jpm.quality_score, jpm.last_updated
        FROM job_performance_metrics jpm
        JOIN jobs j ON j.id = jpm.job_id
        WHERE j.poster_id = $1 AND j.created_at >= ${timeCondition}
        ORDER BY jpm.conversion_rate DESC, jpm.quality_score DESC
        LIMIT $2`,
        [posterId, this.TOP_JOBS_LIMIT]
      )

      // Get underperforming jobs
      const underperformingResult = await (database as any).query(
        `SELECT
          jpm.job_id, jpm.views, jpm.applications, jpm.time_to_fill,
          jpm.average_response_time, jpm.conversion_rate, jpm.quality_score, jpm.last_updated
        FROM job_performance_metrics jpm
        JOIN jobs j ON j.id = jpm.job_id
        WHERE j.poster_id = $1 AND j.created_at >= ${timeCondition}
          AND jpm.conversion_rate < $2 AND jpm.views > 10
        ORDER BY jpm.conversion_rate ASC
        LIMIT $3`,
        [posterId, this.UNDERPERFORMING_THRESHOLD * 100, this.TOP_JOBS_LIMIT]
      )

      // Generate insights and recommendations
      const insights = this.generateInsights(overall, topJobsResult.rows, underperformingResult.rows)
      const recommendations = this.generateRecommendations(overall, topJobsResult.rows, underperformingResult.rows)

      return {
        totalViews: parseInt(overall.total_views) || 0,
        totalApplications: parseInt(overall.total_applications) || 0,
        averageTimeToFill: parseFloat(overall.avg_time_to_fill) || 0,
        conversionRate: parseFloat(overall.avg_conversion_rate) || 0,
        topPerformingJobs: topJobsResult.rows.map(this.mapRowToMetrics),
        underPerformingJobs: underperformingResult.rows.map(this.mapRowToMetrics),
        insights,
        recommendations
      }

    } catch (error) {
      logger.error(`Failed to get job analytics for poster ${posterId}:`, error)
      throw error
    }
  }

  /**
   * Mark a job as filled and calculate time to fill
   */
  async markJobFilled(jobId: string, filledAt: Date = new Date()): Promise<void> {
    try {
      // Get job creation time
      const jobResult = await (database as any).query(
        'SELECT created_at FROM jobs WHERE id = $1',
        [jobId]
      )

      if (jobResult.rows.length === 0) {
        throw new Error(`Job ${jobId} not found`)
      }

      const createdAt = new Date(jobResult.rows[0].created_at)
      const timeToFill = (filledAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60) // hours

      // Update performance metrics with time to fill
      await (database as any).query(
        `UPDATE job_performance_metrics
        SET time_to_fill = $1, last_updated = NOW()
        WHERE job_id = $2`,
        [timeToFill, jobId]
      )

      // Update job status
      await (database as any).query(
        `UPDATE jobs
        SET status = 'filled', filled_at = $1, updated_at = NOW()
        WHERE id = $2`,
        [filledAt, jobId]
      )

      logger.info(`Job ${jobId} marked as filled with time to fill: ${timeToFill.toFixed(2)} hours`)

    } catch (error) {
      logger.error(`Failed to mark job as filled for ${jobId}:`, error)
      throw error
    }
  }

  /**
   * Get time condition for SQL queries
   */
  private getTimeCondition(timeframe: 'day' | 'week' | 'month'): string {
    switch (timeframe) {
      case 'day':
        return "NOW() - INTERVAL '1 day'"
      case 'week':
        return "NOW() - INTERVAL '1 week'"
      case 'month':
        return "NOW() - INTERVAL '1 month'"
      default:
        return "NOW() - INTERVAL '1 week'"
    }
  }

  /**
   * Map database row to JobPerformanceMetrics
   */
  private mapRowToMetrics = (row: any): JobPerformanceMetrics => ({
    jobId: row.job_id,
    views: row.views,
    applications: row.applications,
    timeToFill: row.time_to_fill,
    averageResponseTime: row.average_response_time,
    conversionRate: row.conversion_rate,
    qualityScore: row.quality_score,
    lastUpdated: new Date(row.last_updated)
  })

  /**
   * Generate insights based on performance data
   */
  private generateInsights(overall: any, topJobs: any[], underperformingJobs: any[]): string[] {
    const insights: string[] = []

    const conversionRate = parseFloat(overall.avg_conversion_rate) || 0
    const totalViews = parseInt(overall.total_views) || 0
    const totalApplications = parseInt(overall.total_applications) || 0

    if (conversionRate > 5) {
      insights.push('🎉 Excellent conversion rate! Your jobs are attracting quality applicants.')
    } else if (conversionRate > 2) {
      insights.push('👍 Good conversion rate. Your job postings are performing well.')
    } else if (conversionRate > 0) {
      insights.push('⚠️ Low conversion rate. Consider improving job descriptions and requirements.')
    }

    if (totalViews > 100) {
      insights.push('📈 High visibility! Your jobs are getting good exposure.')
    } else if (totalViews > 20) {
      insights.push('👀 Moderate visibility. Consider optimizing job titles and keywords.')
    } else if (totalViews > 0) {
      insights.push('📉 Low visibility. Your jobs may need better optimization for search.')
    }

    if (topJobs.length > 0) {
      insights.push(`🏆 Your best performing job has a ${topJobs[0].conversion_rate.toFixed(1)}% conversion rate.`)
    }

    if (underperformingJobs.length > 0) {
      insights.push(`⚡ ${underperformingJobs.length} jobs need attention to improve performance.`)
    }

    return insights
  }

  /**
   * Generate recommendations based on performance data
   */
  private generateRecommendations(overall: any, topJobs: any[], underperformingJobs: any[]): string[] {
    const recommendations: string[] = []

    const conversionRate = parseFloat(overall.avg_conversion_rate) || 0
    const avgTimeToFill = parseFloat(overall.avg_time_to_fill) || 0

    if (conversionRate < 2) {
      recommendations.push('📝 Improve job descriptions with clear requirements and attractive benefits')
      recommendations.push('💰 Review budget ranges - competitive pay increases applications')
      recommendations.push('📍 Verify location accuracy and accessibility information')
    }

    if (avgTimeToFill > 168) { // More than 1 week
      recommendations.push('⚡ Consider increasing urgency level to attract faster responses')
      recommendations.push('🎯 Narrow down requirements to attract more qualified candidates')
    }

    if (underperformingJobs.length > 0) {
      recommendations.push('🔧 Review underperforming jobs and update titles or descriptions')
      recommendations.push('📊 A/B test different job titles to improve click-through rates')
    }

    if (topJobs.length > 0) {
      const bestJob = topJobs[0]
      recommendations.push(`✨ Replicate successful patterns from your best performing job (${bestJob.conversion_rate.toFixed(1)}% conversion)`)
    }

    recommendations.push('📱 Ensure jobs are mobile-friendly as most workers browse on mobile')
    recommendations.push('🕐 Post jobs during peak hours (9-11 AM, 2-4 PM) for better visibility')

    return recommendations
  }

  /**
   * Get performance summary for dashboard
   */
  async getPerformanceSummary(posterId: string): Promise<{
    totalJobs: number
    activeJobs: number
    totalViews: number
    totalApplications: number
    averageConversionRate: number
    trendsLastWeek: {
      viewsChange: number
      applicationsChange: number
    }
  }> {
    try {
      // Get current week metrics
      const currentResult = await (database as any).query(
        `SELECT
          COUNT(DISTINCT j.id) as total_jobs,
          COUNT(DISTINCT CASE WHEN j.status = 'active' THEN j.id END) as active_jobs,
          COALESCE(SUM(jpm.views), 0) as total_views,
          COALESCE(SUM(jpm.applications), 0) as total_applications,
          COALESCE(AVG(jpm.conversion_rate), 0) as avg_conversion_rate
        FROM jobs j
        LEFT JOIN job_performance_metrics jpm ON j.id = jpm.job_id
        WHERE j.poster_id = $1 AND j.created_at >= NOW() - INTERVAL '1 week'`,
        [posterId]
      )

      // Get previous week metrics for trends
      const previousResult = await (database as any).query(
        `SELECT
          COALESCE(SUM(jpm.views), 0) as total_views,
          COALESCE(SUM(jpm.applications), 0) as total_applications
        FROM jobs j
        LEFT JOIN job_performance_metrics jpm ON j.id = jpm.job_id
        WHERE j.poster_id = $1
          AND j.created_at >= NOW() - INTERVAL '2 weeks'
          AND j.created_at < NOW() - INTERVAL '1 week'`,
        [posterId]
      )

      const current = currentResult.rows[0]
      const previous = previousResult.rows[0]

      const currentViews = parseInt(current.total_views) || 0
      const currentApplications = parseInt(current.total_applications) || 0
      const previousViews = parseInt(previous.total_views) || 0
      const previousApplications = parseInt(previous.total_applications) || 0

      const viewsChange = previousViews > 0 ? ((currentViews - previousViews) / previousViews) * 100 : 0
      const applicationsChange = previousApplications > 0 ? ((currentApplications - previousApplications) / previousApplications) * 100 : 0

      return {
        totalJobs: parseInt(current.total_jobs) || 0,
        activeJobs: parseInt(current.active_jobs) || 0,
        totalViews: currentViews,
        totalApplications: currentApplications,
        averageConversionRate: parseFloat(current.avg_conversion_rate) || 0,
        trendsLastWeek: {
          viewsChange: Math.round(viewsChange * 100) / 100,
          applicationsChange: Math.round(applicationsChange * 100) / 100
        }
      }

    } catch (error) {
      logger.error(`Failed to get performance summary for poster ${posterId}:`, error)
      throw error
    }
  }
}

export default new JobPerformanceService()