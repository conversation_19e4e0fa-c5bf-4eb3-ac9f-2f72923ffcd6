import database from '../config/database'
import logger from '../utils/logger'

export interface JobFilterOptions {
  skill_category?: string
  urgency?: string
  status?: string
  location?: {
    latitude: number
    longitude: number
    radius_km: number
  }
  budget_range?: {
    min?: number
    max?: number
  }
  poster_id?: string
  limit?: number
  offset?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface JobStats {
  total_jobs: number
  active_jobs: number
  filled_jobs: number
  avg_budget: number
  popular_categories: Array<{
    category: string
    count: number
  }>
}

class JobService {
  // =====================================================
  // CORE JOB OPERATIONS
  // =====================================================

  /**
   * Create a new job with business logic validation
   */
  async createJob(jobData: any, posterId: string): Promise<{ data: any | null; error: any }> {
    try {
      // Add business logic validation
      const validation = await this.validateJobCreation(jobData, posterId)
      if (!validation.isValid) {
        return { data: null, error: validation.error }
      }

      // Check for duplicate jobs (same title, poster, and recent creation)
      const duplicateCheck = await this.checkDuplicateJob(jobData.title, posterId)
      if (duplicateCheck.isDuplicate) {
        return {
          data: null,
          error: 'A similar job was posted recently. Please wait before posting again or edit your existing job.'
        }
      }

      // Create job with enhanced data
      const enhancedJobData = {
        ...jobData,
        poster_id: posterId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        expires_at: this.calculateExpiryDate(jobData.urgency),
        status: 'active'
      }

      const result = await database.createJob(enhancedJobData)

      if (result.data) {
        // Log job creation for analytics
        logger.info(`Job created: ${result.data.id} by user ${posterId}`)

        // TODO: Trigger job visibility notifications to nearby workers
        // await this.notifyNearbyWorkers(result.data)
      }

      return result
    } catch (error) {
      logger.error('Job service create error:', error)
      return { data: null, error: 'Failed to create job' }
    }
  }

  /**
   * Get jobs with advanced filtering and location-based search
   */
  async getJobs(filterOptions: JobFilterOptions): Promise<{ data: any[] | null; error: any; total?: number }> {
    try {
      // Apply business rules for job visibility
      const enhancedFilters = await this.enhanceFilters(filterOptions)

      // Get jobs from database
      const result = await database.getJobs(enhancedFilters)

      if (result.data) {
        // Apply post-processing (distance calculation, relevance scoring)
        const processedJobs = await this.processJobResults(result.data, filterOptions)

        return {
          data: processedJobs,
          error: null,
          total: processedJobs.length
        }
      }

      return result
    } catch (error) {
      logger.error('Job service get jobs error:', error)
      return { data: null, error: 'Failed to retrieve jobs' }
    }
  }

  /**
   * Get job by ID with view tracking and enhanced data
   */
  async getJobById(jobId: string, viewerId?: string): Promise<{ data: any | null; error: any }> {
    try {
      const result = await database.getJobById(jobId)

      if (result.data) {
        // Track view if viewer is not the poster
        if (viewerId && viewerId !== result.data.poster_id) {
          await this.trackJobView(jobId, viewerId)
        }

        // Add computed fields
        const enhancedJob = await this.enhanceJobData(result.data)

        return { data: enhancedJob, error: null }
      }

      return result
    } catch (error) {
      logger.error('Job service get job by ID error:', error)
      return { data: null, error: 'Failed to retrieve job' }
    }
  }

  /**
   * Update job with business logic validation
   */
  async updateJob(jobId: string, updateData: any, posterId: string): Promise<{ data: any | null; error: any }> {
    try {
      // Verify ownership
      const existingJob = await database.getJobById(jobId)
      if (!existingJob.data || existingJob.data.poster_id !== posterId) {
        return { data: null, error: 'Job not found or access denied' }
      }

      // Validate update data
      const validation = await this.validateJobUpdate(updateData, existingJob.data)
      if (!validation.isValid) {
        return { data: null, error: validation.error }
      }

      // Apply business rules for updates
      const processedUpdateData = {
        ...updateData,
        updated_at: new Date().toISOString()
      }

      const result = await database.updateJob(jobId, processedUpdateData)

      if (result.data) {
        logger.info(`Job updated: ${jobId} by user ${posterId}`)
      }

      return result
    } catch (error) {
      logger.error('Job service update error:', error)
      return { data: null, error: 'Failed to update job' }
    }
  }

  /**
   * Delete job with proper cleanup
   */
  async deleteJob(jobId: string, posterId: string): Promise<{ data: any | null; error: any }> {
    try {
      // Verify ownership
      const existingJob = await database.getJobById(jobId)
      if (!existingJob.data || existingJob.data.poster_id !== posterId) {
        return { data: null, error: 'Job not found or access denied' }
      }

      // Check if job can be deleted (no active applications)
      const canDelete = await this.canDeleteJob(jobId)
      if (!canDelete.allowed) {
        return { data: null, error: canDelete.reason }
      }

      const result = await database.deleteJob(jobId)

      if (result.data) {
        logger.info(`Job deleted: ${jobId} by user ${posterId}`)
        // TODO: Notify applicants about job deletion
      }

      return result
    } catch (error) {
      logger.error('Job service delete error:', error)
      return { data: null, error: 'Failed to delete job' }
    }
  }

  // =====================================================
  // BUSINESS LOGIC HELPERS
  // =====================================================

  /**
   * Validate job creation with business rules
   */
  private async validateJobCreation(jobData: any, posterId: string): Promise<{ isValid: boolean; error?: string }> {
    try {
      // Check user's job posting limits (free users: 2 active jobs max)
      const userJobs = await database.getJobsByPosterId(posterId)
      if (userJobs.data) {
        const activeJobs = userJobs.data.filter(job => job.status === 'active')
        if (activeJobs.length >= 2) {
          return {
            isValid: false,
            error: 'You have reached the maximum limit of 2 active job postings. Please upgrade to premium or close existing jobs.'
          }
        }
      }

      // Validate budget against market rates
      const marketValidation = await this.validateMarketRates(jobData.skill_category, jobData.budget_min, jobData.budget_max)
      if (!marketValidation.isValid) {
        return {
          isValid: false,
          error: marketValidation.warning || 'Budget seems unrealistic for this skill category'
        }
      }

      return { isValid: true }
    } catch (error) {
      logger.error('Job creation validation error:', error)
      return { isValid: false, error: 'Validation failed' }
    }
  }

  /**
   * Check for duplicate job postings
   */
  private async checkDuplicateJob(title: string, posterId: string): Promise<{ isDuplicate: boolean }> {
    try {
      const userJobs = await database.getJobsByPosterId(posterId)
      if (userJobs.data) {
        const recentJobs = userJobs.data.filter(job => {
          const createdAt = new Date(job.created_at)
          const hoursSinceCreation = (Date.now() - createdAt.getTime()) / (1000 * 60 * 60)
          return hoursSinceCreation < 24 && job.status === 'active'
        })

        const similarJob = recentJobs.find(job =>
          this.calculateStringSimilarity(job.title.toLowerCase(), title.toLowerCase()) > 0.8
        )

        return { isDuplicate: !!similarJob }
      }

      return { isDuplicate: false }
    } catch (error) {
      logger.error('Duplicate job check error:', error)
      return { isDuplicate: false }
    }
  }

  /**
   * Calculate job expiry date based on urgency
   */
  private calculateExpiryDate(urgency: string): string {
    const now = new Date()
    let daysToAdd = 7 // Default 7 days

    switch (urgency) {
      case 'urgent':
        daysToAdd = 3 // Urgent jobs expire in 3 days
        break
      case 'high':
        daysToAdd = 5 // High priority jobs expire in 5 days
        break
      case 'normal':
        daysToAdd = 7 // Normal jobs expire in 7 days
        break
      case 'low':
        daysToAdd = 14 // Low priority jobs expire in 14 days
        break
    }

    now.setDate(now.getDate() + daysToAdd)
    return now.toISOString()
  }

  /**
   * Enhance job data with computed fields
   */
  private async enhanceJobData(job: any): Promise<any> {
    try {
      return {
        ...job,
        time_since_posted: this.calculateTimeSincePosted(job.created_at),
        expires_in: this.calculateTimeUntilExpiry(job.expires_at),
        is_urgent: job.urgency === 'urgent' || job.urgency === 'high',
        budget_display: this.formatBudgetDisplay(job.budget_min, job.budget_max)
      }
    } catch (error) {
      logger.error('Job data enhancement error:', error)
      return job
    }
  }

  /**
   * Validate market rates for job budget
   */
  private async validateMarketRates(skillCategory: string, budgetMin: number, budgetMax: number): Promise<{ isValid: boolean; warning?: string }> {
    try {
      // Market rate ranges for different skill categories (in INR)
      const marketRates: { [key: string]: { min: number; max: number } } = {
        'electrical': { min: 300, max: 2000 },
        'plumbing': { min: 250, max: 1500 },
        'carpentry': { min: 400, max: 2500 },
        'cooking': { min: 200, max: 1000 },
        'cleaning': { min: 150, max: 800 },
        'driving': { min: 500, max: 3000 },
        'delivery': { min: 200, max: 1200 },
        'security': { min: 400, max: 1800 },
        'gardening': { min: 200, max: 1000 },
        'tutoring': { min: 300, max: 2000 }
      }

      const marketRate = marketRates[skillCategory]
      if (!marketRate) {
        return { isValid: true } // No validation for unknown categories
      }

      if (budgetMax < marketRate.min * 0.5) {
        return {
          isValid: false,
          warning: `Budget seems too low for ${skillCategory}. Typical range is ₹${marketRate.min}-₹${marketRate.max} per day.`
        }
      }

      if (budgetMin > marketRate.max * 2) {
        return {
          isValid: true,
          warning: `Budget is higher than typical market rates for ${skillCategory}. This may attract many applicants.`
        }
      }

      return { isValid: true }
    } catch (error) {
      logger.error('Market rate validation error:', error)
      return { isValid: true }
    }
  }

  /**
   * Calculate string similarity for duplicate detection
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1

    if (longer.length === 0) return 1.0

    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = []

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0]![j] = j
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i]![j] = matrix[i - 1]![j - 1]!
        } else {
          matrix[i]![j] = Math.min(
            matrix[i - 1]![j - 1]! + 1,
            matrix[i]![j - 1]! + 1,
            matrix[i - 1]![j]! + 1
          )
        }
      }
    }

    return matrix[str2.length]![str1.length]!
  }

  // Additional helper methods would go here...
  private async enhanceFilters(filterOptions: JobFilterOptions): Promise<any> {
    return filterOptions // Placeholder for now
  }

  private async processJobResults(jobs: any[], filterOptions: JobFilterOptions): Promise<any[]> {
    return jobs // Placeholder for now
  }

  private async trackJobView(jobId: string, viewerId: string): Promise<void> {
    await database.incrementJobViews(jobId)
  }

  private async validateJobUpdate(updateData: any, existingJob: any): Promise<{ isValid: boolean; error?: string }> {
    return { isValid: true } // Placeholder for now
  }

  private async canDeleteJob(jobId: string): Promise<{ allowed: boolean; reason?: string }> {
    return { allowed: true } // Placeholder for now
  }

  private calculateTimeSincePosted(createdAt: string): string {
    const now = new Date()
    const posted = new Date(createdAt)
    const diffMs = now.getTime() - posted.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))

    if (diffHours < 1) return 'Just now'
    if (diffHours < 24) return `${diffHours} hours ago`

    const diffDays = Math.floor(diffHours / 24)
    return `${diffDays} days ago`
  }

  private calculateTimeUntilExpiry(expiresAt: string): string {
    const now = new Date()
    const expiry = new Date(expiresAt)
    const diffMs = expiry.getTime() - now.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))

    if (diffHours < 0) return 'Expired'
    if (diffHours < 24) return `${diffHours} hours left`

    const diffDays = Math.floor(diffHours / 24)
    return `${diffDays} days left`
  }

  private formatBudgetDisplay(budgetMin: number, budgetMax: number): string {
    if (budgetMin === budgetMax) {
      return `₹${budgetMin.toLocaleString('en-IN')}`
    }
    return `₹${budgetMin.toLocaleString('en-IN')} - ₹${budgetMax.toLocaleString('en-IN')}`
  }
}

export default new JobService()