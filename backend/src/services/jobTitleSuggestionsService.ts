import logger from '../utils/logger'

export interface JobTitleSuggestion {
  title: string
  category: string
  popularity: number
  description?: string
}

export interface SuggestionOptions {
  skill_category?: string
  query?: string
  limit?: number
  include_popular?: boolean
}

class JobTitleSuggestionsService {
  // Job title suggestions organized by skill category
  private readonly jobTitlesByCategory: { [key: string]: JobTitleSuggestion[] } = {
    'electrical': [
      { title: 'Electrician for Home Wiring', category: 'electrical', popularity: 95, description: 'Complete electrical installation and repair' },
      { title: 'AC Installation and Repair', category: 'electrical', popularity: 90, description: 'Air conditioning installation and maintenance' },
      { title: 'Fan Installation', category: 'electrical', popularity: 85, description: 'Ceiling and wall fan installation' },
      { title: 'Light Fixture Installation', category: 'electrical', popularity: 80, description: 'Install lights, chandeliers, and fixtures' },
      { title: 'Electrical Panel Upgrade', category: 'electrical', popularity: 75, description: 'Upgrade electrical panels and circuits' },
      { title: 'Switch and Socket Installation', category: 'electrical', popularity: 70, description: 'Install electrical switches and power outlets' },
      { title: 'Inverter Installation', category: 'electrical', popularity: 65, description: 'Install and configure power inverters' },
      { title: 'Electrical Troubleshooting', category: 'electrical', popularity: 60, description: 'Diagnose and fix electrical issues' },
      { title: 'Smart Home Setup', category: 'electrical', popularity: 55, description: 'Install smart switches and automation' },
      { title: 'Generator Installation', category: 'electrical', popularity: 50, description: 'Install backup power generators' }
    ],
    'plumbing': [
      { title: 'Bathroom Plumbing Repair', category: 'plumbing', popularity: 95, description: 'Fix bathroom pipes and fittings' },
      { title: 'Kitchen Sink Installation', category: 'plumbing', popularity: 90, description: 'Install kitchen sinks and faucets' },
      { title: 'Toilet Installation and Repair', category: 'plumbing', popularity: 85, description: 'Install or repair toilets and fixtures' },
      { title: 'Water Heater Installation', category: 'plumbing', popularity: 80, description: 'Install electric or gas water heaters' },
      { title: 'Pipe Leak Repair', category: 'plumbing', popularity: 75, description: 'Fix leaking pipes and joints' },
      { title: 'Drain Cleaning', category: 'plumbing', popularity: 70, description: 'Clear blocked drains and sewers' },
      { title: 'Shower Installation', category: 'plumbing', popularity: 65, description: 'Install shower systems and fittings' },
      { title: 'Water Tank Installation', category: 'plumbing', popularity: 60, description: 'Install overhead or underground water tanks' },
      { title: 'Tap and Faucet Repair', category: 'plumbing', popularity: 55, description: 'Fix dripping taps and faucets' },
      { title: 'Bathroom Renovation Plumbing', category: 'plumbing', popularity: 50, description: 'Complete bathroom plumbing renovation' }
    ],
    'carpentry': [
      { title: 'Furniture Assembly', category: 'carpentry', popularity: 95, description: 'Assemble beds, wardrobes, and furniture' },
      { title: 'Kitchen Cabinet Installation', category: 'carpentry', popularity: 90, description: 'Install modular kitchen cabinets' },
      { title: 'Door and Window Repair', category: 'carpentry', popularity: 85, description: 'Fix doors, windows, and frames' },
      { title: 'Custom Shelving', category: 'carpentry', popularity: 80, description: 'Build custom shelves and storage' },
      { title: 'Wardrobe Installation', category: 'carpentry', popularity: 75, description: 'Install built-in wardrobes' },
      { title: 'Wooden Flooring', category: 'carpentry', popularity: 70, description: 'Install wooden or laminate flooring' },
      { title: 'Partition Wall Construction', category: 'carpentry', popularity: 65, description: 'Build wooden partition walls' },
      { title: 'Staircase Repair', category: 'carpentry', popularity: 60, description: 'Repair wooden staircases and railings' },
      { title: 'Custom Furniture Making', category: 'carpentry', popularity: 55, description: 'Create custom wooden furniture' },
      { title: 'Ceiling Work', category: 'carpentry', popularity: 50, description: 'Install false ceilings and wooden work' }
    ],
    'cooking': [
      { title: 'Home Cook for Family', category: 'cooking', popularity: 95, description: 'Daily cooking for family meals' },
      { title: 'Party Catering', category: 'cooking', popularity: 90, description: 'Cook for parties and events' },
      { title: 'Tiffin Service Cook', category: 'cooking', popularity: 85, description: 'Prepare daily tiffin meals' },
      { title: 'Festival Cooking', category: 'cooking', popularity: 80, description: 'Special cooking for festivals' },
      { title: 'Wedding Catering', category: 'cooking', popularity: 75, description: 'Large scale wedding cooking' },
      { title: 'Regional Cuisine Specialist', category: 'cooking', popularity: 70, description: 'Cook specific regional dishes' },
      { title: 'Healthy Meal Preparation', category: 'cooking', popularity: 65, description: 'Prepare diet and healthy meals' },
      { title: 'Breakfast Cook', category: 'cooking', popularity: 60, description: 'Prepare morning breakfast meals' },
      { title: 'Snacks and Sweets Making', category: 'cooking', popularity: 55, description: 'Make traditional snacks and sweets' },
      { title: 'Meal Planning and Prep', category: 'cooking', popularity: 50, description: 'Plan and prepare weekly meals' }
    ],
    'cleaning': [
      { title: 'House Deep Cleaning', category: 'cleaning', popularity: 95, description: 'Complete house deep cleaning service' },
      { title: 'Office Cleaning', category: 'cleaning', popularity: 90, description: 'Regular office cleaning and maintenance' },
      { title: 'Bathroom Cleaning', category: 'cleaning', popularity: 85, description: 'Deep bathroom cleaning and sanitization' },
      { title: 'Kitchen Cleaning', category: 'cleaning', popularity: 80, description: 'Kitchen deep cleaning and degreasing' },
      { title: 'Carpet and Sofa Cleaning', category: 'cleaning', popularity: 75, description: 'Professional carpet and upholstery cleaning' },
      { title: 'Window Cleaning', category: 'cleaning', popularity: 70, description: 'Clean windows and glass surfaces' },
      { title: 'Post-Construction Cleaning', category: 'cleaning', popularity: 65, description: 'Clean after renovation or construction' },
      { title: 'Move-in/Move-out Cleaning', category: 'cleaning', popularity: 60, description: 'Cleaning for moving in or out' },
      { title: 'Regular Maid Service', category: 'cleaning', popularity: 55, description: 'Daily or weekly house cleaning' },
      { title: 'Disinfection Service', category: 'cleaning', popularity: 50, description: 'Sanitization and disinfection cleaning' }
    ],
    'driving': [
      { title: 'Personal Driver', category: 'driving', popularity: 95, description: 'Personal chauffeur for daily commute' },
      { title: 'Cab Driver', category: 'driving', popularity: 90, description: 'Taxi service for city travel' },
      { title: 'Delivery Driver', category: 'driving', popularity: 85, description: 'Deliver packages and goods' },
      { title: 'Truck Driver', category: 'driving', popularity: 80, description: 'Drive trucks for goods transportation' },
      { title: 'School Bus Driver', category: 'driving', popularity: 75, description: 'Drive school buses for children' },
      { title: 'Wedding Car Driver', category: 'driving', popularity: 70, description: 'Drive for wedding ceremonies' },
      { title: 'Airport Transfer Driver', category: 'driving', popularity: 65, description: 'Airport pickup and drop service' },
      { title: 'Outstation Driver', category: 'driving', popularity: 60, description: 'Long distance travel driver' },
      { title: 'Company Driver', category: 'driving', popularity: 55, description: 'Corporate driver for office use' },
      { title: 'Tourist Driver', category: 'driving', popularity: 50, description: 'Guide and drive tourists' }
    ]
  }

  // Cache for frequently requested suggestions
  private suggestionCache: Map<string, JobTitleSuggestion[]> = new Map()
  private cacheExpiry: Map<string, number> = new Map()
  private readonly CACHE_DURATION = 30 * 60 * 1000 // 30 minutes

  // =====================================================
  // MAIN SUGGESTION METHODS
  // =====================================================

  /**
   * Get job title suggestions based on skill category and optional query
   */
  async getSuggestions(options: SuggestionOptions): Promise<JobTitleSuggestion[]> {
    try {
      const { skill_category, query, limit = 10, include_popular = true } = options

      // Create cache key
      const cacheKey = this.createCacheKey(options)

      // Check cache first
      const cachedResult = this.getCachedSuggestions(cacheKey)
      if (cachedResult) {
        return cachedResult.slice(0, limit)
      }

      let suggestions: JobTitleSuggestion[] = []

      if (skill_category) {
        // Get suggestions for specific category
        suggestions = this.getSuggestionsByCategory(skill_category)
      } else {
        // Get suggestions from all categories
        suggestions = this.getAllSuggestions()
      }

      // Filter by query if provided
      if (query && query.trim().length > 0) {
        suggestions = this.filterSuggestionsByQuery(suggestions, query.trim())
      }

      // Sort by popularity and relevance
      suggestions = this.sortSuggestions(suggestions, query)

      // Include popular suggestions if requested
      if (include_popular && !query) {
        const popularSuggestions = this.getPopularSuggestions(skill_category)
        suggestions = this.mergeSuggestions(suggestions, popularSuggestions)
      }

      // Cache the result
      this.cacheSuggestions(cacheKey, suggestions)

      return suggestions.slice(0, limit)
    } catch (error) {
      logger.error('Job title suggestions error:', error)
      return []
    }
  }

  /**
   * Get trending job titles based on recent job postings
   */
  async getTrendingSuggestions(limit: number = 5): Promise<JobTitleSuggestion[]> {
    try {
      // In production, this would query the database for trending job titles
      // For now, return top suggestions from each category
      const trending: JobTitleSuggestion[] = []

      Object.values(this.jobTitlesByCategory).forEach(categoryTitles => {
        const topTitle = categoryTitles[0] // Get the most popular from each category
        if (topTitle) {
          trending.push(topTitle)
        }
      })

      return trending
        .sort((a, b) => b.popularity - a.popularity)
        .slice(0, limit)
    } catch (error) {
      logger.error('Trending suggestions error:', error)
      return []
    }
  }

  /**
   * Get suggestions for auto-complete as user types
   */
  async getAutoCompleteSuggestions(query: string, category?: string, limit: number = 5): Promise<string[]> {
    try {
      if (!query || query.length < 2) {
        return []
      }

      const suggestions = await this.getSuggestions({
        skill_category: category || '',
        query,
        limit: limit * 2, // Get more to filter better matches
        include_popular: false
      })

      return suggestions
        .map(s => s.title)
        .filter(title => title.toLowerCase().includes(query.toLowerCase()))
        .slice(0, limit)
    } catch (error) {
      logger.error('Auto-complete suggestions error:', error)
      return []
    }
  }

  // =====================================================
  // HELPER METHODS
  // =====================================================

  /**
   * Get suggestions for a specific category
   */
  private getSuggestionsByCategory(category: string): JobTitleSuggestion[] {
    return this.jobTitlesByCategory[category] || []
  }

  /**
   * Get all suggestions from all categories
   */
  private getAllSuggestions(): JobTitleSuggestion[] {
    const allSuggestions: JobTitleSuggestion[] = []
    Object.values(this.jobTitlesByCategory).forEach(categoryTitles => {
      allSuggestions.push(...categoryTitles)
    })
    return allSuggestions
  }

  /**
   * Filter suggestions based on search query
   */
  private filterSuggestionsByQuery(suggestions: JobTitleSuggestion[], query: string): JobTitleSuggestion[] {
    const queryLower = query.toLowerCase()

    return suggestions.filter(suggestion => {
      const titleMatch = suggestion.title.toLowerCase().includes(queryLower)
      const descriptionMatch = suggestion.description?.toLowerCase().includes(queryLower) || false
      return titleMatch || descriptionMatch
    })
  }

  /**
   * Sort suggestions by relevance and popularity
   */
  private sortSuggestions(suggestions: JobTitleSuggestion[], query?: string): JobTitleSuggestion[] {
    if (!query) {
      // Sort by popularity only
      return suggestions.sort((a, b) => b.popularity - a.popularity)
    }

    const queryLower = query.toLowerCase()

    return suggestions.sort((a, b) => {
      // Calculate relevance score
      const aScore = this.calculateRelevanceScore(a, queryLower)
      const bScore = this.calculateRelevanceScore(b, queryLower)

      if (aScore !== bScore) {
        return bScore - aScore // Higher score first
      }

      // If relevance is equal, sort by popularity
      return b.popularity - a.popularity
    })
  }

  /**
   * Calculate relevance score for a suggestion based on query
   */
  private calculateRelevanceScore(suggestion: JobTitleSuggestion, queryLower: string): number {
    let score = 0
    const titleLower = suggestion.title.toLowerCase()

    // Exact match gets highest score
    if (titleLower === queryLower) {
      score += 100
    }

    // Title starts with query
    if (titleLower.startsWith(queryLower)) {
      score += 50
    }

    // Title contains query
    if (titleLower.includes(queryLower)) {
      score += 25
    }

    // Description contains query
    if (suggestion.description?.toLowerCase().includes(queryLower)) {
      score += 10
    }

    // Add popularity bonus (scaled down)
    score += suggestion.popularity * 0.1

    return score
  }

  /**
   * Get popular suggestions for a category
   */
  private getPopularSuggestions(category?: string): JobTitleSuggestion[] {
    if (category) {
      return this.getSuggestionsByCategory(category)
        .sort((a, b) => b.popularity - a.popularity)
        .slice(0, 5)
    }

    // Get top suggestions from all categories
    return this.getAllSuggestions()
      .sort((a, b) => b.popularity - a.popularity)
      .slice(0, 10)
  }

  /**
   * Merge suggestions while avoiding duplicates
   */
  private mergeSuggestions(suggestions1: JobTitleSuggestion[], suggestions2: JobTitleSuggestion[]): JobTitleSuggestion[] {
    const merged = [...suggestions1]
    const existingTitles = new Set(suggestions1.map(s => s.title.toLowerCase()))

    suggestions2.forEach(suggestion => {
      if (!existingTitles.has(suggestion.title.toLowerCase())) {
        merged.push(suggestion)
        existingTitles.add(suggestion.title.toLowerCase())
      }
    })

    return merged
  }

  /**
   * Create cache key for suggestions
   */
  private createCacheKey(options: SuggestionOptions): string {
    const { skill_category = 'all', query = '', limit = 10, include_popular = true } = options
    return `${skill_category}:${query}:${limit}:${include_popular}`
  }

  /**
   * Get cached suggestions if available and not expired
   */
  private getCachedSuggestions(cacheKey: string): JobTitleSuggestion[] | null {
    const expiry = this.cacheExpiry.get(cacheKey)
    if (expiry && Date.now() > expiry) {
      // Cache expired, remove it
      this.suggestionCache.delete(cacheKey)
      this.cacheExpiry.delete(cacheKey)
      return null
    }

    return this.suggestionCache.get(cacheKey) || null
  }

  /**
   * Cache suggestions with expiry
   */
  private cacheSuggestions(cacheKey: string, suggestions: JobTitleSuggestion[]): void {
    this.suggestionCache.set(cacheKey, suggestions)
    this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_DURATION)

    // Clean up old cache entries periodically
    if (this.suggestionCache.size > 100) {
      this.cleanupCache()
    }
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    this.cacheExpiry.forEach((expiry, key) => {
      if (now > expiry) {
        expiredKeys.push(key)
      }
    })

    expiredKeys.forEach(key => {
      this.suggestionCache.delete(key)
      this.cacheExpiry.delete(key)
    })
  }

  /**
   * Add custom job title to suggestions (for learning)
   */
  async addCustomSuggestion(title: string, category: string, description?: string): Promise<void> {
    try {
      if (!this.jobTitlesByCategory[category]) {
        this.jobTitlesByCategory[category] = []
      }

      // Check if title already exists
      const exists = this.jobTitlesByCategory[category].some(
        suggestion => suggestion.title.toLowerCase() === title.toLowerCase()
      )

      if (!exists) {
        this.jobTitlesByCategory[category].push({
          title,
          category,
          popularity: 1, // Start with low popularity
          description: description || ''
        })

        // Clear cache to reflect new suggestions
        this.suggestionCache.clear()
        this.cacheExpiry.clear()

        logger.info(`Added custom job title suggestion: ${title} in category ${category}`)
      }
    } catch (error) {
      logger.error('Add custom suggestion error:', error)
    }
  }

  /**
   * Get available skill categories
   */
  getAvailableCategories(): string[] {
    return Object.keys(this.jobTitlesByCategory)
  }

  /**
   * Get statistics about suggestions
   */
  getSuggestionStats(): { [category: string]: number } {
    const stats: { [category: string]: number } = {}

    Object.entries(this.jobTitlesByCategory).forEach(([category, suggestions]) => {
      stats[category] = suggestions.length
    })

    return stats
  }
}

export default new JobTitleSuggestionsService()