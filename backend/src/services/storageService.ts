import { createClient } from '@supabase/supabase-js'
import config from '../config'
import logger from '../utils/logger'
import { v4 as uuidv4 } from 'uuid'

class StorageService {
  private client
  private bucketName = 'profile-images'

  constructor() {
    this.client = createClient(
      config.supabase.url,
      config.supabase.serviceRoleKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        }
      }
    )
  }

  /**
   * Initialize storage bucket if it doesn't exist
   */
  async initializeBucket(): Promise<void> {
    try {
      // Check if bucket exists
      const { data: buckets, error: listError } = await this.client.storage.listBuckets()
      
      if (listError) {
        logger.error('Failed to list buckets:', listError)
        return
      }

      const bucketExists = buckets?.some(bucket => bucket.name === this.bucketName)

      if (!bucketExists) {
        // Create bucket with public access for profile images
        const { error: createError } = await this.client.storage.createBucket(this.bucketName, {
          public: true,
          allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
          fileSizeLimit: 10 * 1024 * 1024 // 10MB
        })

        if (createError) {
          logger.error('Failed to create storage bucket:', createError)
        } else {
          logger.info(`Storage bucket '${this.bucketName}' created successfully`)
        }
      }
    } catch (error) {
      logger.error('Storage initialization error:', error)
    }
  }

  /**
   * Upload profile image to Supabase Storage
   */
  async uploadProfileImage(
    userId: string,
    imageBuffer: Buffer,
    mimetype: string
  ): Promise<{ url: string | null; error: string | null }> {
    try {
      // Generate unique filename
      const fileExtension = this.getFileExtension(mimetype)
      const fileName = `${userId}/${uuidv4()}.${fileExtension}`

      // Upload file to Supabase Storage
      const { data, error } = await this.client.storage
        .from(this.bucketName)
        .upload(fileName, imageBuffer, {
          contentType: mimetype,
          upsert: false
        })

      if (error) {
        logger.error('Storage upload error:', error)
        return { url: null, error: 'Failed to upload image to storage' }
      }

      // Get public URL
      const { data: urlData } = this.client.storage
        .from(this.bucketName)
        .getPublicUrl(fileName)

      if (!urlData?.publicUrl) {
        logger.error('Failed to get public URL for uploaded image')
        return { url: null, error: 'Failed to get image URL' }
      }

      logger.info(`Image uploaded successfully: ${fileName}`)
      return { url: urlData.publicUrl, error: null }

    } catch (error) {
      logger.error('Upload profile image error:', error)
      return { url: null, error: 'Internal storage error' }
    }
  }

  /**
   * Delete profile image from storage
   */
  async deleteProfileImage(imageUrl: string): Promise<{ success: boolean; error: string | null }> {
    try {
      // Extract file path from URL
      const filePath = this.extractFilePathFromUrl(imageUrl)
      if (!filePath) {
        return { success: false, error: 'Invalid image URL' }
      }

      const { error } = await this.client.storage
        .from(this.bucketName)
        .remove([filePath])

      if (error) {
        logger.error('Storage delete error:', error)
        return { success: false, error: 'Failed to delete image from storage' }
      }

      logger.info(`Image deleted successfully: ${filePath}`)
      return { success: true, error: null }

    } catch (error) {
      logger.error('Delete profile image error:', error)
      return { success: false, error: 'Internal storage error' }
    }
  }

  /**
   * Get file extension from mimetype
   */
  private getFileExtension(mimetype: string): string {
    const extensions: { [key: string]: string } = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/webp': 'webp'
    }
    return extensions[mimetype] || 'jpg'
  }

  /**
   * Extract file path from Supabase Storage URL
   */
  private extractFilePathFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url)
      const pathParts = urlObj.pathname.split('/')
      const bucketIndex = pathParts.findIndex(part => part === this.bucketName)
      
      if (bucketIndex === -1 || bucketIndex === pathParts.length - 1) {
        return null
      }

      return pathParts.slice(bucketIndex + 1).join('/')
    } catch (error) {
      logger.error('Error extracting file path from URL:', error)
      return null
    }
  }

  /**
   * List user's profile images (for cleanup/management)
   */
  async listUserImages(userId: string): Promise<{ files: string[]; error: string | null }> {
    try {
      const { data, error } = await this.client.storage
        .from(this.bucketName)
        .list(userId)

      if (error) {
        logger.error('Storage list error:', error)
        return { files: [], error: 'Failed to list user images' }
      }

      const fileNames = data?.map(file => `${userId}/${file.name}`) || []
      return { files: fileNames, error: null }

    } catch (error) {
      logger.error('List user images error:', error)
      return { files: [], error: 'Internal storage error' }
    }
  }
}

export const storageService = new StorageService()
