import logger from '../utils/logger'

export interface MarketRate {
  skill_category: string
  location_type: 'metro' | 'tier1' | 'tier2' | 'tier3'
  rate_type: 'hourly' | 'daily' | 'fixed'
  min_rate: number
  max_rate: number
  avg_rate: number
  currency: 'INR'
  last_updated: string
}

export interface RateValidationResult {
  isValid: boolean
  severity: 'info' | 'warning' | 'error'
  message: string
  suggestions?: {
    recommended_min: number
    recommended_max: number
    market_avg: number
    reasoning: string
  }
}

export interface LocationTier {
  tier: 'metro' | 'tier1' | 'tier2' | 'tier3'
  multiplier: number
  description: string
}

class MarketRateService {
  // Market rates by skill category and location tier (in INR)
  private readonly marketRates: { [key: string]: { [key: string]: MarketRate } } = {
    'electrical': {
      'metro': { skill_category: 'electrical', location_type: 'metro', rate_type: 'daily', min_rate: 800, max_rate: 3000, avg_rate: 1500, currency: 'INR', last_updated: '2025-01-01' },
      'tier1': { skill_category: 'electrical', location_type: 'tier1', rate_type: 'daily', min_rate: 600, max_rate: 2500, avg_rate: 1200, currency: 'INR', last_updated: '2025-01-01' },
      'tier2': { skill_category: 'electrical', location_type: 'tier2', rate_type: 'daily', min_rate: 400, max_rate: 2000, avg_rate: 1000, currency: 'INR', last_updated: '2025-01-01' },
      'tier3': { skill_category: 'electrical', location_type: 'tier3', rate_type: 'daily', min_rate: 300, max_rate: 1500, avg_rate: 800, currency: 'INR', last_updated: '2025-01-01' }
    },
    'plumbing': {
      'metro': { skill_category: 'plumbing', location_type: 'metro', rate_type: 'daily', min_rate: 700, max_rate: 2500, avg_rate: 1300, currency: 'INR', last_updated: '2025-01-01' },
      'tier1': { skill_category: 'plumbing', location_type: 'tier1', rate_type: 'daily', min_rate: 500, max_rate: 2000, avg_rate: 1000, currency: 'INR', last_updated: '2025-01-01' },
      'tier2': { skill_category: 'plumbing', location_type: 'tier2', rate_type: 'daily', min_rate: 350, max_rate: 1500, avg_rate: 800, currency: 'INR', last_updated: '2025-01-01' },
      'tier3': { skill_category: 'plumbing', location_type: 'tier3', rate_type: 'daily', min_rate: 250, max_rate: 1200, avg_rate: 600, currency: 'INR', last_updated: '2025-01-01' }
    },
    'carpentry': {
      'metro': { skill_category: 'carpentry', location_type: 'metro', rate_type: 'daily', min_rate: 900, max_rate: 3500, avg_rate: 1800, currency: 'INR', last_updated: '2025-01-01' },
      'tier1': { skill_category: 'carpentry', location_type: 'tier1', rate_type: 'daily', min_rate: 700, max_rate: 3000, avg_rate: 1500, currency: 'INR', last_updated: '2025-01-01' },
      'tier2': { skill_category: 'carpentry', location_type: 'tier2', rate_type: 'daily', min_rate: 500, max_rate: 2500, avg_rate: 1200, currency: 'INR', last_updated: '2025-01-01' },
      'tier3': { skill_category: 'carpentry', location_type: 'tier3', rate_type: 'daily', min_rate: 400, max_rate: 2000, avg_rate: 1000, currency: 'INR', last_updated: '2025-01-01' }
    },
    'cooking': {
      'metro': { skill_category: 'cooking', location_type: 'metro', rate_type: 'daily', min_rate: 500, max_rate: 2000, avg_rate: 1000, currency: 'INR', last_updated: '2025-01-01' },
      'tier1': { skill_category: 'cooking', location_type: 'tier1', rate_type: 'daily', min_rate: 400, max_rate: 1500, avg_rate: 800, currency: 'INR', last_updated: '2025-01-01' },
      'tier2': { skill_category: 'cooking', location_type: 'tier2', rate_type: 'daily', min_rate: 300, max_rate: 1200, avg_rate: 600, currency: 'INR', last_updated: '2025-01-01' },
      'tier3': { skill_category: 'cooking', location_type: 'tier3', rate_type: 'daily', min_rate: 200, max_rate: 1000, avg_rate: 500, currency: 'INR', last_updated: '2025-01-01' }
    },
    'cleaning': {
      'metro': { skill_category: 'cleaning', location_type: 'metro', rate_type: 'daily', min_rate: 400, max_rate: 1500, avg_rate: 800, currency: 'INR', last_updated: '2025-01-01' },
      'tier1': { skill_category: 'cleaning', location_type: 'tier1', rate_type: 'daily', min_rate: 300, max_rate: 1200, avg_rate: 600, currency: 'INR', last_updated: '2025-01-01' },
      'tier2': { skill_category: 'cleaning', location_type: 'tier2', rate_type: 'daily', min_rate: 250, max_rate: 1000, avg_rate: 500, currency: 'INR', last_updated: '2025-01-01' },
      'tier3': { skill_category: 'cleaning', location_type: 'tier3', rate_type: 'daily', min_rate: 150, max_rate: 800, avg_rate: 400, currency: 'INR', last_updated: '2025-01-01' }
    },
    'driving': {
      'metro': { skill_category: 'driving', location_type: 'metro', rate_type: 'daily', min_rate: 1000, max_rate: 4000, avg_rate: 2000, currency: 'INR', last_updated: '2025-01-01' },
      'tier1': { skill_category: 'driving', location_type: 'tier1', rate_type: 'daily', min_rate: 800, max_rate: 3000, avg_rate: 1500, currency: 'INR', last_updated: '2025-01-01' },
      'tier2': { skill_category: 'driving', location_type: 'tier2', rate_type: 'daily', min_rate: 600, max_rate: 2500, avg_rate: 1200, currency: 'INR', last_updated: '2025-01-01' },
      'tier3': { skill_category: 'driving', location_type: 'tier3', rate_type: 'daily', min_rate: 500, max_rate: 2000, avg_rate: 1000, currency: 'INR', last_updated: '2025-01-01' }
    }
  }

  // Location tier mapping for major Indian cities
  private readonly locationTiers: { [key: string]: LocationTier } = {
    // Metro cities
    'mumbai': { tier: 'metro', multiplier: 1.2, description: 'Metro city with highest rates' },
    'delhi': { tier: 'metro', multiplier: 1.15, description: 'Metro city with highest rates' },
    'bangalore': { tier: 'metro', multiplier: 1.1, description: 'Metro city with highest rates' },
    'chennai': { tier: 'metro', multiplier: 1.05, description: 'Metro city with highest rates' },
    'hyderabad': { tier: 'metro', multiplier: 1.0, description: 'Metro city with highest rates' },
    'kolkata': { tier: 'metro', multiplier: 1.0, description: 'Metro city with highest rates' },

    // Tier 1 cities
    'pune': { tier: 'tier1', multiplier: 0.9, description: 'Tier 1 city with competitive rates' },
    'ahmedabad': { tier: 'tier1', multiplier: 0.85, description: 'Tier 1 city with competitive rates' },
    'jaipur': { tier: 'tier1', multiplier: 0.8, description: 'Tier 1 city with competitive rates' },
    'surat': { tier: 'tier1', multiplier: 0.8, description: 'Tier 1 city with competitive rates' }
  }

  // =====================================================
  // MAIN VALIDATION METHODS
  // =====================================================

  /**
   * Validate budget range against market rates
   */
  async validateBudgetRange(
    skillCategory: string,
    budgetMin: number,
    budgetMax: number,
    location?: string
  ): Promise<RateValidationResult> {
    try {
      // Get location tier
      const locationTier = this.getLocationTier(location)

      // Get market rate for this category and location
      const marketRate = this.getMarketRate(skillCategory, locationTier.tier)

      if (!marketRate) {
        return {
          isValid: true,
          severity: 'info',
          message: `No market data available for ${skillCategory}. Budget will be accepted as provided.`
        }
      }

      // Apply location multiplier
      const adjustedMinRate = Math.round(marketRate.min_rate * locationTier.multiplier)
      const adjustedMaxRate = Math.round(marketRate.max_rate * locationTier.multiplier)
      const adjustedAvgRate = Math.round(marketRate.avg_rate * locationTier.multiplier)

      // Validate budget range
      const validation = this.performBudgetValidation(
        budgetMin,
        budgetMax,
        adjustedMinRate,
        adjustedMaxRate,
        adjustedAvgRate,
        skillCategory,
        locationTier
      )

      return validation
    } catch (error) {
      logger.error('Budget validation error:', error)
      return {
        isValid: true,
        severity: 'info',
        message: 'Unable to validate budget against market rates. Budget will be accepted as provided.'
      }
    }
  }

  /**
   * Get market rate suggestions for a skill category and location
   */
  async getMarketRateSuggestions(
    skillCategory: string,
    location?: string
  ): Promise<{
    market_rate: MarketRate | null
    location_tier: LocationTier
    suggestions: {
      recommended_min: number
      recommended_max: number
      market_avg: number
      competitive_range: { min: number; max: number }
    }
  }> {
    try {
      const locationTier = this.getLocationTier(location)
      const marketRate = this.getMarketRate(skillCategory, locationTier.tier)

      if (!marketRate) {
        return {
          market_rate: null,
          location_tier: locationTier,
          suggestions: {
            recommended_min: 0,
            recommended_max: 0,
            market_avg: 0,
            competitive_range: { min: 0, max: 0 }
          }
        }
      }

      // Apply location multiplier
      const adjustedMinRate = Math.round(marketRate.min_rate * locationTier.multiplier)
      const adjustedMaxRate = Math.round(marketRate.max_rate * locationTier.multiplier)
      const adjustedAvgRate = Math.round(marketRate.avg_rate * locationTier.multiplier)

      // Calculate competitive range (80-120% of average)
      const competitiveMin = Math.round(adjustedAvgRate * 0.8)
      const competitiveMax = Math.round(adjustedAvgRate * 1.2)

      return {
        market_rate: {
          ...marketRate,
          min_rate: adjustedMinRate,
          max_rate: adjustedMaxRate,
          avg_rate: adjustedAvgRate
        },
        location_tier: locationTier,
        suggestions: {
          recommended_min: adjustedMinRate,
          recommended_max: adjustedMaxRate,
          market_avg: adjustedAvgRate,
          competitive_range: {
            min: competitiveMin,
            max: competitiveMax
          }
        }
      }
    } catch (error) {
      logger.error('Market rate suggestions error:', error)
      const defaultTier = { tier: 'tier2' as const, multiplier: 1.0, description: 'Default tier' }
      return {
        market_rate: null,
        location_tier: defaultTier,
        suggestions: {
          recommended_min: 0,
          recommended_max: 0,
          market_avg: 0,
          competitive_range: { min: 0, max: 0 }
        }
      }
    }
  }

  /**
   * Get all market rates for comparison
   */
  async getAllMarketRates(): Promise<{ [category: string]: { [tier: string]: MarketRate } }> {
    return this.marketRates
  }

  /**
   * Get popular budget ranges for a skill category
   */
  async getPopularBudgetRanges(skillCategory: string): Promise<{
    budget_ranges: Array<{
      range: string
      min: number
      max: number
      popularity: number
      description: string
    }>
  }> {
    try {
      const marketRate = this.getMarketRate(skillCategory, 'tier1') // Use tier1 as baseline

      if (!marketRate) {
        return { budget_ranges: [] }
      }

      const avgRate = marketRate.avg_rate

      const ranges = [
        {
          range: 'Budget-friendly',
          min: Math.round(avgRate * 0.6),
          max: Math.round(avgRate * 0.8),
          popularity: 30,
          description: 'Lower cost option, may attract fewer experienced workers'
        },
        {
          range: 'Market standard',
          min: Math.round(avgRate * 0.8),
          max: Math.round(avgRate * 1.2),
          popularity: 50,
          description: 'Most common range, good balance of cost and quality'
        },
        {
          range: 'Premium',
          min: Math.round(avgRate * 1.2),
          max: Math.round(avgRate * 1.5),
          popularity: 15,
          description: 'Higher rates attract experienced professionals'
        },
        {
          range: 'Urgent/Specialized',
          min: Math.round(avgRate * 1.5),
          max: Math.round(avgRate * 2.0),
          popularity: 5,
          description: 'Premium rates for urgent or highly specialized work'
        }
      ]

      return { budget_ranges: ranges }
    } catch (error) {
      logger.error('Popular budget ranges error:', error)
      return { budget_ranges: [] }
    }
  }

  // =====================================================
  // HELPER METHODS
  // =====================================================

  /**
   * Get location tier based on city name or coordinates
   */
  private getLocationTier(location?: string): LocationTier {
    if (!location) {
      return { tier: 'tier2', multiplier: 1.0, description: 'Default tier for unknown location' }
    }

    const locationLower = location.toLowerCase()

    // Check for exact city matches
    for (const [city, tier] of Object.entries(this.locationTiers)) {
      if (locationLower.includes(city)) {
        return tier
      }
    }

    // Check for state/region patterns
    if (locationLower.includes('mumbai') || locationLower.includes('maharashtra')) {
      return { tier: 'tier1', multiplier: 0.9, description: 'Maharashtra region' }
    }

    if (locationLower.includes('delhi') || locationLower.includes('ncr') || locationLower.includes('gurgaon') || locationLower.includes('noida')) {
      return { tier: 'metro', multiplier: 1.1, description: 'Delhi NCR region' }
    }

    if (locationLower.includes('bangalore') || locationLower.includes('bengaluru') || locationLower.includes('karnataka')) {
      return { tier: 'metro', multiplier: 1.05, description: 'Karnataka region' }
    }

    // Default to tier2 for unknown locations
    return { tier: 'tier2', multiplier: 1.0, description: 'Default tier for unknown location' }
  }

  /**
   * Get market rate for skill category and location tier
   */
  private getMarketRate(skillCategory: string, locationTier: 'metro' | 'tier1' | 'tier2' | 'tier3'): MarketRate | null {
    const categoryRates = this.marketRates[skillCategory]
    if (!categoryRates) {
      return null
    }

    return categoryRates[locationTier] || null
  }

  /**
   * Perform detailed budget validation
   */
  private performBudgetValidation(
    budgetMin: number,
    budgetMax: number,
    marketMin: number,
    marketMax: number,
    marketAvg: number,
    skillCategory: string,
    locationTier: LocationTier
  ): RateValidationResult {

    // Check if budget is extremely low (less than 50% of market minimum)
    if (budgetMax < marketMin * 0.5) {
      return {
        isValid: false,
        severity: 'error',
        message: `Budget is significantly below market rates for ${skillCategory} in ${locationTier.description.toLowerCase()}. This may not attract qualified workers.`,
        suggestions: {
          recommended_min: marketMin,
          recommended_max: Math.round(marketAvg * 1.2),
          market_avg: marketAvg,
          reasoning: `Market rates for ${skillCategory} typically range from ₹${marketMin} to ₹${marketMax}. Consider increasing your budget to attract quality workers.`
        }
      }
    }

    // Check if budget is very low (50-80% of market minimum)
    if (budgetMax < marketMin * 0.8) {
      return {
        isValid: true,
        severity: 'warning',
        message: `Budget is below typical market rates for ${skillCategory}. You may receive fewer applications.`,
        suggestions: {
          recommended_min: marketMin,
          recommended_max: Math.round(marketAvg * 1.2),
          market_avg: marketAvg,
          reasoning: `Consider increasing to ₹${marketMin}-₹${Math.round(marketAvg * 1.2)} for better response rates.`
        }
      }
    }

    // Check if budget is within normal range
    if (budgetMin >= marketMin * 0.8 && budgetMax <= marketMax * 1.2) {
      return {
        isValid: true,
        severity: 'info',
        message: `Budget is within market range for ${skillCategory} in ${locationTier.description.toLowerCase()}.`,
        suggestions: {
          recommended_min: marketMin,
          recommended_max: marketMax,
          market_avg: marketAvg,
          reasoning: `Your budget aligns well with market rates. You should receive good response from workers.`
        }
      }
    }

    // Check if budget is high (120-200% of market maximum)
    if (budgetMin > marketMax * 1.2 && budgetMin <= marketMax * 2.0) {
      return {
        isValid: true,
        severity: 'info',
        message: `Budget is above market rates for ${skillCategory}. This may attract many applications quickly.`,
        suggestions: {
          recommended_min: marketMin,
          recommended_max: marketMax,
          market_avg: marketAvg,
          reasoning: `Your higher budget should attract experienced workers. You may receive many applications.`
        }
      }
    }

    // Check if budget is very high (more than 200% of market maximum)
    if (budgetMin > marketMax * 2.0) {
      return {
        isValid: true,
        severity: 'warning',
        message: `Budget is significantly above market rates for ${skillCategory}. Consider if this premium is necessary.`,
        suggestions: {
          recommended_min: marketMin,
          recommended_max: marketMax,
          market_avg: marketAvg,
          reasoning: `Market rates are typically ₹${marketMin}-₹${marketMax}. Your budget may be unnecessarily high unless for urgent or specialized work.`
        }
      }
    }

    // Default case
    return {
      isValid: true,
      severity: 'info',
      message: `Budget noted for ${skillCategory}.`,
      suggestions: {
        recommended_min: marketMin,
        recommended_max: marketMax,
        market_avg: marketAvg,
        reasoning: `Market rates for ${skillCategory} typically range from ₹${marketMin} to ₹${marketMax}.`
      }
    }
  }

  /**
   * Format currency for display
   */
  private formatCurrency(amount: number): string {
    return `₹${amount.toLocaleString('en-IN')}`
  }

  /**
   * Get skill categories with market data
   */
  getAvailableSkillCategories(): string[] {
    return Object.keys(this.marketRates)
  }

  /**
   * Get location tiers information
   */
  getLocationTiers(): { [key: string]: LocationTier } {
    return this.locationTiers
  }

  /**
   * Update market rates (for admin use)
   */
  async updateMarketRate(
    skillCategory: string,
    locationTier: 'metro' | 'tier1' | 'tier2' | 'tier3',
    newRate: Partial<MarketRate>
  ): Promise<boolean> {
    try {
      if (!this.marketRates[skillCategory]) {
        this.marketRates[skillCategory] = {}
      }

      const existingRate = this.marketRates[skillCategory][locationTier]
      this.marketRates[skillCategory][locationTier] = {
        skill_category: skillCategory,
        location_type: locationTier,
        rate_type: 'hourly',
        min_rate: 0,
        max_rate: 0,
        avg_rate: 0,
        currency: 'INR',
        ...existingRate,
        ...newRate,
        last_updated: new Date().toISOString().split('T')[0]!
      }

      logger.info(`Updated market rate for ${skillCategory} in ${locationTier}`)
      return true
    } catch (error) {
      logger.error('Update market rate error:', error)
      return false
    }
  }
}

export default new MarketRateService()