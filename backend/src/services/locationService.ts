import logger from '../utils/logger'

export interface Coordinates {
  latitude: number
  longitude: number
}

export interface Address {
  formatted_address: string
  street?: string
  locality?: string
  city?: string
  state?: string
  postal_code?: string
  country?: string
  landmark?: string
}

export interface LocationValidation {
  isValid: boolean
  error?: string
  normalized?: Coordinates
}

export interface ReverseGeocodeResult {
  success: boolean
  address?: Address
  error?: string
}

export interface DistanceResult {
  distance_km: number
  distance_display: string
}

class LocationService {
  // =====================================================
  // COORDINATE VALIDATION
  // =====================================================

  /**
   * Validate GPS coordinates for Indian locations
   */
  validateCoordinates(latitude: number, longitude: number): LocationValidation {
    try {
      // Basic coordinate validation
      if (latitude < -90 || latitude > 90) {
        return { isValid: false, error: 'Invalid latitude. Must be between -90 and 90.' }
      }

      if (longitude < -180 || longitude > 180) {
        return { isValid: false, error: 'Invalid longitude. Must be between -180 and 180.' }
      }

      // India-specific coordinate bounds (approximate)
      const INDIA_BOUNDS = {
        north: 37.6,
        south: 6.4,
        east: 97.25,
        west: 68.7
      }

      if (latitude < INDIA_BOUNDS.south || latitude > INDIA_BOUNDS.north ||
          longitude < INDIA_BOUNDS.west || longitude > INDIA_BOUNDS.east) {
        return {
          isValid: false,
          error: 'Coordinates appear to be outside India. Please verify your location.'
        }
      }

      return {
        isValid: true,
        normalized: {
          latitude: parseFloat(latitude.toFixed(6)),
          longitude: parseFloat(longitude.toFixed(6))
        }
      }
    } catch (error) {
      logger.error('Coordinate validation error:', error)
      return { isValid: false, error: 'Invalid coordinate format' }
    }
  }

  /**
   * Calculate distance between two coordinates using Haversine formula
   */
  calculateDistance(coord1: Coordinates, coord2: Coordinates): DistanceResult {
    try {
      const R = 6371 // Earth's radius in kilometers
      const dLat = this.toRadians(coord2.latitude - coord1.latitude)
      const dLon = this.toRadians(coord2.longitude - coord1.longitude)

      const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(this.toRadians(coord1.latitude)) * Math.cos(this.toRadians(coord2.latitude)) *
                Math.sin(dLon / 2) * Math.sin(dLon / 2)

      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
      const distance = R * c

      return {
        distance_km: parseFloat(distance.toFixed(2)),
        distance_display: this.formatDistance(distance)
      }
    } catch (error) {
      logger.error('Distance calculation error:', error)
      return {
        distance_km: 0,
        distance_display: 'Unknown distance'
      }
    }
  }

  /**
   * Check if coordinates are within a specified radius
   */
  isWithinRadius(center: Coordinates, point: Coordinates, radiusKm: number): boolean {
    try {
      const distance = this.calculateDistance(center, point)
      return distance.distance_km <= radiusKm
    } catch (error) {
      logger.error('Radius check error:', error)
      return false
    }
  }

  // =====================================================
  // ADDRESS VALIDATION & GEOCODING
  // =====================================================

  /**
   * Validate and normalize address format
   */
  validateAddress(address: string): { isValid: boolean; normalized?: string; error?: string } {
    try {
      if (!address || address.trim().length < 10) {
        return { isValid: false, error: 'Address must be at least 10 characters long' }
      }

      if (address.length > 500) {
        return { isValid: false, error: 'Address cannot exceed 500 characters' }
      }

      // Basic address validation for Indian addresses
      const normalized = address.trim()
        .replace(/\s+/g, ' ') // Replace multiple spaces with single space
        .replace(/[^\w\s,.-]/g, '') // Remove special characters except common ones

      // Check for minimum address components (should have at least area/city)
      const components = normalized.split(/[,\s]+/).filter(c => c.length > 2)
      if (components.length < 3) {
        return {
          isValid: false,
          error: 'Address should include area, city, and state for better accuracy'
        }
      }

      return { isValid: true, normalized }
    } catch (error) {
      logger.error('Address validation error:', error)
      return { isValid: false, error: 'Invalid address format' }
    }
  }

  /**
   * Reverse geocoding - convert coordinates to address
   * Note: This is a placeholder implementation. In production, integrate with Google Maps API or similar
   */
  async reverseGeocode(latitude: number, longitude: number): Promise<ReverseGeocodeResult> {
    try {
      // Validate coordinates first
      const validation = this.validateCoordinates(latitude, longitude)
      if (!validation.isValid) {
        return { success: false, error: validation.error || 'Invalid coordinates' }
      }

      // Placeholder implementation - in production, use Google Maps Geocoding API
      // For now, return a generic address based on coordinate ranges
      const address = await this.generateApproximateAddress(latitude, longitude)

      return {
        success: true,
        address
      }
    } catch (error) {
      logger.error('Reverse geocoding error:', error)
      return { success: false, error: 'Failed to get address from coordinates' }
    }
  }

  /**
   * Forward geocoding - convert address to coordinates
   * Note: This is a placeholder implementation
   */
  async geocodeAddress(address: string): Promise<{ success: boolean; coordinates?: Coordinates; error?: string }> {
    try {
      const validation = this.validateAddress(address)
      if (!validation.isValid) {
        return { success: false, error: validation.error || 'Invalid address' }
      }

      // Placeholder implementation - in production, use Google Maps Geocoding API
      // For now, return approximate coordinates for major Indian cities
      const coordinates = await this.generateApproximateCoordinates(address)

      if (coordinates) {
        return { success: true, coordinates }
      }

      return { success: false, error: 'Could not find coordinates for this address' }
    } catch (error) {
      logger.error('Geocoding error:', error)
      return { success: false, error: 'Failed to get coordinates from address' }
    }
  }

  // =====================================================
  // LOCATION-BASED JOB MATCHING
  // =====================================================

  /**
   * Find jobs within radius of given coordinates
   */
  async findJobsNearLocation(
    centerCoords: Coordinates,
    radiusKm: number = 25,
    filters?: any
  ): Promise<{ jobs: any[]; total: number }> {
    try {
      // This would integrate with the job database service
      // For now, return placeholder structure
      logger.info(`Finding jobs within ${radiusKm}km of ${centerCoords.latitude}, ${centerCoords.longitude}`)

      return {
        jobs: [],
        total: 0
      }
    } catch (error) {
      logger.error('Location-based job search error:', error)
      return { jobs: [], total: 0 }
    }
  }

  /**
   * Get popular job locations within a city
   */
  async getPopularJobLocations(cityName: string): Promise<Array<{ area: string; job_count: number; coordinates: Coordinates }>> {
    try {
      // Placeholder implementation
      // In production, this would query the database for job concentration areas
      return []
    } catch (error) {
      logger.error('Popular locations error:', error)
      return []
    }
  }

  // =====================================================
  // HELPER METHODS
  // =====================================================

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  private formatDistance(distanceKm: number): string {
    if (distanceKm < 1) {
      return `${Math.round(distanceKm * 1000)}m away`
    } else if (distanceKm < 10) {
      return `${distanceKm.toFixed(1)}km away`
    } else {
      return `${Math.round(distanceKm)}km away`
    }
  }

  /**
   * Generate approximate address from coordinates (placeholder)
   */
  private async generateApproximateAddress(latitude: number, longitude: number): Promise<Address> {
    // Major Indian cities with approximate coordinate ranges
    const cityRanges = [
      { name: 'Delhi', state: 'Delhi', lat: [28.4, 28.9], lng: [76.8, 77.3] },
      { name: 'Mumbai', state: 'Maharashtra', lat: [18.9, 19.3], lng: [72.7, 73.0] },
      { name: 'Bangalore', state: 'Karnataka', lat: [12.8, 13.1], lng: [77.4, 77.8] },
      { name: 'Chennai', state: 'Tamil Nadu', lat: [12.8, 13.2], lng: [80.1, 80.3] },
      { name: 'Hyderabad', state: 'Telangana', lat: [17.3, 17.5], lng: [78.3, 78.6] },
      { name: 'Pune', state: 'Maharashtra', lat: [18.4, 18.6], lng: [73.7, 74.0] },
      { name: 'Kolkata', state: 'West Bengal', lat: [22.4, 22.7], lng: [88.2, 88.5] }
    ]

    for (const city of cityRanges) {
      if (latitude >= city.lat[0]! && latitude <= city.lat[1]! &&
          longitude >= city.lng[0]! && longitude <= city.lng[1]!) {
        return {
          formatted_address: `${city.name}, ${city.state}, India`,
          city: city.name,
          state: city.state,
          country: 'India'
        }
      }
    }

    // Default fallback
    return {
      formatted_address: 'India',
      country: 'India'
    }
  }

  /**
   * Generate approximate coordinates from address (placeholder)
   */
  private async generateApproximateCoordinates(address: string): Promise<Coordinates | null> {
    const addressLower = address.toLowerCase()

    // Major Indian cities with approximate coordinates
    const cityCoordinates: { [key: string]: Coordinates } = {
      'delhi': { latitude: 28.6139, longitude: 77.2090 },
      'mumbai': { latitude: 19.0760, longitude: 72.8777 },
      'bangalore': { latitude: 12.9716, longitude: 77.5946 },
      'chennai': { latitude: 13.0827, longitude: 80.2707 },
      'hyderabad': { latitude: 17.3850, longitude: 78.4867 },
      'pune': { latitude: 18.5204, longitude: 73.8567 },
      'kolkata': { latitude: 22.5726, longitude: 88.3639 },
      'ahmedabad': { latitude: 23.0225, longitude: 72.5714 },
      'jaipur': { latitude: 26.9124, longitude: 75.7873 },
      'surat': { latitude: 21.1702, longitude: 72.8311 }
    }

    for (const [city, coords] of Object.entries(cityCoordinates)) {
      if (addressLower.includes(city)) {
        return coords
      }
    }

    return null
  }

  /**
   * Get landmark suggestions for a given area
   */
  async getLandmarkSuggestions(coordinates: Coordinates): Promise<string[]> {
    try {
      // Placeholder implementation
      // In production, this would use Google Places API or similar
      const commonLandmarks = [
        'Metro Station',
        'Bus Stop',
        'Shopping Mall',
        'Hospital',
        'School',
        'Temple',
        'Market',
        'Park',
        'Bank',
        'Post Office'
      ]

      return commonLandmarks.slice(0, 5) // Return top 5 suggestions
    } catch (error) {
      logger.error('Landmark suggestions error:', error)
      return []
    }
  }

  /**
   * Validate if location is accessible for job posting
   */
  validateJobLocation(coordinates: Coordinates, address: string): { isValid: boolean; warnings?: string[] } {
    try {
      const warnings: string[] = []

      // Check coordinate validation
      const coordValidation = this.validateCoordinates(coordinates.latitude, coordinates.longitude)
      if (!coordValidation.isValid) {
        return { isValid: false }
      }

      // Check address validation
      const addressValidation = this.validateAddress(address)
      if (!addressValidation.isValid) {
        return { isValid: false }
      }

      // Add warnings for remote locations
      // This is a simplified check - in production, use more sophisticated location data
      if (this.isRemoteLocation(coordinates)) {
        warnings.push('This location appears to be in a remote area. Workers may have difficulty reaching this location.')
      }

      return { isValid: true, warnings }
    } catch (error) {
      logger.error('Job location validation error:', error)
      return { isValid: false }
    }
  }

  /**
   * Check if location is in a remote area (simplified implementation)
   */
  private isRemoteLocation(coordinates: Coordinates): boolean {
    // Major urban centers in India (simplified list)
    const urbanCenters = [
      { lat: 28.6139, lng: 77.2090, radius: 50 }, // Delhi NCR
      { lat: 19.0760, lng: 72.8777, radius: 40 }, // Mumbai
      { lat: 12.9716, lng: 77.5946, radius: 30 }, // Bangalore
      { lat: 13.0827, lng: 80.2707, radius: 25 }, // Chennai
      { lat: 17.3850, lng: 78.4867, radius: 25 }, // Hyderabad
      { lat: 18.5204, lng: 73.8567, radius: 20 }, // Pune
      { lat: 22.5726, lng: 88.3639, radius: 25 }  // Kolkata
    ]

    for (const center of urbanCenters) {
      const distance = this.calculateDistance(
        coordinates,
        { latitude: center.lat, longitude: center.lng }
      )

      if (distance.distance_km <= center.radius) {
        return false // Not remote
      }
    }

    return true // Remote location
  }
}

export default new LocationService()