export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  error?: string
  code?: string
  details?: any
}

// Common error codes used across the API
export type ApiErrorCode =
  | 'INVALID_PHONE'
  | 'RATE_LIMIT_EXCEEDED'
  | 'RATE_LIMIT_ERROR'
  | 'SMS_SEND_FAILED'
  | 'OTP_NOT_FOUND'
  | 'OTP_EXPIRED'
  | 'OTP_ATTEMPTS_EXCEEDED'
  | 'INVALID_OTP'
  | 'USER_CREATE_FAILED'
  | 'REFRESH_TOKEN_MISSING'
  | 'REFRESH_TOKEN_INVALID'
  | 'USER_NOT_FOUND'
  | 'AUTH_REQUIRED'
  | 'VALIDATION_ERROR'
  | 'PROFILE_UPDATE_FAILED'
  | 'PROFILE_COMPLETION_FAILED'
  | 'NO_IMAGE_PROVIDED'
  | 'UPLOAD_FAILED'
  | 'NO_IMAGE_TO_DELETE'
  | 'ACCESS_DENIED'
  | 'INTERNAL_ERROR'
  | 'TOKEN_MISSING'
  | 'TOKEN_EXPIRED'
  | 'TOKEN_INVALID'
  | 'AUTH_ERROR'
  | 'INSUFFICIENT_PERMISSIONS'
  | 'RESOURCE_ACCESS_DENIED'
  | 'INVALID_FILE_TYPE'

// Success response helper
export interface SuccessResponse<T = any> extends ApiResponse<T> {
  success: true
  data: T
}

// Error response helper
export interface ErrorResponse extends ApiResponse<never> {
  success: false
  error: string
  code: ApiErrorCode
}
