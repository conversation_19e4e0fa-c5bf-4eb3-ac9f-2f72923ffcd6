// Re-export skill types from the shared types package
import * as sharedTypes from '@ozgaar/types';

// Export all shared types
export * from '@ozgaar/types';

// API Request/Response types (specific to backend)
export interface GetSkillCategoriesResponse {
  success: boolean;
  data?: sharedTypes.SkillCategoryData[];
  message?: string;
  error?: string;
  code?: string;
}

export interface GetSkillSubcategoriesResponse {
  success: boolean;
  data?: sharedTypes.SkillSubcategoryData[];
  message?: string;
  error?: string;
  code?: string;
}

export interface GetSkillCategoriesWithSubcategoriesResponse {
  success: boolean;
  data?: sharedTypes.SkillCategoryWithSubcategories[];
  message?: string;
  error?: string;
  code?: string;
}

// Search and filter types
export interface SkillSearchFilters {
  category_code?: string;
  is_active?: boolean;
  search_query?: string;
}

// Database query options
export interface SkillQueryOptions {
  include_inactive?: boolean;
  order_by?: 'display_order' | 'name' | 'created_at';
  order_direction?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

import { SkillCategoryEnum } from '@ozgaar/types';

export type SkillCategoryCode = SkillCategoryEnum;

export const SKILL_CATEGORY_CODES: SkillCategoryCode[] = [
  'electrical',
  'plumbing',
  'carpentry',
  'cooking',
  'cleaning',
  'driving',
  'delivery',
  'security',
  'gardening',
  'tutoring'
];

// Validation helpers
export const isValidSkillCategoryCode = (code: string): code is SkillCategoryCode => {
  return SKILL_CATEGORY_CODES.includes(code as any);
};
