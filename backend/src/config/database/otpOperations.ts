import { DatabaseClient } from './client'
import { SmsLog } from '@ozgaar/types';
import logger from '../../utils/logger'

export class OtpOperations {
  private client: DatabaseClient

  constructor(client: DatabaseClient) {
    this.client = client
  }

  // OTP attempts operations
  public async handleOtpRateLimit(phone: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .rpc('handle_otp_rate_limit', { phone_number: phone })

      return { data, error }
    } catch (error) {
      logger.error('Handle OTP rate limit error:', error)
      return { data: null, error }
    }
  }

  // SMS logs operations
  public async createSmsLog(smsData: Partial<SmsLog>): Promise<{ data: SmsLog | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('sms_logs')
        .insert(smsData)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Create SMS log error:', error)
      return { data: null, error }
    }
  }
}
