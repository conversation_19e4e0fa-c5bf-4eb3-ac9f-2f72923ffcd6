import { DatabaseClient } from './client'
import { User } from '@ozgaar/types';
import logger from '../../utils/logger'

export class UserOperations {
  private client: DatabaseClient

  constructor(client: DatabaseClient) {
    this.client = client
  }

  // User operations
  public async createUser(userData: Partial<User>): Promise<{ data: User | null; error: any }> {
    try {
      // Ensure default values for switchable user type flags
      const userDataWithDefaults = {
        is_worker: true,
        is_poster: false,
        ...userData
      };

      const { data, error } = await this.client.getClient()
        .from('users')
        .insert(userDataWithDefaults)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Create user error:', error)
      return { data: null, error }
    }
  }

  public async getUserById(userId: string): Promise<{ data: User | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Get user by ID error:', error)
      return { data: null, error }
    }
  }

  public async getUserByPhone(phone: string): Promise<{ data: User | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('users')
        .select('*')
        .eq('phone', phone)
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Get user by phone error:', error)
      return { data: null, error }
    }
  }

  public async updateUser(userId: string, userData: Partial<User>): Promise<{ data: User | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('users')
        .update({ ...userData, updated_at: new Date().toISOString() })
        .eq('id', userId)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Update user error:', error)
      return { data: null, error }
    }
  }

  public async updateWorkerProfile(userId: string, profileData: Partial<User>): Promise<{ data: User | null; error: any }> {
    try {
      // Prepare update data with location handling
      const updateData: any = { ...profileData, updated_at: new Date().toISOString() }

      // Handle location coordinates if provided
      if (profileData.location) {
        const { latitude, longitude } = profileData.location
        updateData.location = `POINT(${longitude} ${latitude})`
      }

      // Handle switchable user type flags
      if (profileData.is_worker !== undefined) {
        updateData.is_worker = profileData.is_worker;
      }
      
      if (profileData.is_poster !== undefined) {
        updateData.is_poster = profileData.is_poster;
      }

      const { data, error } = await this.client.getClient()
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Update worker profile error:', error)
      return { data: null, error }
    }
  }

  // Delete user (for testing purposes)
  public async deleteUser(userId: string): Promise<{ data: User | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('users')
        .delete()
        .eq('id', userId)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Delete user error:', error)
      return { data: null, error }
    }
  }
}
