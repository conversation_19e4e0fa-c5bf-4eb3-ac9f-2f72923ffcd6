import { DatabaseClient } from './client'
import logger from '../../utils/logger'

export class JobOperations {
  private client: DatabaseClient

  constructor(client: DatabaseClient) {
    this.client = client
  }

  // =====================================================
  // JOB OPERATIONS
  // =====================================================

  // Create a new job
  public async createJob(jobData: any): Promise<{ data: any | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('jobs')
        .insert(jobData)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Create job error:', error)
      return { data: null, error }
    }
  }

  // Get jobs with filtering and location-based search
  public async getJobs(filterOptions: any): Promise<{ data: any[] | null; error: any }> {
    try {
      // If location is provided, use PostGIS distance-based query
      if (filterOptions.location) {
        return this.getJobsWithLocationFiltering(filterOptions)
      }

      // Standard query without location filtering
      let query = this.client.getClient()
        .from('jobs')
        .select(`
          *,
          users!jobs_poster_id_fkey(full_name, profile_image_url)
        `)

      // Apply status filter
      if (filterOptions.status) {
        query = query.eq('status', filterOptions.status)
      }

      // Apply skill category filter
      if (filterOptions.skill_category) {
        query = query.eq('skill_category', filterOptions.skill_category)
      }

      // Apply urgency filter
      if (filterOptions.urgency) {
        query = query.eq('urgency', filterOptions.urgency)
      }

      // Apply sorting
      const sortBy = filterOptions.sort_by || 'created_at'
      const sortOrder = filterOptions.sort_order === 'asc' ? { ascending: true } : { ascending: false }
      query = query.order(sortBy, sortOrder)

      // Apply pagination
      if (filterOptions.limit) {
        query = query.limit(filterOptions.limit)
      }
      if (filterOptions.offset) {
        query = query.range(filterOptions.offset, filterOptions.offset + (filterOptions.limit || 20) - 1)
      }

      const { data, error } = await query

      return { data, error }
    } catch (error) {
      logger.error('Get jobs error:', error)
      return { data: null, error }
    }
  }

  // Enhanced location-based job search with PostGIS distance queries
  private async getJobsWithLocationFiltering(filterOptions: any): Promise<{ data: any[] | null; error: any }> {
    try {
      const { latitude, longitude, radius_km } = filterOptions.location

      // Use the PostGIS function for efficient location-based search
      const { data, error } = await this.client.getClient().rpc('search_jobs_by_location', {
        search_latitude: latitude,
        search_longitude: longitude,
        radius_km: radius_km || 25,
        skill_category_filter: filterOptions.skill_category || null,
        urgency_filter: filterOptions.urgency || null,
        status_filter: filterOptions.status || 'active',
        limit_count: filterOptions.limit || 20,
        offset_count: filterOptions.offset || 0
      })

      if (error) {
        // Fallback to standard query if PostGIS function fails
        logger.warn('PostGIS function failed, falling back to application-level filtering:', error)
        return this.getJobsWithFallbackLocationFiltering(filterOptions)
      }

      // Transform the data to match expected format
      const transformedData = data?.map((job: any) => ({
        ...job,
        skill_category: job.skill_category,
        job_type: job.job_type,
        urgency: job.urgency,
        preferred_gender: job.preferred_gender,
        status: job.status,
        location: job.location_data,
        users: {
          full_name: job.poster_full_name,
          profile_image_url: job.poster_profile_image_url
        }
      }))

      return { data: transformedData, error: null }
    } catch (error) {
      logger.error('Location-based job search error:', error)
      // Fallback to application-level filtering
      return this.getJobsWithFallbackLocationFiltering(filterOptions)
    }
  }

  // Fallback location filtering using application-level distance calculation
  private async getJobsWithFallbackLocationFiltering(filterOptions: any): Promise<{ data: any[] | null; error: any }> {
    try {
      const { latitude, longitude, radius_km } = filterOptions.location

      // Get all jobs without location filtering first
      let query = this.client.getClient()
        .from('jobs')
        .select(`
          *,
          users!jobs_poster_id_fkey(full_name, profile_image_url)
        `)

      // Apply status filter
      if (filterOptions.status) {
        query = query.eq('status', filterOptions.status)
      }

      // Apply skill category filter
      if (filterOptions.skill_category) {
        query = query.eq('skill_category', filterOptions.skill_category)
      }

      // Apply urgency filter
      if (filterOptions.urgency) {
        query = query.eq('urgency', filterOptions.urgency)
      }

      // Add expiry filter
      query = query.gte('expires_at', new Date().toISOString())

      // Get more results to filter by distance
      const fetchLimit = (filterOptions.limit || 20) * 3
      query = query.limit(fetchLimit)

      if (filterOptions.offset) {
        query = query.range(filterOptions.offset, filterOptions.offset + fetchLimit - 1)
      }

      const { data: jobs, error } = await query

      if (error || !jobs) {
        return { data: null, error }
      }

      // Filter and sort by distance using application logic
      const jobsWithDistance = jobs
        .map((job: any) => {
          // Extract coordinates from PostGIS location if available
          let jobLat, jobLng

          if (job.location && typeof job.location === 'object') {
            // Handle PostGIS geometry object
            jobLat = job.location.coordinates?.[1] || job.latitude
            jobLng = job.location.coordinates?.[0] || job.longitude
          } else {
            // Fallback to separate lat/lng columns if they exist
            jobLat = job.latitude
            jobLng = job.longitude
          }

          if (!jobLat || !jobLng) {
            return null // Skip jobs without valid coordinates
          }

          // Calculate distance using Haversine formula
          const distance = this.calculateHaversineDistance(
            latitude, longitude, jobLat, jobLng
          )

          return {
            ...job,
            distance_km: Math.round(distance * 100) / 100, // Round to 2 decimal places
            poster_full_name: job.users?.full_name,
            poster_profile_image_url: job.users?.profile_image_url
          }
        })
        .filter((job: any) => job && job.distance_km <= radius_km) // Filter by radius
        .sort((a: any, b: any) => a.distance_km - b.distance_km) // Sort by distance
        .slice(0, filterOptions.limit || 20) // Apply final limit

      return { data: jobsWithDistance, error: null }
    } catch (error) {
      logger.error('Fallback location filtering error:', error)
      return { data: null, error }
    }
  }

  // Haversine distance calculation for fallback
  private calculateHaversineDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371 // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1)
    const dLon = this.toRadians(lon2 - lon1)

    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2)

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  // Get job by ID
  public async getJobById(jobId: string): Promise<{ data: any | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('jobs')
        .select(`
          *,
          users!jobs_poster_id_fkey(full_name, profile_image_url, phone)
        `)
        .eq('id', jobId)
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Get job by ID error:', error)
      return { data: null, error }
    }
  }

  // Update job
  public async updateJob(jobId: string, updateData: any): Promise<{ data: any | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('jobs')
        .update(updateData)
        .eq('id', jobId)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Update job error:', error)
      return { data: null, error }
    }
  }

  // Delete job
  public async deleteJob(jobId: string): Promise<{ data: any | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('jobs')
        .delete()
        .eq('id', jobId)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Delete job error:', error)
      return { data: null, error }
    }
  }

  // Increment job view count
  public async incrementJobViews(jobId: string): Promise<{ data: any | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .rpc('increment_job_views', { job_id: jobId })

      return { data, error }
    } catch (error) {
      logger.error('Increment job views error:', error)
      return { data: null, error }
    }
  }

  // Get jobs by poster ID
  public async getJobsByPosterId(posterId: string): Promise<{ data: any[] | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('jobs')
        .select('*')
        .eq('poster_id', posterId)
        .order('created_at', { ascending: false })

      return { data, error }
    } catch (error) {
      logger.error('Get jobs by poster ID error:', error)
      return { data: null, error }
    }
  }
}
