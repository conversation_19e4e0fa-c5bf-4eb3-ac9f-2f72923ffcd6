import { createClient, SupabaseClient } from '@supabase/supabase-js'
import config from '../index'
import logger from '../../utils/logger'

// Base Database class with connection management
export class DatabaseClient {
  private client: SupabaseClient
  private static instance: DatabaseClient

  private constructor() {
    this.client = createClient(
      config.supabase.url,
      config.supabase.serviceRoleKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
        db: {
          schema: 'public',
        },
      }
    )

    logger.info('Database connection initialized')
  }

  public static getInstance(): DatabaseClient {
    if (!DatabaseClient.instance) {
      DatabaseClient.instance = new DatabaseClient()
    }
    return DatabaseClient.instance
  }

  public getClient(): SupabaseClient {
    return this.client
  }

  // Health check method
  public async healthCheck(): Promise<boolean> {
    try {
      const { error } = await this.client
        .from('users')
        .select('count')
        .limit(1)

      if (error) {
        logger.error('Database health check failed:', error)
        return false
      }

      return true
    } catch (error) {
      logger.error('Database health check error:', error)
      return false
    }
  }
}
