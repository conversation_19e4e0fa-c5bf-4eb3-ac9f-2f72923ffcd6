import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

interface Config {
  server: {
    port: number
    host: string
    env: string
    trustProxy: boolean
  }
  supabase: {
    url: string
    serviceRoleKey: string
    anonKey: string
  }
  jwt: {
    secret: string
    expiresIn: string
    refreshExpiresIn: string
  }
  twoFactor: {
    apiKey: string
  }
  rateLimit: {
    windowMs: number
    maxRequests: number
    otpAttempts: number
    otpWindowHours: number
  }
  regional: {
    defaultCountryCode: string
    supportedLanguages: string[]
  }
  security: {
    bcryptRounds: number
    corsOrigin: string
  }
  logging: {
    level: string
    format: string
  }
  database: {
    poolMin: number
    poolMax: number
    timeout: number
  }
}

const config: Config = {
  server: {
    port: parseInt(process.env['PORT'] || '3000', 10),
    host: process.env['HOST'] || 'localhost',
    env: process.env['NODE_ENV'] || 'development',
    trustProxy: process.env['TRUST_PROXY'] === 'true',
  },
  supabase: {
    url: process.env['SUPABASE_URL'] || '',
    serviceRoleKey: process.env['SUPABASE_SERVICE_ROLE_KEY'] || '',
    anonKey: process.env['SUPABASE_ANON_KEY'] || '',
  },
  jwt: {
    secret: process.env['JWT_SECRET'] || 'fallback-secret-key',
    expiresIn: process.env['JWT_EXPIRES_IN'] || '7d',
    refreshExpiresIn: process.env['JWT_REFRESH_EXPIRES_IN'] || '30d',
  },
  twoFactor: {
    apiKey: process.env['TWOFACTOR_API_KEY'] || '',
  },
  rateLimit: {
    windowMs: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '900000', 10), // 15 minutes
    maxRequests: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '100', 10),
    otpAttempts: parseInt(process.env['OTP_RATE_LIMIT_ATTEMPTS'] || '3', 10),
    otpWindowHours: parseInt(process.env['OTP_RATE_LIMIT_WINDOW_HOURS'] || '1', 10),
  },
  regional: {
    defaultCountryCode: process.env['DEFAULT_COUNTRY_CODE'] || '+91',
    supportedLanguages: (process.env['SUPPORTED_LANGUAGES'] || 'hindi,english').split(','),
  },
  security: {
    bcryptRounds: parseInt(process.env['BCRYPT_ROUNDS'] || '12', 10),
    corsOrigin: process.env['CORS_ORIGIN'] || '*',
  },
  logging: {
    level: process.env['LOG_LEVEL'] || 'info',
    format: process.env['LOG_FORMAT'] || 'combined',
  },
  database: {
    poolMin: parseInt(process.env['DB_POOL_MIN'] || '2', 10),
    poolMax: parseInt(process.env['DB_POOL_MAX'] || '10', 10),
    timeout: parseInt(process.env['DB_TIMEOUT'] || '30000', 10),
  },
}

// Validate required configuration
const requiredEnvVars = [
  'SUPABASE_URL',
  'SUPABASE_SERVICE_ROLE_KEY',
  'JWT_SECRET',
]

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar])

if (missingEnvVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`)
}

export default config
