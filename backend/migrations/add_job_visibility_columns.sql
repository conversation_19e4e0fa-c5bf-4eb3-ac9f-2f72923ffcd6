-- Add job visibility columns for the job visibility system
-- This migration adds columns needed for job indexing and visibility tracking

-- Add visibility-related columns to jobs table
ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS search_keywords JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS visibility_score DECIMAL(10,2) DEFAULT 100.0,
ADD COLUMN IF NOT EXISTS indexed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS is_visible BOOLEAN DEFAULT false;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_jobs_visibility_score ON jobs(visibility_score DESC);
CREATE INDEX IF NOT EXISTS idx_jobs_indexed_at ON jobs(indexed_at);
CREATE INDEX IF NOT EXISTS idx_jobs_is_visible ON jobs(is_visible);
CREATE INDEX IF NOT EXISTS idx_jobs_search_keywords ON jobs USING GIN(search_keywords);
CREATE INDEX IF NOT EXISTS idx_jobs_created_at_visible ON jobs(created_at, is_visible);

-- Create index for visibility metrics query
CREATE INDEX IF NOT EXISTS idx_jobs_visibility_metrics ON jobs(created_at, is_visible, indexed_at);

-- Update existing jobs to be visible (if any exist)
UPDATE jobs
SET is_visible = true,
    indexed_at = created_at,
    visibility_score = 100.0,
    search_keywords = '[]'
WHERE is_visible IS NULL OR is_visible = false;

-- Add comments for documentation
COMMENT ON COLUMN jobs.search_keywords IS 'JSON array of keywords for search indexing';
COMMENT ON COLUMN jobs.visibility_score IS 'Score used for ranking jobs in search results (higher = more visible)';
COMMENT ON COLUMN jobs.indexed_at IS 'Timestamp when job was indexed for search';
COMMENT ON COLUMN jobs.is_visible IS 'Whether job is visible to workers in search results';