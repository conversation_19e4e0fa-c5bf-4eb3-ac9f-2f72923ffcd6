-- =====================================================
-- FIX OTP RATE LIMIT FUNCTION
-- =====================================================
-- This migration fixes the type mismatch error in the handle_otp_rate_limit function
-- that was causing the "operator does not exist: timestamp with time zone > time with time zone" error
-- =====================================================

-- Drop and recreate the function with proper type handling
DROP FUNCTION IF EXISTS handle_otp_rate_limit(TEXT);

CREATE OR REPLACE FUNCTION handle_otp_rate_limit(phone_number TEXT)
RETURNS JSONB AS $$
DECLARE
  attempt_record RECORD;
  current_time TIMESTAMP WITH TIME ZONE := NOW();
  rate_limit_window INTERVAL := '1 hour';
  max_attempts INTEGER := 3;
  block_duration INTERVAL := '1 hour';
BEGIN
  -- Get or create attempt record
  SELECT * INTO attempt_record
  FROM otp_attempts
  WHERE phone = phone_number
  ORDER BY created_at DESC
  LIMIT 1;

  -- Check if phone is currently blocked
  IF attempt_record.blocked_until IS NOT NULL AND attempt_record.blocked_until > current_time THEN
    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'blocked',
      'blocked_until', attempt_record.blocked_until,
      'remaining_attempts', 0
    );
  END IF;

  -- Reset attempts if window has passed
  IF attempt_record.last_attempt_at IS NULL OR 
     (current_time - attempt_record.last_attempt_at) > rate_limit_window THEN

    INSERT INTO otp_attempts (phone, attempts_count, last_attempt_at)
    VALUES (phone_number, 1, current_time)
    ON CONFLICT (phone) DO UPDATE SET
      attempts_count = 1,
      last_attempt_at = current_time,
      blocked_until = NULL;

    RETURN jsonb_build_object(
      'allowed', true,
      'remaining_attempts', max_attempts - 1
    );
  END IF;

  -- Check if max attempts reached
  IF attempt_record.attempts_count >= max_attempts THEN
    UPDATE otp_attempts
    SET blocked_until = current_time + block_duration,
        last_attempt_at = current_time
    WHERE phone = phone_number;

    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'rate_limited',
      'blocked_until', current_time + block_duration,
      'remaining_attempts', 0
    );
  END IF;

  -- Increment attempt count
  UPDATE otp_attempts
  SET attempts_count = attempts_count + 1,
      last_attempt_at = current_time
  WHERE phone = phone_number;

  RETURN jsonb_build_object(
    'allowed', true,
    'remaining_attempts', max_attempts - (attempt_record.attempts_count + 1)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add unique constraint to phone column for ON CONFLICT to work
ALTER TABLE otp_attempts ADD CONSTRAINT IF NOT EXISTS otp_attempts_phone_unique UNIQUE (phone);

-- =====================================================
-- FIX COMPLETE
-- =====================================================
-- Changes made:
-- 1. Fixed type handling in handle_otp_rate_limit function
-- 2. Ensured all timestamp comparisons use TIMESTAMP WITH TIME ZONE
-- 3. Added unique constraint to phone column for ON CONFLICT to work properly
-- =====================================================