-- Add job performance tracking tables
-- This migration creates tables for tracking job views, applications, and performance metrics

-- Job views tracking table
CREATE TABLE IF NOT EXISTS job_views (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    viewer_id UUID, -- Can be null for anonymous views
    viewer_type VARCHAR(20) NOT NULL CHECK (viewer_type IN ('worker', 'anonymous')),
    source VARCHAR(50) NOT NULL CHECK (source IN ('search', 'direct', 'recommendation', 'notification')),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    platform VARCHAR(50),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Job applications tracking table (separate from actual applications for analytics)
CREATE TABLE IF NOT EXISTS job_applications_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    applicant_id UUID NOT NULL,
    application_id UUID NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    application_quality VARCHAR(20) NOT NULL CHECK (application_quality IN ('high', 'medium', 'low')),
    response_time_hours DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Job performance metrics table (aggregated data)
CREATE TABLE IF NOT EXISTS job_performance_metrics (
    job_id UUID PRIMARY KEY REFERENCES jobs(id) ON DELETE CASCADE,
    views INTEGER NOT NULL DEFAULT 0,
    applications INTEGER NOT NULL DEFAULT 0,
    time_to_fill DECIMAL(10, 2), -- in hours
    average_response_time DECIMAL(10, 2) NOT NULL DEFAULT 0, -- in hours
    conversion_rate DECIMAL(5, 2) NOT NULL DEFAULT 0, -- percentage
    quality_score DECIMAL(3, 2) NOT NULL DEFAULT 1, -- 1-3 scale
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add status and filled_at columns to jobs table if they don't exist
ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'paused', 'filled', 'expired', 'cancelled')),
ADD COLUMN IF NOT EXISTS filled_at TIMESTAMP WITH TIME ZONE;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_job_views_job_id ON job_views(job_id);
CREATE INDEX IF NOT EXISTS idx_job_views_timestamp ON job_views(timestamp);
CREATE INDEX IF NOT EXISTS idx_job_views_viewer_type ON job_views(viewer_type);
CREATE INDEX IF NOT EXISTS idx_job_views_source ON job_views(source);

CREATE INDEX IF NOT EXISTS idx_job_applications_tracking_job_id ON job_applications_tracking(job_id);
CREATE INDEX IF NOT EXISTS idx_job_applications_tracking_timestamp ON job_applications_tracking(timestamp);
CREATE INDEX IF NOT EXISTS idx_job_applications_tracking_applicant_id ON job_applications_tracking(applicant_id);

CREATE INDEX IF NOT EXISTS idx_job_performance_metrics_views ON job_performance_metrics(views);
CREATE INDEX IF NOT EXISTS idx_job_performance_metrics_conversion_rate ON job_performance_metrics(conversion_rate);
CREATE INDEX IF NOT EXISTS idx_job_performance_metrics_quality_score ON job_performance_metrics(quality_score);
CREATE INDEX IF NOT EXISTS idx_job_performance_metrics_last_updated ON job_performance_metrics(last_updated);

CREATE INDEX IF NOT EXISTS idx_jobs_status ON jobs(status);
CREATE INDEX IF NOT EXISTS idx_jobs_poster_id_created_at ON jobs(poster_id, created_at);
CREATE INDEX IF NOT EXISTS idx_jobs_poster_id_status ON jobs(poster_id, status);

-- Add comments for documentation
COMMENT ON TABLE job_views IS 'Tracks individual job view events for analytics';
COMMENT ON TABLE job_applications_tracking IS 'Tracks job application events for performance metrics';
COMMENT ON TABLE job_performance_metrics IS 'Aggregated performance metrics for each job';

COMMENT ON COLUMN job_views.viewer_type IS 'Type of viewer: worker (logged in) or anonymous';
COMMENT ON COLUMN job_views.source IS 'How the viewer found the job: search, direct link, recommendation, or notification';
COMMENT ON COLUMN job_applications_tracking.application_quality IS 'Quality assessment of the application: high, medium, or low';
COMMENT ON COLUMN job_applications_tracking.response_time_hours IS 'Hours between job posting and application submission';
COMMENT ON COLUMN job_performance_metrics.conversion_rate IS 'Percentage of views that resulted in applications';
COMMENT ON COLUMN job_performance_metrics.quality_score IS 'Average quality score of applications (1-3 scale)';
COMMENT ON COLUMN jobs.status IS 'Current status of the job posting';
COMMENT ON COLUMN jobs.filled_at IS 'Timestamp when the job was marked as filled';