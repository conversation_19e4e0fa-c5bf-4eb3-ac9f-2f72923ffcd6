-- Add PostGIS function for location-based job search
-- This migration creates a function to efficiently search jobs by location with distance calculation

-- Function to search jobs within a radius with distance calculation
CREATE OR REPLACE FUNCTION search_jobs_by_location(
  search_latitude DECIMAL,
  search_longitude DECIMAL,
  radius_km INTEGER DEFAULT 25,
  skill_category_filter TEXT DEFAULT NULL,
  urgency_filter TEXT DEFAULT NULL,
  status_filter TEXT DEFAULT 'active',
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  poster_id UUID,
  title VARCHAR(200),
  description TEXT,
  skill_category TEXT,
  location_data GEOGRAPHY,
  address TEXT,
  landmark TEXT,
  job_type TEXT,
  urgency TEXT,
  budget_min DECIMAL(10,2),
  budget_max DECIMAL(10,2),
  estimated_duration_hours INTEGER,
  requirements TEXT,
  preferred_gender TEXT,
  min_experience_years INTEGER,
  min_rating DECIMAL(3,2),
  status TEXT,
  applications_count INTEGER,
  views_count INTEGER,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  poster_full_name VARCHAR(100),
  poster_profile_image_url TEXT,
  distance_km DECIMAL(10,2)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    j.id,
    j.poster_id,
    j.title,
    j.description,
    j.skill_category::TEXT,
    j.location as location_data,
    j.address,
    j.landmark,
    j.job_type::TEXT,
    j.urgency::TEXT,
    j.budget_min,
    j.budget_max,
    j.estimated_duration_hours,
    j.requirements,
    j.preferred_gender::TEXT,
    j.min_experience_years,
    j.min_rating,
    j.status::TEXT,
    j.applications_count,
    j.views_count,
    j.expires_at,
    j.created_at,
    j.updated_at,
    u.full_name as poster_full_name,
    u.profile_image_url as poster_profile_image_url,
    ROUND(
      (ST_Distance(j.location, ST_SetSRID(ST_Point(search_longitude, search_latitude), 4326)) / 1000.0)::numeric, 
      2
    ) as distance_km
  FROM jobs j
  JOIN users u ON j.poster_id = u.id
  WHERE j.status = status_filter
    AND j.expires_at > NOW()
    AND ST_DWithin(j.location, ST_SetSRID(ST_Point(search_longitude, search_latitude), 4326), radius_km * 1000)
    AND (skill_category_filter IS NULL OR j.skill_category::TEXT = skill_category_filter)
    AND (urgency_filter IS NULL OR j.urgency::TEXT = urgency_filter)
  ORDER BY
    -- Primary sort: Distance (closest first)
    distance_km ASC,
    -- Secondary sort: Urgency priority (urgent > high > normal > low)
    CASE j.urgency
      WHEN 'urgent' THEN 1
      WHEN 'high' THEN 2
      WHEN 'normal' THEN 3
      WHEN 'low' THEN 4
      ELSE 5
    END ASC,
    -- Tertiary sort: Most recent first
    j.created_at DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- Create index for better performance on location-based queries
CREATE INDEX IF NOT EXISTS idx_jobs_location_status ON jobs USING GIST (location) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_jobs_expires_at_status ON jobs (expires_at, status) WHERE status = 'active';

-- Add comments for documentation
COMMENT ON FUNCTION search_jobs_by_location IS 'Search jobs within a radius with distance calculation and sorting';
