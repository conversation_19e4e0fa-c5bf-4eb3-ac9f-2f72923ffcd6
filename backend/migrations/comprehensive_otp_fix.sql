-- =====================================================
-- COMPREHENSIVE DATABASE FIX FOR OTP RATE LIMITING
-- =====================================================
-- This migration fixes all issues related to OTP rate limiting and timestamp handling
-- =====================================================

-- First, ensure the otp_attempts table has the correct structure
ALTER TABLE otp_attempts ALTER COLUMN last_attempt_at TYPE TIMESTAMP WITH TIME ZONE;
ALTER TABLE otp_attempts ALTER COLUMN blocked_until TYPE TIMESTAMP WITH TIME ZONE;
ALTER TABLE otp_attempts ALTER COLUMN created_at TYPE TIMESTAMP WITH TIME ZONE;

-- Add unique constraint to phone column for ON CONFLICT to work
ALTER TABLE otp_attempts ADD CONSTRAINT IF NOT EXISTS otp_attempts_phone_unique UNIQUE (phone);

-- Drop and recreate the function with proper type handling
DROP FUNCTION IF EXISTS handle_otp_rate_limit(TEXT);

CREATE OR REPLACE FUNCTION handle_otp_rate_limit(phone_number TEXT)
RETURNS JSONB AS $$
DECLARE
  attempt_record RECORD;
  current_time TIMESTAMP WITH TIME ZONE := NOW();
  rate_limit_window INTERVAL := '1 hour';
  max_attempts INTEGER := 3;
  block_duration INTERVAL := '1 hour';
BEGIN
  -- Get or create attempt record
  SELECT * INTO attempt_record
  FROM otp_attempts
  WHERE phone = phone_number
  ORDER BY created_at DESC
  LIMIT 1;

  -- Check if phone is currently blocked
  IF attempt_record.blocked_until IS NOT NULL AND attempt_record.blocked_until > current_time THEN
    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'blocked',
      'blocked_until', attempt_record.blocked_until,
      'remaining_attempts', 0
    );
  END IF;

  -- Reset attempts if window has passed
  IF attempt_record.last_attempt_at IS NULL OR 
     (current_time - attempt_record.last_attempt_at) > rate_limit_window THEN

    INSERT INTO otp_attempts (phone, attempts_count, last_attempt_at)
    VALUES (phone_number, 1, current_time)
    ON CONFLICT (phone) DO UPDATE SET
      attempts_count = 1,
      last_attempt_at = EXCLUDED.last_attempt_at,
      blocked_until = NULL;

    RETURN jsonb_build_object(
      'allowed', true,
      'remaining_attempts', max_attempts - 1
    );
  END IF;

  -- Check if max attempts reached
  IF attempt_record.attempts_count >= max_attempts THEN
    UPDATE otp_attempts
    SET blocked_until = current_time + block_duration,
        last_attempt_at = current_time
    WHERE phone = phone_number;

    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'rate_limited',
      'blocked_until', current_time + block_duration,
      'remaining_attempts', 0
    );
  END IF;

  -- Increment attempt count
  UPDATE otp_attempts
  SET attempts_count = attempts_count + 1,
      last_attempt_at = current_time
  WHERE phone = phone_number;

  RETURN jsonb_build_object(
    'allowed', true,
    'remaining_attempts', max_attempts - (attempt_record.attempts_count + 1)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also fix the alternative version in migrations_with_modifications.sql
DROP FUNCTION IF EXISTS handle_otp_rate_limit(TEXT);

CREATE OR REPLACE FUNCTION handle_otp_rate_limit(phone_number TEXT)
RETURNS JSONB AS $$
DECLARE
  attempt_count INTEGER;
  last_attempt TIMESTAMP WITH TIME ZONE;
  blocked_until_time TIMESTAMP WITH TIME ZONE;
  current_timestamp TIMESTAMP WITH TIME ZONE := NOW();
  rate_limit_window INTERVAL := '1 hour';
  max_attempts INTEGER := 3;
  block_duration INTERVAL := '1 hour';
BEGIN
  -- Get attempt record with explicit column selection
  SELECT attempts_count, last_attempt_at, blocked_until
  INTO attempt_count, last_attempt, blocked_until_time
  FROM otp_attempts
  WHERE phone = phone_number
  ORDER BY created_at DESC
  LIMIT 1;

  -- Check if phone is currently blocked
  IF blocked_until_time IS NOT NULL AND blocked_until_time > current_timestamp THEN
    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'blocked',
      'blocked_until', blocked_until_time,
      'remaining_attempts', 0
    );
  END IF;

  -- Reset attempts if window has passed or no record exists
  IF attempt_count IS NULL OR
     last_attempt IS NULL OR
     (current_timestamp - last_attempt) > rate_limit_window THEN

    INSERT INTO otp_attempts (phone, attempts_count, last_attempt_at)
    VALUES (phone_number, 1, current_timestamp)
    ON CONFLICT (phone) DO UPDATE SET
      attempts_count = 1,
      last_attempt_at = EXCLUDED.last_attempt_at,
      blocked_until = NULL;

    RETURN jsonb_build_object(
      'allowed', true,
      'remaining_attempts', max_attempts - 1
    );
  END IF;

  -- Check if max attempts reached
  IF attempt_count >= max_attempts THEN
    UPDATE otp_attempts
    SET blocked_until = current_timestamp + block_duration,
        last_attempt_at = current_timestamp
    WHERE phone = phone_number;

    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'rate_limited',
      'blocked_until', current_timestamp + block_duration,
      'remaining_attempts', 0
    );
  END IF;

  -- Increment attempt count
  UPDATE otp_attempts
  SET attempts_count = COALESCE(attempt_count, 0) + 1,
      last_attempt_at = current_timestamp
  WHERE phone = phone_number;

  RETURN jsonb_build_object(
    'allowed', true,
    'remaining_attempts', max_attempts - (COALESCE(attempt_count, 0) + 1)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix the get_sms_stats function as well to ensure consistent timestamp handling
CREATE OR REPLACE FUNCTION get_sms_stats(
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '24 hours',
  end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS JSONB AS $$
DECLARE
  stats JSONB;
BEGIN
  SELECT jsonb_build_object(
    'total_sent', COUNT(*),
    'delivered', COUNT(*) FILTER (WHERE status = 'delivered'),
    'failed', COUNT(*) FILTER (WHERE status = 'failed'),
    'pending', COUNT(*) FILTER (WHERE status = 'sent'),
    'delivery_rate',
      CASE
        WHEN COUNT(*) > 0 THEN
          ROUND((COUNT(*) FILTER (WHERE status = 'delivered')::numeric / COUNT(*)::numeric) * 100, 2)
        ELSE 0
      END,
    'by_hour', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'hour', date_trunc('hour', created_at),
          'count', count
        ) ORDER BY hour
      )
      FROM (
        SELECT
          date_trunc('hour', created_at) as hour,
          COUNT(*) as count
        FROM sms_logs
        WHERE created_at BETWEEN start_date AND end_date
        GROUP BY date_trunc('hour', created_at)
      ) hourly_stats
    )
  ) INTO stats
  FROM sms_logs
  WHERE created_at BETWEEN start_date AND end_date;

  RETURN COALESCE(stats, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- COMPREHENSIVE FIX COMPLETE
-- =====================================================
-- Changes made:
-- 1. Ensured all timestamp columns use TIMESTAMP WITH TIME ZONE
-- 2. Fixed handle_otp_rate_limit function with proper type handling
-- 3. Added unique constraint to phone column for ON CONFLICT to work properly
-- 4. Fixed get_sms_stats function for consistent timestamp handling
-- =====================================================