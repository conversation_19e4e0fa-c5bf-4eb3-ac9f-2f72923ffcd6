-- =====================================================
-- OZGAAR DATABASE SETUP FOR SUPABASE CLOUD - COMPLETE INITIALIZATION
-- =====================================================
-- Run this script in your Supabase Cloud SQL Editor
-- This will create all tables, indexes, RLS policies, and functions
--
-- INCLUDES AUTHENTICATION FLOW FIXES:
-- - Nullable full_name for initial user creation
-- - Profile completion tracking system
-- - Helper functions for profile management
-- - Updated constraints and indexes
-- - Switchable user types (worker/poster)
-- - Skill categories and subcategories
-- - Job visibility and performance tracking
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- =====================================================
-- CUSTOM TYPES FOR INDIAN JOB MARKET
-- =====================================================

CREATE TYPE user_role_enum AS ENUM ('worker', 'poster');
CREATE TYPE supported_language_enum AS ENUM (
  'hindi', 'english', 'tamil', 'telugu', 'bengali', 
  'marathi', 'gujarati', 'kannada'
);
CREATE TYPE skill_category_enum AS ENUM (
  'electrical', 'plumbing', 'carpentry', 'cooking', 'cleaning',
  'driving', 'delivery', 'security', 'gardening', 'tutoring'
);
CREATE TYPE job_type_enum AS ENUM ('one_time', 'recurring', 'permanent');
CREATE TYPE urgency_enum AS ENUM ('low', 'normal', 'high', 'urgent');
CREATE TYPE job_status_enum AS ENUM ('active', 'paused', 'filled', 'cancelled', 'expired');
CREATE TYPE application_status_enum AS ENUM ('pending', 'accepted', 'rejected', 'withdrawn');
CREATE TYPE review_type_enum AS ENUM ('worker_review', 'poster_review');
CREATE TYPE gender_preference_enum AS ENUM ('any', 'male', 'female');

-- =====================================================
-- MAIN TABLES
-- =====================================================

-- Users table (base entity for all platform users)
-- UPDATED FOR AUTHENTICATION FLOW: full_name is nullable for initial user creation
-- UPDATED FOR SWITCHABLE USER TYPES: Added IsWorker and IsPoster flags
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone VARCHAR(15) UNIQUE NOT NULL, -- +91 format for India
    email VARCHAR(255),
    full_name VARCHAR(100), -- NULLABLE: Allows user creation without complete profile
    preferred_language supported_language_enum DEFAULT 'english',
    location GEOGRAPHY(POINT, 4326), -- PostGIS point for lat/lng
    address TEXT,
    user_type user_role_enum DEFAULT 'worker',
    is_verified BOOLEAN DEFAULT FALSE,
    profile_completed BOOLEAN DEFAULT FALSE, -- NEW: Tracks profile completion status
    profile_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- NEW: Switchable user type flags
    is_worker BOOLEAN DEFAULT TRUE,
    is_poster BOOLEAN DEFAULT FALSE,

    -- Constraints
    CONSTRAINT valid_phone CHECK (phone ~ '^\+91[6-9][0-9]{9}$'),
    CONSTRAINT valid_email CHECK (email IS NULL OR email ~ '^[^@]+@[^@]+\.[^@]+$'),
    -- NEW: Ensures profile consistency - complete profiles must have valid full_name
    CONSTRAINT check_profile_completion CHECK (
        (full_name IS NULL AND profile_completed = FALSE) OR
        (full_name IS NOT NULL AND LENGTH(TRIM(full_name)) >= 2 AND profile_completed = TRUE)
    )
);

-- Worker personas table (core multi-persona feature)
CREATE TABLE worker_personas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(100) NOT NULL, -- "Experienced Electrician"
    skill_category skill_category_enum NOT NULL,
    skill_subcategories TEXT[], -- Array of related skills
    description TEXT,
    experience_years INTEGER DEFAULT 0 CHECK (experience_years >= 0),
    
    -- Pricing structure for Indian market
    hourly_rate DECIMAL(8,2) CHECK (hourly_rate >= 0),
    daily_rate DECIMAL(8,2) CHECK (daily_rate >= 0),
    monthly_rate DECIMAL(10,2) CHECK (monthly_rate >= 0),
    is_rate_negotiable BOOLEAN DEFAULT TRUE,
    
    -- Availability and location
    availability_pattern JSONB NOT NULL DEFAULT '{}', -- Weekly schedule
    travel_radius_km INTEGER DEFAULT 10 CHECK (travel_radius_km > 0),
    
    -- Performance metrics
    total_jobs_completed INTEGER DEFAULT 0 CHECK (total_jobs_completed >= 0),
    total_earnings DECIMAL(12,2) DEFAULT 0 CHECK (total_earnings >= 0),
    average_rating DECIMAL(3,2) DEFAULT 0 CHECK (average_rating >= 0 AND average_rating <= 5),
    total_reviews INTEGER DEFAULT 0 CHECK (total_reviews >= 0),
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_date TIMESTAMP WITH TIME ZONE,
    profile_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_rating CHECK (
      average_rating = 0 OR (total_reviews > 0 AND average_rating > 0)
    )
);

-- Jobs table (job postings from employers)
CREATE TABLE jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    poster_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    skill_category skill_category_enum NOT NULL,
    
    -- Location for hyper-local matching
    location GEOGRAPHY(POINT, 4326) NOT NULL,
    address TEXT NOT NULL,
    landmark TEXT,
    
    -- Job specifications
    job_type job_type_enum DEFAULT 'one_time',
    urgency urgency_enum DEFAULT 'normal',
    budget_min DECIMAL(10,2) NOT NULL CHECK (budget_min > 0),
    budget_max DECIMAL(10,2) NOT NULL CHECK (budget_max >= budget_min),
    estimated_duration_hours INTEGER CHECK (estimated_duration_hours > 0),
    
    -- Requirements and preferences
    requirements TEXT,
    preferred_gender gender_preference_enum DEFAULT 'any',
    min_experience_years INTEGER DEFAULT 0 CHECK (min_experience_years >= 0),
    min_rating DECIMAL(3,2) DEFAULT 0 CHECK (min_rating >= 0 AND min_rating <= 5),
    
    -- Status and metrics
    status job_status_enum DEFAULT 'active',
    applications_count INTEGER DEFAULT 0 CHECK (applications_count >= 0),
    views_count INTEGER DEFAULT 0 CHECK (views_count >= 0),
    
    -- Timestamps
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Visibility and performance tracking
    search_keywords JSONB DEFAULT '[]',
    visibility_score DECIMAL(10,2) DEFAULT 100.0,
    indexed_at TIMESTAMP WITH TIME ZONE,
    is_visible BOOLEAN DEFAULT false,
    filled_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT valid_expiry CHECK (expires_at > created_at)
);

-- Job applications (connects personas to jobs)
CREATE TABLE job_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    worker_persona_id UUID NOT NULL REFERENCES worker_personas(id) ON DELETE CASCADE,
    
    -- Application details
    status application_status_enum DEFAULT 'pending',
    proposed_rate DECIMAL(8,2),
    message TEXT,
    
    -- Response tracking
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    poster_response TEXT,
    
    -- Prevent duplicate applications
    UNIQUE(job_id, worker_persona_id)
);

-- Reviews table (bidirectional trust system)
CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reviewee_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    worker_persona_id UUID REFERENCES worker_personas(id) ON DELETE CASCADE,
    
    -- Review content
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    review_type review_type_enum NOT NULL,
    
    -- Verification
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate reviews
    UNIQUE(job_id, reviewer_id, review_type),
    
    -- Ensure reviewee consistency
    CONSTRAINT valid_reviewee CHECK (
      (review_type = 'worker_review' AND worker_persona_id IS NOT NULL) OR
      (review_type = 'poster_review' AND worker_persona_id IS NULL)
    )
);

-- OTP attempts tracking table for rate limiting
CREATE TABLE otp_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone VARCHAR(15) NOT NULL,
    attempts_count INTEGER DEFAULT 1,
    last_attempt_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    blocked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_phone CHECK (phone ~ '^\+91[6-9][0-9]{9}$')
);

-- SMS logs table for tracking SMS delivery and analytics
CREATE TABLE sms_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone VARCHAR(15) NOT NULL,
    session_id VARCHAR(100), -- 2Factor.in session ID for OTP verification
    status VARCHAR(20) NOT NULL, -- sent, delivered, failed, etc.
    provider VARCHAR(20) NOT NULL DEFAULT '2factor',
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    delivered_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT valid_phone CHECK (phone ~ '^\+91[6-9][0-9]{9}$')
);

-- User activity log to track role switching
CREATE TABLE user_activity (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    activity_type VARCHAR(50), -- 'job_posted', 'job_applied', 'job_completed'
    job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
    persona_id UUID REFERENCES worker_personas(id) ON DELETE CASCADE,
    activity_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table for in-app, push, email, and SMS notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'job_application_received', 'application_status_updated', etc.
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    data JSONB, -- Additional data for deep linking, etc.
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Skill categories master table
CREATE TABLE skill_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL, -- 'electrical', 'plumbing', etc.
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_name VARCHAR(50), -- Icon identifier for UI
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Skill subcategories master table
CREATE TABLE skill_subcategories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID NOT NULL REFERENCES skill_categories(id) ON DELETE CASCADE,
    code VARCHAR(100) UNIQUE NOT NULL, -- 'house_wiring', 'appliance_repair', etc.
    name VARCHAR(150) NOT NULL,
    description TEXT,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Job views tracking table
CREATE TABLE job_views (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    viewer_id UUID, -- Can be null for anonymous views
    viewer_type VARCHAR(20) NOT NULL CHECK (viewer_type IN ('worker', 'anonymous')),
    source VARCHAR(50) NOT NULL CHECK (source IN ('search', 'direct', 'recommendation', 'notification')),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    platform VARCHAR(50),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Job applications tracking table (separate from actual applications for analytics)
CREATE TABLE job_applications_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    applicant_id UUID NOT NULL,
    application_id UUID NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    application_quality VARCHAR(20) NOT NULL CHECK (application_quality IN ('high', 'medium', 'low')),
    response_time_hours DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Job performance metrics table (aggregated data)
CREATE TABLE job_performance_metrics (
    job_id UUID PRIMARY KEY REFERENCES jobs(id) ON DELETE CASCADE,
    views INTEGER NOT NULL DEFAULT 0,
    applications INTEGER NOT NULL DEFAULT 0,
    time_to_fill DECIMAL(10, 2), -- in hours
    average_response_time DECIMAL(10, 2) NOT NULL DEFAULT 0, -- in hours
    conversion_rate DECIMAL(5, 2) NOT NULL DEFAULT 0, -- percentage
    quality_score DECIMAL(3, 2) NOT NULL DEFAULT 1, -- 1-3 scale
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- PERFORMANCE OPTIMIZATION INDEXES
-- =====================================================

CREATE INDEX idx_users_location ON users USING GIST (location);
CREATE INDEX idx_jobs_location ON jobs USING GIST (location);
CREATE INDEX idx_jobs_active_category ON jobs (skill_category, status, created_at) WHERE status = 'active';
CREATE INDEX idx_personas_active_category ON worker_personas (skill_category, is_active) WHERE is_active = TRUE;
CREATE INDEX idx_applications_job ON job_applications (job_id, status);
CREATE INDEX idx_reviews_reviewee_persona ON reviews (reviewee_id, worker_persona_id, rating);
CREATE INDEX idx_otp_attempts_phone ON otp_attempts (phone, last_attempt_at);
CREATE INDEX idx_sms_logs_phone_status ON sms_logs (phone, status, created_at);
CREATE INDEX idx_sms_logs_created_at ON sms_logs (created_at);
-- NEW: Index for profile completion queries (authentication flow optimization)
CREATE INDEX idx_users_profile_completed ON users (profile_completed, created_at);
-- Indexes for switchable user types
CREATE INDEX idx_users_worker ON users (is_worker) WHERE is_worker = TRUE;
CREATE INDEX idx_users_poster ON users (is_poster) WHERE is_poster = TRUE;
-- Indexes for notifications
CREATE INDEX idx_notifications_user_id ON notifications (user_id, created_at DESC);
CREATE INDEX idx_notifications_user_read ON notifications (user_id, read, created_at DESC);
-- Indexes for user activity
CREATE INDEX idx_user_activity_user_id ON user_activity (user_id, activity_date DESC);
CREATE INDEX idx_user_activity_job_id ON user_activity (job_id);
CREATE INDEX idx_user_activity_persona_id ON user_activity (persona_id);
-- Indexes for skill categories
CREATE INDEX idx_skill_categories_code ON skill_categories(code);
CREATE INDEX idx_skill_categories_active ON skill_categories(is_active);
CREATE INDEX idx_skill_categories_display_order ON skill_categories(display_order);
CREATE INDEX idx_skill_subcategories_category_id ON skill_subcategories(category_id);
CREATE INDEX idx_skill_subcategories_code ON skill_subcategories(code);
CREATE INDEX idx_skill_subcategories_active ON skill_subcategories(is_active);
-- Indexes for job visibility and performance
CREATE INDEX idx_jobs_visibility_score ON jobs(visibility_score DESC);
CREATE INDEX idx_jobs_indexed_at ON jobs(indexed_at);
CREATE INDEX idx_jobs_is_visible ON jobs(is_visible);
CREATE INDEX idx_jobs_search_keywords ON jobs USING GIN(search_keywords);
CREATE INDEX idx_jobs_created_at_visible ON jobs(created_at, is_visible);
CREATE INDEX idx_jobs_visibility_metrics ON jobs(created_at, is_visible, indexed_at);
CREATE INDEX idx_job_views_job_id ON job_views(job_id);
CREATE INDEX idx_job_views_timestamp ON job_views(timestamp);
CREATE INDEX idx_job_views_viewer_type ON job_views(viewer_type);
CREATE INDEX idx_job_views_source ON job_views(source);
CREATE INDEX idx_job_applications_tracking_job_id ON job_applications_tracking(job_id);
CREATE INDEX idx_job_applications_tracking_timestamp ON job_applications_tracking(timestamp);
CREATE INDEX idx_job_applications_tracking_applicant_id ON job_applications_tracking(applicant_id);
CREATE INDEX idx_job_performance_metrics_views ON job_performance_metrics(views);
CREATE INDEX idx_job_performance_metrics_conversion_rate ON job_performance_metrics(conversion_rate);
CREATE INDEX idx_job_performance_metrics_quality_score ON job_performance_metrics(quality_score);
CREATE INDEX idx_job_performance_metrics_last_updated ON job_performance_metrics(last_updated);
CREATE INDEX idx_jobs_status ON jobs(status);
CREATE INDEX idx_jobs_poster_id_created_at ON jobs(poster_id, created_at);
CREATE INDEX idx_jobs_poster_id_status ON jobs(poster_id, status);
CREATE INDEX idx_jobs_location_status ON jobs USING GIST (location) WHERE status = 'active';
CREATE INDEX idx_jobs_expires_at_status ON jobs (expires_at, status) WHERE status = 'active';

-- =====================================================
-- ROW LEVEL SECURITY SETUP
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE worker_personas ENABLE ROW LEVEL SECURITY;
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE otp_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE sms_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE skill_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE skill_subcategories ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_applications_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_performance_metrics ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- RLS POLICIES
-- =====================================================

-- Users table policies
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON users
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Worker personas policies
CREATE POLICY "Users can manage own personas" ON worker_personas
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Public can view active personas" ON worker_personas
  FOR SELECT USING (is_active = TRUE);

-- Jobs table policies
CREATE POLICY "Public can view active jobs" ON jobs
  FOR SELECT USING (status = 'active');

CREATE POLICY "Users can manage own job postings" ON jobs
  FOR ALL USING (poster_id = auth.uid());

-- Job applications policies
CREATE POLICY "Users can view applications for their jobs" ON job_applications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM jobs
      WHERE jobs.id = job_applications.job_id
      AND jobs.poster_id = auth.uid()
    )
  );

CREATE POLICY "Users can view their own applications" ON job_applications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM worker_personas
      WHERE worker_personas.id = job_applications.worker_persona_id
      AND worker_personas.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create applications for their personas" ON job_applications
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM worker_personas
      WHERE worker_personas.id = job_applications.worker_persona_id
      AND worker_personas.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own applications" ON job_applications
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM worker_personas
      WHERE worker_personas.id = job_applications.worker_persona_id
      AND worker_personas.user_id = auth.uid()
    )
  );

-- Reviews table policies
CREATE POLICY "Users can view reviews for their personas" ON reviews
  FOR SELECT USING (
    reviewer_id = auth.uid() OR
    reviewee_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM worker_personas
      WHERE worker_personas.id = reviews.worker_persona_id
      AND worker_personas.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create reviews" ON reviews
  FOR INSERT WITH CHECK (reviewer_id = auth.uid());

-- OTP attempts policies (service role only)
CREATE POLICY "Service role can manage OTP attempts" ON otp_attempts
  FOR ALL USING (auth.role() = 'service_role');

-- SMS logs policies (service role only)
CREATE POLICY "Service role can manage SMS logs" ON sms_logs
  FOR ALL USING (auth.role() = 'service_role');

-- User activity policies
CREATE POLICY "Users can view their own activity" ON user_activity
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can insert user activity" ON user_activity
  FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- Notifications table policies
CREATE POLICY "Users can view own notifications" ON notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage own notifications" ON notifications
  FOR ALL USING (user_id = auth.uid());

-- Skill categories policies
CREATE POLICY "Public can view active skill categories" ON skill_categories
  FOR SELECT USING (is_active = TRUE);

-- Skill subcategories policies
CREATE POLICY "Public can view active skill subcategories" ON skill_subcategories
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM skill_categories
      WHERE skill_categories.id = skill_subcategories.category_id
      AND skill_categories.is_active = TRUE
    )
  );

-- Job views policies
CREATE POLICY "Service role can manage job views" ON job_views
  FOR ALL USING (auth.role() = 'service_role');

-- Job applications tracking policies
CREATE POLICY "Service role can manage job applications tracking" ON job_applications_tracking
  FOR ALL USING (auth.role() = 'service_role');

-- Job performance metrics policies
CREATE POLICY "Public can view job performance metrics" ON job_performance_metrics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM jobs
      WHERE jobs.id = job_performance_metrics.job_id
      AND jobs.status = 'active'
    )
  );

CREATE POLICY "Users can view performance metrics for their jobs" ON job_performance_metrics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM jobs
      WHERE jobs.id = job_performance_metrics.job_id
      AND jobs.poster_id = auth.uid()
    )
  );

-- =====================================================
-- DATABASE FUNCTIONS AND TRIGGERS
-- =====================================================

-- Auto-update triggers for ratings and counters
CREATE OR REPLACE FUNCTION update_persona_rating()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.worker_persona_id IS NOT NULL THEN
    UPDATE worker_personas
    SET
      average_rating = (
        SELECT ROUND(AVG(rating)::numeric, 2)
        FROM reviews
        WHERE worker_persona_id = NEW.worker_persona_id
        AND review_type = 'worker_review'
      ),
      total_reviews = (
        SELECT COUNT(*)
        FROM reviews
        WHERE worker_persona_id = NEW.worker_persona_id
        AND review_type = 'worker_review'
      ),
      updated_at = NOW()
    WHERE id = NEW.worker_persona_id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_persona_rating
  AFTER INSERT OR UPDATE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_persona_rating();

-- Function to update job application counts
CREATE OR REPLACE FUNCTION update_job_application_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE jobs
    SET applications_count = applications_count + 1,
        updated_at = NOW()
    WHERE id = NEW.job_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE jobs
    SET applications_count = applications_count - 1,
        updated_at = NOW()
    WHERE id = OLD.job_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_job_application_count
  AFTER INSERT OR DELETE ON job_applications
  FOR EACH ROW
  EXECUTE FUNCTION update_job_application_count();

-- Function to handle OTP rate limiting
CREATE OR REPLACE FUNCTION handle_otp_rate_limit(phone_number TEXT)
RETURNS JSONB AS $$
DECLARE
  attempt_record RECORD;
  current_time TIMESTAMP WITH TIME ZONE := NOW();
  rate_limit_window INTERVAL := '1 hour';
  max_attempts INTEGER := 3;
  block_duration INTERVAL := '1 hour';
BEGIN
  -- Get or create attempt record
  SELECT * INTO attempt_record
  FROM otp_attempts
  WHERE phone = phone_number
  ORDER BY created_at DESC
  LIMIT 1;

  -- Check if phone is currently blocked
  IF attempt_record.blocked_until IS NOT NULL AND attempt_record.blocked_until > current_time THEN
    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'blocked',
      'blocked_until', attempt_record.blocked_until,
      'remaining_attempts', 0
    );
  END IF;

  -- Reset attempts if window has passed
  IF attempt_record.last_attempt_at IS NULL OR
     (current_time - attempt_record.last_attempt_at) > rate_limit_window THEN

    INSERT INTO otp_attempts (phone, attempts_count, last_attempt_at)
    VALUES (phone_number, 1, current_time)
    ON CONFLICT (phone) DO UPDATE SET
      attempts_count = 1,
      last_attempt_at = current_time,
      blocked_until = NULL;

    RETURN jsonb_build_object(
      'allowed', true,
      'remaining_attempts', max_attempts - 1
    );
  END IF;

  -- Check if max attempts reached
  IF attempt_record.attempts_count >= max_attempts THEN
    UPDATE otp_attempts
    SET blocked_until = current_time + block_duration,
        last_attempt_at = current_time
    WHERE phone = phone_number;

    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'rate_limited',
      'blocked_until', current_time + block_duration,
      'remaining_attempts', 0
    );
  END IF;

  -- Increment attempt count
  UPDATE otp_attempts
  SET attempts_count = attempts_count + 1,
      last_attempt_at = current_time
  WHERE phone = phone_number;

  RETURN jsonb_build_object(
    'allowed', true,
    'remaining_attempts', max_attempts - (attempt_record.attempts_count + 1)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get SMS delivery statistics
CREATE OR REPLACE FUNCTION get_sms_stats(
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '24 hours',
  end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS JSONB AS $$
DECLARE
  stats JSONB;
BEGIN
  SELECT jsonb_build_object(
    'total_sent', COUNT(*),
    'delivered', COUNT(*) FILTER (WHERE status = 'delivered'),
    'failed', COUNT(*) FILTER (WHERE status = 'failed'),
    'pending', COUNT(*) FILTER (WHERE status = 'sent'),
    'delivery_rate',
      CASE
        WHEN COUNT(*) > 0 THEN
          ROUND((COUNT(*) FILTER (WHERE status = 'delivered')::numeric / COUNT(*)::numeric) * 100, 2)
        ELSE 0
      END,
    'by_hour', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'hour', date_trunc('hour', created_at),
          'count', count
        ) ORDER BY hour
      )
      FROM (
        SELECT
          date_trunc('hour', created_at) as hour,
          COUNT(*) as count
        FROM sms_logs
        WHERE created_at BETWEEN start_date AND end_date
        GROUP BY date_trunc('hour', created_at)
      ) hourly_stats
    )
  ) INTO stats
  FROM sms_logs
  WHERE created_at BETWEEN start_date AND end_date;

  RETURN COALESCE(stats, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to complete user profile (used by authentication flow)
CREATE OR REPLACE FUNCTION complete_user_profile(
  user_id UUID,
  full_name_param TEXT,
  preferred_language_param supported_language_enum DEFAULT 'english',
  user_type_param user_role_enum DEFAULT 'worker',
  email_param TEXT DEFAULT NULL,
  address_param TEXT DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  updated_user users%ROWTYPE;
BEGIN
  -- Validate full_name
  IF full_name_param IS NULL OR LENGTH(TRIM(full_name_param)) < 2 THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Full name must be at least 2 characters long'
    );
  END IF;

  -- Update user profile
  UPDATE users
  SET
    full_name = TRIM(full_name_param),
    preferred_language = preferred_language_param,
    user_type = user_type_param,
    email = email_param,
    address = address_param,
    profile_completed = TRUE,
    updated_at = NOW()
  WHERE id = user_id
  RETURNING * INTO updated_user;

  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'User not found'
    );
  END IF;

  RETURN jsonb_build_object(
    'success', true,
    'user', row_to_json(updated_user)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user profile is complete
CREATE OR REPLACE FUNCTION is_profile_complete(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_record users%ROWTYPE;
BEGIN
  SELECT * INTO user_record FROM users WHERE id = user_id;

  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;

  RETURN (
    user_record.profile_completed = TRUE AND
    user_record.full_name IS NOT NULL AND
    LENGTH(TRIM(user_record.full_name)) >= 2
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search jobs within a radius with distance calculation
CREATE OR REPLACE FUNCTION search_jobs_by_location(
  search_latitude DECIMAL,
  search_longitude DECIMAL,
  radius_km INTEGER DEFAULT 25,
  skill_category_filter TEXT DEFAULT NULL,
  urgency_filter TEXT DEFAULT NULL,
  status_filter TEXT DEFAULT 'active',
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  poster_id UUID,
  title VARCHAR(200),
  description TEXT,
  skill_category TEXT,
  location_data GEOGRAPHY,
  address TEXT,
  landmark TEXT,
  job_type TEXT,
  urgency TEXT,
  budget_min DECIMAL(10,2),
  budget_max DECIMAL(10,2),
  estimated_duration_hours INTEGER,
  requirements TEXT,
  preferred_gender TEXT,
  min_experience_years INTEGER,
  min_rating DECIMAL(3,2),
  status TEXT,
  applications_count INTEGER,
  views_count INTEGER,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  poster_full_name VARCHAR(100),
  poster_profile_image_url TEXT,
  distance_km DECIMAL(10,2)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    j.id,
    j.poster_id,
    j.title,
    j.description,
    j.skill_category::TEXT,
    j.location as location_data,
    j.address,
    j.landmark,
    j.job_type::TEXT,
    j.urgency::TEXT,
    j.budget_min,
    j.budget_max,
    j.estimated_duration_hours,
    j.requirements,
    j.preferred_gender::TEXT,
    j.min_experience_years,
    j.min_rating,
    j.status::TEXT,
    j.applications_count,
    j.views_count,
    j.expires_at,
    j.created_at,
    j.updated_at,
    u.full_name as poster_full_name,
    u.profile_image_url as poster_profile_image_url,
    ROUND(
      (ST_Distance(j.location, ST_SetSRID(ST_Point(search_longitude, search_latitude), 4326)) / 1000.0)::numeric, 
      2
    ) as distance_km
  FROM jobs j
  JOIN users u ON j.poster_id = u.id
  WHERE j.status = status_filter
    AND j.expires_at > NOW()
    AND ST_DWithin(j.location, ST_SetSRID(ST_Point(search_longitude, search_latitude), 4326), radius_km * 1000)
    AND (skill_category_filter IS NULL OR j.skill_category::TEXT = skill_category_filter)
    AND (urgency_filter IS NULL OR j.urgency::TEXT = urgency_filter)
  ORDER BY
    -- Primary sort: Distance (closest first)
    distance_km ASC,
    -- Secondary sort: Urgency priority (urgent > high > normal > low)
    CASE j.urgency
      WHEN 'urgent' THEN 1
      WHEN 'high' THEN 2
      WHEN 'normal' THEN 3
      WHEN 'low' THEN 4
      ELSE 5
    END ASC,
    -- Tertiary sort: Most recent first
    j.created_at DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at timestamp for skill categories
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_skill_categories_updated_at BEFORE UPDATE ON skill_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_skill_subcategories_updated_at BEFORE UPDATE ON skill_subcategories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- POPULATE SKILL CATEGORIES
-- =====================================================

INSERT INTO skill_categories (code, name, description, icon_name, display_order) VALUES
('electrical', 'Electrical Work', 'Electrical installation, repair, and maintenance services', 'zap', 1),
('plumbing', 'Plumbing', 'Water supply, drainage, and plumbing fixture services', 'droplet', 2),
('carpentry', 'Carpentry', 'Wood work, furniture making, and repair services', 'hammer', 3),
('cooking', 'Cooking', 'Food preparation and cooking services', 'chef-hat', 4),
('cleaning', 'Cleaning', 'House and office cleaning services', 'sparkles', 5),
('driving', 'Driving', 'Personal and commercial driving services', 'car', 6),
('delivery', 'Delivery', 'Package and goods delivery services', 'truck', 7),
('security', 'Security', 'Security and surveillance services', 'shield', 8),
('gardening', 'Gardening', 'Garden maintenance and landscaping services', 'leaf', 9),
('tutoring', 'Tutoring', 'Educational and skill training services', 'book-open', 10);

-- =====================================================
-- POPULATE SKILL SUBCATEGORIES
-- =====================================================

-- Electrical subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'house_wiring', 'House Wiring', 1),
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'appliance_repair', 'Appliance Repair', 2),
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'fan_installation', 'Fan Installation', 3),
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'light_fitting', 'Light Fitting', 4),
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'switch_board', 'Switch Board Work', 5),
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'solar_installation', 'Solar Installation', 6),
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'inverter_setup', 'Inverter Setup', 7);

-- Plumbing subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'pipe_repair', 'Pipe Repair', 1),
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'bathroom_fitting', 'Bathroom Fitting', 2),
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'kitchen_plumbing', 'Kitchen Plumbing', 3),
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'water_heater', 'Water Heater', 4),
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'drainage_work', 'Drainage Work', 5),
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'bore_well', 'Bore Well', 6),
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'water_tank', 'Water Tank', 7);

-- Carpentry subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'carpentry'), 'furniture_making', 'Furniture Making', 1),
((SELECT id FROM skill_categories WHERE code = 'carpentry'), 'door_window', 'Door/Window', 2),
((SELECT id FROM skill_categories WHERE code = 'carpentry'), 'kitchen_cabinets', 'Kitchen Cabinets', 3),
((SELECT id FROM skill_categories WHERE code = 'carpentry'), 'wardrobe', 'Wardrobe', 4),
((SELECT id FROM skill_categories WHERE code = 'carpentry'), 'bed_sofa', 'Bed/Sofa', 5),
((SELECT id FROM skill_categories WHERE code = 'carpentry'), 'repair_work', 'Repair Work', 6);

-- Cooking subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'north_indian', 'North Indian', 1),
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'south_indian', 'South Indian', 2),
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'chinese', 'Chinese', 3),
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'continental', 'Continental', 4),
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'catering', 'Catering', 5),
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'tiffin_service', 'Tiffin Service', 6),
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'party_cooking', 'Party Cooking', 7);

-- Cleaning subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'house_cleaning', 'House Cleaning', 1),
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'office_cleaning', 'Office Cleaning', 2),
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'deep_cleaning', 'Deep Cleaning', 3),
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'carpet_cleaning', 'Carpet Cleaning', 4),
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'window_cleaning', 'Window Cleaning', 5),
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'bathroom_cleaning', 'Bathroom Cleaning', 6),
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'kitchen_cleaning', 'Kitchen Cleaning', 7);

-- Driving subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'driving'), 'personal_driver', 'Personal Driver', 1),
((SELECT id FROM skill_categories WHERE code = 'driving'), 'cab_driver', 'Cab Driver', 2),
((SELECT id FROM skill_categories WHERE code = 'driving'), 'truck_driver', 'Truck Driver', 3),
((SELECT id FROM skill_categories WHERE code = 'driving'), 'two_wheeler', 'Two Wheeler', 4),
((SELECT id FROM skill_categories WHERE code = 'driving'), 'chauffeur', 'Chauffeur', 5),
((SELECT id FROM skill_categories WHERE code = 'driving'), 'tour_driver', 'Tour Driver', 6);

-- Delivery subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'delivery'), 'food_delivery', 'Food Delivery', 1),
((SELECT id FROM skill_categories WHERE code = 'delivery'), 'package_delivery', 'Package Delivery', 2),
((SELECT id FROM skill_categories WHERE code = 'delivery'), 'grocery_delivery', 'Grocery Delivery', 3),
((SELECT id FROM skill_categories WHERE code = 'delivery'), 'medicine_delivery', 'Medicine Delivery', 4),
((SELECT id FROM skill_categories WHERE code = 'delivery'), 'document_delivery', 'Document Delivery', 5),
((SELECT id FROM skill_categories WHERE code = 'delivery'), 'bulk_delivery', 'Bulk Delivery', 6);

-- Security subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'security'), 'residential_security', 'Residential Security', 1),
((SELECT id FROM skill_categories WHERE code = 'security'), 'office_security', 'Office Security', 2),
((SELECT id FROM skill_categories WHERE code = 'security'), 'event_security', 'Event Security', 3),
((SELECT id FROM skill_categories WHERE code = 'security'), 'night_watchman', 'Night Watchman', 4),
((SELECT id FROM skill_categories WHERE code = 'security'), 'cctv_monitoring', 'CCTV Monitoring', 5);

-- Gardening subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'gardening'), 'lawn_maintenance', 'Lawn Maintenance', 1),
((SELECT id FROM skill_categories WHERE code = 'gardening'), 'plant_care', 'Plant Care', 2),
((SELECT id FROM skill_categories WHERE code = 'gardening'), 'landscaping', 'Landscaping', 3),
((SELECT id FROM skill_categories WHERE code = 'gardening'), 'tree_cutting', 'Tree Cutting', 4),
((SELECT id FROM skill_categories WHERE code = 'gardening'), 'pest_control', 'Pest Control', 5);

-- Tutoring subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'tutoring'), 'academic_subjects', 'Academic Subjects', 1),
((SELECT id FROM skill_categories WHERE code = 'tutoring'), 'language_teaching', 'Language Teaching', 2),
((SELECT id FROM skill_categories WHERE code = 'tutoring'), 'music_lessons', 'Music Lessons', 3),
((SELECT id FROM skill_categories WHERE code = 'tutoring'), 'computer_training', 'Computer Training', 4),
((SELECT id FROM skill_categories WHERE code = 'tutoring'), 'skill_training', 'Skill Training', 5);

-- Update existing jobs to be visible (if any exist)
UPDATE jobs
SET is_visible = true,
    indexed_at = created_at,
    visibility_score = 100.0,
    search_keywords = '[]'
WHERE is_visible IS NULL OR is_visible = false;

-- Add comments for documentation
COMMENT ON COLUMN jobs.search_keywords IS 'JSON array of keywords for search indexing';
COMMENT ON COLUMN jobs.visibility_score IS 'Score used for ranking jobs in search results (higher = more visible)';
COMMENT ON COLUMN jobs.indexed_at IS 'Timestamp when job was indexed for search';
COMMENT ON COLUMN jobs.is_visible IS 'Whether job is visible to workers in search results';
COMMENT ON COLUMN jobs.status IS 'Current status of the job posting';
COMMENT ON COLUMN jobs.filled_at IS 'Timestamp when the job was marked as filled';

COMMENT ON TABLE job_views IS 'Tracks individual job view events for analytics';
COMMENT ON TABLE job_applications_tracking IS 'Tracks job application events for performance metrics';
COMMENT ON TABLE job_performance_metrics IS 'Aggregated performance metrics for each job';

COMMENT ON COLUMN job_views.viewer_type IS 'Type of viewer: worker (logged in) or anonymous';
COMMENT ON COLUMN job_views.source IS 'How the viewer found the job: search, direct link, recommendation, or notification';
COMMENT ON COLUMN job_applications_tracking.application_quality IS 'Quality assessment of the application: high, medium, or low';
COMMENT ON COLUMN job_applications_tracking.response_time_hours IS 'Hours between job posting and application submission';
COMMENT ON COLUMN job_performance_metrics.conversion_rate IS 'Percentage of views that resulted in applications';
COMMENT ON COLUMN job_performance_metrics.quality_score IS 'Average quality score of applications (1-3 scale)';

COMMENT ON FUNCTION search_jobs_by_location IS 'Search jobs within a radius with distance calculation and sorting';

-- =====================================================
-- SETUP COMPLETE
-- =====================================================
-- Your Ozgaar database is now ready with authentication flow fixes!
--
-- AUTHENTICATION FLOW FEATURES:
-- ✅ Users can be created with minimal data (phone only)
-- ✅ Profile completion is tracked with profile_completed column
-- ✅ Helper functions for profile management
-- ✅ Proper constraints ensure data consistency
-- ✅ Optimized indexes for profile completion queries
--
-- Next steps:
-- 1. Configure your backend server with Supabase Cloud credentials
-- 2. Test the complete authentication flow:
--    - Phone registration → OTP verification → Profile completion
-- 3. Start building your application features
-- =====================================================