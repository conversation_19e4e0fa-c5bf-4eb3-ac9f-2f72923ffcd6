-- =====================================================
-- SKILL CATEGORIES MASTER DATA SYSTEM
-- =====================================================
-- Migration: Create comprehensive skill categories and subcategories
-- for Indian job market
-- Date: 2025-08-17
-- Task: US-002.2 - Create Skill Categories Master Data System

-- =====================================================
-- CREATE MASTER DATA TABLES
-- =====================================================

-- Skill categories master table
CREATE TABLE skill_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL, -- 'electrical', 'plumbing', etc.
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_name VARCHAR(50), -- Icon identifier for UI
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Skill subcategories master table
CREATE TABLE skill_subcategories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID NOT NULL REFERENCES skill_categories(id) ON DELETE CASCADE,
    code VARCHAR(100) UNIQUE NOT NULL, -- 'house_wiring', 'appliance_repair', etc.
    name VARCHAR(150) NOT NULL,
    description TEXT,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_skill_categories_code ON skill_categories(code);
CREATE INDEX idx_skill_categories_active ON skill_categories(is_active);
CREATE INDEX idx_skill_categories_display_order ON skill_categories(display_order);
CREATE INDEX idx_skill_subcategories_category_id ON skill_subcategories(category_id);
CREATE INDEX idx_skill_subcategories_code ON skill_subcategories(code);
CREATE INDEX idx_skill_subcategories_active ON skill_subcategories(is_active);

-- =====================================================
-- POPULATE SKILL CATEGORIES
-- =====================================================

INSERT INTO skill_categories (code, name, description, icon_name, display_order) VALUES
('electrical', 'Electrical Work', 'Electrical installation, repair, and maintenance services', 'zap', 1),
('plumbing', 'Plumbing', 'Water supply, drainage, and plumbing fixture services', 'droplet', 2),
('carpentry', 'Carpentry', 'Wood work, furniture making, and repair services', 'hammer', 3),
('cooking', 'Cooking', 'Food preparation and cooking services', 'chef-hat', 4),
('cleaning', 'Cleaning', 'House and office cleaning services', 'sparkles', 5),
('driving', 'Driving', 'Personal and commercial driving services', 'car', 6),
('delivery', 'Delivery', 'Package and goods delivery services', 'truck', 7),
('security', 'Security', 'Security and surveillance services', 'shield', 8),
('gardening', 'Gardening', 'Garden maintenance and landscaping services', 'leaf', 9),
('tutoring', 'Tutoring', 'Educational and skill training services', 'book-open', 10);

-- =====================================================
-- POPULATE SKILL SUBCATEGORIES
-- =====================================================

-- Electrical subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'house_wiring', 'House Wiring', 1),
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'appliance_repair', 'Appliance Repair', 2),
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'fan_installation', 'Fan Installation', 3),
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'light_fitting', 'Light Fitting', 4),
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'switch_board', 'Switch Board Work', 5),
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'solar_installation', 'Solar Installation', 6),
((SELECT id FROM skill_categories WHERE code = 'electrical'), 'inverter_setup', 'Inverter Setup', 7);

-- Plumbing subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'pipe_repair', 'Pipe Repair', 1),
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'bathroom_fitting', 'Bathroom Fitting', 2),
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'kitchen_plumbing', 'Kitchen Plumbing', 3),
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'water_heater', 'Water Heater', 4),
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'drainage_work', 'Drainage Work', 5),
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'bore_well', 'Bore Well', 6),
((SELECT id FROM skill_categories WHERE code = 'plumbing'), 'water_tank', 'Water Tank', 7);

-- Carpentry subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'carpentry'), 'furniture_making', 'Furniture Making', 1),
((SELECT id FROM skill_categories WHERE code = 'carpentry'), 'door_window', 'Door/Window', 2),
((SELECT id FROM skill_categories WHERE code = 'carpentry'), 'kitchen_cabinets', 'Kitchen Cabinets', 3),
((SELECT id FROM skill_categories WHERE code = 'carpentry'), 'wardrobe', 'Wardrobe', 4),
((SELECT id FROM skill_categories WHERE code = 'carpentry'), 'bed_sofa', 'Bed/Sofa', 5),
((SELECT id FROM skill_categories WHERE code = 'carpentry'), 'repair_work', 'Repair Work', 6);

-- Cooking subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'north_indian', 'North Indian', 1),
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'south_indian', 'South Indian', 2),
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'chinese', 'Chinese', 3),
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'continental', 'Continental', 4),
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'catering', 'Catering', 5),
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'tiffin_service', 'Tiffin Service', 6),
((SELECT id FROM skill_categories WHERE code = 'cooking'), 'party_cooking', 'Party Cooking', 7);

-- Cleaning subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'house_cleaning', 'House Cleaning', 1),
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'office_cleaning', 'Office Cleaning', 2),
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'deep_cleaning', 'Deep Cleaning', 3),
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'carpet_cleaning', 'Carpet Cleaning', 4),
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'window_cleaning', 'Window Cleaning', 5),
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'bathroom_cleaning', 'Bathroom Cleaning', 6),
((SELECT id FROM skill_categories WHERE code = 'cleaning'), 'kitchen_cleaning', 'Kitchen Cleaning', 7);

-- Driving subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'driving'), 'personal_driver', 'Personal Driver', 1),
((SELECT id FROM skill_categories WHERE code = 'driving'), 'cab_driver', 'Cab Driver', 2),
((SELECT id FROM skill_categories WHERE code = 'driving'), 'truck_driver', 'Truck Driver', 3),
((SELECT id FROM skill_categories WHERE code = 'driving'), 'two_wheeler', 'Two Wheeler', 4),
((SELECT id FROM skill_categories WHERE code = 'driving'), 'chauffeur', 'Chauffeur', 5),
((SELECT id FROM skill_categories WHERE code = 'driving'), 'tour_driver', 'Tour Driver', 6);

-- Delivery subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'delivery'), 'food_delivery', 'Food Delivery', 1),
((SELECT id FROM skill_categories WHERE code = 'delivery'), 'package_delivery', 'Package Delivery', 2),
((SELECT id FROM skill_categories WHERE code = 'delivery'), 'grocery_delivery', 'Grocery Delivery', 3),
((SELECT id FROM skill_categories WHERE code = 'delivery'), 'medicine_delivery', 'Medicine Delivery', 4),
((SELECT id FROM skill_categories WHERE code = 'delivery'), 'document_delivery', 'Document Delivery', 5),
((SELECT id FROM skill_categories WHERE code = 'delivery'), 'bulk_delivery', 'Bulk Delivery', 6);

-- Security subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'security'), 'residential_security', 'Residential Security', 1),
((SELECT id FROM skill_categories WHERE code = 'security'), 'office_security', 'Office Security', 2),
((SELECT id FROM skill_categories WHERE code = 'security'), 'event_security', 'Event Security', 3),
((SELECT id FROM skill_categories WHERE code = 'security'), 'night_watchman', 'Night Watchman', 4),
((SELECT id FROM skill_categories WHERE code = 'security'), 'cctv_monitoring', 'CCTV Monitoring', 5);

-- Gardening subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'gardening'), 'lawn_maintenance', 'Lawn Maintenance', 1),
((SELECT id FROM skill_categories WHERE code = 'gardening'), 'plant_care', 'Plant Care', 2),
((SELECT id FROM skill_categories WHERE code = 'gardening'), 'landscaping', 'Landscaping', 3),
((SELECT id FROM skill_categories WHERE code = 'gardening'), 'tree_cutting', 'Tree Cutting', 4),
((SELECT id FROM skill_categories WHERE code = 'gardening'), 'pest_control', 'Pest Control', 5);

-- Tutoring subcategories
INSERT INTO skill_subcategories (category_id, code, name, display_order) VALUES
((SELECT id FROM skill_categories WHERE code = 'tutoring'), 'academic_subjects', 'Academic Subjects', 1),
((SELECT id FROM skill_categories WHERE code = 'tutoring'), 'language_teaching', 'Language Teaching', 2),
((SELECT id FROM skill_categories WHERE code = 'tutoring'), 'music_lessons', 'Music Lessons', 3),
((SELECT id FROM skill_categories WHERE code = 'tutoring'), 'computer_training', 'Computer Training', 4),
((SELECT id FROM skill_categories WHERE code = 'tutoring'), 'skill_training', 'Skill Training', 5);

-- =====================================================
-- UPDATE EXISTING TABLES TO REFERENCE NEW SYSTEM
-- =====================================================

-- Add foreign key reference to skill_categories in users table
-- Note: This will be handled in a separate migration to maintain compatibility
-- ALTER TABLE users ADD COLUMN primary_skill_category_id UUID REFERENCES skill_categories(id);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_skill_categories_updated_at BEFORE UPDATE ON skill_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_skill_subcategories_updated_at BEFORE UPDATE ON skill_subcategories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant permissions for the application user
-- GRANT SELECT, INSERT, UPDATE, DELETE ON skill_categories TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON skill_subcategories TO your_app_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO your_app_user;

COMMIT;