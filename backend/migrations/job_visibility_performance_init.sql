-- Add job visibility columns for the job visibility system
-- This migration adds columns needed for job indexing and visibility tracking

-- Add visibility-related columns to jobs table
ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS search_keywords JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS visibility_score DECIMAL(10,2) DEFAULT 100.0,
ADD COLUMN IF NOT EXISTS indexed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS is_visible BOOLEAN DEFAULT false;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_jobs_visibility_score ON jobs(visibility_score DESC);
CREATE INDEX IF NOT EXISTS idx_jobs_indexed_at ON jobs(indexed_at);
CREATE INDEX IF NOT EXISTS idx_jobs_is_visible ON jobs(is_visible);
CREATE INDEX IF NOT EXISTS idx_jobs_search_keywords ON jobs USING GIN(search_keywords);
CREATE INDEX IF NOT EXISTS idx_jobs_created_at_visible ON jobs(created_at, is_visible);

-- Create index for visibility metrics query
CREATE INDEX IF NOT EXISTS idx_jobs_visibility_metrics ON jobs(created_at, is_visible, indexed_at);

-- Update existing jobs to be visible (if any exist)
UPDATE jobs
SET is_visible = true,
    indexed_at = created_at,
    visibility_score = 100.0,
    search_keywords = '[]'
WHERE is_visible IS NULL OR is_visible = false;

-- Add comments for documentation
COMMENT ON COLUMN jobs.search_keywords IS 'JSON array of keywords for search indexing';
COMMENT ON COLUMN jobs.visibility_score IS 'Score used for ranking jobs in search results (higher = more visible)';
COMMENT ON COLUMN jobs.indexed_at IS 'Timestamp when job was indexed for search';
COMMENT ON COLUMN jobs.is_visible IS 'Whether job is visible to workers in search results';

-- Add job performance tracking tables
-- This migration creates tables for tracking job views, applications, and performance metrics

-- Job views tracking table
CREATE TABLE IF NOT EXISTS job_views (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    viewer_id UUID, -- Can be null for anonymous views
    viewer_type VARCHAR(20) NOT NULL CHECK (viewer_type IN ('worker', 'anonymous')),
    source VARCHAR(50) NOT NULL CHECK (source IN ('search', 'direct', 'recommendation', 'notification')),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    platform VARCHAR(50),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Job applications tracking table (separate from actual applications for analytics)
CREATE TABLE IF NOT EXISTS job_applications_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    applicant_id UUID NOT NULL,
    application_id UUID NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    application_quality VARCHAR(20) NOT NULL CHECK (application_quality IN ('high', 'medium', 'low')),
    response_time_hours DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Job performance metrics table (aggregated data)
CREATE TABLE IF NOT EXISTS job_performance_metrics (
    job_id UUID PRIMARY KEY REFERENCES jobs(id) ON DELETE CASCADE,
    views INTEGER NOT NULL DEFAULT 0,
    applications INTEGER NOT NULL DEFAULT 0,
    time_to_fill DECIMAL(10, 2), -- in hours
    average_response_time DECIMAL(10, 2) NOT NULL DEFAULT 0, -- in hours
    conversion_rate DECIMAL(5, 2) NOT NULL DEFAULT 0, -- percentage
    quality_score DECIMAL(3, 2) NOT NULL DEFAULT 1, -- 1-3 scale
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add status and filled_at columns to jobs table if they don't exist
ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'paused', 'filled', 'expired', 'cancelled')),
ADD COLUMN IF NOT EXISTS filled_at TIMESTAMP WITH TIME ZONE;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_job_views_job_id ON job_views(job_id);
CREATE INDEX IF NOT EXISTS idx_job_views_timestamp ON job_views(timestamp);
CREATE INDEX IF NOT EXISTS idx_job_views_viewer_type ON job_views(viewer_type);
CREATE INDEX IF NOT EXISTS idx_job_views_source ON job_views(source);

CREATE INDEX IF NOT EXISTS idx_job_applications_tracking_job_id ON job_applications_tracking(job_id);
CREATE INDEX IF NOT EXISTS idx_job_applications_tracking_timestamp ON job_applications_tracking(timestamp);
CREATE INDEX IF NOT EXISTS idx_job_applications_tracking_applicant_id ON job_applications_tracking(applicant_id);

CREATE INDEX IF NOT EXISTS idx_job_performance_metrics_views ON job_performance_metrics(views);
CREATE INDEX IF NOT EXISTS idx_job_performance_metrics_conversion_rate ON job_performance_metrics(conversion_rate);
CREATE INDEX IF NOT EXISTS idx_job_performance_metrics_quality_score ON job_performance_metrics(quality_score);
CREATE INDEX IF NOT EXISTS idx_job_performance_metrics_last_updated ON job_performance_metrics(last_updated);

CREATE INDEX IF NOT EXISTS idx_jobs_status ON jobs(status);
CREATE INDEX IF NOT EXISTS idx_jobs_poster_id_created_at ON jobs(poster_id, created_at);
CREATE INDEX IF NOT EXISTS idx_jobs_poster_id_status ON jobs(poster_id, status);

-- Add comments for documentation
COMMENT ON TABLE job_views IS 'Tracks individual job view events for analytics';
COMMENT ON TABLE job_applications_tracking IS 'Tracks job application events for performance metrics';
COMMENT ON TABLE job_performance_metrics IS 'Aggregated performance metrics for each job';

COMMENT ON COLUMN job_views.viewer_type IS 'Type of viewer: worker (logged in) or anonymous';
COMMENT ON COLUMN job_views.source IS 'How the viewer found the job: search, direct link, recommendation, or notification';
COMMENT ON COLUMN job_applications_tracking.application_quality IS 'Quality assessment of the application: high, medium, or low';
COMMENT ON COLUMN job_applications_tracking.response_time_hours IS 'Hours between job posting and application submission';
COMMENT ON COLUMN job_performance_metrics.conversion_rate IS 'Percentage of views that resulted in applications';
COMMENT ON COLUMN job_performance_metrics.quality_score IS 'Average quality score of applications (1-3 scale)';
COMMENT ON COLUMN jobs.status IS 'Current status of the job posting';
COMMENT ON COLUMN jobs.filled_at IS 'Timestamp when the job was marked as filled';

-- Add PostGIS function for location-based job search
-- This migration creates a function to efficiently search jobs by location with distance calculation

-- Function to search jobs within a radius with distance calculation
CREATE OR REPLACE FUNCTION search_jobs_by_location(
  search_latitude DECIMAL,
  search_longitude DECIMAL,
  radius_km INTEGER DEFAULT 25,
  skill_category_filter TEXT DEFAULT NULL,
  urgency_filter TEXT DEFAULT NULL,
  status_filter TEXT DEFAULT 'active',
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  poster_id UUID,
  title VARCHAR(200),
  description TEXT,
  skill_category TEXT,
  location_data GEOGRAPHY,
  address TEXT,
  landmark TEXT,
  job_type TEXT,
  urgency TEXT,
  budget_min DECIMAL(10,2),
  budget_max DECIMAL(10,2),
  estimated_duration_hours INTEGER,
  requirements TEXT,
  preferred_gender TEXT,
  min_experience_years INTEGER,
  min_rating DECIMAL(3,2),
  status TEXT,
  applications_count INTEGER,
  views_count INTEGER,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  poster_full_name VARCHAR(100),
  poster_profile_image_url TEXT,
  distance_km DECIMAL(10,2)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    j.id,
    j.poster_id,
    j.title,
    j.description,
    j.skill_category::TEXT,
    j.location as location_data,
    j.address,
    j.landmark,
    j.job_type::TEXT,
    j.urgency::TEXT,
    j.budget_min,
    j.budget_max,
    j.estimated_duration_hours,
    j.requirements,
    j.preferred_gender::TEXT,
    j.min_experience_years,
    j.min_rating,
    j.status::TEXT,
    j.applications_count,
    j.views_count,
    j.expires_at,
    j.created_at,
    j.updated_at,
    u.full_name as poster_full_name,
    u.profile_image_url as poster_profile_image_url,
    ROUND(
      (ST_Distance(j.location, ST_SetSRID(ST_Point(search_longitude, search_latitude), 4326)) / 1000.0)::numeric, 
      2
    ) as distance_km
  FROM jobs j
  JOIN users u ON j.poster_id = u.id
  WHERE j.status = status_filter
    AND j.expires_at > NOW()
    AND ST_DWithin(j.location, ST_SetSRID(ST_Point(search_longitude, search_latitude), 4326), radius_km * 1000)
    AND (skill_category_filter IS NULL OR j.skill_category::TEXT = skill_category_filter)
    AND (urgency_filter IS NULL OR j.urgency::TEXT = urgency_filter)
  ORDER BY
    -- Primary sort: Distance (closest first)
    distance_km ASC,
    -- Secondary sort: Urgency priority (urgent > high > normal > low)
    CASE j.urgency
      WHEN 'urgent' THEN 1
      WHEN 'high' THEN 2
      WHEN 'normal' THEN 3
      WHEN 'low' THEN 4
      ELSE 5
    END ASC,
    -- Tertiary sort: Most recent first
    j.created_at DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- Create index for better performance on location-based queries
CREATE INDEX IF NOT EXISTS idx_jobs_location_status ON jobs USING GIST (location) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_jobs_expires_at_status ON jobs (expires_at, status) WHERE status = 'active';

-- Add comments for documentation
COMMENT ON FUNCTION search_jobs_by_location IS 'Search jobs within a radius with distance calculation and sorting';