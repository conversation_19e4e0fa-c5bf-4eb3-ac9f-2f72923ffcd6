# OTP Rate Limiting Fix

## Problem
After upgrading the Supabase database, we encountered the following error when trying to send OTPs:

```
error: Rate limit check failed: operator does not exist: timestamp with time zone > time with time zone
{"code":"42883","details":null,"hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","timestamp":"2025-08-21 20:51:20:5120"}
```

## Root Cause
The error was caused by a type mismatch in the `handle_otp_rate_limit` PostgreSQL function. The function was comparing timestamps of different types:
- `blocked_until` column was defined as `TIMESTAMP WITH TIME ZONE`
- But somewhere in the comparison logic, it was being compared against a value that PostgreSQL interpreted as `TIME WITH TIME ZONE`

This issue was likely introduced during the database upgrade process where column types might have been altered or the function implementation had inconsistencies.

## Solution
We've implemented a comprehensive fix that addresses the issue at multiple levels:

### 1. Fixed Database Schema
- Ensured all timestamp columns in `otp_attempts` table use `TIMESTAMP WITH TIME ZONE`
- Added a unique constraint on the `phone` column to enable `ON CONFLICT` clauses

### 2. Updated PostgreSQL Functions
- Completely rewrote the `handle_otp_rate_limit` function to ensure consistent type handling
- Fixed the `get_sms_stats` function for consistent timestamp handling
- Added proper error handling and type casting

### 3. Migration Scripts
Two migration scripts have been created:
1. `fix_otp_rate_limit_function.sql` - A targeted fix for the specific function
2. `comprehensive_otp_fix.sql` - A complete fix for all OTP-related database issues

## Implementation
To apply the fix:

1. Run the migration script in your Supabase SQL editor:
   ```sql
   -- Run either the targeted fix or comprehensive fix
   -- Targeted fix:
   \i backend/migrations/fix_otp_rate_limit_function.sql
   
   -- OR Comprehensive fix:
   \i backend/migrations/comprehensive_otp_fix.sql
   ```

2. Restart your backend server to ensure the changes take effect

## Verification
After applying the fix, you should be able to:
1. Send OTPs without encountering the timestamp error
2. Verify that rate limiting is working correctly
3. Check that SMS logs are being recorded properly

## Prevention
To prevent similar issues in the future:
1. Always explicitly specify timestamp types when creating tables
2. Use consistent timestamp handling in all PostgreSQL functions
3. Test database upgrades thoroughly, especially for functions that handle time-based operations