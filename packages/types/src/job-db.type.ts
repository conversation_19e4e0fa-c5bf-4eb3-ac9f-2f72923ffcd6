import { 
  SkillCategoryEnum, 
  JobTypeEnum, 
  UrgencyEnum, 
  JobStatusEnum, 
  GenderPreferenceEnum 
} from './enums';

export interface Job {
  id: string;
  poster_id: string;
  title: string;
  description: string;
  skill_category: SkillCategoryEnum;
  
  // Location for hyper-local matching
  location: {
    latitude: number;
    longitude: number;
  };
  address: string;
  landmark: string | null;
  
  // Job specifications
  job_type: JobTypeEnum;
  urgency: UrgencyEnum;
  budget_min: number;
  budget_max: number;
  estimated_duration_hours: number | null;
  
  // Requirements and preferences
  requirements: string | null;
  preferred_gender: GenderPreferenceEnum;
  min_experience_years: number;
  min_rating: number;
  
  // Status and metrics
  status: JobStatusEnum;
  applications_count: number;
  views_count: number;
  
  // Timestamps
  expires_at: string;
  created_at: string;
  updated_at: string;
  
  // Visibility and performance tracking
  search_keywords: string[]; // JSONB array
  visibility_score: number;
  indexed_at: string | null;
  is_visible: boolean;
  filled_at: string | null;
}