import { SkillCategoryEnum } from './enums';

export interface WorkerPersona {
  id: string;
  user_id: string;
  title: string;
  skill_category: SkillCategoryEnum;
  skill_subcategories: string[];
  description: string;
  experience_years: number;
  
  // Pricing structure for Indian market
  hourly_rate: number | null;
  daily_rate: number | null;
  monthly_rate: number | null;
  is_rate_negotiable: boolean;
  
  // Availability and location
  availability_pattern: any; // JSONB - Weekly schedule
  travel_radius_km: number;
  
  // Performance metrics
  total_jobs_completed: number;
  total_earnings: number;
  average_rating: number;
  total_reviews: number;
  
  // Status
  is_active: boolean;
  is_verified: boolean;
  verification_date: string | null;
  profile_image_url: string | null;
  created_at: string;
  updated_at: string;
}