export interface SkillCategory {
  id: string;
  code: string;
  name: string;
  description?: string | null;
  icon_name?: string | null;
  display_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SkillSubcategory {
  id: string;
  category_id: string;
  code: string;
  name: string;
  description?: string | null;
  display_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}