import { SkillCategory } from './index';

export interface WeeklyAvailability {
  monday: TimeSlot[];
  tuesday: TimeSlot[];
  wednesday: TimeSlot[];
  thursday: TimeSlot[];
  friday: TimeSlot[];
  saturday: TimeSlot[];
  sunday: TimeSlot[];
}

export interface TimeSlot {
  start: string; // HH:MM format
  end: string; // HH:MM format
}

// This is the simplified worker persona type used in the app
// For the full database schema, see worker-persona.type.ts
export interface WorkerPersona {
  id: string;
  user_id: string;
  title: string;
  skill_category: SkillCategory;
  skill_subcategories: string[];
  description: string;
  experience_years: number;
  hourly_rate?: number;
  daily_rate?: number;
  is_rate_negotiable: boolean;
  availability_pattern: WeeklyAvailability;
  travel_radius_km: number;
  is_active: boolean;
  total_jobs_completed: number;
  average_rating: number;
  profile_image_url?: string;
  created_at: string;
  updated_at: string;
}