// Types for Supabase function responses

export interface OtpRateLimitResponse {
  allowed: boolean;
  reason?: 'blocked' | 'rate_limited';
  blocked_until?: string;
  remaining_attempts?: number;
}

export interface CompleteUserProfileResponse {
  success: boolean;
  error?: string;
  user?: any;
}

export interface IsProfileCompleteResponse {
  is_complete: boolean;
}

export interface SearchJobsByLocationResponse {
  id: string;
  poster_id: string;
  title: string;
  description: string;
  skill_category: string;
  location_data: {
    latitude: number;
    longitude: number;
  };
  address: string;
  landmark: string | null;
  job_type: string;
  urgency: string;
  budget_min: number;
  budget_max: number;
  estimated_duration_hours: number | null;
  requirements: string | null;
  preferred_gender: string;
  min_experience_years: number;
  min_rating: number;
  status: string;
  applications_count: number;
  views_count: number;
  expires_at: string;
  created_at: string;
  updated_at: string;
  poster_full_name: string | null;
  poster_profile_image_url: string | null;
  distance_km: number;
}

export interface SmsStatsResponse {
  total_sent: number;
  delivered: number;
  failed: number;
  pending: number;
  delivery_rate: number;
  by_hour: {
    hour: string;
    count: number;
  }[];
}