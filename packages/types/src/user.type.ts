import { SupportedLanguage, SkillCategory } from './index';

export interface User {
  id: string;
  phone: string;
  email?: string;
  full_name: string | null; // Made nullable for initial user creation
  preferred_language: SupportedLanguage;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  address?: string;
  user_type: 'worker' | 'poster'; // In DB this is user_role_enum
  is_verified: boolean;
  profile_completed?: boolean; // Added for profile completion tracking
  profile_image_url?: string;
  
  // Switchable user type flags (from DB)
  is_worker: boolean;
  is_poster: boolean;
  
  // Worker profile fields at user level
  primary_skill_category?: SkillCategory;
  currently_available?: boolean; // Quick availability status
  years_of_experience?: number; // Overall experience across all skills

  // Worker-specific pricing fields
  hourly_rate?: number;
  daily_rate?: number;
  description?: string; // Worker bio/description
  travel_radius_km?: number;

  // Poster-specific fields
  company_name?: string;
  business_type?: string;
  poster_bio?: string;

  created_at: string;
  updated_at: string;
  last_active_at: string;
}