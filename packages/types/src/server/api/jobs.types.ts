/**
 * Jobs API Types
 * Request and response types for job-related endpoints
 */

export interface CreateJobRequest {
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  location: string;
  latitude?: number;
  longitude?: number;
  rate: number;
  rateType: 'hourly' | 'daily' | 'fixed';
  duration?: string;
  requirements?: string[];
  skillsRequired?: string[];
  experienceLevel?: string;
  urgency?: 'normal' | 'urgent';
  photos?: string[];
  maxApplications?: number;
  autoAccept?: boolean;
}

export interface CreateJobResponse {
  success: boolean;
  message: string;
  job?: {
    id: string;
    title: string;
    status: string;
    createdAt: string;
  };
}

export interface GetJobsQuery {
  page?: number;
  limit?: number;
  category?: string;
  location?: string;
  minRate?: number;
  maxRate?: number;
  rateType?: 'hourly' | 'daily' | 'fixed';
  urgency?: 'normal' | 'urgent';
  sortBy?: 'created_at' | 'rate' | 'distance';
  sortOrder?: 'asc' | 'desc';
}

export interface GetJobsResponse {
  success: boolean;
  jobs: Array<{
    id: string;
    title: string;
    description: string;
    category: string;
    location: string;
    rate: number;
    rateType: string;
    urgency: string;
    photos: string[];
    viewCount: number;
    applicationCount: number;
    createdAt: string;
    poster: {
      id: string;
      fullName: string;
      avatarUrl?: string;
      rating: number;
      reviewCount: number;
    };
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasMore: boolean;
  };
}

export interface GetJobByIdResponse {
  success: boolean;
  job?: {
    id: string;
    title: string;
    description: string;
    category: string;
    subcategory?: string;
    location: string;
    latitude?: number;
    longitude?: number;
    rate: number;
    rateType: string;
    duration?: string;
    requirements: string[];
    skillsRequired: string[];
    experienceLevel?: string;
    status: string;
    urgency: string;
    photos: string[];
    viewCount: number;
    applicationCount: number;
    maxApplications?: number;
    autoAccept: boolean;
    createdAt: string;
    updatedAt: string;
    poster: {
      id: string;
      fullName: string;
      avatarUrl?: string;
      rating: number;
      reviewCount: number;
      jobsPosted: number;
    };
    canApply: boolean;
    hasApplied: boolean;
    isBookmarked: boolean;
  };
}

export interface BookmarkJobRequest {
  jobId: string;
}

export interface BookmarkJobResponse {
  success: boolean;
  message: string;
  isBookmarked: boolean;
}

// Job Categories Types
export interface IJobCategoriesResponse {
  success: boolean;
  message: string;
  error?: string;
  categories?: IJobCategory[];
}

export interface IJobCategory {
  id: string;
  name: string;
  jobCount?: number;
}

// Job Title Suggestions Types
export interface IJobTitleSuggestionsRequest {
  category?: string;
  keyword?: string;
  limit?: number;
}

export interface IJobTitleSuggestionsResponse {
  success: boolean;
  message: string;
  error?: string;
  suggestions?: string[];
}

// Job Applicants Types
export interface IJobApplicantsRequest {
  sortBy?: 'match_score' | 'applied_at' | 'rating';
  sortOrder?: 'asc' | 'desc';
  status?: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
  page?: number;
  limit?: number;
}

export interface IJobApplicant {
  id: string;
  workerId: string;
  workerName: string;
  workerAvatar?: string;
  workerRating: number;
  workerReviewCount: number;
  workerCompletedJobs: number;
  personaId?: string;
  personaSkill?: string;
  coverLetter?: string;
  proposedRate?: number;
  proposedRateType?: 'hourly' | 'daily' | 'fixed';
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
  appliedAt: string;
  respondedAt?: string;
  hasMessages: boolean;
  lastMessageAt?: string;
  matchScore?: number;
}

export interface IJobApplicantsResponse {
  success: boolean;
  message: string;
  error?: string;
  applicants?: IJobApplicant[];
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasMore: boolean;
  };
  summary?: {
    total: number;
    pending: number;
    accepted: number;
    rejected: number;
    withdrawn: number;
  };
}

// My Jobs Types
export interface IMyJobsRequest {
  status?: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  page?: number;
  limit?: number;
}

export interface IMyJobSummary {
  id: string;
  title: string;
  category: string;
  location: string;
  rate: number;
  rateType: 'hourly' | 'daily' | 'fixed';
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  urgency: 'normal' | 'urgent';
  applicationCount: number;
  maxApplications?: number;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
}

export interface IMyJobsResponse {
  success: boolean;
  message: string;
  error?: string;
  jobs?: IMyJobSummary[];
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasMore: boolean;
  };
  summary?: {
    total: number;
    draft: number;
    active: number;
    paused: number;
    completed: number;
    cancelled: number;
  };
}
