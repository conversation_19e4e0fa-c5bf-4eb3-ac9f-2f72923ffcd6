/**
 * Users API Types
 * Request and response types for user-related endpoints
 */

export interface CreateWorkerProfileRequest {
  primarySkill: string;
  skills: string[];
  experience: '0-1' | '2-5' | '5-10' | '10+';
  hourlyRate?: number;
  dailyRate?: number;
  about?: string;
  phoneVisible?: boolean;
  portfolioPhotos?: string[];
}

export interface CreateWorkerProfileResponse {
  success: boolean;
  message: string;
  profile?: {
    userId: string;
    primarySkill: string;
    skills: string[];
    experience: string;
    hourlyRate?: number;
    dailyRate?: number;
    about?: string;
    isAvailable: boolean;
    createdAt: string;
  };
}

export interface CreatePosterProfileRequest {
  companyName?: string;
  about?: string;
  businessVerified?: boolean;
  businessDocuments?: string[];
  industry?: string;
  companySize?: 'individual' | 'small' | 'medium' | 'large' | 'enterprise';
  website?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  gstNumber?: string;
  panNumber?: string;
}

export interface CreatePosterProfileResponse {
  success: boolean;
  message: string;
  profile?: {
    userId: string;
    companyName?: string;
    about?: string;
    industry?: string;
    companySize?: string;
    website?: string;
    address?: string;
    city?: string;
    state?: string;
    pincode?: string;
    businessVerified: boolean;
    jobsPosted: number;
    totalSpent: number;
    rating: number;
    reviewCount: number;
    createdAt: string;
  };
}

export interface UpdateUserProfileRequest {
  fullName?: string;
  email?: string;
  location?: string;
  avatarUrl?: string;
}

export interface UpdateUserProfileResponse {
  success: boolean;
  message: string;
  user?: {
    id: string;
    phone: string;
    fullName?: string;
    email?: string;
    location?: string;
    avatarUrl?: string;
    updatedAt: string;
  };
}

export interface GetUserSettingsResponse {
  success: boolean;
  settings?: {
    userId: string;
    notifications: Record<string, any>;
    privacy: Record<string, any>;
    language: 'english' | 'hindi';
    preferences: Record<string, any>;
    updatedAt: string;
  };
}

export interface UpdateUserSettingsRequest {
  notifications?: Record<string, any>;
  privacy?: Record<string, any>;
  language?: 'english' | 'hindi';
  preferences?: Record<string, any>;
}

export interface UpdateUserSettingsResponse {
  success: boolean;
  message: string;
  settings?: {
    userId: string;
    notifications: Record<string, any>;
    privacy: Record<string, any>;
    language: 'english' | 'hindi';
    preferences: Record<string, any>;
    updatedAt: string;
  };
}

export interface UploadAvatarResponse {
  success: boolean;
  message: string;
  avatarUrl?: string;
}
