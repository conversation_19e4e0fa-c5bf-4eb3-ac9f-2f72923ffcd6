/**
 * Server API Types
 * Exports all API request/response types
 */

export * from './auth.types';
export * from './jobs.types';
export * from './users.types';

// Common API response types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface ApiError {
  success: false;
  message: string;
  error: string;
  statusCode: number;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
}

export interface PaginationResponse {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasMore: boolean;
}
