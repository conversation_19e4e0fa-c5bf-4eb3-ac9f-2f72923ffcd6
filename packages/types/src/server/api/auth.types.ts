/**
 * Authentication API Types
 * Request and response types for authentication endpoints
 */

export interface SendOtpRequest {
  phone: string;
}

export interface SendOtpResponse {
  success: boolean;
  message: string;
  otpId?: string;
}

export interface VerifyOtpRequest {
  phone: string;
  otp: string;
  otpId: string;
}

export interface VerifyOtpResponse {
  success: boolean;
  message: string;
  user?: {
    id: string;
    phone: string;
    phoneVerified: boolean;
    fullName?: string;
    isNewUser: boolean;
  };
  tokens?: {
    accessToken: string;
    refreshToken: string;
  };
}

export interface ResendOtpRequest {
  phone: string;
  otpId: string;
}

export interface ResendOtpResponse {
  success: boolean;
  message: string;
  otpId?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  success: boolean;
  message: string;
  tokens?: {
    accessToken: string;
    refreshToken: string;
  };
}

export interface LogoutRequest {
  refreshToken: string;
}

export interface LogoutResponse {
  success: boolean;
  message: string;
}

export interface MeResponse {
  success: boolean;
  user?: {
    id: string;
    phone: string;
    phoneVerified: boolean;
    fullName?: string;
    email?: string;
    avatarUrl?: string;
    location?: string;
    isActive: boolean;
    isVerified: boolean;
    createdAt: string;
    updatedAt: string;
  };
}
