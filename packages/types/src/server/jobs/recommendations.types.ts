/**
 * Job Recommendations Types
 * Types for job recommendations and dashboard functionality
 */

export interface IJobRecommendationRequest {
  page?: number;
  limit?: number;
  location?: string;
  radius?: number; // in kilometers
  skills?: string[];
  categories?: string[];
}

export interface IJobSummary {
  id: string;
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  location: string;
  latitude?: number;
  longitude?: number;
  rate: number;
  rateType: 'hourly' | 'daily' | 'fixed';
  duration?: string;
  skillsRequired: string[];
  experienceLevel?: string;
  urgency: 'normal' | 'urgent';
  photos: string[];
  viewCount: number;
  applicationCount: number;
  maxApplications?: number;
  distance?: number; // in kilometers, for nearby jobs
  matchScore?: number; // 0-100, for skill matching
  createdAt: string;
  expiresAt?: string;
  poster: {
    id: string;
    name?: string;
    companyName?: string;
    rating: number;
    reviewCount: number;
    businessVerified: boolean;
  };
}

export interface IJobRecommendationBucket {
  title: string;
  description: string;
  jobs: IJobSummary[];
  hasMore: boolean;
  totalCount: number;
}

export interface IJobRecommendationsResponse {
  success: boolean;
  message: string;
  error?: string;
  recommendations?: {
    featured: IJobRecommendationBucket;
    nearby: IJobRecommendationBucket;
    skillMatch: IJobRecommendationBucket;
  };
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
  };
}

export interface IWorkerDashboardStats {
  applications: {
    total: number;
    pending: number;
    accepted: number;
    rejected: number;
    withdrawn: number;
  };
  bookmarks: {
    total: number;
    recentCount: number; // last 7 days
  };
  profile: {
    completionPercentage: number;
    missingFields: string[];
    hasWorkerProfile: boolean;
    hasPersonas: boolean;
    personaCount: number;
  };
  activity: {
    lastApplicationDate?: string;
    lastLoginDate?: string;
    jobsViewedToday: number;
    jobsViewedThisWeek: number;
  };
  earnings: {
    totalEarnings: number;
    completedJobs: number;
    averageRating: number;
    reviewCount: number;
  };
  recommendations: {
    newJobsCount: number; // jobs posted in last 24 hours matching skills
    urgentJobsCount: number;
    nearbyJobsCount: number;
  };
}

export interface IWorkerDashboardStatsResponse {
  success: boolean;
  message: string;
  error?: string;
  stats?: IWorkerDashboardStats;
}

// Job Search Types
export interface IJobSearchRequest {
  keyword?: string;
  category?: string;
  subcategory?: string;
  location?: string;
  radius?: number; // in kilometers
  minRate?: number;
  maxRate?: number;
  rateType?: 'hourly' | 'daily' | 'fixed';
  experienceLevel?: string;
  urgency?: 'normal' | 'urgent';
  skills?: string[];
  sortBy?: 'relevance' | 'date' | 'rate' | 'distance';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface IJobSearchResponse {
  success: boolean;
  message: string;
  error?: string;
  jobs?: IJobSummary[];
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasMore: boolean;
  };
  searchMeta?: {
    keyword?: string;
    appliedFilters: {
      category?: string;
      location?: string;
      rateRange?: { min: number; max: number };
      skills?: string[];
    };
    searchTime: number; // in milliseconds
  };
}

// Job Filters Types
export interface IJobFilterOption {
  value: string;
  label: string;
  count: number;
  isSelected?: boolean;
}

export interface IJobFilterGroup {
  name: string;
  label: string;
  type: 'single' | 'multiple' | 'range';
  options: IJobFilterOption[];
}

export interface IJobFiltersResponse {
  success: boolean;
  message: string;
  error?: string;
  filters?: {
    categories: IJobFilterGroup;
    locations: IJobFilterGroup;
    rateRanges: IJobFilterGroup;
    experienceLevels: IJobFilterGroup;
    urgency: IJobFilterGroup;
    skills: IJobFilterGroup;
  };
  totalJobsCount?: number;
}
