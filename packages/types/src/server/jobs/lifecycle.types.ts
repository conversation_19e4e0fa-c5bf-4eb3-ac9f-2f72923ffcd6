/**
 * Job Lifecycle API Types
 */

// Job Status Types
export interface IJobStatusRequest {
  // No additional parameters needed - jobId comes from URL
}

export interface IJobStatusTimeline {
  id: string;
  event: 'created' | 'assigned' | 'started' | 'progress_updated' | 'completed' | 'cancelled';
  description: string;
  timestamp: string;
  userId?: string;
  userName?: string;
  metadata?: Record<string, any>;
}

export interface IJobStatusDetails {
  id: string;
  title: string;
  description: string;
  category: string;
  location: string;
  rate: number;
  rateType: 'hourly' | 'daily' | 'fixed';
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  urgency: 'normal' | 'urgent';
  posterId: string;
  posterName: string;
  posterAvatar?: string;
  assignedWorkerId?: string;
  assignedWorkerName?: string;
  assignedWorkerAvatar?: string;
  startedAt?: string;
  completedAt?: string;
  workPhotos?: string[];
  paymentStatus: 'pending' | 'processing' | 'completed' | 'failed' | 'disputed' | 'refunded';
  timeline: IJobStatusTimeline[];
  createdAt: string;
  updatedAt: string;
}

export interface IJobStatusResponse {
  success: boolean;
  message: string;
  error?: string;
  jobStatus?: IJobStatusDetails;
}

// Start Work Types
export interface IStartWorkRequest {
  // No additional body parameters needed - worker is authenticated user
}

export interface IStartWorkResponse {
  success: boolean;
  message: string;
  error?: string;
  startedAt?: string;
}

// Update Progress Types
export interface IUpdateProgressRequest {
  progressDescription: string;
  progressPhotos?: string[];
  estimatedCompletion?: string;
  metadata?: Record<string, any>;
}

export interface IUpdateProgressResponse {
  success: boolean;
  message: string;
  error?: string;
  updatedAt?: string;
}

// Complete Job Types
export interface ICompleteJobRequest {
  completionNotes?: string;
  finalPhotos?: string[];
  hoursWorked?: number;
  metadata?: Record<string, any>;
}

export interface ICompleteJobResponse {
  success: boolean;
  message: string;
  error?: string;
  completedAt?: string;
}

// Assign Worker Types
export interface IAssignWorkerRequest {
  workerId: string;
  source?: 'application' | 'invite' | 'direct'; // Source of assignment
  applicationId?: string; // If assigning from application
  inviteId?: string; // If assigning from invite
  message?: string; // Optional message to worker
}

export interface IAssignWorkerResponse {
  success: boolean;
  message: string;
  error?: string;
  job?: IJobStatusDetails;
}
