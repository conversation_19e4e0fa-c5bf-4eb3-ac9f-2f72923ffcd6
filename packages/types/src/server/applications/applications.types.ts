/**
 * Application Types
 * Types for job application management, limits, and workflow
 */

import { ApplicationStatus, RateType } from '../database/enums.types';

// Apply to Job Request/Response
export interface IApplyToJobRequest {
  personaId?: string;
  coverLetter?: string;
  proposedRate?: number;
  proposedRateType?: RateType;
}

export interface IApplyToJobResponse {
  success: boolean;
  message: string;
  error?: string;
  application?: {
    id: string;
    jobId: string;
    workerId: string;
    personaId?: string;
    coverLetter?: string;
    proposedRate?: number;
    proposedRateType?: RateType;
    status: ApplicationStatus;
    appliedAt: string;
  };
}

// Application Limits Request/Response
export interface IApplicationLimitsResponse {
  success: boolean;
  message: string;
  error?: string;
  limits?: {
    dailyLimit: number;
    applicationsToday: number;
    remainingToday: number;
    nextResetAt: string;
    canApply: boolean;
  };
}

// My Applications Request/Response
export interface IMyApplicationsRequest {
  status?: ApplicationStatus;
  page?: number;
  limit?: number;
}

export interface IApplicationSummary {
  id: string;
  jobId: string;
  jobTitle: string;
  jobCategory: string;
  jobLocation: string;
  jobRate: number;
  jobRateType: RateType;
  jobUrgency: 'normal' | 'urgent';
  posterName: string;
  posterCompany?: string;
  posterVerified: boolean;
  personaId?: string;
  personaSkill?: string;
  coverLetter?: string;
  proposedRate?: number;
  proposedRateType?: RateType;
  status: ApplicationStatus;
  appliedAt: string;
  respondedAt?: string;
  hasMessages: boolean;
  lastMessageAt?: string;
}

export interface IMyApplicationsResponse {
  success: boolean;
  message: string;
  error?: string;
  applications?: IApplicationSummary[];
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasMore: boolean;
  };
  summary?: {
    total: number;
    pending: number;
    accepted: number;
    rejected: number;
    withdrawn: number;
  };
}

// Withdraw Application Request/Response
export interface IWithdrawApplicationResponse {
  success: boolean;
  message: string;
  error?: string;
  application?: {
    id: string;
    status: ApplicationStatus;
    withdrawnAt: string;
  };
}
