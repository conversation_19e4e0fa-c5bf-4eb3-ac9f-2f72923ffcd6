/**
 * Common Utility Types
 * Shared utility types for the server
 */

export interface Environment {
  NODE_ENV: 'development' | 'production' | 'test';
  PORT: number;
  SUPABASE_URL: string;
  SUPABASE_SERVICE_KEY: string;
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;
  REFRESH_TOKEN_EXPIRES_IN: string;
  TWOFACTOR_API_KEY: string;
  MOCK_OTP: string;
  MOCK_OTP_VALUE: string;
  UPLOAD_MAX_SIZE: number;
  RATE_LIMIT_WINDOW_MS: number;
  RATE_LIMIT_MAX_REQUESTS: number;
  DB_POOL_SIZE: number;
  DB_QUERY_TIMEOUT: number;
  SUPABASE_ANON_KEY: string;
}

export interface LoggerConfig {
  level: 'error' | 'warn' | 'info' | 'debug';
  format: 'json' | 'simple';
  transports: Array<'console' | 'file'>;
  filename?: string;
  maxSize?: string;
  maxFiles?: number;
}

export interface CorsConfig {
  origin: string | string[] | boolean;
  methods: string[];
  allowedHeaders: string[];
  credentials: boolean;
}

export interface RateLimitConfig {
  windowMs: number;
  max: number;
  message: string;
  standardHeaders: boolean;
  legacyHeaders: boolean;
}

export interface UploadConfig {
  maxSize: number;
  allowedMimeTypes: string[];
  destination: string;
  filename: (originalName: string) => string;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface HttpError extends Error {
  statusCode: number;
  isOperational: boolean;
}

export type AsyncHandler<T = any> = (
  req: any,
  res: any,
  next: any
) => Promise<T>;

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasMore: boolean;
  };
}
