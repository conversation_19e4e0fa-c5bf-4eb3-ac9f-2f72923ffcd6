/**
 * Validation Utility Types
 * Types for request validation and schema definitions
 */

// Note: Joi types should be imported in the actual implementation files

export interface ValidationSchema {
  body?: any; // Joi.ObjectSchema - imported in implementation
  query?: any; // Joi.ObjectSchema - imported in implementation
  params?: any; // Joi.ObjectSchema - imported in implementation
  headers?: any; // Joi.ObjectSchema - imported in implementation
}

export interface ValidationOptions {
  abortEarly?: boolean;
  allowUnknown?: boolean;
  stripUnknown?: boolean;
}

export interface ValidationResult<T = any> {
  error?: any; // Joi.ValidationError - imported in implementation
  value: T;
}

export interface PhoneValidation {
  isValid: boolean;
  formatted?: string;
  country?: string;
  error?: string;
}

export interface FileValidation {
  isValid: boolean;
  mimeType?: string;
  size?: number;
  error?: string;
}

export interface CoordinateValidation {
  isValid: boolean;
  latitude?: number;
  longitude?: number;
  error?: string;
}

// Common validation schemas
export interface AuthValidationSchemas {
  sendOtp: ValidationSchema;
  verifyOtp: ValidationSchema;
  resendOtp: ValidationSchema;
  refreshToken: ValidationSchema;
  logout: ValidationSchema;
}

export interface JobValidationSchemas {
  createJob: ValidationSchema;
  updateJob: ValidationSchema;
  getJobs: ValidationSchema;
  getJobById: ValidationSchema;
  bookmarkJob: ValidationSchema;
}

export interface UserValidationSchemas {
  createWorkerProfile: ValidationSchema;
  createPosterProfile: ValidationSchema;
  updateProfile: ValidationSchema;
  updateSettings: ValidationSchema;
  uploadAvatar: ValidationSchema;
}
