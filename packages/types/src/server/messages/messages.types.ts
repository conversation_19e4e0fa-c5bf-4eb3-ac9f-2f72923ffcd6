/**
 * Message API Types
 */

// Send Message Types
export interface ISendMessageRequest {
  text: string;
  messageType?: 'text' | 'image' | 'document';
  attachments?: {
    url: string;
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
  }[];
}

export interface IMessage {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  receiverId: string;
  text: string;
  messageType: 'text' | 'image' | 'document';
  status: 'sent' | 'delivered' | 'read';
  timestamp: string;
  attachments?: {
    id: string;
    url: string;
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
  }[];
}

export interface ISendMessageResponse {
  success: boolean;
  message: string;
  error?: string;
  messageData?: IMessage;
}

// Get Messages Types
export interface IGetMessagesRequest {
  page?: number;
  limit?: number;
  before?: string; // timestamp for pagination
}

export interface IGetMessagesResponse {
  success: boolean;
  message: string;
  error?: string;
  messages?: IMessage[];
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasMore: boolean;
    before?: string;
  };
}

// Mark Message as Read Types
export interface IMarkMessageReadRequest {
  // No body needed, messageId comes from URL params
}

export interface IMarkMessageReadResponse {
  success: boolean;
  message: string;
  error?: string;
  messageData?: {
    id: string;
    conversationId: string;
    senderId: string;
    receiverId: string;
    text: string;
    messageType: 'text' | 'image' | 'document';
    status: 'sent' | 'delivered' | 'read';
    timestamp: string;
  };
}
