/**
 * WebSocket Message Types
 */

import { IConversationSummary, IGetConversationsRequest } from './conversations.types';
import { IMessage, ISendMessageRequest, IGetMessagesRequest } from './messages.types';

// WebSocket Event Types
export interface ISocketEvents {
  // Client to Server Events
  'messages:conversations:list': (data: IGetConversationsRequest, callback: (response: ISocketConversationsListResponse) => void) => void;
  'messages:conversation:get': (data: ISocketConversationGetRequest, callback: (response: ISocketConversationGetResponse) => void) => void;
  'messages:message:send': (data: ISocketMessageSendRequest, callback: (response: ISocketMessageSendResponse) => void) => void;
  'messages:message:read': (data: ISocketMessageReadRequest, callback: (response: ISocketMessageReadResponse) => void) => void;
  'messages:typing': (data: ISocketTypingRequest) => void;

  // Server to Client Events
  'message:new': (data: ISocketNewMessageEvent) => void;
  'message:read': (data: ISocketMessageReadEvent) => void;
  'conversation:updated': (data: ISocketConversationUpdatedEvent) => void;
  'typing:start': (data: ISocketTypingEvent) => void;
  'typing:stop': (data: ISocketTypingEvent) => void;
}

// Request/Response Types
export interface ISocketConversationsListResponse {
  success: boolean;
  message: string;
  error?: string;
  conversations?: IConversationSummary[];
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasMore: boolean;
  };
}

export interface ISocketConversationGetRequest {
  conversationId: string;
  page?: number;
  limit?: number;
}

export interface ISocketConversationGetResponse {
  success: boolean;
  message: string;
  error?: string;
  messages?: IMessage[];
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasMore: boolean;
  };
}

export interface ISocketMessageSendRequest {
  conversationId: string;
  text: string;
  messageType?: 'text' | 'image' | 'document';
}

export interface ISocketMessageSendResponse {
  success: boolean;
  message: string;
  error?: string;
  messageData?: IMessage;
}

export interface ISocketMessageReadRequest {
  messageId: string;
}

export interface ISocketMessageReadResponse {
  success: boolean;
  message: string;
  error?: string;
  messageData?: {
    id: string;
    conversationId: string;
    senderId: string;
    receiverId: string;
    text: string;
    messageType: 'text' | 'image' | 'document';
    status: 'sent' | 'delivered' | 'read';
    timestamp: string;
  };
}

export interface ISocketTypingRequest {
  conversationId: string;
}

// Event Types
export interface ISocketNewMessageEvent {
  conversationId: string;
  message: IMessage;
}

export interface ISocketMessageReadEvent {
  conversationId: string;
  messageId: string;
  readBy: string;
  readAt: string;
}

export interface ISocketConversationUpdatedEvent {
  conversation: IConversationSummary;
}

export interface ISocketTypingEvent {
  conversationId: string;
  userId: string;
  userName: string;
}

// Socket Authentication
export interface ISocketAuthData {
  token: string;
}

export interface ISocketUser {
  id: string;
  name: string;
  avatar?: string;
}
