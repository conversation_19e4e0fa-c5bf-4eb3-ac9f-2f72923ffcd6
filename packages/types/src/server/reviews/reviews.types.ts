/**
 * Reviews API Types
 */

// Get Reviews Types
export interface IGetReviewsRequest {
  reviewType?: 'worker_to_poster' | 'poster_to_worker';
  isPublic?: boolean;
  page?: number;
  limit?: number;
}

export interface IReviewItem {
  id: string;
  jobId: string;
  jobTitle: string;
  reviewerId: string;
  reviewerName: string;
  reviewerAvatar?: string;
  revieweeId: string;
  revieweeName: string;
  rating: number;
  comment?: string;
  reviewType: 'worker_to_poster' | 'poster_to_worker';
  communicationRating?: number;
  workQualityRating?: number;
  timelinessRating?: number;
  professionalismRating?: number;
  isPublic: boolean;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IReviewsSummary {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  categoryAverages: {
    communication?: number;
    workQuality?: number;
    timeliness?: number;
    professionalism?: number;
  };
}

export interface IGetReviewsResponse {
  success: boolean;
  message: string;
  error?: string;
  reviews?: IReviewItem[];
  summary?: IReviewsSummary;
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasMore: boolean;
  };
}

// Post Review Types
export interface IPostReviewRequest {
  jobId: string;
  revieweeId: string;
  rating: number;
  comment?: string;
  communicationRating?: number;
  workQualityRating?: number;
  timelinessRating?: number;
  professionalismRating?: number;
  isPublic?: boolean;
}

export interface IPostReviewResponse {
  success: boolean;
  message: string;
  error?: string;
  review?: IReviewItem;
}
