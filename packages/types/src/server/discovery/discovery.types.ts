/**
 * Discovery API Types
 * For poster worker discovery and search functionality
 */

// Poster Dashboard Types
export interface IPosterDashboardRequest {
  radius?: number; // Search radius in kilometers
  limit?: number;
}

export interface INearbyWorker {
  userId: string;
  fullName: string;
  avatarUrl?: string;
  location?: string;
  distance?: number; // Distance in kilometers
  primarySkill?: string;
  skills: string[];
  experience?: string;
  hourlyRate?: number;
  dailyRate?: number;
  rating: number;
  reviewCount: number;
  completedJobs: number;
  isAvailable: boolean;
  lastActive?: string;
  personas: IWorkerPersonaSummary[];
}

export interface IWorkerPersonaSummary {
  id: string;
  skill: string;
  experience?: string;
  hourlyRate?: number;
  dailyRate?: number;
  rating: number;
  reviewCount: number;
  completedJobs: number;
  isActive: boolean;
}

export interface IPosterDashboardStats {
  totalActiveJobs: number;
  totalApplications: number;
  totalCompletedJobs: number;
  totalSpent: number;
  nearbyWorkersCount: number;
}

export interface IPosterDashboardResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: {
    stats: IPosterDashboardStats;
    nearbyWorkers: INearbyWorker[];
    pagination: {
      page: number;
      limit: number;
      totalCount: number;
      hasMore: boolean;
    };
  };
}

// Worker Search Types
export interface IWorkerSearchRequest {
  query?: string; // Search query for skills or name
  skills?: string[]; // Filter by specific skills
  experience?: string; // Filter by experience level
  minHourlyRate?: number;
  maxHourlyRate?: number;
  minDailyRate?: number;
  maxDailyRate?: number;
  location?: string; // Location filter
  radius?: number; // Search radius in kilometers
  minRating?: number; // Minimum rating filter
  availability?: boolean; // Filter by availability
  sortBy?: 'rating' | 'distance' | 'rate' | 'experience' | 'completedJobs';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface IWorkerSearchResult {
  userId: string;
  fullName: string;
  avatarUrl?: string;
  location?: string;
  distance?: number;
  primarySkill?: string;
  skills: string[];
  experience?: string;
  hourlyRate?: number;
  dailyRate?: number;
  about?: string;
  rating: number;
  reviewCount: number;
  completedJobs: number;
  totalEarnings: number;
  isAvailable: boolean;
  phoneVisible: boolean;
  portfolioPhotos: string[];
  lastActive?: string;
  personas: IWorkerPersonaSummary[];
  matchScore?: number; // Relevance score for search query
}

export interface IWorkerSearchFilters {
  skills: Array<{
    skill: string;
    count: number;
  }>;
  experienceLevels: Array<{
    level: string;
    count: number;
  }>;
  rateRanges: {
    hourly: {
      min: number;
      max: number;
      average: number;
    };
    daily: {
      min: number;
      max: number;
      average: number;
    };
  };
  locations: Array<{
    location: string;
    count: number;
  }>;
}

export interface IWorkerSearchResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: {
    workers: IWorkerSearchResult[];
    filters: IWorkerSearchFilters;
    pagination: {
      page: number;
      limit: number;
      totalPages: number;
      totalCount: number;
      hasMore: boolean;
    };
  };
}
