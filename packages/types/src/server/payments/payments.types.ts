/**
 * Payment API Types
 */

// Payment History Types
export interface IPaymentHistoryRequest {
  status?: 'pending' | 'processing' | 'completed' | 'failed' | 'disputed' | 'refunded';
  method?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

export interface IPaymentHistoryItem {
  id: string;
  jobId: string;
  jobTitle: string;
  payerId: string;
  payerName: string;
  payeeId: string;
  payeeName: string;
  amount: number;
  currency: string;
  method: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'disputed' | 'refunded';
  transactionId?: string;
  description?: string;
  receiptUrl?: string;
  hasDispute: boolean;
  disputeReason?: string;
  disputeStatus?: 'open' | 'resolved' | 'escalated';
  createdAt: string;
  processedAt?: string;
  completedAt?: string;
}

export interface IPaymentHistoryResponse {
  success: boolean;
  message: string;
  error?: string;
  payments?: IPaymentHistoryItem[];
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasMore: boolean;
  };
  summary?: {
    total: number;
    pending: number;
    completed: number;
    failed: number;
    disputed: number;
  };
}

// Earnings Types
export interface IEarningsRequest {
  period?: 'week' | 'month' | 'quarter' | 'year' | 'all';
  startDate?: string;
  endDate?: string;
}

export interface IEarningsBreakdown {
  period: string;
  totalEarnings: number;
  completedJobs: number;
  averageJobValue: number;
  currency: string;
}

export interface IEarningsStats {
  totalEarnings: number;
  totalJobs: number;
  averageJobValue: number;
  currency: string;
  breakdown: IEarningsBreakdown[];
  topCategories: {
    category: string;
    earnings: number;
    jobCount: number;
  }[];
}

export interface IEarningsResponse {
  success: boolean;
  message: string;
  error?: string;
  earnings?: IEarningsStats;
}

// Payment Export Types
export interface IPaymentExportRequest {
  format?: 'csv' | 'xlsx' | 'pdf';
  status?: 'pending' | 'processing' | 'completed' | 'failed' | 'disputed' | 'refunded';
  startDate?: string;
  endDate?: string;
}

export interface IPaymentExportResponse {
  success: boolean;
  message: string;
  error?: string;
  exportUrl?: string;
  expiresAt?: string;
}

// Payment Processing Types
export interface IPaymentProcessRequest {
  jobId: string;
  amount: number;
  method: string;
  description?: string;
  metadata?: Record<string, any>;
}

export interface IPaymentProcessResponse {
  success: boolean;
  message: string;
  error?: string;
  paymentId?: string;
  transactionId?: string;
  status?: string;
  gatewayResponse?: Record<string, any>;
}
