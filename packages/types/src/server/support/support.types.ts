/**
 * Support API Types
 */

// FAQ Types
export interface IFAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IFAQCategory {
  category: string;
  displayName: string;
  order: number;
  faqs: IFAQItem[];
}

export interface IGetFAQResponse {
  success: boolean;
  message: string;
  error?: string;
  categories?: IFAQCategory[];
}

// Support Ticket Types
export interface ICreateSupportTicketRequest {
  subject: string;
  description: string;
  category: 'technical' | 'billing' | 'account' | 'general' | 'bug_report' | 'feature_request';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  userEmail?: string;
  userPhone?: string;
  attachments?: string[];
}

export interface ISupportTicket {
  id: string;
  ticketNumber: string;
  userId?: string;
  subject: string;
  description: string;
  category: string;
  priority: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  userEmail?: string;
  userPhone?: string;
  attachments?: string[];
  assignedTo?: string;
  slaDeadline?: string;
  resolvedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ICreateSupportTicketResponse {
  success: boolean;
  message: string;
  error?: string;
  ticket?: ISupportTicket;
}

// Contact Info Types
export interface IContactInfo {
  email: string;
  phone: string;
  whatsapp?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  businessHours: {
    monday: string;
    tuesday: string;
    wednesday: string;
    thursday: string;
    friday: string;
    saturday: string;
    sunday: string;
  };
  socialMedia?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  emergencyContact?: {
    phone: string;
    hours: string;
  };
}

export interface IGetContactInfoResponse {
  success: boolean;
  message: string;
  error?: string;
  contactInfo?: IContactInfo;
}
