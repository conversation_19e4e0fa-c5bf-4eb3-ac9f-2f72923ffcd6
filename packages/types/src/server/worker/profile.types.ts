/**
 * Worker Profile Types
 * Types for worker profile management and personas
 */

export interface ICreateWorkerProfileRequest {
  primarySkill: string;
  skills: string[];
  experience: '0-1' | '2-5' | '5-10' | '10+';
  hourlyRate?: number;
  dailyRate?: number;
  about?: string;
  phoneVisible?: boolean;
  portfolioPhotos?: string[];
}

export interface ICreateWorkerProfileResponse {
  success: boolean;
  message: string;
  error?: string;
  profile?: {
    userId: string;
    primarySkill: string;
    skills: string[];
    experience: string;
    hourlyRate?: number;
    dailyRate?: number;
    about?: string;
    isAvailable: boolean;
    createdAt: string;
  };
}

export interface IWorkerPersonaRequest {
  skill: string;
  experience?: string;
  hourlyRate?: number;
  dailyRate?: number;
  isActive?: boolean;
}

export interface IWorkerPersonaResponse {
  success: boolean;
  message: string;
  error?: string;
  persona?: {
    id: string;
    userId: string;
    skill: string;
    experience?: string;
    hourlyRate?: number;
    dailyRate?: number;
    isActive: boolean;
    completedJobs: number;
    rating: number;
    reviewCount: number;
    createdAt: string;
  };
}

export interface IGetWorkerPersonasResponse {
  success: boolean;
  message: string;
  error?: string;
  personas?: Array<{
    id: string;
    userId: string;
    skill: string;
    experience?: string;
    hourlyRate?: number;
    dailyRate?: number;
    isActive: boolean;
    completedJobs: number;
    rating: number;
    reviewCount: number;
    createdAt: string;
    updatedAt: string;
  }>;
}

export interface ISkillCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  skills: string[];
  isActive: boolean;
}

export interface IGetSkillCategoriesResponse {
  success: boolean;
  message: string;
  error?: string;
  categories?: ISkillCategory[];
}

export interface IMarketRate {
  skill: string;
  category: string;
  averageHourlyRate: number;
  averageDailyRate: number;
  minRate: number;
  maxRate: number;
  currency: string;
  location?: string;
}

export interface IGetMarketRatesRequest {
  skills?: string[];
  category?: string;
  location?: string;
  experienceLevel?: string;
}

export interface IGetMarketRatesResponse {
  success: boolean;
  message: string;
  error?: string;
  rates?: IMarketRate[];
}

/**
 * Poster Profile Types
 * Types for poster profile management
 */

export interface ICreatePosterProfileRequest {
  companyName?: string;
  about?: string;
  businessVerified?: boolean;
  businessDocuments?: string[];
  industry?: string;
  companySize?: 'individual' | 'small' | 'medium' | 'large' | 'enterprise';
  website?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  gstNumber?: string;
  panNumber?: string;
}

export interface ICreatePosterProfileResponse {
  success: boolean;
  message: string;
  error?: string;
  profile?: {
    userId: string;
    companyName?: string;
    about?: string;
    industry?: string;
    companySize?: string;
    website?: string;
    address?: string;
    city?: string;
    state?: string;
    pincode?: string;
    businessVerified: boolean;
    jobsPosted: number;
    totalSpent: number;
    rating: number;
    reviewCount: number;
    createdAt: string;
  };
}

export interface IUpdatePosterProfileRequest {
  companyName?: string;
  about?: string;
  industry?: string;
  companySize?: 'individual' | 'small' | 'medium' | 'large' | 'enterprise';
  website?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  gstNumber?: string;
  panNumber?: string;
}

export interface IUpdatePosterProfileResponse {
  success: boolean;
  message: string;
  error?: string;
  profile?: {
    userId: string;
    companyName?: string;
    about?: string;
    industry?: string;
    companySize?: string;
    website?: string;
    address?: string;
    city?: string;
    state?: string;
    pincode?: string;
    businessVerified: boolean;
    updatedAt: string;
  };
}

export interface IGetPosterProfileResponse {
  success: boolean;
  message: string;
  error?: string;
  profile?: {
    userId: string;
    companyName?: string;
    about?: string;
    industry?: string;
    companySize?: string;
    website?: string;
    address?: string;
    city?: string;
    state?: string;
    pincode?: string;
    gstNumber?: string;
    panNumber?: string;
    businessVerified: boolean;
    businessDocuments: string[];
    jobsPosted: number;
    totalSpent: number;
    rating: number;
    reviewCount: number;
    createdAt: string;
    updatedAt: string;
  };
}

export interface IUploadBusinessDocumentRequest {
  documentType: 'gst_certificate' | 'pan_card' | 'business_license' | 'incorporation_certificate' | 'other';
  documentName: string;
}

export interface IUploadBusinessDocumentResponse {
  success: boolean;
  message: string;
  error?: string;
  document?: {
    id: string;
    documentType: string;
    documentName: string;
    documentUrl: string;
    uploadedAt: string;
  };
}

export interface IPosterStats {
  totalJobsPosted: number;
  activeJobs: number;
  completedJobs: number;
  totalApplicationsReceived: number;
  totalSpent: number;
  averageRating: number;
  totalReviews: number;
  responseRate: number;
  averageResponseTime: string; // in hours
}

export interface IGetPosterStatsResponse {
  success: boolean;
  message: string;
  error?: string;
  stats?: IPosterStats;
}