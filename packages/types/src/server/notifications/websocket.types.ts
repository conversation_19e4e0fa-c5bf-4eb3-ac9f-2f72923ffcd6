/**
 * WebSocket Notification Types
 */

import { INotification, IGetNotificationsRequest } from './notifications.types';

// WebSocket Event Types for Notifications
export interface INotificationSocketEvents {
  // Client to Server Events
  'notifications:list': (data: ISocketNotificationsListRequest, callback: (response: ISocketNotificationsListResponse) => void) => void;
  'notifications:mark-read': (data: ISocketNotificationMarkReadRequest, callback: (response: ISocketNotificationMarkReadResponse) => void) => void;
  'notifications:mark-all-read': (data: ISocketNotificationMarkAllReadRequest, callback: (response: ISocketNotificationMarkAllReadResponse) => void) => void;
  'notifications:delete': (data: ISocketNotificationDeleteRequest, callback: (response: ISocketNotificationDeleteResponse) => void) => void;
  'notifications:register-device': (data: ISocketRegisterDeviceRequest, callback: (response: ISocketRegisterDeviceResponse) => void) => void;

  // Server to Client Events
  'notifications:new': (data: ISocketNewNotificationEvent) => void;
  'notifications:updated': (data: ISocketNotificationUpdatedEvent) => void;
  'notifications:deleted': (data: ISocketNotificationDeletedEvent) => void;
  'notifications:count-updated': (data: ISocketNotificationCountUpdatedEvent) => void;
}

// Request/Response Types
export interface ISocketNotificationsListRequest extends IGetNotificationsRequest {}

export interface ISocketNotificationsListResponse {
  success: boolean;
  message: string;
  error?: string;
  notifications?: INotification[];
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasMore: boolean;
  };
  summary?: {
    total: number;
    unread: number;
    urgent: number;
  };
}

export interface ISocketNotificationMarkReadRequest {
  notificationId: string;
}

export interface ISocketNotificationMarkReadResponse {
  success: boolean;
  message: string;
  error?: string;
}

export interface ISocketNotificationMarkAllReadRequest {
  // No additional fields needed
}

export interface ISocketNotificationMarkAllReadResponse {
  success: boolean;
  message: string;
  error?: string;
  updatedCount?: number;
}

export interface ISocketNotificationDeleteRequest {
  notificationId: string;
}

export interface ISocketNotificationDeleteResponse {
  success: boolean;
  message: string;
  error?: string;
}

export interface ISocketRegisterDeviceRequest {
  deviceToken: string;
  platform: 'ios' | 'android' | 'web';
}

export interface ISocketRegisterDeviceResponse {
  success: boolean;
  message: string;
  error?: string;
}

// Event Types
export interface ISocketNewNotificationEvent {
  notification: INotification;
}

export interface ISocketNotificationUpdatedEvent {
  notification: INotification;
}

export interface ISocketNotificationDeletedEvent {
  notificationId: string;
}

export interface ISocketNotificationCountUpdatedEvent {
  unreadCount: number;
  totalCount: number;
  urgentCount: number;
}
