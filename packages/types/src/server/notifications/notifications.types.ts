/**
 * Notification API Types
 */

// Get Notifications Types
export interface IGetNotificationsRequest {
  type?: string;
  isRead?: boolean;
  page?: number;
  limit?: number;
}

export interface INotification {
  id: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  isRead: boolean;
  isUrgent: boolean;
  actionUrl?: string;
  metadata?: Record<string, any>;
  createdAt: string;
}

export interface IGetNotificationsResponse {
  success: boolean;
  message: string;
  error?: string;
  notifications?: INotification[];
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasMore: boolean;
  };
  summary?: {
    total: number;
    unread: number;
    urgent: number;
  };
}

// Mark Notification as Read Types
export interface IMarkNotificationReadResponse {
  success: boolean;
  message: string;
  error?: string;
}

// Mark All Notifications as Read Types
export interface IMarkAllNotificationsReadResponse {
  success: boolean;
  message: string;
  error?: string;
  updatedCount?: number;
}

// Delete Notification Types
export interface IDeleteNotificationResponse {
  success: boolean;
  message: string;
  error?: string;
}

// Register Device Token Types
export interface IRegisterDeviceTokenRequest {
  deviceToken: string;
  platform: 'ios' | 'android' | 'web';
}

export interface IRegisterDeviceTokenResponse {
  success: boolean;
  message: string;
  error?: string;
}

// Create Notification Types (for internal use)
export interface ICreateNotificationRequest {
  userId: string;
  type: string;
  title: string;
  message: string;
  isUrgent?: boolean;
  actionUrl?: string;
  metadata?: Record<string, any>;
}

export interface ICreateNotificationResponse {
  success: boolean;
  message: string;
  error?: string;
  notification?: INotification;
}
