/**
 * Database Service Types
 * Types for database service layer
 */

// Note: SupabaseClient type should be imported in the actual implementation files

export interface DatabaseServiceConfig {
  supabaseUrl: string;
  supabaseServiceKey: string;
  supabaseAnonKey: string;
  connectionPoolSize?: number;
  queryTimeout?: number;
}

export interface DatabaseService {
  client: any; // SupabaseClient - imported in implementation
  isConnected(): Promise<boolean>;
  disconnect(): Promise<void>;
}

export interface QueryResult<T = any> {
  data: T | null;
  error: Error | null;
  count?: number;
}

export interface TransactionOptions {
  isolationLevel?: 'READ_UNCOMMITTED' | 'READ_COMMITTED' | 'REPEATABLE_READ' | 'SERIALIZABLE';
  timeout?: number;
}

export interface DatabaseError extends Error {
  code?: string;
  details?: string;
  hint?: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  offset?: number;
}

export interface SortOptions {
  column: string;
  ascending: boolean;
}

export interface FilterCondition {
  column: string;
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'ilike' | 'in' | 'is' | 'not';
  value: any;
}

export interface QueryBuilder {
  select(columns?: string): QueryBuilder;
  insert(data: any): QueryBuilder;
  update(data: any): QueryBuilder;
  delete(): QueryBuilder;
  filter(condition: FilterCondition): QueryBuilder;
  order(column: string, ascending?: boolean): QueryBuilder;
  limit(count: number): QueryBuilder;
  offset(count: number): QueryBuilder;
  execute<T = any>(): Promise<QueryResult<T>>;
}
