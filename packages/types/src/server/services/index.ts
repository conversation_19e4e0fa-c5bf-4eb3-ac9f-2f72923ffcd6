/**
 * Server Service Types
 * Exports all service-related types
 */

export * from './auth.service.types';
export * from './database.service.types';

// Common service types
export interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ServiceConfig {
  [key: string]: any;
}

export interface ServiceDependencies {
  [serviceName: string]: any;
}
