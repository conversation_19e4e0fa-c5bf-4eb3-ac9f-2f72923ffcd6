/**
 * Authentication Service Types
 * Types for authentication service layer
 */

export interface AuthServiceConfig {
  jwtSecret: string;
  jwtExpiresIn: string;
  refreshTokenExpiresIn: string;
  otpExpiryMinutes: number;
  maxOtpAttempts: number;
  otpRateLimitMinutes: number;
}

export interface OtpServiceResult {
  success: boolean;
  otpId?: string;
  message: string;
  error?: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

export interface JwtPayload {
  userId: string;
  phone: string;
  iat: number;
  exp: number;
}

export interface RefreshTokenPayload {
  userId: string;
  phone: string;
  tokenId: string;
  iat: number;
  exp: number;
}

export interface AuthenticatedUser {
  id: string;
  phone: string;
  phoneVerified: boolean;
  fullName?: string;
  email?: string;
  avatarUrl?: string;
  location?: string;
  isActive: boolean;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface OtpVerificationResult {
  success: boolean;
  user?: AuthenticatedUser;
  tokens?: TokenPair;
  isNewUser: boolean;
  message: string;
  error?: string;
}
