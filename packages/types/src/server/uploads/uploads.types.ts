/**
 * Upload API Types
 */

// Upload Request Types
export interface IUploadImagesRequest {
  category: 'profile' | 'job' | 'work_progress' | 'portfolio' | 'verification';
  jobId?: string;
  captions?: string[];
  metadata?: Record<string, any>;
}

// Upload Response Types
export interface IUploadedImage {
  id: string;
  originalName: string;
  url: string;
  thumbnailUrl?: string;
  size: number;
  mimeType: string;
  category: string;
  jobId?: string;
  uploadedAt: string;
}

export interface IUploadImagesResponse {
  success: boolean;
  message: string;
  error?: string;
  images?: IUploadedImage[];
}

// Image Processing Options
export interface IImageProcessingOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
  generateThumbnail?: boolean;
  thumbnailSize?: number;
}

// Upload Configuration
export interface IUploadConfig {
  maxFileSize: number; // in bytes
  allowedMimeTypes: string[];
  maxFiles: number;
  imageProcessing: IImageProcessingOptions;
}

// Upload Validation
export interface IUploadValidation {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}
