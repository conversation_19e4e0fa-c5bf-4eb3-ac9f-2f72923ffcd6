/**
 * Jobs Database Types
 * Database schema types for job-related tables
 */

export interface IJobDB {
  id: string;
  poster_id: string;
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  location: string;
  latitude?: number;
  longitude?: number;
  rate: number;
  rate_type: 'hourly' | 'daily' | 'fixed';
  duration?: string;
  requirements?: string[];
  skills_required?: string[];
  experience_level?: string;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  urgency: 'normal' | 'urgent';
  photos?: string[];
  view_count: number;
  application_count: number;
  max_applications?: number;
  auto_accept: boolean;
  assigned_worker_id?: string;
  started_at?: string;
  completed_at?: string;
  work_photos?: string[];
  payment_status: 'pending' | 'processing' | 'completed' | 'failed' | 'disputed' | 'refunded';
  payment_method?: string;
  transaction_id?: string;
  created_at: string;
  updated_at: string;
  expires_at?: string;
}

// Note: IJobCategoryDB, ISkillDB, and IMarketRateDB are defined in worker.types.ts

export interface IJobBookmarkDB {
  id: string;
  user_id: string;
  job_id: string;
  created_at: string;
}

export interface IJobPhotoDB {
  id: string;
  job_id: string;
  url: string;
  caption?: string;
  uploaded_by?: string;
  uploaded_at: string;
}

export interface IJobApplicationDB {
  id: string;
  job_id: string;
  worker_id: string;
  persona_id?: string;
  cover_letter?: string;
  proposed_rate?: number;
  proposed_rate_type?: 'hourly' | 'daily' | 'fixed';
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
  applied_at: string;
  responded_at?: string;
  has_messages: boolean;
  last_message_at?: string;
}

// Default job categories for recommendations
export const DEFAULT_JOB_CATEGORIES = [
  'Construction & Building',
  'Home Services & Maintenance', 
  'Technology & IT Services',
  'Healthcare & Medical',
  'Education & Training',
  'Food & Beverage',
  'Retail & E-commerce',
  'Transportation & Logistics',
  'Manufacturing',
  'Professional Services',
  'Entertainment & Events',
  'Agriculture & Farming',
  'Automotive',
  'Beauty & Personal Care',
  'Consulting',
  'Finance & Insurance',
  'Marketing & Advertising',
  'Non-Profit & NGO',
  'Government & Public Sector',
  'Other'
];

// Common skills for job matching
export const COMMON_SKILLS = [
  'Carpenter', 'Electrician', 'Plumber', 'Mason', 'Painter', 'Welder',
  'House Cleaner', 'Gardener', 'Cook', 'Driver', 'Delivery Person',
  'Computer Repair', 'Software Developer', 'Web Designer', 'Data Entry',
  'Tutor', 'Babysitter', 'Pet Sitter', 'Personal Trainer',
  'Barber', 'Hair Stylist', 'Massage Therapist',
  'Auto Mechanic', 'Bike Mechanic', 'Appliance Repair'
];

// Experience levels
export const EXPERIENCE_LEVELS = [
  '0-1 years',
  '2-5 years', 
  '5-10 years',
  '10+ years'
];

// Rate type definitions
export const RATE_TYPES = {
  hourly: 'Per Hour',
  daily: 'Per Day', 
  fixed: 'Fixed Price'
};

// Job status definitions
export const JOB_STATUSES = {
  draft: 'Draft',
  active: 'Active',
  paused: 'Paused',
  completed: 'Completed',
  cancelled: 'Cancelled'
};

// Application status definitions
export const APPLICATION_STATUSES = {
  pending: 'Pending',
  accepted: 'Accepted',
  rejected: 'Rejected',
  withdrawn: 'Withdrawn'
};
