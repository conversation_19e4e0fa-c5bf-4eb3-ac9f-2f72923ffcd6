/**
 * Server Database Types
 * Exports all database-related types
 */

export * from './enums.types';
export * from './jobs.types';
export * from './messages.types';
export * from './notifications.types';
export * from './otp.types';
export * from './tables.types';
export * from './user.types';
export * from './worker.types';

// Database operation result types
export interface DatabaseResult<T = any> {
  data: T | null;
  error: Error | null;
}

export interface DatabaseListResult<T = any> {
  data: T[] | null;
  error: Error | null;
  count?: number;
}

// Query builder types
export interface QueryOptions {
  select?: string;
  limit?: number;
  offset?: number;
  orderBy?: string;
  ascending?: boolean;
}

export interface FilterOptions {
  [key: string]: any;
}
