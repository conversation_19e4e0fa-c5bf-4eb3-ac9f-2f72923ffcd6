/**
 * Database Table Types
 * TypeScript interfaces matching Supabase database tables
 */

import {
  RateType,
  JobStatus,
  UrgencyType,
  ApplicationStatus,
  MessageType,
  MessageStatus,
  PaymentStatusType,
  ReviewType,
  DisputeStatusType,
  VisibilityLevel,
  AppLanguage,
  DayOfWeek
} from './enums.types';

export interface UserProfile {
  user_id: string;
  phone: string;
  phone_verified: boolean;
  email?: string;
  full_name: string;
  avatar_url?: string;
  location?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface WorkerProfile {
  user_id: string;
  primary_skill?: string;
  skills?: string[];
  experience?: string;
  hourly_rate?: number;
  daily_rate?: number;
  about?: string;
  phone_visible: boolean;
  completed_jobs: number;
  total_earnings: number;
  rating: number;
  review_count: number;
  portfolio_photos?: string[];
  is_available: boolean;
  updated_at: string;
}

export interface PosterProfile {
  user_id: string;
  company_name?: string;
  about?: string;
  jobs_posted: number;
  total_spent: number;
  rating: number;
  review_count: number;
  business_verified: boolean;
  business_documents?: string[];
  updated_at: string;
}

export interface WorkerPersona {
  id: string;
  user_id: string;
  skill: string;
  experience?: string;
  hourly_rate?: number;
  daily_rate?: number;
  is_active: boolean;
  completed_jobs: number;
  rating: number;
  review_count: number;
  created_at: string;
  updated_at: string;
}

export interface Job {
  id: string;
  poster_id: string;
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  location: string;
  latitude?: number;
  longitude?: number;
  rate: number;
  rate_type: RateType;
  duration?: string;
  requirements?: string[];
  skills_required?: string[];
  experience_level?: string;
  status: JobStatus;
  urgency: UrgencyType;
  photos?: string[];
  view_count: number;
  application_count: number;
  max_applications?: number;
  auto_accept: boolean;
  assigned_worker_id?: string;
  started_at?: string;
  completed_at?: string;
  work_photos?: string[];
  payment_status: PaymentStatusType;
  payment_method?: string;
  transaction_id?: string;
  created_at: string;
  updated_at: string;
  expires_at?: string;
}

export interface JobBookmark {
  id: string;
  user_id: string;
  job_id: string;
  created_at: string;
}

export interface JobApplication {
  id: string;
  job_id: string;
  worker_id: string;
  persona_id?: string;
  cover_letter?: string;
  proposed_rate?: number;
  proposed_rate_type?: RateType;
  status: ApplicationStatus;
  applied_at: string;
  responded_at?: string;
  has_messages: boolean;
  last_message_at?: string;
}

export interface OtpRequest {
  id: string;
  phone: string;
  otp_id?: string;
  requested_at: string;
  status: string;
  ip?: string;
}

export interface UserSettings {
  user_id: string;
  notifications: Record<string, any>;
  privacy: Record<string, any>;
  language: AppLanguage;
  preferences: Record<string, any>;
  updated_at: string;
}
