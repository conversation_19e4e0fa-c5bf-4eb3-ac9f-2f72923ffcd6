/**
 * Database types for messages and conversations
 */

import { MessageType, MessageStatus } from './enums.types';

export interface IConversationDB {
  id: string;
  job_id: string;
  worker_id: string;
  poster_id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface IMessageDB {
  id: string;
  conversation_id: string;
  sender_id: string;
  receiver_id: string;
  text: string;
  message_type: MessageType;
  status: MessageStatus;
  timestamp: string;
}

export interface IMessageAttachmentDB {
  id: string;
  message_id: string;
  url: string;
  file_name?: string;
  file_size?: number;
  mime_type?: string;
}
