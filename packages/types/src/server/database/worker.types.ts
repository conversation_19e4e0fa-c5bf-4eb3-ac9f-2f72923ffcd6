/**
 * Worker Database Types
 * Database schema types for worker-related tables
 */

export interface IWorkerProfileDB {
  user_id: string;
  primary_skill?: string;
  skills?: string[];
  experience?: string;
  hourly_rate?: number;
  daily_rate?: number;
  about?: string;
  phone_visible: boolean;
  completed_jobs: number;
  total_earnings: number;
  rating: number;
  review_count: number;
  portfolio_photos?: string[];
  is_available: boolean;
  updated_at: string;
}

export interface IWorkerPersonaDB {
  id: string;
  user_id: string;
  skill: string;
  experience?: string;
  hourly_rate?: number;
  daily_rate?: number;
  is_active: boolean;
  completed_jobs: number;
  rating: number;
  review_count: number;
  created_at: string;
  updated_at: string;
}

export interface IJobCategoryDB {
  id: string;
  name: string;
}

export interface ISkillDB {
  id: string;
  name: string;
  category_id: string;
}

export interface IMarketRateDB {
  id: string;
  skill: string;
  location?: string;
  hourly_min: number;
  hourly_max: number;
  daily_min: number;
  daily_max: number;
  currency: string;
}

export interface IUploadDB {
  id: string;
  user_id: string;
  file_name: string;
  original_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  upload_type: 'avatar' | 'portfolio' | 'document' | 'job_photo';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Default skill categories and skills
export const DEFAULT_SKILL_CATEGORIES = [
  {
    id: 'construction',
    name: 'Construction & Building',
    description: 'Construction, renovation, and building services',
    icon: 'construction',
    skills: [
      'Carpenter',
      'Electrician',
      'Plumber',
      'Mason',
      'Painter',
      'Welder',
      'Roofer',
      'Flooring Specialist',
      'HVAC Technician',
      'General Contractor'
    ],
    isActive: true
  },
  {
    id: 'home-services',
    name: 'Home Services',
    description: 'Home maintenance and repair services',
    icon: 'home',
    skills: [
      'House Cleaner',
      'Gardener',
      'Pest Control',
      'Appliance Repair',
      'Locksmith',
      'Security System Installer',
      'Interior Designer',
      'Home Organizer'
    ],
    isActive: true
  },
  {
    id: 'automotive',
    name: 'Automotive',
    description: 'Vehicle repair and maintenance services',
    icon: 'car',
    skills: [
      'Auto Mechanic',
      'Car Wash',
      'Tire Technician',
      'Auto Electrician',
      'Body Shop Technician',
      'Car Detailing',
      'Motorcycle Mechanic'
    ],
    isActive: true
  },
  {
    id: 'technology',
    name: 'Technology',
    description: 'IT and technology services',
    icon: 'computer',
    skills: [
      'Computer Repair',
      'Software Developer',
      'Web Designer',
      'Network Technician',
      'Mobile App Developer',
      'Data Entry',
      'Digital Marketing',
      'Graphic Designer'
    ],
    isActive: true
  },
  {
    id: 'personal-services',
    name: 'Personal Services',
    description: 'Personal care and wellness services',
    icon: 'person',
    skills: [
      'Barber',
      'Hair Stylist',
      'Massage Therapist',
      'Personal Trainer',
      'Tutor',
      'Babysitter',
      'Pet Sitter',
      'Driver',
      'Delivery Person'
    ],
    isActive: true
  },
  {
    id: 'food-beverage',
    name: 'Food & Beverage',
    description: 'Food service and catering',
    icon: 'restaurant',
    skills: [
      'Chef',
      'Cook',
      'Waiter',
      'Bartender',
      'Caterer',
      'Food Delivery',
      'Baker',
      'Kitchen Helper'
    ],
    isActive: true
  }
];

export const DEFAULT_MARKET_RATES = [
  // Construction rates
  { skill: 'Carpenter', category: 'construction', hourlyRate: 300, dailyRate: 2400 },
  { skill: 'Electrician', category: 'construction', hourlyRate: 400, dailyRate: 3200 },
  { skill: 'Plumber', category: 'construction', hourlyRate: 350, dailyRate: 2800 },
  { skill: 'Mason', category: 'construction', hourlyRate: 250, dailyRate: 2000 },
  { skill: 'Painter', category: 'construction', hourlyRate: 200, dailyRate: 1600 },
  
  // Home services rates
  { skill: 'House Cleaner', category: 'home-services', hourlyRate: 150, dailyRate: 1200 },
  { skill: 'Gardener', category: 'home-services', hourlyRate: 180, dailyRate: 1440 },
  { skill: 'Appliance Repair', category: 'home-services', hourlyRate: 300, dailyRate: 2400 },
  
  // Technology rates
  { skill: 'Computer Repair', category: 'technology', hourlyRate: 400, dailyRate: 3200 },
  { skill: 'Software Developer', category: 'technology', hourlyRate: 800, dailyRate: 6400 },
  { skill: 'Web Designer', category: 'technology', hourlyRate: 600, dailyRate: 4800 },
  
  // Personal services rates
  { skill: 'Barber', category: 'personal-services', hourlyRate: 200, dailyRate: 1600 },
  { skill: 'Driver', category: 'personal-services', hourlyRate: 120, dailyRate: 960 },
  { skill: 'Tutor', category: 'personal-services', hourlyRate: 300, dailyRate: 2400 }
];
