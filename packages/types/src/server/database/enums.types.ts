/**
 * Database Enum Types
 * TypeScript enums matching Supabase database enums
 */

export enum RateType {
  HOURLY = 'hourly',
  DAILY = 'daily',
  FIXED = 'fixed'
}

export enum JobStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum UrgencyType {
  NORMAL = 'normal',
  URGENT = 'urgent'
}

export enum ApplicationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn'
}

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  DOCUMENT = 'document'
}

export enum MessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read'
}

export enum PaymentStatusType {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  DISPUTED = 'disputed',
  REFUNDED = 'refunded'
}

export enum ReviewType {
  WORKER_TO_POSTER = 'worker_to_poster',
  POSTER_TO_WORKER = 'poster_to_worker'
}

export enum DisputeStatusType {
  OPEN = 'open',
  RESOLVED = 'resolved',
  ESCALATED = 'escalated'
}

export enum VisibilityLevel {
  PUBLIC = 'public',
  PRIVATE = 'private',
  CONTACTS = 'contacts'
}

export enum AppLanguage {
  ENGLISH = 'english',
  HINDI = 'hindi'
}

export enum DayOfWeek {
  MONDAY = 'monday',
  TUESDAY = 'tuesday',
  WEDNESDAY = 'wednesday',
  THURSDAY = 'thursday',
  FRIDAY = 'friday',
  SATURDAY = 'saturday',
  SUNDAY = 'sunday'
}
