/**
 * OTP Database Types
 * TypeScript interfaces for OTP-related database operations
 */

export interface IOtpRequest {
  id: string;
  phone: string;
  otp_id?: string;
  requested_at: string;
  status: 'sent' | 'verified' | 'expired' | 'failed';
  ip?: string;
  attempts?: number;
  expires_at?: string;
}

export interface IOtpVerification {
  phone: string;
  otp: string;
  otp_id: string;
  verified_at?: string;
  is_valid: boolean;
}

export interface IOtpRateLimit {
  phone: string;
  request_count: number;
  window_start: string;
  blocked_until?: string;
}

export interface IOtpServiceConfig {
  provider: '2factor' | 'twilio' | 'mock';
  api_key?: string;
  sender_id?: string;
  template_id?: string;
  otp_length: number;
  otp_expiry_minutes: number;
  max_attempts: number;
  rate_limit_window_minutes: number;
  max_requests_per_window: number;
}

export interface IOtpSendResult {
  success: boolean;
  otp_id?: string;
  message: string;
  error?: string;
  retry_after?: number;
}

export interface IOtpVerifyResult {
  success: boolean;
  is_valid: boolean;
  message: string;
  error?: string;
  attempts_remaining?: number;
}
