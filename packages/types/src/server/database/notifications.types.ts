/**
 * Database types for notifications
 */

export interface INotificationDB {
  id: string;
  user_id: string;
  type: string;
  title: string;
  message: string;
  is_read: boolean;
  is_urgent: boolean;
  action_url?: string;
  metadata?: Record<string, any>;
  created_at: string;
}

export interface INotificationDeviceDB {
  id: string;
  user_id: string;
  device_token: string;
  platform: 'ios' | 'android' | 'web';
  created_at: string;
}
