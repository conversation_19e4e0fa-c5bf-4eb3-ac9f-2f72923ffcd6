/**
 * User Database Types
 * Database schema types for user-related tables
 */

export interface IUserProfile {
  user_id: string;
  phone: string;
  phone_verified: boolean;
  email?: string;
  full_name: string;
  avatar_url?: string;
  location?: string;
  is_active: boolean;
  is_verified: boolean;
  selected_mode?: 'worker' | 'poster';
  profile_completed: boolean;
  created_at: string;
  updated_at: string;
}

export interface IWorkerProfile {
  user_id: string;
  primary_skill?: string;
  skills?: string[];
  experience?: string;
  hourly_rate?: number;
  daily_rate?: number;
  about?: string;
  phone_visible: boolean;
  completed_jobs: number;
  total_earnings: number;
  rating: number;
  review_count: number;
  portfolio_photos?: string[];
  is_available: boolean;
  updated_at: string;
}

export interface IPosterProfile {
  user_id: string;
  company_name?: string;
  about?: string;
  jobs_posted: number;
  total_spent: number;
  rating: number;
  review_count: number;
  business_verified: boolean;
  business_documents?: string[];
  updated_at: string;
}

export interface IUserSettings {
  user_id: string;
  notifications: {
    jobAlerts?: boolean;
    applicationUpdates?: boolean;
    messages?: boolean;
    marketing?: boolean;
    pushEnabled?: boolean;
    emailEnabled?: boolean;
    smsEnabled?: boolean;
  };
  privacy: {
    profileVisibility?: 'public' | 'private' | 'contacts';
    phoneVisibility?: boolean;
    locationSharing?: boolean;
    showOnlineStatus?: boolean;
    allowDirectMessages?: boolean;
  };
  language: 'english' | 'hindi';
  preferences: {
    theme?: 'light' | 'dark' | 'system';
    currency?: 'INR';
    distanceUnit?: 'km' | 'miles';
    autoAcceptJobs?: boolean;
    jobRadius?: number;
    workingHours?: {
      enabled: boolean;
      schedule: Record<string, { start: string; end: string; available: boolean }>;
    };
  };
  updated_at: string;
}

export interface IUserModeSelection {
  user_id: string;
  selected_mode: 'worker' | 'poster';
  selected_at: string;
  settings_initialized: boolean;
  profile_created: boolean;
}

// Default settings templates
export const DEFAULT_NOTIFICATIONS = {
  jobAlerts: true,
  applicationUpdates: true,
  messages: true,
  marketing: false,
  pushEnabled: true,
  emailEnabled: true,
  smsEnabled: true
};

export const DEFAULT_PRIVACY = {
  profileVisibility: 'public' as const,
  phoneVisibility: false,
  locationSharing: true,
  showOnlineStatus: true,
  allowDirectMessages: true
};

export const DEFAULT_PREFERENCES = {
  theme: 'system' as const,
  currency: 'INR' as const,
  distanceUnit: 'km' as const,
  autoAcceptJobs: false,
  jobRadius: 10,
  workingHours: {
    enabled: false,
    schedule: {
      monday: { start: '09:00', end: '18:00', available: true },
      tuesday: { start: '09:00', end: '18:00', available: true },
      wednesday: { start: '09:00', end: '18:00', available: true },
      thursday: { start: '09:00', end: '18:00', available: true },
      friday: { start: '09:00', end: '18:00', available: true },
      saturday: { start: '09:00', end: '18:00', available: true },
      sunday: { start: '09:00', end: '18:00', available: false }
    }
  }
};
