/**
 * Mode Selection Types
 * Types for user mode selection and initialization
 */

export type UserMode = 'worker' | 'poster';

export interface IModeSelectionRequest {
  mode: UserMode;
  language?: 'english' | 'hindi';
  preferences?: {
    notifications?: {
      jobAlerts?: boolean;
      applicationUpdates?: boolean;
      messages?: boolean;
      marketing?: boolean;
    };
    privacy?: {
      profileVisibility?: 'public' | 'private' | 'contacts';
      phoneVisibility?: boolean;
      locationSharing?: boolean;
    };
  };
}

export interface IModeSelectionResponse {
  success: boolean;
  message: string;
  error?: string;
  user?: {
    id: string;
    mode: UserMode;
    hasProfile: boolean;
    settings: {
      language: 'english' | 'hindi';
      notifications: Record<string, any>;
      privacy: Record<string, any>;
      preferences: Record<string, any>;
    };
  };
  nextStep?: 'create_profile' | 'complete';
}

export interface IUserModeStatus {
  userId: string;
  selectedMode?: UserMode;
  hasWorkerProfile: boolean;
  hasPosterProfile: boolean;
  hasSettings: boolean;
  isProfileComplete: boolean;
}

export interface IInitializeSettingsRequest {
  language: 'english' | 'hindi';
  notifications?: Record<string, any>;
  privacy?: Record<string, any>;
  preferences?: Record<string, any>;
}

export interface IInitializeSettingsResponse {
  success: boolean;
  message: string;
  error?: string;
  settings?: {
    userId: string;
    language: 'english' | 'hindi';
    notifications: Record<string, any>;
    privacy: Record<string, any>;
    preferences: Record<string, any>;
    createdAt: string;
  };
}

export interface IGetModeStatusResponse {
  success: boolean;
  status?: IUserModeStatus;
  message: string;
  error?: string;
}
