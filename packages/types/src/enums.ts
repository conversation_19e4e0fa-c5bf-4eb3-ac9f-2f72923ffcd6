// Enum types matching the database schema

export type UserRoleEnum = 'worker' | 'poster';

export type SupportedLanguageEnum = 
  | 'hindi' 
  | 'english' 
  | 'tamil' 
  | 'telugu' 
  | 'bengali' 
  | 'marathi' 
  | 'gujarati' 
  | 'kannada';

export type SkillCategoryEnum = 
  | 'electrical' 
  | 'plumbing' 
  | 'carpentry' 
  | 'cooking' 
  | 'cleaning'
  | 'driving' 
  | 'delivery' 
  | 'security' 
  | 'gardening' 
  | 'tutoring';

export type JobTypeEnum = 'one_time' | 'recurring' | 'permanent';

export type UrgencyEnum = 'low' | 'normal' | 'high' | 'urgent';

export type JobStatusEnum = 'active' | 'paused' | 'filled' | 'cancelled' | 'expired';

export type ApplicationStatusEnum = 'pending' | 'accepted' | 'rejected' | 'withdrawn';

export type ReviewTypeEnum = 'worker_review' | 'poster_review';

export type GenderPreferenceEnum = 'any' | 'male' | 'female';