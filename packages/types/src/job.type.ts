import { SkillCategory } from './index';

// This is the simplified job type used in the app
// For the full database schema, see job-db.type.ts
export interface Job {
  id: string;
  title: string;
  description: string;
  skill_category: SkillCategory;
  skill_subcategories: string[];
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  budget_min?: number;
  budget_max?: number;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  preferred_start_time?: string;
  estimated_duration_hours?: number;
  requirements: string[];
  is_active: boolean;
  poster_id: string;
  created_at: string;
  applications_count: number;
}