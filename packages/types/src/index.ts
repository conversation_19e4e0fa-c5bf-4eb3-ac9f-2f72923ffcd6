// Shared types for Ozgaar mobile and backend applications

// Enum types
export * from './enums';
import { SkillCategoryEnum, SupportedLanguageEnum } from './enums';

export type SupportedLanguage = SupportedLanguageEnum;
// User types
export { User } from './user.type';

// Worker persona types
export { WorkerPersona } from './worker.type';

// Job types
export { Job } from './job.type';

// Database table types
export type { WorkerPersona as WorkerPersonaDb } from './worker-persona.type';
export type { Job as JobDb } from './job-db.type';
export type { JobApplication } from './job-application.type';
export type { Review } from './review.type';
export type { OtpAttempt } from './otp-attempt.type';
export type { SmsLog } from './sms-log.type';
export type { UserActivity } from './user-activity.type';
export type { Notification } from './notification.type';
export type { SkillCategory as SkillCategoryData } from './skill-category.type';
export type { SkillSubcategory as SkillSubcategoryData } from './skill-category.type';
export type { JobView } from './job-view.type';
export type { JobApplicationTracking } from './job-application-tracking.type';
export type { JobPerformanceMetric } from './job-performance-metric.type';

import { SkillCategory as SkillCategoryData, SkillSubcategory as SkillSubcategoryData } from './skill-category.type';

// Extended interfaces with relationships
export interface SkillCategoryWithSubcategories extends SkillCategoryData {
  subcategories: SkillSubcategoryData[];
}

// skill category type
export type SkillCategory = SkillCategoryEnum

// API Response types for skills
export interface SkillCategoriesResponse {
  success: boolean;
  data?: SkillCategoryData[];
  message?: string;
  error?: string;
  code?: string;
}

export interface SkillSubcategoriesResponse {
  success: boolean;
  data?: SkillSubcategoryData[];
  message?: string;
  error?: string;
  code?: string;
}

export interface SkillSearchResponse {
  success: boolean;
  data?: {
    categories: SkillCategoryData[];
    subcategories: SkillSubcategoryData[];
  };
  message?: string;
  error?: string;
  code?: string;
}

// Trust score types
export interface TrustScore {
  overall_rating: number;
  total_reviews: number;
  identity_verified: boolean;
  phone_verified: boolean;
  email_verified: boolean;
  background_check: boolean;
  skill_certifications: string[];
}

// Supabase function types
export * from './supabase/functions';

// Server types (for backend/server applications)
export * from './server';