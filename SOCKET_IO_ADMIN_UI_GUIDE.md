# Socket.IO Admin UI Guide

This guide explains how to use the Socket.IO Admin UI dashboard that has been integrated into your Express.js application.

## Accessing the Dashboard

1. Start your server (if not already running):
   ```bash
   cd server
   pnpm dev
   ```

2. Open your browser and navigate to:
   https://admin.socket.io

3. In the "Server URL" field, enter your local server address:
   ```
   http://localhost:3000
   ```
   (Replace 3000 with your actual port if different)

## Authentication

The Admin UI is protected with basic authentication. Use these credentials to log in:

- **Username**: `admin`
- **Password**: `yourSecurePassword`

## Connecting to the Server with Authentication

When testing your WebSocket events, you'll need to provide a JWT token for authentication. Here's how:

1. After logging into the Admin UI, click on the "Connect" button
2. In the connection options, find the "Auth" section
3. Add your JWT token in one of these formats:
   - As a token parameter: `{ "token": "YOUR_JWT_TOKEN_HERE" }`
   - In the Authorization header: Add to headers as `{ "Authorization": "Bearer YOUR_JWT_TOKEN_HERE" }`

## Testing Events

### Viewing Connected Sockets and Rooms

1. After connecting to your server, go to the "Sockets" tab
2. You'll see a list of all connected sockets
3. Click on any socket to see details including:
   - Socket ID
   - Connected user information
   - Rooms the socket has joined (e.g., `user:${userId}` and `conversation:${conversationId}`)

### Sending Test Events

1. Go to the "Events" tab
2. Select the socket you want to emit events from
3. Click "Emit event"
4. Enter the event name (e.g., `messages:message:send`)
5. Provide the payload in JSON format, for example:

```json
{
  "conversationId": "YOUR_CONVERSATION_ID",
  "text": "Hello from Admin UI!",
  "messageType": "text"
}
```

6. Click "Emit" to send the event

### Example Events

Here are some common events you can test:

1. **Get conversations list**:
   - Event: `messages:conversations:list`
   - Payload: 
     ```json
     {
       "page": 1,
       "limit": 20
     }
     ```

2. **Send a message**:
   - Event: `messages:message:send`
   - Payload:
     ```json
     {
       "conversationId": "YOUR_CONVERSATION_UUID",
       "text": "Test message from Admin UI",
       "messageType": "text"
     }
     ```

3. **Get conversation messages**:
   - Event: `messages:conversation:get`
   - Payload:
     ```json
     {
       "conversationId": "YOUR_CONVERSATION_UUID",
       "page": 1,
       "limit": 50
     }
     ```

4. **Mark message as read**:
   - Event: `messages:message:read`
   - Payload:
     ```json
     {
       "messageId": "YOUR_MESSAGE_UUID"
     }
     ```

5. **Send typing indicator**:
   - Event: `messages:typing`
   - Payload:
     ```json
     {
       "conversationId": "YOUR_CONVERSATION_UUID"
     }
     ```

## Monitoring Real-time Events

1. Go to the "Events" tab
2. You'll see real-time logs of all events being emitted and received
3. You can filter events by type or socket ID
4. Each event log shows:
   - Event name
   - Payload data
   - Direction (sent/received)
   - Timestamp

## Rooms Management

1. Navigate to the "Rooms" tab
2. View all active rooms and their occupants
3. See which users are in which conversation rooms
4. Monitor room joins and leaves in real-time

## Troubleshooting

If you encounter connection issues:

1. Ensure your server is running
2. Verify the server URL is correct (http://localhost:PORT)
3. Check that your JWT token is valid and not expired
4. Make sure the CORS configuration allows connections from the admin UI domain
5. Check server logs for authentication errors

## Security Note

The admin UI is intended for development and testing purposes only. In a production environment, you should:
- Use a strong, unique password
- Restrict access to trusted IPs
- Consider using the `readonly` option to prevent administrative operations
- Use `mode: "production"` to reduce memory footprint

## Troubleshooting CORS Issues

If you encounter CORS errors when connecting to the admin UI:

1. Ensure the CORS configuration in your Socket.IO server includes `https://admin.socket.io` as an allowed origin
2. Verify that `credentials: true` is set in the CORS options
3. Check that your server is restarted after any configuration changes

The correct CORS configuration should look like:
```javascript
cors: {
  origin: [process.env.CORS_ORIGIN || '*', "https://admin.socket.io"],
  methods: ['GET', 'POST'],
  credentials: true
}
```