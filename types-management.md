# Proper ways to maintain types across frontend and backend

Keeping types in sync prevents runtime mismatches, speeds development, and makes refactors safer. The right approach depends on API style, repo structure, and validation needs. Below are the proven patterns, trade-offs, and practical tips.

## 1) Monorepo with shared TypeScript packages

A monorepo lets frontend and backend import a common “types” (or “schema”) package directly, avoiding duplication and drift. This is the simplest path to consistent types when owning both sides of the stack, especially with TypeScript project references and workspaces.[1]

- Use workspaces (pnpm/yarn/npm) + TS Project References to publish a local package like @acme/types that both apps import.[1]
- Keep runtime-agnostic code (types, Zod schemas, DTOs) in the shared package; avoid Node/browser-specific imports to keep it universal.[1]
- Enforce strict builds and CI checks so type changes don’t silently break dependents.[1]

If using tRPC, a monorepo is the strongly preferred setup because the client infers types from the server router; otherwise you’ll need a packaging workaround to export the server’s type definitions.[2][3]

> “tRPC lets you define API endpoints on the server-side, and automatically get typed results on the frontend… The only requirement is to have your backend and frontend in a monorepo.”[3]

## 2) API contract as source of truth (OpenAPI/Swagger)

For REST APIs or service boundaries between teams, define the contract in OpenAPI and generate types and clients from it. This de-couples repos while keeping types synchronized automatically.[4][5]

- Generate TS types/clients via openapi-generator or swagger-codegen (e.g., typescript-axios, typescript-fetch), and commit or build in CI.[4]
- Regenerate on every spec change (prepublish/CI) to prevent drift.[4]
- Treat the spec as versioned artifact; use semantic versioning and spec diffs to catch breaking changes early.[4]
- This approach scales across multiple services and teams better than ad-hoc type sharing.[5]

> “Automating the generation of TypeScript types from OpenAPI… ensures types reflect the latest API specification… include the generator in CI to keep them up to date.”[4]

## 3) RPC frameworks with end-to-end typing (tRPC)

If both ends are TS and co-developed, tRPC gives end-to-end type safety without writing OpenAPI schemas or manual DTOs. It infers types from the server’s router and propagates them to the client automatically, typically in a monorepo.[3]

- Best for greenfield or internal apps where REST/GraphQL is not a requirement.[3]
- If not in a monorepo, you can still publish the server’s API types as an npm package and import them in the client, but this is more involved and less ergonomic than monorepo sharing.[6][2]
- Pair with Zod for input/output validation at runtime.[3]

> tRPC “synchronizes the types between the backend and the frontend” by design, with the server router as the source of truth.[3]

## 4) Share validation schemas (Zod) across layers

Define Zod schemas once and reuse them for backend validation and frontend form validation to ensure consistent constraints and TypeScript inference.[7][8][9]

- Centralize Zod schemas in a shared package (e.g., @acme/schemas) in a monorepo; import in server middleware and client forms.[9][7]
- Generate types from Zod (z.infer) to keep TS types derived from the single source of truth.[9]
- If not using a monorepo, publish the schemas as a versioned npm package or a Bit component for reuse across repos.[8]
- Ensure shared schema packages have no heavy runtime dependencies to keep them lightweight for the browser.[7][8]

## Choosing the right approach

- Internal app, single team, TS across stack: Monorepo + shared package for types/schemas, or tRPC for full-stack type inference.[1][3]
- Public/multi-team REST API: OpenAPI as the contract, generate types/clients; version the spec and automate codegen in CI.[5][4]
- Need runtime validation + typing: Zod schemas in a shared package; optionally layer OpenAPI generation or adapters if exposing REST.[7][9]

## Practical implementation tips

- Structure
  - packages/types: shared DTOs, utilities, enums.[1]
  - packages/schemas: Zod schemas + z.infer types.[9][7]
  - apps/backend and apps/frontend import from packages/* via workspaces and TS path aliases.[1]
- Tooling
  - OpenAPI: @openapitools/openapi-generator-cli with typescript-axios or typescript-fetch; run in CI to regenerate on spec changes.[4]
  - tRPC: colocate router definitions in server; export type AppRouter for the client; prefer monorepo for frictionless consumption.[2][3]
  - Zod: use shared schemas for express/hono middlewares server-side and for react-hook-form client-side via @hookform/resolvers.[7][9]
- Governance
  - Version shared packages (or OpenAPI spec) and publish/tag; adopt semantic versioning to communicate breaking changes.[4]
  - Add PR checks that build all dependents to catch breaking type changes early.[1]
- When repos are separate
  - Publish a versioned npm package for types/schemas or the tRPC API types; or use component platforms like Bit for distribution and updates.[6][8][2]

## Common pitfalls to avoid

- Duplicating types manually in frontend and backend—guaranteed drift over time.[5][4]
- Mixing runtime-specific code in shared packages (e.g., Node APIs in browser types), causing bundling issues.[1]
- Relying on inferred any due to TS config leaks across workspaces; ensure proper tsconfig references and strict settings.[10]
- Skipping CI automation for OpenAPI/tRPC/type package updates, leading to stale consumers.[4]

In short, pick a single source of truth—OpenAPI spec, tRPC router, or Zod schemas—then automate distribution via monorepo workspaces or published packages so both frontend and backend consume identical types with minimal manual upkeep.[10][8][2][6][5][9][7][3][4][1]

[1] https://monorepo.tools/typescript
[2] https://github.com/trpc/trpc/discussions/1860
[3] https://marmelab.com/blog/2024/04/04/trpc-full-stack-types.html
[4] https://www.pullrequest.com/blog/generating-typescript-types-with-openapi-for-rest-api-consumption/
[5] https://blog.bitsrc.io/how-to-share-types-between-frontend-and-backend-apps-15753f26d53e
[6] https://billyjacoby.com/blog/export-trpc-types
[7] https://stackoverflow.com/questions/78962954/how-do-i-share-a-zod-validation-schema-between-backend-and-frontend-in-a-pnpm-mo
[8] https://blog.bitsrc.io/share-zod-validation-schemas-between-repositories-8f9ec5fa3ae7
[9] https://dev.to/franciscomendes10866/schema-validation-with-zod-and-expressjs-111p
[10] https://stackoverflow.com/questions/74185198/typescript-losing-zod-and-trpc-types-across-monorepo-projects-types-result-in
[11] https://www.reddit.com/r/typescript/comments/1cjvvln/best_practices_for_sharing_types_between_backend/