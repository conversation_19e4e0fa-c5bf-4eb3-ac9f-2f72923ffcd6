import logger from '../utils/logger'
import { User } from '@ozgaar/types'

export interface ProfileValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  completenessScore: number
  missingFields: string[]
  suggestions: string[]
  completenessLevel: 'draft' | 'basic' | 'good' | 'complete'
  platformAccess: {
    canApplyToJobs: boolean
    canReceiveJobInvites: boolean
    searchVisibilityMultiplier: number
    hasFullAccess: boolean
  }
}

export interface MarketRateGuidance {
  skillCategory: string
  location?: string | undefined
  suggestedHourlyMin: number
  suggestedHourlyMax: number
  suggestedDailyMin: number
  suggestedDailyMax: number
  marketAverage: number
  confidence: 'high' | 'medium' | 'low'
}

class ProfileValidationService {
  
  /**
   * Validate complete user profile with business rules
   */
  validateProfile(user: Partial<User>, additionalData?: any): ProfileValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    const missingFields: string[] = []
    const suggestions: string[] = []

    // Required field validation
    if (!user.full_name || user.full_name.trim().length < 2) {
      errors.push('Full name is required and must be at least 2 characters')
      missingFields.push('full_name')
    }

    if (!user.phone) {
      errors.push('Phone number is required')
      missingFields.push('phone')
    }

    // Worker-specific validation
    if (user.user_type === 'worker') {
      this.validateWorkerProfile(user, additionalData, errors, warnings, missingFields, suggestions)
    }

    // Calculate completeness score
    const completenessScore = this.calculateCompletenessScore(user, additionalData)

    // Determine completeness level and platform access
    const completenessLevel = this.getCompletenessLevel(completenessScore)
    const platformAccess = this.calculatePlatformAccess(completenessScore, user.is_verified || false)

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      completenessScore,
      missingFields,
      suggestions,
      completenessLevel,
      platformAccess
    }
  }

  /**
   * Validate worker-specific profile fields
   */
  private validateWorkerProfile(
    user: Partial<User>, 
    additionalData: any, 
    errors: string[], 
    warnings: string[], 
    missingFields: string[], 
    suggestions: string[]
  ): void {
    // Primary skill category is required for workers
    if (!user.primary_skill_category) {
      errors.push('Primary skill category is required for workers')
      missingFields.push('primary_skill_category')
    }

    // Experience validation
    if (user.years_of_experience !== undefined) {
      if (user.years_of_experience < 0) {
        errors.push('Years of experience cannot be negative')
      }
      if (user.years_of_experience > 50) {
        warnings.push('Years of experience seems unusually high (>50 years)')
      }
    }

    // Rate validation with market guidance
    if (additionalData?.hourly_rate || additionalData?.daily_rate) {
      this.validateRates(user, additionalData, warnings, suggestions)
    }

    // Profile completeness suggestions
    if (!user.profile_image_url) {
      suggestions.push('Add a profile photo to increase trust and job applications')
      missingFields.push('profile_image_url')
    }

    if (!additionalData?.description) {
      suggestions.push('Add a brief description of your skills and experience')
      missingFields.push('description')
    }

    if (!additionalData?.hourly_rate && !additionalData?.daily_rate) {
      suggestions.push('Set your rates to help employers understand your pricing')
      missingFields.push('rates')
    }

    if (!user.location && !user.address) {
      warnings.push('Location information helps you get more relevant job matches')
      missingFields.push('location')
    }
  }

  /**
   * Validate rates against market data
   */
  private validateRates(
    user: Partial<User>, 
    additionalData: any, 
    warnings: string[], 
    suggestions: string[]
  ): void {
    const marketGuidance = this.getMarketRateGuidance(user.primary_skill_category, user.address)

    if (additionalData.hourly_rate) {
      if (additionalData.hourly_rate < marketGuidance.suggestedHourlyMin) {
        warnings.push(`Your hourly rate (₹${additionalData.hourly_rate}) is below market average (₹${marketGuidance.suggestedHourlyMin}-₹${marketGuidance.suggestedHourlyMax})`)
        suggestions.push(`Consider increasing your hourly rate to ₹${marketGuidance.suggestedHourlyMin} or higher`)
      }
      if (additionalData.hourly_rate > marketGuidance.suggestedHourlyMax * 2) {
        warnings.push(`Your hourly rate (₹${additionalData.hourly_rate}) is significantly above market average`)
        suggestions.push('High rates may reduce job applications. Consider market rates for your skill level')
      }
    }

    if (additionalData.daily_rate) {
      if (additionalData.daily_rate < marketGuidance.suggestedDailyMin) {
        warnings.push(`Your daily rate (₹${additionalData.daily_rate}) is below market average (₹${marketGuidance.suggestedDailyMin}-₹${marketGuidance.suggestedDailyMax})`)
        suggestions.push(`Consider increasing your daily rate to ₹${marketGuidance.suggestedDailyMin} or higher`)
      }
      if (additionalData.daily_rate > marketGuidance.suggestedDailyMax * 2) {
        warnings.push(`Your daily rate (₹${additionalData.daily_rate}) is significantly above market average`)
        suggestions.push('High rates may reduce job applications. Consider market rates for your skill level')
      }
    }
  }

  /**
   * Calculate profile completeness score (0-100)
   */
  calculateCompletenessScore(user: Partial<User>, additionalData?: any): number {
    let score = 0
    const maxScore = 100

    // Basic required fields (40 points)
    if (user.full_name && user.full_name.trim().length >= 2) score += 15
    if (user.phone) score += 10
    if (user.is_verified) score += 15

    // Worker profile fields (35 points)
    if (user.user_type === 'worker') {
      if (user.primary_skill_category) score += 10
      if (user.profile_image_url) score += 10
      if (additionalData?.description && additionalData.description.length > 20) score += 8
      if (additionalData?.hourly_rate || additionalData?.daily_rate) score += 7
    }

    // Optional enhancement fields (25 points)
    if (user.email) score += 5
    if (user.address || user.location) score += 8
    if (user.years_of_experience !== undefined && user.years_of_experience > 0) score += 7
    if (user.preferred_language) score += 5

    return Math.min(score, maxScore)
  }

  /**
   * Get market rate guidance for skill category and location
   */
  getMarketRateGuidance(skillCategory?: string, location?: string): MarketRateGuidance {
    // Market rate data for Indian job market (in INR)
    const marketRates: Record<string, MarketRateGuidance> = {
      electrical: {
        skillCategory: 'electrical',
        suggestedHourlyMin: 150,
        suggestedHourlyMax: 400,
        suggestedDailyMin: 800,
        suggestedDailyMax: 2500,
        marketAverage: 250,
        confidence: 'high'
      },
      plumbing: {
        skillCategory: 'plumbing',
        suggestedHourlyMin: 120,
        suggestedHourlyMax: 350,
        suggestedDailyMin: 700,
        suggestedDailyMax: 2200,
        marketAverage: 200,
        confidence: 'high'
      },
      carpentry: {
        skillCategory: 'carpentry',
        suggestedHourlyMin: 100,
        suggestedHourlyMax: 300,
        suggestedDailyMin: 600,
        suggestedDailyMax: 2000,
        marketAverage: 180,
        confidence: 'medium'
      },
      cooking: {
        skillCategory: 'cooking',
        suggestedHourlyMin: 80,
        suggestedHourlyMax: 250,
        suggestedDailyMin: 500,
        suggestedDailyMax: 1800,
        marketAverage: 150,
        confidence: 'medium'
      },
      cleaning: {
        skillCategory: 'cleaning',
        suggestedHourlyMin: 60,
        suggestedHourlyMax: 150,
        suggestedDailyMin: 400,
        suggestedDailyMax: 1000,
        marketAverage: 100,
        confidence: 'high'
      },
      driving: {
        skillCategory: 'driving',
        suggestedHourlyMin: 100,
        suggestedHourlyMax: 300,
        suggestedDailyMin: 800,
        suggestedDailyMax: 2000,
        marketAverage: 180,
        confidence: 'high'
      },
      delivery: {
        skillCategory: 'delivery',
        suggestedHourlyMin: 80,
        suggestedHourlyMax: 200,
        suggestedDailyMin: 600,
        suggestedDailyMax: 1500,
        marketAverage: 120,
        confidence: 'medium'
      },
      security: {
        skillCategory: 'security',
        suggestedHourlyMin: 70,
        suggestedHourlyMax: 180,
        suggestedDailyMin: 500,
        suggestedDailyMax: 1200,
        marketAverage: 110,
        confidence: 'medium'
      },
      gardening: {
        skillCategory: 'gardening',
        suggestedHourlyMin: 60,
        suggestedHourlyMax: 150,
        suggestedDailyMin: 400,
        suggestedDailyMax: 1000,
        marketAverage: 90,
        confidence: 'low'
      },
      tutoring: {
        skillCategory: 'tutoring',
        suggestedHourlyMin: 150,
        suggestedHourlyMax: 500,
        suggestedDailyMin: 800,
        suggestedDailyMax: 3000,
        marketAverage: 300,
        confidence: 'medium'
      }
    }

    const baseRates = marketRates[skillCategory || 'cleaning'] || marketRates['cleaning']

    if (!baseRates) {
      // Fallback to default cleaning rates if skill category not found
      return {
        skillCategory: skillCategory || 'cleaning',
        location,
        suggestedHourlyMin: 60,
        suggestedHourlyMax: 150,
        suggestedDailyMin: 400,
        suggestedDailyMax: 1000,
        marketAverage: 100,
        confidence: 'low' as const
      }
    }

    // Location-based adjustments (simplified)
    let locationMultiplier = 1.0
    if (location) {
      const locationLower = location.toLowerCase()
      if (locationLower.includes('mumbai') || locationLower.includes('delhi') || locationLower.includes('bangalore')) {
        locationMultiplier = 1.3 // Metro cities
      } else if (locationLower.includes('pune') || locationLower.includes('hyderabad') || locationLower.includes('chennai')) {
        locationMultiplier = 1.2 // Tier 1 cities
      } else if (locationLower.includes('ahmedabad') || locationLower.includes('kolkata') || locationLower.includes('jaipur')) {
        locationMultiplier = 1.1 // Tier 2 cities
      }
    }

    return {
      skillCategory: baseRates.skillCategory,
      location,
      suggestedHourlyMin: Math.round(baseRates.suggestedHourlyMin * locationMultiplier),
      suggestedHourlyMax: Math.round(baseRates.suggestedHourlyMax * locationMultiplier),
      suggestedDailyMin: Math.round(baseRates.suggestedDailyMin * locationMultiplier),
      suggestedDailyMax: Math.round(baseRates.suggestedDailyMax * locationMultiplier),
      marketAverage: Math.round(baseRates.marketAverage * locationMultiplier),
      confidence: baseRates.confidence
    }
  }

  /**
   * Get completeness level based on score
   */
  getCompletenessLevel(score: number): 'draft' | 'basic' | 'good' | 'complete' {
    if (score >= 90) return 'complete'
    if (score >= 80) return 'good'
    if (score >= 60) return 'basic'
    return 'draft'
  }

  /**
   * Calculate platform access permissions based on completeness and verification
   */
  calculatePlatformAccess(completenessScore: number, isVerified: boolean): {
    canApplyToJobs: boolean
    canReceiveJobInvites: boolean
    searchVisibilityMultiplier: number
    hasFullAccess: boolean
  } {
    const completenessLevel = this.getCompletenessLevel(completenessScore)

    // Base access rules
    let canApplyToJobs = false
    let canReceiveJobInvites = false
    let searchVisibilityMultiplier = 0.1 // Very low visibility for incomplete profiles
    let hasFullAccess = false

    // Phone verification is required for any platform access
    if (!isVerified) {
      return {
        canApplyToJobs: false,
        canReceiveJobInvites: false,
        searchVisibilityMultiplier: 0,
        hasFullAccess: false
      }
    }

    // Access levels based on completeness
    switch (completenessLevel) {
      case 'complete': // 90%+
        canApplyToJobs = true
        canReceiveJobInvites = true
        searchVisibilityMultiplier = 3.0 // 3x visibility boost
        hasFullAccess = true
        break

      case 'good': // 80-89%
        canApplyToJobs = true
        canReceiveJobInvites = true
        searchVisibilityMultiplier = 2.0 // 2x visibility boost
        hasFullAccess = true
        break

      case 'basic': // 60-79%
        canApplyToJobs = true
        canReceiveJobInvites = false // Can apply but won't receive invites
        searchVisibilityMultiplier = 1.0 // Normal visibility
        hasFullAccess = false
        break

      case 'draft': // <60%
        canApplyToJobs = false
        canReceiveJobInvites = false
        searchVisibilityMultiplier = 0.3 // Very limited visibility
        hasFullAccess = false
        break
    }

    return {
      canApplyToJobs,
      canReceiveJobInvites,
      searchVisibilityMultiplier,
      hasFullAccess
    }
  }

  /**
   * Get profile completeness requirements for each level
   */
  getCompletenessRequirements(): Record<string, { minScore: number; description: string; benefits: string[] }> {
    return {
      draft: {
        minScore: 0,
        description: 'Basic profile information only',
        benefits: ['Limited profile visibility', 'Cannot apply to jobs']
      },
      basic: {
        minScore: 60,
        description: 'Essential information completed',
        benefits: ['Can apply to jobs', 'Normal search visibility', 'Basic platform access']
      },
      good: {
        minScore: 80,
        description: 'Most profile fields completed',
        benefits: ['Can receive job invites', '2x search visibility', 'Full platform access']
      },
      complete: {
        minScore: 90,
        description: 'All profile information completed',
        benefits: ['Priority in search results', '3x search visibility', 'Maximum job opportunities']
      }
    }
  }

  /**
   * Suggest relevant skill categories based on description
   */
  suggestSkillCategories(description: string): string[] {
    const keywords: Record<string, string[]> = {
      electrical: ['electric', 'wiring', 'voltage', 'circuit', 'power', 'electrical', 'electrician'],
      plumbing: ['plumb', 'pipe', 'water', 'drain', 'leak', 'plumber', 'plumbing'],
      carpentry: ['wood', 'carpenter', 'furniture', 'cabinet', 'carpentry', 'timber'],
      cooking: ['cook', 'chef', 'kitchen', 'food', 'recipe', 'cooking', 'culinary'],
      cleaning: ['clean', 'housekeeping', 'maid', 'cleaning', 'sanitize', 'sweep'],
      driving: ['drive', 'driver', 'vehicle', 'car', 'transport', 'driving'],
      delivery: ['deliver', 'courier', 'package', 'delivery', 'logistics'],
      security: ['security', 'guard', 'watchman', 'safety', 'protection'],
      gardening: ['garden', 'plant', 'landscaping', 'gardening', 'horticulture'],
      tutoring: ['teach', 'tutor', 'education', 'tutoring', 'academic', 'student']
    }

    const descriptionLower = description.toLowerCase()
    const suggestions: string[] = []

    for (const [category, categoryKeywords] of Object.entries(keywords)) {
      const matches = categoryKeywords.filter(keyword => descriptionLower.includes(keyword))
      if (matches.length > 0) {
        suggestions.push(category)
      }
    }

    return suggestions
  }
}

export const profileValidationService = new ProfileValidationService()
