import * as jwt from 'jsonwebtoken'
import { SignOptions } from 'jsonwebtoken'
import config from '../config'
import logger from './logger'

export interface JwtPayload {
  userId: string
  phone: string
  userType: 'worker' | 'poster'
  iat?: number
  exp?: number
}

export interface RefreshTokenPayload {
  userId: string
  tokenVersion: number
  iat?: number
  exp?: number
}

class JwtService {
  private accessTokenSecret: string
  private refreshTokenSecret: string
  private accessTokenExpiry: string
  private refreshTokenExpiry: string

  constructor() {
    this.accessTokenSecret = config.jwt.secret
    this.refreshTokenSecret = `${config.jwt.secret}_refresh`
    this.accessTokenExpiry = config.jwt.expiresIn
    this.refreshTokenExpiry = config.jwt.refreshExpiresIn
  }

  // Generate access token
  generateAccessToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): string {
    try {
      const options = {
        expiresIn: this.accessTokenExpiry,
        issuer: 'ozgaar-backend',
        audience: 'ozgaar-mobile',
      } as SignOptions
      return jwt.sign(payload, this.accessTokenSecret, options)
    } catch (error) {
      logger.error('Error generating access token:', error)
      throw new Error('Failed to generate access token')
    }
  }

  // Generate refresh token
  generateRefreshToken(payload: Omit<RefreshTokenPayload, 'iat' | 'exp'>): string {
    try {
      const options = {
        expiresIn: this.refreshTokenExpiry,
        issuer: 'ozgaar-backend',
        audience: 'ozgaar-mobile',
      } as SignOptions
      return jwt.sign(payload, this.refreshTokenSecret, options)
    } catch (error) {
      logger.error('Error generating refresh token:', error)
      throw new Error('Failed to generate refresh token')
    }
  }

  // Verify access token
  verifyAccessToken(token: string): JwtPayload {
    try {
      const decoded = jwt.verify(token, this.accessTokenSecret, {
        issuer: 'ozgaar-backend',
        audience: 'ozgaar-mobile',
      }) as JwtPayload

      return decoded
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Access token expired')
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid access token')
      } else {
        logger.error('Error verifying access token:', error)
        throw new Error('Token verification failed')
      }
    }
  }

  // Verify refresh token
  verifyRefreshToken(token: string): RefreshTokenPayload {
    try {
      const decoded = jwt.verify(token, this.refreshTokenSecret, {
        issuer: 'ozgaar-backend',
        audience: 'ozgaar-mobile',
      }) as RefreshTokenPayload

      return decoded
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Refresh token expired')
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid refresh token')
      } else {
        logger.error('Error verifying refresh token:', error)
        throw new Error('Refresh token verification failed')
      }
    }
  }

  // Decode token without verification (for debugging)
  decodeToken(token: string): any {
    try {
      return jwt.decode(token)
    } catch (error) {
      logger.error('Error decoding token:', error)
      return null
    }
  }

  // Get token expiry time
  getTokenExpiry(token: string): Date | null {
    try {
      const decoded = this.decodeToken(token)
      if (decoded && decoded.exp) {
        return new Date(decoded.exp * 1000)
      }
      return null
    } catch (error) {
      logger.error('Error getting token expiry:', error)
      return null
    }
  }

  // Check if token is expired
  isTokenExpired(token: string): boolean {
    try {
      const expiry = this.getTokenExpiry(token)
      if (!expiry) return true
      return expiry < new Date()
    } catch (error) {
      logger.error('Error checking token expiry:', error)
      return true
    }
  }

  // Generate token pair (access + refresh)
  generateTokenPair(userPayload: Omit<JwtPayload, 'iat' | 'exp'>, tokenVersion: number = 1) {
    const accessToken = this.generateAccessToken(userPayload)
    const refreshToken = this.generateRefreshToken({
      userId: userPayload.userId,
      tokenVersion,
    })

    return {
      accessToken,
      refreshToken,
      expiresIn: this.accessTokenExpiry,
    }
  }
}

export default new JwtService()
