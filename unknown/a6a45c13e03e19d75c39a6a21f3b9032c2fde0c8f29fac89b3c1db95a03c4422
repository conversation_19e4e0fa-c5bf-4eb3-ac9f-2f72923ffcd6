/**
 * Conversation API Types
 */

// Get Conversations Types
export interface IGetConversationsRequest {
  page?: number;
  limit?: number;
}

export interface IConversationSummary {
  id: string;
  jobId: string;
  jobTitle: string;
  workerId: string;
  workerName: string;
  workerAvatar?: string;
  posterId: string;
  posterName: string;
  posterAvatar?: string;
  isActive: boolean;
  lastMessage?: {
    id: string;
    text: string;
    senderId: string;
    timestamp: string;
    status: 'sent' | 'delivered' | 'read';
  };
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface IGetConversationsResponse {
  success: boolean;
  message: string;
  error?: string;
  conversations?: IConversationSummary[];
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasMore: boolean;
  };
}

// Get Conversation Details Types
export interface IGetConversationRequest {
  page?: number;
  limit?: number;
}

export interface IConversationDetails {
  id: string;
  jobId: string;
  jobTitle: string;
  jobLocation: string;
  jobRate: number;
  jobRateType: 'hourly' | 'daily' | 'fixed';
  workerId: string;
  workerName: string;
  workerAvatar?: string;
  posterId: string;
  posterName: string;
  posterAvatar?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IGetConversationResponse {
  success: boolean;
  message: string;
  error?: string;
  conversation?: IConversationDetails;
}
