# 2factor.in OTP API Specification Document

## Overview
This specification document covers the 2factor.in OTP service APIs for sending and verifying SMS OTPs. This document is designed for AI coding agents to understand and implement OTP functionality.

## Base Configuration
- **Base URL**: `https://2factor.in/API/V1/`
- **Authentication**: API Key-based authentication
- **Protocol**: HTTPS only
- **Method**: GET requests for all endpoints

## API Endpoints

### 1. Send OTP

**Endpoint**: `{API_KEY}/SMS/{PHONE_NUMBER}/AUTOGEN`

**Purpose**: Sends an auto-generated OTP to the specified phone number via SMS.

**HTTP Method**: GET

**Full URL Format**: 
```
https://2factor.in/API/V1/{API_KEY}/SMS/{PHONE_NUMBER}/AUTOGEN
```

**Parameters**:
- `API_KEY` (string, required): Your unique API key from 2factor.in dashboard
- `PHONE_NUMBER` (string, required): Target phone number with country code (without + symbol)
  - Format: Country code + phone number (e.g., "************" for India)

**Response Format**:
```json
{
  "Status": "Success|Error",
  "Details": "Session_ID_Here"
}
```

**Success Response Example**:
```json
{
  "Status": "Success",
  "Details": "d4c4b2a1-1234-5678-9abc-def123456789"
}
```

**Error Response Example**:
```json
{
  "Status": "Error",
  "Details": "Invalid phone number"
}
```

### 2. Verify OTP

**Endpoint**: `{API_KEY}/SMS/VERIFY/{SESSION_ID}/{OTP}`

**Purpose**: Verifies the OTP entered by the user against the session.

**HTTP Method**: GET

**Full URL Format**: 
```
https://2factor.in/API/V1/{API_KEY}/SMS/VERIFY/{SESSION_ID}/{OTP}
```

**Parameters**:
- `API_KEY` (string, required): Your unique API key from 2factor.in dashboard
- `SESSION_ID` (string, required): Session ID returned from the send OTP request
- `OTP` (string, required): The OTP code entered by the user (typically 4-6 digits)

**Response Format**:
```json
{
  "Status": "Success|Error",
  "Details": "Verification_Message"
}
```

**Success Response Example**:
```json
{
  "Status": "Success",
  "Details": "OTP Matched"
}
```

**Error Response Example**:
```json
{
  "Status": "Error",
  "Details": "OTP Expired"
}
```

## Implementation Guidelines

### Error Handling
Common error scenarios to handle:
- Invalid API key
- Invalid phone number format
- OTP expired (usually 5-10 minutes)
- OTP attempts exceeded
- Invalid session ID
- Network timeouts

### Best Practices

1. **Session Management**: Store the session ID returned from send OTP for verification
2. **Timeout Handling**: Implement proper timeout handling for network requests
3. **Rate Limiting**: Respect API rate limits to avoid account suspension
4. **Security**: Never expose API keys in client-side code
5. **User Experience**: Provide clear error messages based on API responses

### Sample Implementation Flow

```python
import requests

class TwoFactorOTP:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://2factor.in/API/V1"
        
    def send_otp(self, phone_number):
        """Send OTP to phone number"""
        url = f"{self.base_url}/{self.api_key}/SMS/{phone_number}/AUTOGEN"
        response = requests.get(url)
        return response.json()
        
    def verify_otp(self, session_id, otp):
        """Verify OTP with session ID"""
        url = f"{self.base_url}/{self.api_key}/SMS/VERIFY/{session_id}/{otp}"
        response = requests.get(url)
        return response.json()
```

### Usage Example

```python
# Initialize
otp_service = TwoFactorOTP("your_api_key_here")

# Send OTP
phone = "************"  # India number example
send_result = otp_service.send_otp(phone)

if send_result["Status"] == "Success":
    session_id = send_result["Details"]
    
    # Later, when user enters OTP
    user_otp = "1234"  # OTP entered by user
    verify_result = otp_service.verify_otp(session_id, user_otp)
    
    if verify_result["Status"] == "Success":
        print("OTP verified successfully")
    else:
        print(f"Verification failed: {verify_result['Details']}")
else:
    print(f"Failed to send OTP: {send_result['Details']}")
```

## Additional Notes

- **OTP Validity**: OTPs typically expire within 5-10 minutes
- **Phone Number Format**: Always include country code without the + symbol
- **Session Persistence**: Session IDs should be stored temporarily for verification
- **Cost**: Each SMS sent incurs a cost based on your 2factor.in plan
- **Testing**: Use the provided test credentials for development/testing

This specification provides everything your AI coding agent needs to implement OTP sending and verification functionality using the 2factor.in service.

[1] https://documenter.getpostman.com/view/301893/TWDamFGh