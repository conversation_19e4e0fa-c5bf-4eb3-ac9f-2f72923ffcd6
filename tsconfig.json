{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "paths": {"@mobile/*": ["./mobile/*"], "@backend/*": ["./backend/src/*"], "@ozgaar/types": ["./packages/types/src"]}}, "references": [{"path": "./packages/types"}, {"path": "./mobile"}, {"path": "./backend"}], "files": []}