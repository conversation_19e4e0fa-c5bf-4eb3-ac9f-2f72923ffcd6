import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import * as RNLocalize from 'react-native-localize'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { fontAndVoiceSupport } from './fontAndVoiceSupport'

// Import translation files
import en from '../locales/en.json'
import hi from '../locales/hi.json'
import ta from '../locales/ta.json'
import te from '../locales/te.json'
import bn from '../locales/bn.json'
import mr from '../locales/mr.json'
import gu from '../locales/gu.json'
import kn from '../locales/kn.json'

// Language resources
const resources = {
  en: { translation: en },
  hi: { translation: hi },
  ta: { translation: ta },
  te: { translation: te },
  bn: { translation: bn },
  mr: { translation: mr },
  gu: { translation: gu },
  kn: { translation: kn },
}

// Supported language codes
export const SUPPORTED_LANGUAGES = [
  'en', 'hi', 'ta', 'te', 'bn', 'mr', 'gu', 'kn'
]

export interface LanguageMetadata {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  sample: string;
  rtl: boolean;
  hasVoiceInput: boolean;
  fontSupport?: any;
  voiceSupport?: any;
}

// Language metadata with font and voice support
export const LANGUAGE_METADATA: Record<string, LanguageMetadata> = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇬🇧',
    sample: 'Hello',
    rtl: false,
    hasVoiceInput: fontAndVoiceSupport.getVoiceSupport('en').isSupported,
    fontSupport: fontAndVoiceSupport.getFontSupport('en'),
    voiceSupport: fontAndVoiceSupport.getVoiceSupport('en'),
  },
  hi: {
    code: 'hi',
    name: 'Hindi',
    nativeName: 'हिन्दी',
    flag: '🇮🇳',
    sample: 'नमस्ते',
    rtl: false,
    hasVoiceInput: fontAndVoiceSupport.getVoiceSupport('hi').isSupported,
    fontSupport: fontAndVoiceSupport.getFontSupport('hi'),
    voiceSupport: fontAndVoiceSupport.getVoiceSupport('hi'),
  },
  ta: {
    code: 'ta',
    name: 'Tamil',
    nativeName: 'தமிழ்',
    flag: '🇮🇳',
    sample: 'வணக்கம்',
    rtl: false,
    hasVoiceInput: fontAndVoiceSupport.getVoiceSupport('ta').isSupported,
    fontSupport: fontAndVoiceSupport.getFontSupport('ta'),
    voiceSupport: fontAndVoiceSupport.getVoiceSupport('ta'),
  },
  te: {
    code: 'te',
    name: 'Telugu',
    nativeName: 'తెలుగు',
    flag: '🇮🇳',
    sample: 'నమస్కారం',
    rtl: false,
    hasVoiceInput: fontAndVoiceSupport.getVoiceSupport('te').isSupported,
    fontSupport: fontAndVoiceSupport.getFontSupport('te'),
    voiceSupport: fontAndVoiceSupport.getVoiceSupport('te'),
  },
  bn: {
    code: 'bn',
    name: 'Bengali',
    nativeName: 'বাংলা',
    flag: '🇮🇳',
    sample: 'নমস্কার',
    rtl: false,
    hasVoiceInput: fontAndVoiceSupport.getVoiceSupport('bn').isSupported,
    fontSupport: fontAndVoiceSupport.getFontSupport('bn'),
    voiceSupport: fontAndVoiceSupport.getVoiceSupport('bn'),
  },
  mr: {
    code: 'mr',
    name: 'Marathi',
    nativeName: 'मराठी',
    flag: '🇮🇳',
    sample: 'नमस्कार',
    rtl: false,
    hasVoiceInput: fontAndVoiceSupport.getVoiceSupport('mr').isSupported,
    fontSupport: fontAndVoiceSupport.getFontSupport('mr'),
    voiceSupport: fontAndVoiceSupport.getVoiceSupport('mr'),
  },
  gu: {
    code: 'gu',
    name: 'Gujarati',
    nativeName: 'ગુજરાતી',
    flag: '🇮🇳',
    sample: 'નમસ્તે',
    rtl: false,
    hasVoiceInput: fontAndVoiceSupport.getVoiceSupport('gu').isSupported,
    fontSupport: fontAndVoiceSupport.getFontSupport('gu'),
    voiceSupport: fontAndVoiceSupport.getVoiceSupport('gu'),
  },
  kn: {
    code: 'kn',
    name: 'Kannada',
    nativeName: 'ಕನ್ನಡ',
    flag: '🇮🇳',
    sample: 'ನಮಸ್ಕಾರ',
    rtl: false,
    hasVoiceInput: fontAndVoiceSupport.getVoiceSupport('kn').isSupported,
    fontSupport: fontAndVoiceSupport.getFontSupport('kn'),
    voiceSupport: fontAndVoiceSupport.getVoiceSupport('kn'),
  },
}

// Get device language with fallback
const getDeviceLanguage = (): string => {
  const locales = RNLocalize.getLocales()
  
  if (locales.length > 0) {
    const deviceLanguage = locales[0].languageCode
    
    // Check if device language is supported
    if (SUPPORTED_LANGUAGES.includes(deviceLanguage)) {
      return deviceLanguage
    }
    
    // Check for language variants (e.g., 'en-US' -> 'en')
    const baseLanguage = deviceLanguage.split('-')[0]
    if (SUPPORTED_LANGUAGES.includes(baseLanguage)) {
      return baseLanguage
    }
  }
  
  return 'en' // Default fallback
}

// Regional language detection based on phone area code
export const detectRegionalLanguage = (phone: string): string => {
  if (!phone.includes('+91')) {
    return getDeviceLanguage()
  }
  
  // Extract area code (first 3 digits after +91)
  const areaCode = phone.substring(3, 6)
  
  const languageMap: Record<string, string> = {
    '022': 'mr', // Mumbai - Marathi
    '033': 'bn', // Kolkata - Bengali
    '044': 'ta', // Chennai - Tamil
    '080': 'kn', // Bangalore - Kannada
    '040': 'te', // Hyderabad - Telugu
    '079': 'gu', // Ahmedabad - Gujarati
    '011': 'hi', // Delhi - Hindi
    '020': 'mr', // Pune - Marathi
    '0484': 'en', // Kochi - English (IT hub)
  }
  
  return languageMap[areaCode] || getDeviceLanguage()
}

// AsyncStorage keys
const LANGUAGE_STORAGE_KEY = '@ozgaar_language'

// Language persistence functions
export const saveLanguageToStorage = async (languageCode: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, languageCode)
  } catch (error) {
    console.error('Failed to save language to storage:', error)
  }
}

export const getLanguageFromStorage = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY)
  } catch (error) {
    console.error('Failed to get language from storage:', error)
    return null
  }
}

// Initialize i18n
const initI18n = async () => {
  let savedLanguage = await getLanguageFromStorage()

  // If no saved language, use device language
  if (!savedLanguage) {
    savedLanguage = getDeviceLanguage()
    await saveLanguageToStorage(savedLanguage)
  }

  // Load custom fonts
  await fontAndVoiceSupport.loadCustomFonts()

  await i18n
    .use(initReactI18next)
    .init({
      resources,
      lng: savedLanguage,
      fallbackLng: 'en',

      interpolation: {
        escapeValue: false, // React already escapes values
      },

      react: {
        useSuspense: false, // Disable suspense for React Native
      },

      // Enable debugging in development
      debug: __DEV__,
    })
}

// Change language function
export const changeLanguage = async (languageCode: string): Promise<void> => {
  if (!SUPPORTED_LANGUAGES.includes(languageCode)) {
    console.warn(`Unsupported language: ${languageCode}`)
    return
  }
  
  await i18n.changeLanguage(languageCode)
  await saveLanguageToStorage(languageCode)
}

// Initialize i18n
initI18n()

export default i18n
