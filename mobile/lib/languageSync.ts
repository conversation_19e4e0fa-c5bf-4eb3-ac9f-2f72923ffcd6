import AsyncStorage from '@react-native-async-storage/async-storage'
import NetInfo from '@react-native-community/netinfo'
import { authHelpers } from './supabase'
import { SupportedLanguage } from '../types'

// AsyncStorage keys
const LANGUAGE_STORAGE_KEY = '@ozgaar_language'
const LANGUAGE_SYNC_PENDING_KEY = '@ozgaar_language_sync_pending'
const LAST_SYNC_TIME_KEY = '@ozgaar_last_language_sync'

// Language code mapping between new ISO codes and old SupportedLanguage codes
const ISO_TO_SUPPORTED_LANGUAGE: Record<string, SupportedLanguage> = {
  'en': 'english',
  'hi': 'hindi',
  'ta': 'tamil',
  'te': 'telugu',
  'bn': 'bengali',
  'mr': 'marathi',
  'gu': 'gujarati',
  'kn': 'kannada'
}

const SUPPORTED_TO_ISO_LANGUAGE: Record<SupportedLanguage, string> = {
  'english': 'en',
  'hindi': 'hi',
  'tamil': 'ta',
  'telugu': 'te',
  'bengali': 'bn',
  'marathi': 'mr',
  'gujarati': 'gu',
  'kannada': 'kn'
}

export interface LanguageSyncService {
  saveLanguageLocally: (languageCode: string) => Promise<void>
  getLanguageFromStorage: () => Promise<string | null>
  syncLanguageToServer: (languageCode: string, userId?: string) => Promise<boolean>
  checkAndSyncPendingLanguage: () => Promise<void>
  isOnline: () => Promise<boolean>
  getLastSyncTime: () => Promise<number | null>
  saveLanguageWithSync: (languageCode: string, userId?: string) => Promise<void>
}

class LanguageSyncServiceImpl implements LanguageSyncService {
  private syncInProgress = false

  // Save language preference locally
  async saveLanguageLocally(languageCode: string): Promise<void> {
    try {
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, languageCode)
      console.log(`Language saved locally: ${languageCode}`)
    } catch (error) {
      console.error('Failed to save language locally:', error)
      throw error
    }
  }

  // Get language preference from local storage
  async getLanguageFromStorage(): Promise<string | null> {
    try {
      const language = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY)
      return language
    } catch (error) {
      console.error('Failed to get language from storage:', error)
      return null
    }
  }

  // Check if device is online
  async isOnline(): Promise<boolean> {
    try {
      const netInfo = await NetInfo.fetch()
      return netInfo.isConnected === true && netInfo.isInternetReachable === true
    } catch (error) {
      console.error('Failed to check network status:', error)
      return false
    }
  }

  // Get last sync time
  async getLastSyncTime(): Promise<number | null> {
    try {
      const lastSync = await AsyncStorage.getItem(LAST_SYNC_TIME_KEY)
      return lastSync ? parseInt(lastSync, 10) : null
    } catch (error) {
      console.error('Failed to get last sync time:', error)
      return null
    }
  }

  // Set last sync time
  private async setLastSyncTime(timestamp: number): Promise<void> {
    try {
      await AsyncStorage.setItem(LAST_SYNC_TIME_KEY, timestamp.toString())
    } catch (error) {
      console.error('Failed to set last sync time:', error)
    }
  }

  // Mark language as pending sync
  private async markLanguageForSync(languageCode: string): Promise<void> {
    try {
      await AsyncStorage.setItem(LANGUAGE_SYNC_PENDING_KEY, languageCode)
    } catch (error) {
      console.error('Failed to mark language for sync:', error)
    }
  }

  // Clear pending sync flag
  private async clearPendingSync(): Promise<void> {
    try {
      await AsyncStorage.removeItem(LANGUAGE_SYNC_PENDING_KEY)
    } catch (error) {
      console.error('Failed to clear pending sync:', error)
    }
  }

  // Get pending language sync
  private async getPendingLanguageSync(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(LANGUAGE_SYNC_PENDING_KEY)
    } catch (error) {
      console.error('Failed to get pending language sync:', error)
      return null
    }
  }

  // Sync language preference to server
  async syncLanguageToServer(languageCode: string, userId?: string): Promise<boolean> {
    if (this.syncInProgress) {
      console.log('Sync already in progress, skipping...')
      return false
    }

    try {
      this.syncInProgress = true

      // Check if online
      const online = await this.isOnline()
      if (!online) {
        console.log('Device offline, marking language for sync later')
        await this.markLanguageForSync(languageCode)
        return false
      }

      // Get current user session if userId not provided
      let currentUserId = userId
      if (!currentUserId) {
        const { session } = await authHelpers.getSession()
        if (!session?.user?.id) {
          console.log('No user session found, marking language for sync later')
          await this.markLanguageForSync(languageCode)
          return false
        }
        currentUserId = session.user.id
      }

      // Convert ISO language code to SupportedLanguage format for server
      const supportedLanguageCode = ISO_TO_SUPPORTED_LANGUAGE[languageCode] || 'english'

      // Sync to server
      const { error } = await authHelpers.upsertUserProfile({
        id: currentUserId,
        preferred_language: supportedLanguageCode,
        updated_at: new Date().toISOString(),
      })

      if (error) {
        console.error('Failed to sync language to server:', error)
        await this.markLanguageForSync(languageCode)
        return false
      }

      // Success - clear pending sync and update last sync time
      await this.clearPendingSync()
      await this.setLastSyncTime(Date.now())
      console.log(`Language synced to server successfully: ${languageCode}`)
      return true

    } catch (error) {
      console.error('Error syncing language to server:', error)
      await this.markLanguageForSync(languageCode)
      return false
    } finally {
      this.syncInProgress = false
    }
  }

  // Check and sync any pending language changes
  async checkAndSyncPendingLanguage(): Promise<void> {
    try {
      const pendingLanguage = await this.getPendingLanguageSync()
      if (!pendingLanguage) {
        return // No pending sync
      }

      const online = await this.isOnline()
      if (!online) {
        console.log('Device still offline, keeping language sync pending')
        return
      }

      console.log(`Attempting to sync pending language: ${pendingLanguage}`)
      const success = await this.syncLanguageToServer(pendingLanguage)
      
      if (success) {
        console.log('Pending language sync completed successfully')
      } else {
        console.log('Pending language sync failed, will retry later')
      }
    } catch (error) {
      console.error('Error checking pending language sync:', error)
    }
  }

  // Save language with automatic sync attempt
  async saveLanguageWithSync(languageCode: string, userId?: string): Promise<void> {
    // Always save locally first
    await this.saveLanguageLocally(languageCode)

    // Attempt to sync to server
    const syncSuccess = await this.syncLanguageToServer(languageCode, userId)
    
    if (!syncSuccess) {
      console.log('Language saved locally, will sync to server when online')
    }
  }
}

// Create singleton instance
export const languageSyncService: LanguageSyncService = new LanguageSyncServiceImpl()

// Setup network listener for automatic sync when coming online
let networkListener: (() => void) | null = null

export const setupNetworkListener = () => {
  if (networkListener) {
    return // Already setup
  }

  networkListener = NetInfo.addEventListener(state => {
    if (state.isConnected && state.isInternetReachable) {
      console.log('Device came online, checking for pending language sync')
      languageSyncService.checkAndSyncPendingLanguage()
    }
  })
}

export const removeNetworkListener = () => {
  if (networkListener) {
    networkListener()
    networkListener = null
  }
}

export default languageSyncService
