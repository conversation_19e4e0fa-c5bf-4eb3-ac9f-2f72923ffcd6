import AsyncStorage from '@react-native-async-storage/async-storage'
import { SupportedLanguage, User } from '@ozgaar/types'

// Backend API configuration
const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL || 'http://localhost:3000/api'

// HTTP client configuration
const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
}

// API response types
interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  error?: string
  code?: string
  details?: any
}

// Auth API response types
interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: string
}

interface SendOtpResponse {
  phone: string
  expiresIn: number
  remaining_attempts: number
}

interface VerifyOtpResponse {
  user: User
  tokens: AuthTokens
}

interface ProfileResponse {
  user: User
}

// HTTP client class
class ApiClient {
  private baseUrl: string
  private defaultHeaders: Record<string, string>

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl
    this.defaultHeaders = DEFAULT_HEADERS
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    try {
      const token = await AsyncStorage.getItem('access_token')
      if (token) {
        return {
          ...this.defaultHeaders,
          'Authorization': `Bearer ${token}`
        }
      }
    } catch (error) {
      console.warn('Failed to get auth token:', error)
    }
    return this.defaultHeaders
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`
      const headers = options.headers ?
        { ...await this.getAuthHeaders(), ...options.headers } :
        await this.getAuthHeaders()

      const response = await fetch(url, {
        ...options,
        headers,
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`)
      }

      return data
    } catch (error) {
      console.error('API request failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error'
      }
    }
  }

  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  // Expose baseUrl for direct fetch calls (like file uploads)
  get apiBaseUrl(): string {
    return this.baseUrl
  }

  // Expose auth headers for direct fetch calls
  async getPublicAuthHeaders(): Promise<Record<string, string>> {
    return this.getAuthHeaders()
  }
}

export const apiClient = new ApiClient()


// Authentication helper functions using the new API
export const authHelpers = {
  // Send OTP to phone number
  async sendOTP(phone: string) {
    try {
      const response = await apiClient.post<SendOtpResponse>('/auth/send-otp', { phone })

      if (!response.success) {
        return {
          data: null,
          error: { message: response.error || 'Failed to send OTP' },
          backup_used: false
        }
      }

      return {
        data: response.data,
        error: null,
        backup_used: false // Backend handles SMS routing
      }
    } catch (err) {
      console.error('OTP send error:', err)
      return {
        data: null,
        error: { message: err instanceof Error ? err.message : 'Failed to send OTP' },
        backup_used: false
      }
    }
  },

  // Verify OTP
  async verifyOTP(phone: string, otp: string) {
    try {
      const response = await apiClient.post<VerifyOtpResponse>('/auth/verify-otp', { phone, otp })

      if (!response.success) {
        return {
          data: null,
          error: { message: response.error || 'Invalid OTP' }
        }
      }

      // Store tokens in AsyncStorage
      if (response.data?.tokens) {
        await AsyncStorage.setItem('access_token', response.data.tokens.accessToken)
        await AsyncStorage.setItem('refresh_token', response.data.tokens.refreshToken)
      }

      return { data: response.data, error: null }
    } catch (err) {
      console.error('OTP verification error:', err)
      return {
        data: null,
        error: { message: err instanceof Error ? err.message : 'Invalid OTP' }
      }
    }
  },

  // Get current session (check if user is authenticated)
  async getSession() {
    try {
      const token = await AsyncStorage.getItem('access_token')
      if (!token) {
        return { session: null, error: null }
      }

      // Verify token by getting user profile
      const response = await apiClient.get<ProfileResponse>('/auth/profile')

      if (!response.success) {
        // Token might be expired, try to refresh
        const refreshResult = await this.refreshToken()
        if (refreshResult.success) {
          // Retry getting profile with new token
          const retryResponse = await apiClient.get<ProfileResponse>('/auth/profile')
          if (retryResponse.success && retryResponse.data) {
            return {
              session: { user: retryResponse.data.user },
              error: null
            }
          }
        }

        // Clear invalid tokens
        await AsyncStorage.multiRemove(['access_token', 'refresh_token'])
        return { session: null, error: null }
      }

      return {
        session: response.data ? { user: response.data.user } : null,
        error: null
      }
    } catch (err) {
      console.error('Get session error:', err)
      return { session: null, error: err }
    }
  },

  // Refresh access token
  async refreshToken() {
    try {
      const refreshToken = await AsyncStorage.getItem('refresh_token')
      if (!refreshToken) {
        return { success: false, error: 'No refresh token' }
      }

      const response = await apiClient.post<{ tokens: AuthTokens }>('/auth/refresh-token', { refreshToken })

      if (!response.success) {
        await AsyncStorage.multiRemove(['access_token', 'refresh_token'])
        return { success: false, error: response.error }
      }

      // Store new tokens
      if (response.data?.tokens) {
        await AsyncStorage.setItem('access_token', response.data.tokens.accessToken)
        await AsyncStorage.setItem('refresh_token', response.data.tokens.refreshToken)
      }

      return { success: true, data: response.data }
    } catch (err) {
      console.error('Refresh token error:', err)
      return { success: false, error: err }
    }
  },

  // Sign out
  async signOut() {
    try {
      // Clear local tokens
      await AsyncStorage.multiRemove(['access_token', 'refresh_token'])
      return { error: null }
    } catch (err) {
      console.error('Sign out error:', err)
      return { error: err }
    }
  },

  // Create or update user profile
  async upsertUserProfile(userData: Partial<User>) {
    try {
      const response = await apiClient.put<ProfileResponse>('/auth/profile', userData)

      if (!response.success) {
        return { data: null, error: { message: response.error } }
      }

      return { data: response.data?.user, error: null }
    } catch (err) {
      console.error('Upsert user profile error:', err)
      return { data: null, error: err }
    }
  },

  // Get user profile
  async getUserProfile(userId?: string) {
    try {
      const response = await apiClient.get<ProfileResponse>('/auth/profile')

      if (!response.success) {
        return { data: null, error: { message: response.error } }
      }

      return { data: response.data?.user, error: null }
    } catch (err) {
      console.error('Get user profile error:', err)
      return { data: null, error: err }
    }
  },

  // Complete user profile (first time setup)
  async completeUserProfile(profileData: {
    full_name: string
    preferred_language?: SupportedLanguage
    user_type?: 'worker' | 'poster'
    email?: string
    address?: string
    primary_skill_category?: 'electrical' | 'plumbing' | 'carpentry' | 'cooking' | 'cleaning' | 'driving' | 'delivery' | 'security' | 'gardening' | 'tutoring'
    skill_categories?: ('electrical' | 'plumbing' | 'carpentry' | 'cooking' | 'cleaning' | 'driving' | 'delivery' | 'security' | 'gardening' | 'tutoring')[]
  }) {
    try {
      const response = await apiClient.post<ProfileResponse>('/auth/complete-profile', profileData)

      if (!response.success) {
        return { data: null, error: { message: response.error } }
      }

      return { data: response.data?.user, error: null }
    } catch (err) {
      console.error('Complete user profile error:', err)
      return { data: null, error: err }
    }
  },

  // Upload profile image
  async uploadProfileImage(imageUri: string) {
    try {
      // Create FormData for file upload
      const formData = new FormData()

      // Get file info
      const filename = imageUri.split('/').pop() || 'profile.jpg'
      const match = /\.(\w+)$/.exec(filename)
      const type = match ? `image/${match[1]}` : 'image/jpeg'

      formData.append('profileImage', {
        uri: imageUri,
        name: filename,
        type
      } as any)

      // Upload to backend
      const response = await fetch(`${apiClient.apiBaseUrl}/auth/upload-profile-image`, {
        method: 'POST',
        headers: {
          ...await apiClient.getPublicAuthHeaders(),
          'Content-Type': 'multipart/form-data'
        },
        body: formData
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        return { data: null, error: { message: result.error || 'Upload failed' } }
      }

      return { data: result.data, error: null }

    } catch (err) {
      console.error('Upload profile image error:', err)
      return { data: null, error: { message: err instanceof Error ? err.message : 'Upload failed' } }
    }
  },

  // Delete profile image
  async deleteProfileImage() {
    try {
      const response = await apiClient.delete<ProfileResponse>('/auth/profile-image')

      if (!response.success) {
        return { data: null, error: { message: response.error } }
      }

      return { data: response.data?.user, error: null }
    } catch (err) {
      console.error('Delete profile image error:', err)
      return { data: null, error: err }
    }
  }
}

// Phone number validation helper
export const validatePhoneNumber = (phone: string): { isValid: boolean; formatted: string; error?: string } => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '')
  
  // Check if it's a valid Indian mobile number
  if (cleaned.length === 10 && /^[6-9]/.test(cleaned)) {
    return {
      isValid: true,
      formatted: `+91${cleaned}`
    }
  } else if (cleaned.length === 12 && cleaned.startsWith('91') && /^91[6-9]/.test(cleaned)) {
    return {
      isValid: true,
      formatted: `+${cleaned}`
    }
  } else if (cleaned.length === 13 && cleaned.startsWith('91') && /^91[6-9]/.test(cleaned.substring(1))) {
    return {
      isValid: true,
      formatted: `+${cleaned.substring(1)}`
    }
  }
  
  return {
    isValid: false,
    formatted: phone,
    error: 'Please enter a valid Indian mobile number (10 digits starting with 6-9)'
  }
}

// Format phone number for display
export const formatPhoneForDisplay = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length === 12 && cleaned.startsWith('91')) {
    const number = cleaned.substring(2)
    return `${number.substring(0, 5)}-${number.substring(5)}`
  }
  return phone
}
