import { Platform } from 'react-native'

// Font configuration for different scripts
export const FONT_CONFIG = {
  // Devanagari script (Hindi, Marathi)
  devanagari: {
    fontFamily: Platform.select({
      ios: 'Devanagari Sangam MN',
      android: 'NotoSansDevanagari-Regular',
      default: 'system'
    }),
    testText: 'नमस्ते',
    languages: ['hi', 'mr']
  },
  
  // Tamil script
  tamil: {
    fontFamily: Platform.select({
      ios: 'Tamil Sangam MN',
      android: 'NotoSansTamil-Regular',
      default: 'system'
    }),
    testText: 'வணக்கம்',
    languages: ['ta']
  },
  
  // Telugu script
  telugu: {
    fontFamily: Platform.select({
      ios: 'Telugu Sangam MN',
      android: 'NotoSansTelugu-Regular',
      default: 'system'
    }),
    testText: 'నమస్కారం',
    languages: ['te']
  },
  
  // Bengali script
  bengali: {
    fontFamily: Platform.select({
      ios: 'Bangla Sangam MN',
      android: 'NotoSansBengali-Regular',
      default: 'system'
    }),
    testText: 'নমস্কার',
    languages: ['bn']
  },
  
  // Gujarati script
  gujarati: {
    fontFamily: Platform.select({
      ios: 'Gujarati Sangam MN',
      android: 'NotoSansGujarati-Regular',
      default: 'system'
    }),
    testText: 'નમસ્તે',
    languages: ['gu']
  },
  
  // Kannada script
  kannada: {
    fontFamily: Platform.select({
      ios: 'Kannada Sangam MN',
      android: 'NotoSansKannada-Regular',
      default: 'system'
    }),
    testText: 'ನಮಸ್ಕಾರ',
    languages: ['kn']
  },
  
  // Latin script (English)
  latin: {
    fontFamily: Platform.select({
      ios: 'System',
      android: 'Roboto',
      default: 'system'
    }),
    testText: 'Hello',
    languages: ['en']
  }
}

// Voice input support configuration
export const VOICE_INPUT_CONFIG = {
  hi: {
    isSupported: true,
    locale: 'hi-IN',
    displayName: 'Hindi (India)',
    confidence: 0.8
  },
  en: {
    isSupported: true,
    locale: 'en-IN',
    displayName: 'English (India)',
    confidence: 0.9
  },
  ta: {
    isSupported: true,
    locale: 'ta-IN',
    displayName: 'Tamil (India)',
    confidence: 0.7
  },
  te: {
    isSupported: true,
    locale: 'te-IN',
    displayName: 'Telugu (India)',
    confidence: 0.7
  },
  bn: {
    isSupported: true,
    locale: 'bn-IN',
    displayName: 'Bengali (India)',
    confidence: 0.7
  },
  mr: {
    isSupported: true,
    locale: 'mr-IN',
    displayName: 'Marathi (India)',
    confidence: 0.7
  },
  gu: {
    isSupported: true,
    locale: 'gu-IN',
    displayName: 'Gujarati (India)',
    confidence: 0.7
  },
  kn: {
    isSupported: true,
    locale: 'kn-IN',
    displayName: 'Kannada (India)',
    confidence: 0.7
  }
}

export interface FontSupport {
  isSupported: boolean
  fontFamily: string
  testText: string
  script: string
}

export interface VoiceSupport {
  isSupported: boolean
  locale: string
  displayName: string
  confidence: number
}

class FontAndVoiceSupportService {
  private fontCache = new Map<string, FontSupport>()
  private voiceCache = new Map<string, VoiceSupport>()

  // Get font support for a language
  getFontSupport(languageCode: string): FontSupport {
    if (this.fontCache.has(languageCode)) {
      return this.fontCache.get(languageCode)!
    }

    // Find the appropriate script for the language
    let fontConfig = FONT_CONFIG.latin // Default fallback
    let scriptName = 'latin'

    for (const [script, config] of Object.entries(FONT_CONFIG)) {
      if (config.languages.includes(languageCode)) {
        fontConfig = config
        scriptName = script
        break
      }
    }

    const support: FontSupport = {
      isSupported: true, // Assume supported with fallback
      fontFamily: fontConfig.fontFamily,
      testText: fontConfig.testText,
      script: scriptName
    }

    this.fontCache.set(languageCode, support)
    return support
  }

  // Get voice input support for a language
  getVoiceSupport(languageCode: string): VoiceSupport {
    if (this.voiceCache.has(languageCode)) {
      return this.voiceCache.get(languageCode)!
    }

    const voiceConfig = VOICE_INPUT_CONFIG[languageCode as keyof typeof VOICE_INPUT_CONFIG]
    
    const support: VoiceSupport = voiceConfig || {
      isSupported: false,
      locale: languageCode,
      displayName: `${languageCode} (Not Supported)`,
      confidence: 0
    }

    this.voiceCache.set(languageCode, support)
    return support
  }

  // Check if a language has proper font rendering
  async testFontRendering(languageCode: string): Promise<boolean> {
    try {
      const fontSupport = this.getFontSupport(languageCode)
      
      // For now, we assume fonts are available
      // In a real implementation, you might want to:
      // 1. Load a test font
      // 2. Render test text
      // 3. Check if it renders correctly
      
      return fontSupport.isSupported
    } catch (error) {
      console.error(`Error testing font rendering for ${languageCode}:`, error)
      return false
    }
  }

  // Get font style for a language
  getFontStyle(languageCode: string) {
    const fontSupport = this.getFontSupport(languageCode)
    return {
      fontFamily: fontSupport.fontFamily,
      // Add additional styling based on script
      ...(fontSupport.script === 'devanagari' && {
        lineHeight: 1.6, // Better line height for Devanagari
      }),
      ...(fontSupport.script === 'tamil' && {
        lineHeight: 1.5,
      }),
      ...(fontSupport.script === 'telugu' && {
        lineHeight: 1.5,
      }),
      ...(fontSupport.script === 'bengali' && {
        lineHeight: 1.5,
      }),
      ...(fontSupport.script === 'gujarati' && {
        lineHeight: 1.5,
      }),
      ...(fontSupport.script === 'kannada' && {
        lineHeight: 1.5,
      })
    }
  }

  // Load custom fonts if needed
  async loadCustomFonts(): Promise<void> {
    try {
      // Load any custom fonts here
      // For now, we rely on system fonts
      console.log('Font loading completed (using system fonts)')
    } catch (error) {
      console.error('Error loading custom fonts:', error)
    }
  }

  // Get all supported languages with their capabilities
  getAllLanguageCapabilities() {
    const capabilities = new Map()
    
    for (const languageCode of Object.keys(VOICE_INPUT_CONFIG)) {
      capabilities.set(languageCode, {
        font: this.getFontSupport(languageCode),
        voice: this.getVoiceSupport(languageCode)
      })
    }
    
    return capabilities
  }

  // Check if RTL support is needed
  isRTL(languageCode: string): boolean {
    // None of our supported Indian languages are RTL
    // But this could be extended for Arabic, Hebrew, etc.
    return false
  }

  // Get text direction for a language
  getTextDirection(languageCode: string): 'ltr' | 'rtl' {
    return this.isRTL(languageCode) ? 'rtl' : 'ltr'
  }
}

// Create singleton instance
export const fontAndVoiceSupport = new FontAndVoiceSupportService()

export default fontAndVoiceSupport
