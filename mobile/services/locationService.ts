import * as Location from 'expo-location'
import { Alert, Platform } from 'react-native'
import AsyncStorage from '@react-native-async-storage/async-storage'

export interface LocationData {
  latitude: number
  longitude: number
  address?: string
  landmark?: string
  accuracy?: number
  source: 'gps' | 'manual' | 'search' | 'cached'
  timestamp?: number
}

export interface LocationPermissionStatus {
  granted: boolean
  canAskAgain: boolean
  status: Location.LocationPermissionResponse['status']
}

export interface LocationServiceConfig {
  enableHighAccuracy: boolean
  timeout: number
  maximumAge: number
  distanceInterval: number
}

class LocationService {
  private static instance: LocationService
  private currentLocation: LocationData | null = null
  private watchId: Location.LocationSubscription | null = null
  private config: LocationServiceConfig = {
    enableHighAccuracy: true,
    timeout: 15000, // 15 seconds
    maximumAge: 300000, // 5 minutes
    distanceInterval: 100 // 100 meters
  }

  private constructor() {}

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService()
    }
    return LocationService.instance
  }

  /**
   * Check current location permission status
   */
  async getPermissionStatus(): Promise<LocationPermissionStatus> {
    try {
      const { status, canAskAgain } = await Location.getForegroundPermissionsAsync()
      
      return {
        granted: status === 'granted',
        canAskAgain,
        status
      }
    } catch (error) {
      console.error('Error checking location permission:', error)
      return {
        granted: false,
        canAskAgain: true,
        status: Location.PermissionStatus.UNDETERMINED
      }
    }
  }

  /**
   * Request location permission with user-friendly messaging
   */
  async requestPermission(): Promise<LocationPermissionStatus> {
    try {
      const currentStatus = await this.getPermissionStatus()
      
      if (currentStatus.granted) {
        return currentStatus
      }

      // If we can't ask again, guide user to settings
      if (!currentStatus.canAskAgain) {
        return new Promise((resolve) => {
          Alert.alert(
            'Location Permission Required',
            'Location access is needed to show nearby jobs. Please enable location permission in your device settings.',
            [
              {
                text: 'Cancel',
                style: 'cancel',
                onPress: () => resolve(currentStatus)
              },
              {
                text: 'Open Settings',
                onPress: () => {
                  Location.enableNetworkProviderAsync()
                  resolve(currentStatus)
                }
              }
            ]
          )
        })
      }

      // Request permission with explanation
      const { status, canAskAgain } = await Location.requestForegroundPermissionsAsync()
      
      return {
        granted: status === 'granted',
        canAskAgain,
        status
      }
    } catch (error) {
      console.error('Error requesting location permission:', error)
      return {
        granted: false,
        canAskAgain: true,
        status: Location.PermissionStatus.UNDETERMINED
      }
    }
  }

  /**
   * Get current location with permission handling
   */
  async getCurrentLocation(showPermissionDialog: boolean = true): Promise<LocationData | null> {
    try {
      // Check if location services are enabled
      const isEnabled = await Location.hasServicesEnabledAsync()
      if (!isEnabled) {
        if (showPermissionDialog) {
          Alert.alert(
            'Location Services Disabled',
            'Please enable location services in your device settings to find nearby jobs.',
            [{ text: 'OK' }]
          )
        }
        return null
      }

      // Check/request permission
      const permission = await this.getPermissionStatus()
      if (!permission.granted) {
        if (showPermissionDialog) {
          const requestResult = await this.requestPermission()
          if (!requestResult.granted) {
            return null
          }
        } else {
          return null
        }
      }

      // Get current position
      const location = await Location.getCurrentPositionAsync({
        accuracy: this.config.enableHighAccuracy ? 
          Location.Accuracy.High : Location.Accuracy.Balanced,
        timeInterval: this.config.timeout,
        distanceInterval: this.config.distanceInterval
      })

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || undefined,
        source: 'gps',
        timestamp: Date.now()
      }

      // Try to get address from coordinates
      try {
        const addresses = await Location.reverseGeocodeAsync({
          latitude: locationData.latitude,
          longitude: locationData.longitude
        })

        if (addresses.length > 0) {
          const address = addresses[0]
          locationData.address = [
            address.name,
            address.street,
            address.city,
            address.region,
            address.country
          ].filter(Boolean).join(', ')
        }
      } catch (geocodeError) {
        console.warn('Reverse geocoding failed:', geocodeError)
      }

      // Cache the location
      this.currentLocation = locationData
      await this.cacheLocation(locationData)

      return locationData
    } catch (error) {
      console.error('Error getting current location:', error)
      
      if (showPermissionDialog) {
        Alert.alert(
          'Location Error',
          'Unable to get your current location. Please try again or enter your location manually.',
          [{ text: 'OK' }]
        )
      }
      
      return null
    }
  }

  /**
   * Get cached location if available and recent
   */
  async getCachedLocation(): Promise<LocationData | null> {
    try {
      const cached = await AsyncStorage.getItem('cached_location')
      if (!cached) return null

      const locationData: LocationData = JSON.parse(cached)
      
      // Check if cache is still valid (within 30 minutes)
      const maxAge = 30 * 60 * 1000 // 30 minutes
      if (locationData.timestamp && (Date.now() - locationData.timestamp) < maxAge) {
        this.currentLocation = locationData
        return locationData
      }

      return null
    } catch (error) {
      console.error('Error getting cached location:', error)
      return null
    }
  }

  /**
   * Cache location data
   */
  private async cacheLocation(location: LocationData): Promise<void> {
    try {
      await AsyncStorage.setItem('cached_location', JSON.stringify(location))
    } catch (error) {
      console.error('Error caching location:', error)
    }
  }

  /**
   * Calculate distance between two points using Haversine formula
   */
  calculateDistance(
    lat1: number, 
    lon1: number, 
    lat2: number, 
    lon2: number
  ): number {
    const R = 6371 // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1)
    const dLon = this.toRadians(lon2 - lon1)

    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2)

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  /**
   * Format distance for display
   */
  formatDistance(distanceKm: number): string {
    if (distanceKm < 1) {
      return `${Math.round(distanceKm * 1000)}m`
    } else if (distanceKm < 10) {
      return `${distanceKm.toFixed(1)}km`
    } else {
      return `${Math.round(distanceKm)}km`
    }
  }

  /**
   * Get current location or fallback to cached
   */
  async getLocationWithFallback(): Promise<LocationData | null> {
    // Try to get current location first
    let location = await this.getCurrentLocation(false)
    
    if (!location) {
      // Fallback to cached location
      location = await this.getCachedLocation()
    }

    return location
  }

  /**
   * Clear cached location
   */
  async clearCache(): Promise<void> {
    try {
      await AsyncStorage.removeItem('cached_location')
      this.currentLocation = null
    } catch (error) {
      console.error('Error clearing location cache:', error)
    }
  }

  /**
   * Get current location data (cached or null)
   */
  getCurrentLocationData(): LocationData | null {
    return this.currentLocation
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<LocationServiceConfig>): void {
    this.config = { ...this.config, ...config }
  }
}

// Export singleton instance
export const locationService = LocationService.getInstance()
export default locationService
