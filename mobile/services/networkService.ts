import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface NetworkState {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: string;
}

export interface NetworkListener {
  unsubscribe: () => void;
}

class NetworkService {
  private listeners: Set<(state: NetworkState) => void> = new Set();
  private currentState: NetworkState = {
    isConnected: false,
    isInternetReachable: false,
    type: 'unknown'
  };

  constructor() {
    this.initialize();
  }

  private async initialize() {
    // Get initial network state
    const netInfo = await NetInfo.fetch();
    this.currentState = {
      isConnected: netInfo.isConnected ?? false,
      isInternetReachable: netInfo.isInternetReachable ?? false,
      type: netInfo.type || 'unknown'
    };

    // Listen for network changes
    NetInfo.addEventListener(state => {
      const newState: NetworkState = {
        isConnected: state.isConnected ?? false,
        isInternetReachable: state.isInternetReachable ?? false,
        type: state.type || 'unknown'
      };

      // Only notify if state actually changed
      if (this.hasStateChanged(newState)) {
        this.currentState = newState;
        this.notifyListeners(newState);
      }
    });
  }

  private hasStateChanged(newState: NetworkState): boolean {
    return (
      this.currentState.isConnected !== newState.isConnected ||
      this.currentState.isInternetReachable !== newState.isInternetReachable ||
      this.currentState.type !== newState.type
    );
  }

  private notifyListeners(state: NetworkState) {
    this.listeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('Error in network listener:', error);
      }
    });
  }

  /**
   * Get current network state
   */
  async getCurrentState(): Promise<NetworkState> {
    const netInfo = await NetInfo.fetch();
    return {
      isConnected: netInfo.isConnected ?? false,
      isInternetReachable: netInfo.isInternetReachable ?? false,
      type: netInfo.type || 'unknown'
    };
  }

  /**
   * Check if device is online (connected and internet reachable)
   */
  async isOnline(): Promise<boolean> {
    const state = await this.getCurrentState();
    return state.isConnected && state.isInternetReachable;
  }

  /**
   * Add network state listener
   */
  addListener(callback: (state: NetworkState) => void): NetworkListener {
    this.listeners.add(callback);
    
    // Immediately call with current state
    callback(this.currentState);

    return {
      unsubscribe: () => {
        this.listeners.delete(callback);
      }
    };
  }

  /**
   * Remove all listeners
   */
  removeAllListeners() {
    this.listeners.clear();
  }

  /**
   * Wait for network to come online
   */
  async waitForConnection(timeoutMs: number = 30000): Promise<boolean> {
    if (await this.isOnline()) {
      return true;
    }

    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        listener.unsubscribe();
        resolve(false);
      }, timeoutMs);

      const listener = this.addListener((state) => {
        if (state.isConnected && state.isInternetReachable) {
          clearTimeout(timeout);
          listener.unsubscribe();
          resolve(true);
        }
      });
    });
  }
}

export const networkService = new NetworkService();
export default networkService;
