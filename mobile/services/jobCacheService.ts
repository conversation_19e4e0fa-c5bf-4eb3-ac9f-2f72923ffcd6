import AsyncStorage from '@react-native-async-storage/async-storage';
import { Job } from '../services/jobService';
import { LocationData } from '../services/locationService';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface JobCacheKey {
  filters: any;
  location?: LocationData | null;
}

class JobCacheService {
  private readonly CACHE_PREFIX = '@ozgaar_job_cache_';
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly BACKGROUND_REFRESH_TTL = 30 * 1000; // 30 seconds for background refresh
  private memoryCache = new Map<string, CacheEntry<Job[]>>();
  private readonly MAX_MEMORY_CACHE_SIZE = 10;

  /**
   * Generate cache key from filters and location
   */
  private generateCacheKey(filters: any, location?: LocationData | null): string {
    const keyData: JobCacheKey = { filters };
    if (location) {
      keyData.location = location;
    }
    return JSON.stringify(keyData);
  }

  /**
   * Check if cache entry is expired
   */
  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * Check if cache entry needs background refresh (older than 30 seconds)
   */
  private needsBackgroundRefresh(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp > this.BACKGROUND_REFRESH_TTL;
  }

  /**
   * Get jobs from cache
   */
  async getCachedJobs(
    filters: any,
    location?: LocationData | null
  ): Promise<{ jobs: Job[] | null; needsRefresh: boolean }> {
    const cacheKey = this.generateCacheKey(filters, location);
    
    // Check memory cache first
    const memoryEntry = this.memoryCache.get(cacheKey);
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      return {
        jobs: memoryEntry.data,
        needsRefresh: this.needsBackgroundRefresh(memoryEntry)
      };
    }

    // Check persistent cache
    try {
      const cachedData = await AsyncStorage.getItem(this.CACHE_PREFIX + cacheKey);
      if (cachedData) {
        const entry: CacheEntry<Job[]> = JSON.parse(cachedData);
        if (!this.isExpired(entry)) {
          // Promote to memory cache
          this.setMemoryCache(cacheKey, entry);
          return {
            jobs: entry.data,
            needsRefresh: this.needsBackgroundRefresh(entry)
          };
        }
      }
    } catch (error) {
      console.error('Error reading job cache:', error);
    }

    return { jobs: null, needsRefresh: true };
  }

  /**
   * Cache jobs
   */
  async cacheJobs(
    jobs: Job[],
    filters: any,
    location?: LocationData | null,
    ttl: number = this.DEFAULT_TTL
  ): Promise<void> {
    const cacheKey = this.generateCacheKey(filters, location);
    const entry: CacheEntry<Job[]> = {
      data: jobs,
      timestamp: Date.now(),
      ttl
    };

    // Set in memory cache
    this.setMemoryCache(cacheKey, entry);

    // Set in persistent cache
    try {
      await AsyncStorage.setItem(
        this.CACHE_PREFIX + cacheKey, 
        JSON.stringify(entry)
      );
    } catch (error) {
      console.error('Error caching jobs:', error);
    }
  }

  /**
   * Set entry in memory cache with LRU eviction
   */
  private setMemoryCache(key: string, entry: CacheEntry<Job[]>): void {
    // Remove if already exists to update position
    if (this.memoryCache.has(key)) {
      this.memoryCache.delete(key);
    }

    // Evict oldest entry if cache is full
    if (this.memoryCache.size >= this.MAX_MEMORY_CACHE_SIZE) {
      const oldestKey = this.memoryCache.keys().next().value;
      if (oldestKey) {
        this.memoryCache.delete(oldestKey);
      }
    }

    this.memoryCache.set(key, entry);
  }

  /**
   * Check if there are newer jobs available (for background refresh)
   */
  async hasNewerJobs(
    newJobs: Job[],
    filters: any,
    location?: LocationData | null
  ): Promise<boolean> {
    const cached = await this.getCachedJobs(filters, location);
    if (!cached.jobs || cached.jobs.length === 0) {
      return newJobs.length > 0;
    }

    // Check if there are new jobs by comparing the most recent job timestamps
    const cachedNewestTimestamp = Math.max(
      ...cached.jobs.map(job => new Date(job.created_at).getTime())
    );
    
    const newNewestTimestamp = Math.max(
      ...newJobs.map(job => new Date(job.created_at).getTime())
    );

    return newNewestTimestamp > cachedNewestTimestamp;
  }

  /**
   * Clear all cached jobs
   */
  async clearCache(): Promise<void> {
    this.memoryCache.clear();
    
    try {
      const keys = await AsyncStorage.getAllKeys();
      const jobCacheKeys = keys.filter(key => key.startsWith(this.CACHE_PREFIX));
      await AsyncStorage.multiRemove(jobCacheKeys);
    } catch (error) {
      console.error('Error clearing job cache:', error);
    }
  }

  /**
   * Clear expired cache entries
   */
  async clearExpiredCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const jobCacheKeys = keys.filter(key => key.startsWith(this.CACHE_PREFIX));
      
      for (const key of jobCacheKeys) {
        const cachedData = await AsyncStorage.getItem(key);
        if (cachedData) {
          const entry: CacheEntry<Job[]> = JSON.parse(cachedData);
          if (this.isExpired(entry)) {
            await AsyncStorage.removeItem(key);
          }
        }
      }
    } catch (error) {
      console.error('Error clearing expired cache:', error);
    }
  }
}

export const jobCacheService = new JobCacheService();
export default jobCacheService;
