import { useState, useCallback, useRef, useEffect } from 'react'

interface ValidationRule<T> {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: T[keyof T], formData: T) => string | null
}

type ValidationRules<T> = {
  [K in keyof T]?: ValidationRule<T>
}

interface FormState<T> {
  data: T
  errors: Partial<Record<keyof T, string>>
  isDirty: boolean
  touchedFields: Set<keyof T>
}

export const useDynamicForm = <T extends Record<string, any>>(
  initialData: T,
  validationRules?: ValidationRules<T>
) => {
  const initialDataRef = useRef<T>(initialData)
  const [formState, setFormState] = useState<FormState<T>>({
    data: initialData,
    errors: {},
    isDirty: false,
    touchedFields: new Set()
  })

  // Update initial data reference when it changes (only if form is not dirty)
  useEffect(() => {
    const hasChanged = JSON.stringify(initialDataRef.current) !== JSON.stringify(initialData)

    if (hasChanged && !formState.isDirty) {
      initialDataRef.current = initialData
      setFormState(prev => ({
        ...prev,
        data: initialData,
        isDirty: false,
        touchedFields: new Set()
      }))
    } else if (hasChanged) {
      // Just update the reference without changing form state if user has made changes
      initialDataRef.current = initialData
    }
  }, [initialData, formState.isDirty])

  const validateField = useCallback((
    fieldName: keyof T, 
    value: T[keyof T], 
    currentData: T
  ): string | null => {
    const rules = validationRules?.[fieldName]
    if (!rules) return null

    // Required validation
    if (rules.required && (!value || (typeof value === 'string' && !value.trim()))) {
      return `${String(fieldName)} is required`
    }

    // String-specific validations
    if (typeof value === 'string') {
      if (rules.minLength && value.length < rules.minLength) {
        return `${String(fieldName)} must be at least ${rules.minLength} characters`
      }
      
      if (rules.maxLength && value.length > rules.maxLength) {
        return `${String(fieldName)} must be no more than ${rules.maxLength} characters`
      }
      
      if (rules.pattern && !rules.pattern.test(value)) {
        return `${String(fieldName)} format is invalid`
      }
    }

    // Custom validation
    if (rules.custom) {
      return rules.custom(value, currentData)
    }

    return null
  }, [validationRules])

  const updateField = useCallback((fieldName: keyof T, value: T[keyof T]) => {
    setFormState(prev => {
      const newData = { ...prev.data, [fieldName]: value }
      const newTouchedFields = new Set(prev.touchedFields).add(fieldName)
      
      // Validate the field
      const fieldError = validateField(fieldName, value, newData)
      const newErrors = { ...prev.errors }
      
      if (fieldError) {
        newErrors[fieldName] = fieldError
      } else {
        delete newErrors[fieldName]
      }

      // Check if form is dirty
      const isDirty = Object.keys(newData).some(key => 
        newData[key as keyof T] !== initialDataRef.current[key as keyof T]
      )

      return {
        data: newData,
        errors: newErrors,
        isDirty,
        touchedFields: newTouchedFields
      }
    })
  }, [validateField])

  const validateForm = useCallback((): boolean => {
    const newErrors: Partial<Record<keyof T, string>> = {}
    let isValid = true

    Object.keys(formState.data).forEach(key => {
      const fieldName = key as keyof T
      const error = validateField(fieldName, formState.data[fieldName], formState.data)
      if (error) {
        newErrors[fieldName] = error
        isValid = false
      }
    })

    setFormState(prev => ({
      ...prev,
      errors: newErrors,
      touchedFields: new Set(Object.keys(formState.data) as (keyof T)[])
    }))

    return isValid
  }, [formState.data, validateField])

  const resetForm = useCallback(() => {
    setFormState({
      data: initialDataRef.current,
      errors: {},
      isDirty: false,
      touchedFields: new Set()
    })
  }, [])

  const setFieldError = useCallback((fieldName: keyof T, error: string | null) => {
    setFormState(prev => {
      const newErrors = { ...prev.errors }
      if (error) {
        newErrors[fieldName] = error
      } else {
        delete newErrors[fieldName]
      }
      return { ...prev, errors: newErrors }
    })
  }, [])

  return {
    formData: formState.data,
    errors: formState.errors,
    isDirty: formState.isDirty,
    touchedFields: formState.touchedFields,
    updateField,
    validateForm,
    resetForm,
    setFieldError,
    isFieldTouched: (fieldName: keyof T) => formState.touchedFields.has(fieldName),
    hasErrors: Object.keys(formState.errors).length > 0
  }
}

// Common validation rules
export const commonValidationRules = {
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    custom: (value: any) => {
      if (value && typeof value === 'string' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        return 'Please enter a valid email address'
      }
      return null
    }
  },

  phone: {
    pattern: /^\+?[\d\s-()]+$/,
    minLength: 10,
    custom: (value: any) => {
      if (value && typeof value === 'string' && value.replace(/\D/g, '').length < 10) {
        return 'Please enter a valid phone number'
      }
      return null
    }
  },

  rate: {
    custom: (value: any) => {
      if (value && typeof value === 'string') {
        const numValue = parseFloat(value)
        if (isNaN(numValue) || numValue < 0) {
          return 'Please enter a valid rate'
        }
        if (numValue > 10000) {
          return 'Rate seems unusually high. Please verify.'
        }
      }
      return null
    }
  },

  experience: {
    custom: (value: any) => {
      if (value && typeof value === 'string') {
        const numValue = parseInt(value)
        if (isNaN(numValue) || numValue < 0) {
          return 'Please enter valid years of experience'
        }
        if (numValue > 50) {
          return 'Years of experience seems unusually high'
        }
      }
      return null
    }
  },

  bio: {
    maxLength: 500,
    custom: (value: any) => {
      if (value && typeof value === 'string' && value.trim().length > 500) {
        return 'Bio must be no more than 500 characters'
      }
      return null
    }
  }
}
