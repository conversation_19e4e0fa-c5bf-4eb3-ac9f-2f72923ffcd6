import { useEffect, useRef, useCallback } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'

interface AutoSaveOptions {
  key: string
  data: any
  enabled?: boolean
  interval?: number // in milliseconds
  onSave?: (data: any) => void
  onRestore?: (data: any) => void
}

export const useAutoSave = ({
  key,
  data,
  enabled = true,
  interval = 30000, // 30 seconds
  onSave,
  onRestore
}: AutoSaveOptions) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastSavedDataRef = useRef<string>('')

  // Save data to AsyncStorage
  const saveData = useCallback(async (dataToSave: any) => {
    try {
      const serializedData = JSON.stringify(dataToSave)
      
      // Only save if data has changed
      if (serializedData !== lastSavedDataRef.current) {
        await AsyncStorage.setItem(`autosave_${key}`, serializedData)
        lastSavedDataRef.current = serializedData
        onSave?.(dataToSave)
      }
    } catch (error) {
      console.error('Auto-save error:', error)
    }
  }, [key, onSave])

  // Restore data from AsyncStorage
  const restoreData = useCallback(async () => {
    try {
      const savedData = await AsyncStorage.getItem(`autosave_${key}`)
      if (savedData) {
        const parsedData = JSON.parse(savedData)
        onRestore?.(parsedData)
        return parsedData
      }
    } catch (error) {
      console.error('Auto-restore error:', error)
    }
    return null
  }, [key, onRestore])

  // Clear saved data
  const clearSavedData = useCallback(async () => {
    try {
      await AsyncStorage.removeItem(`autosave_${key}`)
      lastSavedDataRef.current = ''
    } catch (error) {
      console.error('Clear auto-save error:', error)
    }
  }, [key])

  // Check if there's saved data available
  const hasSavedData = useCallback(async () => {
    try {
      const savedData = await AsyncStorage.getItem(`autosave_${key}`)
      return !!savedData
    } catch (error) {
      console.error('Check saved data error:', error)
      return false
    }
  }, [key])

  // Set up auto-save interval
  useEffect(() => {
    if (!enabled) return

    // Clear existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    // Set up new interval
    intervalRef.current = setInterval(() => {
      if (data) {
        saveData(data)
      }
    }, interval)

    // Cleanup on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [enabled, interval, saveData]) // Removed data from dependencies

  // Save immediately when data changes (debounced)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastDataRef = useRef<string>('')

  useEffect(() => {
    if (!enabled || !data) return

    // Only save if data has actually changed
    const currentDataString = JSON.stringify(data)
    if (currentDataString === lastDataRef.current) return

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Set new timeout for immediate save (debounced)
    timeoutRef.current = setTimeout(() => {
      saveData(data)
      lastDataRef.current = currentDataString
    }, 2000) // 2 second debounce

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [data, enabled, saveData])

  return {
    saveData,
    restoreData,
    clearSavedData,
    hasSavedData
  }
}
