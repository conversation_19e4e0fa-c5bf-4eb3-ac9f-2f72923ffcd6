import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { useCallback } from 'react';
import {
  setViewMode as setViewModeAction,
  setSelectedPersona as setSelectedPersonaAction,
  setNotificationPreferences as setNotificationPreferencesAction,
  setPrivacyPreferences as setPrivacyPreferencesAction,
  setUIPreferences as setUIPreferencesAction,
  setPreferences as setPreferencesAction,
  resetUserState as resetUserStateAction,
} from '../store/userSlice';

export const useUserState = () => {
  const dispatch = useAppDispatch();
  const userState = useAppSelector(state => state.user);

  const setViewMode = useCallback((viewMode: 'worker' | 'poster' | 'both' | null) => {
    dispatch(setViewModeAction(viewMode));
  }, [dispatch]);

  const setSelectedPersona = useCallback((personaId: string | null) => {
    dispatch(setSelectedPersonaAction(personaId));
  }, [dispatch]);

  const setNotificationPreferences = useCallback((preferences: Partial<typeof userState.preferences.notifications>) => {
    dispatch(setNotificationPreferencesAction(preferences));
  }, [dispatch]);

  const setPrivacyPreferences = useCallback((preferences: Partial<typeof userState.preferences.privacy>) => {
    dispatch(setPrivacyPreferencesAction(preferences));
  }, [dispatch]);

  const setUIPreferences = useCallback((preferences: Partial<typeof userState.preferences.ui>) => {
    dispatch(setUIPreferencesAction(preferences));
  }, [dispatch]);

  const setPreferences = useCallback((preferences: Partial<typeof userState.preferences>) => {
    dispatch(setPreferencesAction(preferences));
  }, [dispatch]);

  const resetUserState = useCallback(() => {
    dispatch(resetUserStateAction());
  }, [dispatch]);

  return {
    ...userState,
    setViewMode,
    setSelectedPersona,
    setNotificationPreferences,
    setPrivacyPreferences,
    setUIPreferences,
    setPreferences,
    resetUserState,
  };
};