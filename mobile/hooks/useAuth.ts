import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { useCallback } from 'react';
import { authHelpers } from '../lib/supabase';
import { setSession, setUser, signOut as signOutAction } from '../store/authSlice';
import type { User } from '@ozgaar/types';

// Session type for our API
interface Session {
  user: User | null;
}

export const useAuth = () => {
  const dispatch = useAppDispatch();
  const authState = useAppSelector(state => state.auth);
  const { session, user, loading } = authState;

  // Fetch user profile from API
  const fetchUserProfile = useCallback(async () => {
    try {
      const { data, error } = await authHelpers.getUserProfile();

      if (error) {
        console.error('Error fetching user profile:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  }, []);

  // Refresh user data
  const refreshUser = useCallback(async () => {
    const userProfile = await fetchUserProfile();
    if (userProfile) {
      dispatch(setUser(userProfile));
    }
  }, [dispatch, fetchUserProfile]);

  // Sign out
  const signOut = useCallback(async () => {
    try {
      await authHelpers.signOut();
      console.log('Dispatching signOut action');
      dispatch(signOutAction(undefined));
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }, [dispatch]);

  return {
    session: session as Session | null,
    user,
    loading,
    signOut,
    refreshUser,
  };
};