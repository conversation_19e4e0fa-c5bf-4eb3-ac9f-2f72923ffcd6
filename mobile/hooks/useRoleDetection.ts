import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { apiClient } from '@/lib/supabase';

export type UserRole = 'worker' | 'poster' | 'both' | 'unknown';

interface RoleDetectionResult {
  detectedRole: UserRole;
  confidence: 'high' | 'medium' | 'low';
  activitySummary: {
    jobApplications: number;
    jobPostings: number;
    workerProfileCompleteness: number;
    posterProfileCompleteness: number;
  };
}

export const useRoleDetection = () => {
  const { user } = useAuth();
  const [detectedRole, setDetectedRole] = useState<UserRole>('unknown');
  const [confidence, setConfidence] = useState<'high' | 'medium' | 'low'>('low');
  const [isDetecting, setIsDetecting] = useState(true);
  const [activitySummary, setActivitySummary] = useState({
    jobApplications: 0,
    jobPostings: 0,
    workerProfileCompleteness: 0,
    posterProfileCompleteness: 0
  });

  useEffect(() => {
    if (!user?.id) {
      setIsDetecting(false);
      return;
    }

    detectUserRole();
  }, [user?.id]);

  const detectUserRole = async () => {
    try {
      setIsDetecting(true);

      // Fetch user activity data
      const response = await apiClient.get<RoleDetectionResult>(`/auth/role-detection/${user?.id}`);

      if (response.success && response.data) {
        const { detectedRole: role, confidence: conf, activitySummary: summary } = response.data;
        setDetectedRole(role);
        setConfidence(conf);
        setActivitySummary(summary);
      } else {
        // Fallback: Basic role detection based on user profile
        await fallbackRoleDetection();
      }
    } catch (error) {
      console.error('Role detection error:', error);
      // Fallback to basic detection
      await fallbackRoleDetection();
    } finally {
      setIsDetecting(false);
    }
  };

  const fallbackRoleDetection = async () => {
    try {
      // Get current user profile
      const userResponse = await apiClient.get('/auth/profile');
      
      if (userResponse.success && userResponse.data) {
        const userData = userResponse.data as any;
        
        // Simple heuristic based on profile fields
        const hasWorkerFields = !!(
          userData.primary_skill_category || 
          userData.hourly_rate || 
          userData.daily_rate ||
          userData.years_of_experience
        );
        
        const hasPosterFields = !!(
          userData.company_name || 
          userData.business_type ||
          userData.poster_bio
        );

        if (hasWorkerFields && hasPosterFields) {
          setDetectedRole('both');
          setConfidence('medium');
        } else if (hasWorkerFields) {
          setDetectedRole('worker');
          setConfidence('medium');
        } else if (hasPosterFields) {
          setDetectedRole('poster');
          setConfidence('medium');
        } else {
          // Default to worker if no clear indication
          setDetectedRole(user?.user_type as UserRole || 'worker');
          setConfidence('low');
        }

        // Set basic activity summary
        setActivitySummary({
          jobApplications: 0,
          jobPostings: 0,
          workerProfileCompleteness: hasWorkerFields ? 70 : 30,
          posterProfileCompleteness: hasPosterFields ? 70 : 30
        });
      }
    } catch (error) {
      console.error('Fallback role detection error:', error);
      // Ultimate fallback
      setDetectedRole(user?.user_type as UserRole || 'worker');
      setConfidence('low');
    }
  };

  return {
    detectedRole,
    confidence,
    isDetecting,
    activitySummary,
    refetchRole: detectUserRole
  };
};