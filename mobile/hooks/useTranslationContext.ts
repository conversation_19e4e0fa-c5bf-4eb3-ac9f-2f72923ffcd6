import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { useCallback } from 'react';
import * as RNLocalize from 'react-native-localize';
import { 
  changeLanguage as changeLanguageUtil,
  LANGUAGE_METADATA,
  SUPPORTED_LANGUAGES,
  detectRegionalLanguage as detectRegionalLanguageUtil
} from '../lib/i18n';
import { 
  languageSyncService
} from '../lib/languageSync';
import { setCurrentLanguage, setIsLoading } from '../store/translationSlice';
import type { LanguageMetadata } from '../lib/i18n';
import { useTranslation } from 'react-i18next';

export const useTranslationContext = () => {
  const { t: translate, i18n } = useTranslation();
  const dispatch = useAppDispatch();
  const translationState = useAppSelector(state => state.translation);
  const { currentLanguage, availableLanguages, isLoading } = translationState;

  // Detect device language
  const detectDeviceLanguage = useCallback((): string => {
    const locales = RNLocalize.getLocales();
    
    if (locales.length > 0) {
      const deviceLanguage = locales[0].languageCode;
      
      // Check if device language is supported
      if (SUPPORTED_LANGUAGES.includes(deviceLanguage)) {
        return deviceLanguage;
      }
      
      // Check for language variants (e.g., 'en-US' -> 'en')
      const baseLanguage = deviceLanguage.split('-')[0];
      if (SUPPORTED_LANGUAGES.includes(baseLanguage)) {
        return baseLanguage;
      }
    }
    
    return 'en'; // Default fallback
  }, []);

  // Change language function with sync
  const changeLanguage = useCallback(async (languageCode: string): Promise<void> => {
    if (!SUPPORTED_LANGUAGES.includes(languageCode)) {
      console.warn(`Unsupported language: ${languageCode}`);
      return;
    }

    try {
      dispatch(setIsLoading(true));
      await changeLanguageUtil(languageCode);
      dispatch(setCurrentLanguage(languageCode));

      // Save locally and attempt to sync to server
      await languageSyncService.saveLanguageWithSync(languageCode);

      console.log(`Language changed to: ${languageCode}`);
    } catch (error) {
      console.error('Failed to change language:', error);
    } finally {
      dispatch(setIsLoading(false));
    }
  }, [dispatch]);

  // Get language metadata
  const getLanguageMetadata = useCallback((code: string): LanguageMetadata | undefined => {
    return LANGUAGE_METADATA[code as keyof typeof LANGUAGE_METADATA];
  }, []);

  // Check if language is supported
  const isLanguageSupported = useCallback((code: string): boolean => {
    return SUPPORTED_LANGUAGES.includes(code);
  }, []);

  // Regional language detection
  const detectRegionalLanguage = useCallback((phone: string): string => {
    return detectRegionalLanguageUtil(phone);
  }, []);

  // Translation function
  const t = useCallback((key: string, options?: any): string => {
    return translate(key, options).toString();
  }, [translate]);

  return {
    currentLanguage,
    availableLanguages,
    isLoading,
    changeLanguage,
    detectDeviceLanguage,
    detectRegionalLanguage,
    t,
    getLanguageMetadata,
    isLanguageSupported,
  };
};