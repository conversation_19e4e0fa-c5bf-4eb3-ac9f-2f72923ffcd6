import React, { useEffect } from 'react';
import { useAppDispatch } from '../hooks/redux';
import { setSession, setUser, setLoading} from '../store/authSlice';
import { 
  setCurrentLanguage, 
  setAvailableLanguages, 
  setIsLoading as setTranslationLoading 
} from '../store/translationSlice';
import { authHelpers } from '../lib/supabase';
import { 
  changeLanguage,
  LANGUAGE_METADATA,
  SUPPORTED_LANGUAGES,
} from '../lib/i18n';
import { 
  languageSyncService,
  setupNetworkListener,
  removeNetworkListener
} from '../lib/languageSync';
import * as RNLocalize from 'react-native-localize';
import type { LanguageMetadata } from '../lib/i18n';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from '@/store';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

interface TranslationProviderProps {
  children: React.ReactNode;
}

export const TranslationProvider: React.FC<TranslationProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();

  // Get available languages with metadata
  const languageMetadata: LanguageMetadata[] = SUPPORTED_LANGUAGES.map(code => 
    LANGUAGE_METADATA[code as keyof typeof LANGUAGE_METADATA]
  );

  // Detect device language
  const detectDeviceLanguage = (): string => {
    const locales = RNLocalize.getLocales();
    
    if (locales.length > 0) {
      const deviceLanguage = locales[0].languageCode;
      
      // Check if device language is supported
      if (SUPPORTED_LANGUAGES.includes(deviceLanguage)) {
        return deviceLanguage;
      }
      
      // Check for language variants (e.g., 'en-US' -> 'en')
      const baseLanguage = deviceLanguage.split('-')[0];
      if (SUPPORTED_LANGUAGES.includes(baseLanguage)) {
        return baseLanguage;
      }
    }
    
    return 'en'; // Default fallback
  };

  // Initialize auth on mount
  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        dispatch(setLoading(true));
        const { session: initialSession } = await authHelpers.getSession();
        dispatch(setSession(initialSession));

        if (initialSession?.user) {
          dispatch(setUser(initialSession.user));
        }
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        dispatch(setLoading(false));
      }
    };

    getInitialSession();
  }, [dispatch]);

  // Initialize translation on mount
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        dispatch(setTranslationLoading(true));
        dispatch(setAvailableLanguages(languageMetadata));
        
        // Try to get saved language from AsyncStorage
        let savedLanguage = await languageSyncService.getLanguageFromStorage();

        // If no saved language, detect device language
        if (!savedLanguage) {
          savedLanguage = detectDeviceLanguage();
          await languageSyncService.saveLanguageLocally(savedLanguage);
        }

        // Ensure the language is supported
        if (!SUPPORTED_LANGUAGES.includes(savedLanguage)) {
          savedLanguage = 'en';
          await languageSyncService.saveLanguageLocally(savedLanguage);
        }

        // Set the language in i18n
        await changeLanguage(savedLanguage);
        dispatch(setCurrentLanguage(savedLanguage));
        
        console.log(`Initialized language: ${savedLanguage}`);

        // Setup network listener for automatic sync
        setupNetworkListener();

        // Check for any pending language sync
        await languageSyncService.checkAndSyncPendingLanguage();

        // Initialize complete
        dispatch(setTranslationLoading(false));
      } catch (error) {
        console.error('Failed to initialize language:', error);
        // Fallback to English
        await changeLanguage('en');
        dispatch(setCurrentLanguage('en'));
        dispatch(setTranslationLoading(false));
      }
    };

    initializeLanguage();

    // Cleanup network listener on unmount
    return () => {
      removeNetworkListener();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  return <>{children}</>;
};

interface ProvidersProps {
  children: React.ReactNode;
}

export const Providers : React.FC<ProvidersProps> = ({ children }) => {

  const queryClient = new QueryClient();
  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <TranslationProvider>
          <PersistGate loading={null} persistor={persistor}>
            {children}
          </PersistGate>
        </TranslationProvider>
      </QueryClientProvider>
    </Provider>
  );
};