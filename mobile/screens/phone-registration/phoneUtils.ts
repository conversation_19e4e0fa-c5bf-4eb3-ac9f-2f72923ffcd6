import { validatePhoneNumber, formatPhoneForDisplay } from '../../lib/supabase';
import { ValidationResult } from './types';

export const formatPhoneNumber = (text: string): string => {
  // Remove all non-digit characters
  const cleaned = text.replace(/\D/g, '');
  
  // Limit to 10 digits
  if (cleaned.length <= 10) {
    // Format as 12345-67890
    if (cleaned.length > 5) {
      const formatted = `${cleaned.substring(0, 5)}-${cleaned.substring(5)}`;
      return formatted;
    } else {
      return cleaned;
    }
  }
  // Return the original text if more than 10 digits
  return text;
};

export const handlePhoneValidation = (phoneNumber: string): ValidationResult => {
  return validatePhoneNumber(phoneNumber);
};

export { formatPhoneForDisplay };