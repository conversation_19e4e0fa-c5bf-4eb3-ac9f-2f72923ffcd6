import React from 'react';
import { View, StyleSheet, Text } from 'react-native';

interface HeaderProps {
  isOtpSent: boolean;
  phoneNumber: string;
  formatPhoneForDisplay: (phone: string) => string;
}

export const Header: React.FC<HeaderProps> = ({ isOtpSent, phoneNumber, formatPhoneForDisplay }) => {
  return (
    <View style={styles.header}>
      <Text style={styles.title}>
        {isOtpSent ? 'Verify Phone Number' : 'Enter Phone Number'}
      </Text>
      <Text style={styles.subtitle}>
        {isOtpSent 
          ? `Enter the 6-digit code sent to ${formatPhoneForDisplay(phoneNumber)}`
          : 'We\'ll send you a verification code via SMS'
        }
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    marginTop: 40,
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
});