import { router } from 'expo-router';
import { authHelpers } from '@/lib/supabase';
import { SendOTPResult, VerifyOTPResult, ValidationResult } from '@/screens/phone-registration/types';

export const sendOTP = async (validation: ValidationResult): Promise<SendOTPResult> => {
  return await authHelpers.sendOTP(validation.formatted!);
};

export const verifyOTP = async (validation: ValidationResult, otp: string): Promise<VerifyOTPResult> => {
  const result = await authHelpers.verifyOTP(validation.formatted!, otp);
  return result;
};

export const handleVerificationSuccess = (result: VerifyOTPResult) => {
  // Success - navigate to user type selection
    // Success - check if user needs profile completion
  if (result.data?.user) {
    if (result.data.user.profile_completed === false) {
      // User needs to complete profile
      router.push('/(auth)/user-type-selection');
    } else {
      // User already has complete profile
      router.replace('/(tabs)');
    }
  } else {
    // Fallback - navigate to profile completion
    router.push('/(auth)/user-type-selection');
  }
};