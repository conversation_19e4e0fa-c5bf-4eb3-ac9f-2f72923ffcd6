import React, { useRef } from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity, Text} from 'react-native';

interface OTPInputProps {
  otp: string;
  setOtp: (otp: string) => void;
  handleResendOtp: () => void;
  countdown: number;
  remainingAttempts: number;
}

export const OTPInput: React.FC<OTPInputProps> = ({ 
  otp, 
  setOtp, 
  handleResendOtp, 
  countdown, 
  remainingAttempts 
}) => {
  const otpInputRef = useRef<TextInput>(null);

  return (
    <View style={styles.otpContainer}>
      <TextInput
        ref={otpInputRef}
        style={styles.otpInput}
        value={otp}
        onChangeText={setOtp}
        placeholder="000000"
        keyboardType="number-pad"
        maxLength={6}
        textAlign="center"
        autoFocus
      />
      
      <View style={styles.resendContainer}>
        <Text style={styles.attemptsText}>
          {remainingAttempts} attempts remaining
        </Text>
        
        {countdown > 0 ? (
          <Text style={styles.countdownText}>
            Resend in {countdown}s
          </Text>
        ) : (
          <TouchableOpacity onPress={handleResendOtp}>
            <Text style={styles.resendText}>Resend Code</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  otpContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  otpInput: {
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderRadius: 12,
    fontSize: 24,
    fontWeight: 'bold',
    letterSpacing: 4,
    width: '100%',
    marginBottom: 16,
  },
  resendContainer: {
    alignItems: 'center',
  },
  attemptsText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  countdownText: {
    fontSize: 14,
    color: '#666',
  },
  resendText: {
    fontSize: 14,
    color: '#3b82f6',
    fontWeight: '600',
  },
});