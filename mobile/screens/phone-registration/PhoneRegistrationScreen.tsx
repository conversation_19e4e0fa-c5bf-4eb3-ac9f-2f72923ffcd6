import React, { useState, useRef } from 'react';
import {
  View,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Header } from './Header';
import { PhoneInput } from './PhoneInput';
import { OTPInput } from './OTPInput';
import { SubmitButton } from './SubmitButton';
import { Footer } from './Footer';
import { styles } from './styles';
import { formatPhoneNumber, handlePhoneValidation, formatPhoneForDisplay } from './phoneUtils';
import { sendOTP, verifyOTP, handleVerificationSuccess } from './authService';
import { SendOTPResult, VerifyOTPResult } from './types';

export default function PhoneRegistrationScreen() {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [remainingAttempts, setRemainingAttempts] = useState(3);
  const [countdown, setCountdown] = useState(0);
  
  const otpInputRef = useRef<TextInput>(null);

  // Format phone number as user types
  const handlePhoneChange = (text: string) => {
    const formatted = formatPhoneNumber(text);
    setPhoneNumber(formatted);
  };

  // Send OTP
  const handleSendOtp = async () => {
    const validation = handlePhoneValidation(phoneNumber);
    
    if (!validation.isValid) {
      Alert.alert('Invalid Phone Number', validation.error || 'Please enter a valid phone number');
      return;
    }

    setIsLoading(true);
    
    try {
      const result: SendOTPResult = await sendOTP(validation);
      
      if (result.error) {
        Alert.alert(
          'OTP Send Failed', 
          result.error.message || 'Failed to send OTP. Please try again.'
        );
        return;
      }

      setIsOtpSent(true);
      setRemainingAttempts(result.data?.remaining_attempts || 3);
      
      // Start countdown timer
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      // Focus OTP input
      setTimeout(() => {
        otpInputRef.current?.focus();
      }, 100);

      Alert.alert(
        'OTP Sent', 
        `Verification code sent to ${formatPhoneForDisplay(validation.formatted!)}${
          result.backup_used ? ' via SMS backup service' : ''
        }`
      );
      
    } catch (error) {
      console.error('Send OTP error:', error);
      Alert.alert('Error', 'Failed to send OTP. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Verify OTP
  const handleVerifyOtp = async () => {
    if (otp.length !== 6) {
      Alert.alert('Invalid OTP', 'Please enter the 6-digit verification code');
      return;
    }

    const validation = handlePhoneValidation(phoneNumber);
    if (!validation.isValid) {
      Alert.alert('Error', 'Invalid phone number');
      return;
    }

    setIsLoading(true);

    try {
      const result: VerifyOTPResult = await verifyOTP(validation, otp);
      
      if (result.error) {
        Alert.alert('Verification Failed', result.error.message || 'Invalid OTP. Please try again.');
        setRemainingAttempts(prev => Math.max(0, prev - 1));
        
        if (remainingAttempts <= 1) {
          Alert.alert(
            'Too Many Attempts', 
            'You have exceeded the maximum number of attempts. Please try again later.',
            [{ text: 'OK', onPress: () => router.back() }]
          );
        }
        return;
      }

      handleVerificationSuccess(result);
      
    } catch (error) {
      console.error('Verify OTP error:', error);
      Alert.alert('Error', 'Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Resend OTP
  const handleResendOtp = () => {
    if (countdown > 0) return;
    
    setOtp('');
    setIsOtpSent(false);
    handleSendOtp();
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Header 
            isOtpSent={isOtpSent} 
            phoneNumber={phoneNumber} 
            formatPhoneForDisplay={formatPhoneForDisplay} 
          />

          <View style={styles.form}>
            {!isOtpSent ? (
              <PhoneInput 
                phoneNumber={phoneNumber} 
                handlePhoneChange={handlePhoneChange} 
              />
            ) : (
              <OTPInput 
                otp={otp}
                setOtp={setOtp}
                handleResendOtp={handleResendOtp}
                countdown={countdown}
                remainingAttempts={remainingAttempts}
              />
            )}

            <SubmitButton
              isLoading={isLoading}
              isOtpSent={isOtpSent}
              phoneNumber={phoneNumber}
              otp={otp}
              handleSendOtp={handleSendOtp}
              handleVerifyOtp={handleVerifyOtp}
            />
          </View>

          <Footer />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}