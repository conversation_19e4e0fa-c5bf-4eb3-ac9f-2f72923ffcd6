import React, { useRef } from 'react';
import { View, TextInput, StyleSheet, Text } from 'react-native';

interface PhoneInputProps {
  phoneNumber: string;
  handlePhoneChange: (text: string) => void;
}

export const PhoneInput: React.FC<PhoneInputProps> = ({ phoneNumber, handlePhoneChange }) => {
  const phoneInputRef = useRef<TextInput>(null);

  return (
    <View style={styles.phoneInputContainer}>
      <View style={styles.countryCode}>
        <Text style={styles.countryCodeText}>🇮🇳 +91</Text>
      </View>
      <TextInput
        ref={phoneInputRef}
        style={styles.phoneInput}
        value={phoneNumber}
        onChangeText={handlePhoneChange}
        placeholder="98765-43210"
        keyboardType="number-pad"
        maxLength={11} // 5 + 1 (dash) + 5
        autoFocus
      />
    </View>
  );
};

const styles = StyleSheet.create({
  phoneInputContainer: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  countryCode: {
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderRadius: 12,
    marginRight: 12,
    justifyContent: 'center',
  },
  countryCodeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  phoneInput: {
    flex: 1,
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderRadius: 12,
    fontSize: 18,
    fontWeight: '500',
    letterSpacing: 1,
  },
});