import React from 'react';
import { TouchableOpacity, StyleSheet, ActivityIndicator, Text } from 'react-native';

interface SubmitButtonProps {
  isLoading: boolean;
  isOtpSent: boolean;
  phoneNumber: string;
  otp: string;
  handleSendOtp: () => void;
  handleVerifyOtp: () => void;
}

export const SubmitButton: React.FC<SubmitButtonProps> = ({ 
  isLoading, 
  isOtpSent, 
  phoneNumber, 
  otp, 
  handleSendOtp, 
  handleVerifyOtp 
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.button,
        (isLoading || (isOtpSent && otp.length !== 6) || (!isOtpSent && phoneNumber.length < 11)) && styles.buttonDisabled
      ]}
      onPress={isOtpSent ? handleVerifyOtp : handleSendOtp}
      disabled={isLoading || (isOtpSent && otp.length !== 6) || (!isOtpSent && phoneNumber.length < 11)}
    >
      {isLoading ? (
        <ActivityIndicator color="#fff" />
      ) : (
        <Text style={styles.buttonText}>
          {isOtpSent ? 'Verify Code' : 'Send Code'}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#3b82f6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 'auto',
  },
  buttonDisabled: {
    backgroundColor: '#d1d5db',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});