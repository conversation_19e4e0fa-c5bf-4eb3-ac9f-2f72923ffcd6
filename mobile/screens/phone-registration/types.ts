import { User } from "@ozgaar/types";

export interface ValidationResult {
  isValid: boolean;
  formatted?: string;
  error?: string;
}

export interface SendOTPResult {
  error?: {
    message: string;
  } | null;
  data?: {
    phone: string;
    expiresIn: number;
    remaining_attempts: number;
  } | null;
  backup_used?: boolean;
}

export interface VerifyOTPResult {
  error?: {
    message: string;
  } | null;
  data?: {
    user: User;
    tokens: {
      accessToken: string;
      refreshToken: string;
      expiresIn: string;
    };
  } | null;
}