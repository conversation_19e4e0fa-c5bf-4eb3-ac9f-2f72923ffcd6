import type { SupportedLanguage } from '@/types'

export type SkillCategory = 'electrical' | 'plumbing' | 'carpentry' | 'cooking' | 'cleaning' | 'driving' | 'delivery' | 'security' | 'gardening' | 'tutoring';

export type UserType = 'worker' | 'poster';

export interface SkillOption {
  value: SkillCategory;
  label: string;
  description: string;
}

export interface ProfileFormData {
  fullName: string;
  language: SupportedLanguage;
  userType: UserType;
  primarySkill: SkillCategory | '';
  email: string;
  profileImageUrl: string | null;
}

// Changed from optional properties to required properties for better type compatibility
export type FormErrors = Record<string, string>;