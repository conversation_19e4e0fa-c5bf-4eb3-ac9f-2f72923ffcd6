import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { CheckCircle, User, Globe } from 'lucide-react-native';
import { router } from 'expo-router';
import { authHelpers } from '@/lib/supabase'
import LanguageSelector from '@/components/LanguageSelector'
import ProfilePhotoUpload from '@/components/ProfilePhotoUpload'
import type { SupportedLanguage } from '@/types'
import { validateProfileForm } from './validation';
import { styles } from './styles';
import { ProfileFormData, UserType } from './types';
import { useAuth } from '@/hooks/useAuth';

const ProfileCompletionScreen = () => {
  const { user } = useAuth();
  const [formData, setFormData] = useState<ProfileFormData>({
    fullName: '',
    language: 'english',
    userType: user?.user_type as UserType || 'poster',
    primarySkill: '',
    email: '',
    profileImageUrl: null
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const updateFormData = (field: keyof ProfileFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleCompleteProfile = async () => {
    const validationErrors = validateProfileForm(formData, formData.userType);
    
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsLoading(true);

    try {
      const profileData: any = {
        full_name: formData.fullName.trim(),
        preferred_language: formData.language,
        user_type: formData.userType,
      };

      if (formData.email.trim()) {
        profileData.email = formData.email.trim();
      }

      const response = await authHelpers.completeUserProfile(profileData);

      if (response.error) {
        const errorMessage = typeof response.error === 'string' ? response.error :
          (response.error as any)?.message || 'Failed to complete profile';
        Alert.alert('Profile Completion Failed', errorMessage);
        return;
      }
      Alert.alert(
        'Welcome to Ozgaar!',
        'Your profile has been created successfully. You can now start using the app.',
        [
          {
            text: 'Get Started',
            onPress: () => router.replace('/(tabs)')
          }
        ]
      )

    } catch (error) {
      console.error('Profile completion error:', error)
      Alert.alert('Error', 'Failed to complete profile. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <CheckCircle size={32} color="#22C55E" />
          </View>
          <Text style={styles.title}>Complete Your Profile</Text>
          <Text style={styles.subtitle}>
            Tell us a bit about yourself to get started on Ozgaar
          </Text>
        </View>

        {/* Profile Photo Upload */}
        <ProfilePhotoUpload
          currentImageUrl={formData.profileImageUrl}
          onImageUploaded={(imageUrl: string) => updateFormData('profileImageUrl', imageUrl)}
          onImageDeleted={() => updateFormData('profileImageUrl', null)}
          size={100}
        />

        <View style={styles.form}>
          {/* Full Name */}
          <View style={styles.inputGroup}>
            <View style={styles.labelContainer}>
              <User size={20} color="#6B7280" />
              <Text style={styles.label}>Full Name *</Text>
            </View>
            <TextInput
              style={[styles.input, errors.fullName && styles.inputError]}
              placeholder="Enter your full name (This CANNOT be changed later)"
              value={formData.fullName}
              onChangeText={(text) => updateFormData('fullName', text)}
              autoCapitalize="words"
              autoComplete="name"
            />
            {errors.fullName && <Text style={styles.errorText}>{errors.fullName}</Text>}
          </View>

          {/* Language Selection */}
          <View style={styles.inputGroup}>
            <View style={styles.labelContainer}>
              <Globe size={20} color="#6B7280" />
              <Text style={styles.label}>Preferred Language</Text>
            </View>
            <LanguageSelector 
              value={formData.language} 
              onChange={(lang: SupportedLanguage) => updateFormData('language', lang)} 
            />
          </View>

          {/* Email (Optional) */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email (Optional)</Text>
            <TextInput
              style={[styles.input, errors.email && styles.inputError]}
              placeholder="Enter your email address"
              value={formData.email}
              onChangeText={(text) => updateFormData('email', text)}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
            />
            {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
          </View>

          <TouchableOpacity
            style={[
              styles.completeButton,
              (!formData.fullName.trim() || 
               (formData.userType === 'worker' && !formData.primarySkill) || 
               isLoading) && styles.completeButtonDisabled
            ]}
            onPress={handleCompleteProfile}
            disabled={!formData.fullName.trim() || 
                      (formData.userType === 'worker' && !formData.primarySkill) || 
                      isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.completeButtonText}>Complete Profile</Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  )
}

export default ProfileCompletionScreen