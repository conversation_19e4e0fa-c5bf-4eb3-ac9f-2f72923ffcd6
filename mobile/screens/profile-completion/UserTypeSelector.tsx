import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { UserType } from './types';

interface UserTypeSelectorProps {
  userType: UserType;
  setUserType: (type: UserType) => void;
  clearPrimarySkillError: () => void;
  clearPrimarySkill: () => void;
}

const UserTypeSelector: React.FC<UserTypeSelectorProps> = ({
  userType,
  setUserType,
  clearPrimarySkillError,
  clearPrimarySkill
}) => {
  return (
    <View style={styles.userTypeContainer}>
      <Text style={styles.label}>I want to</Text>
      <View style={styles.userTypeOptions}>
        <TouchableOpacity
          style={[
            styles.userTypeOption,
            userType === 'worker' && styles.userTypeOptionSelected
          ]}
          onPress={() => {
            setUserType('worker');
            clearPrimarySkillError();
          }}
        >
          <Text style={[
            styles.userTypeText,
            userType === 'worker' && styles.userTypeTextSelected
          ]}>
            Find Work
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.userTypeOption,
            userType === 'poster' && styles.userTypeOptionSelected
          ]}
          onPress={() => {
            setUserType('poster');
            clearPrimarySkill();
            clearPrimarySkillError();
          }}
        >
          <Text style={[
            styles.userTypeText,
            userType === 'poster' && styles.userTypeTextSelected
          ]}>
            Hire Workers
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  userTypeContainer: {
    marginTop: 8,
  },
  userTypeOptions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  userTypeOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
    alignItems: 'center',
  },
  userTypeOptionSelected: {
    borderColor: '#059669',
    backgroundColor: 'rgba(5, 150, 105, 0.1)',
  },
  userTypeText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  userTypeTextSelected: {
    color: '#059669',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 8,
  },
});

export default UserTypeSelector;