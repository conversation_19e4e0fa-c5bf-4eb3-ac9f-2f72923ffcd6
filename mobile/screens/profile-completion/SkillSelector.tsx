import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Wrench } from 'lucide-react-native';
import { SKILL_CATEGORIES } from './constants';
import { SkillCategory } from './types';

interface SkillSelectorProps {
  primarySkill: SkillCategory | '';
  setPrimarySkill: (skill: SkillCategory) => void;
  clearPrimarySkillError: () => void;
  hasError: boolean;
}

const SkillSelector: React.FC<SkillSelectorProps> = ({
  primarySkill,
  setPrimarySkill,
  clearPrimarySkillError,
  hasError
}) => {
  return (
    <View style={styles.inputGroup}>
      <View style={styles.labelContainer}>
        <Wrench size={20} color="#6B7280" />
        <Text style={styles.label}>Primary Skill *</Text>
      </View>
      <Text style={styles.helperText}>Choose your main area of expertise</Text>
      <View style={styles.skillGrid}>
        {SKILL_CATEGORIES.map((skill) => (
          <TouchableOpacity
            key={skill.value}
            style={[
              styles.skillOption,
              primarySkill === skill.value && styles.skillSelected,
              hasError && styles.skillError
            ]}
            onPress={() => {
              setPrimarySkill(skill.value);
              clearPrimarySkillError();
            }}
          >
            <Text style={[
              styles.skillLabel,
              primarySkill === skill.value && styles.skillLabelSelected
            ]}>
              {skill.label}
            </Text>
            <Text style={[
              styles.skillDescription,
              primarySkill === skill.value && styles.skillDescriptionSelected
            ]}>
              {skill.description}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  inputGroup: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 8,
  },
  helperText: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: -4,
    marginBottom: 8,
  },
  skillGrid: {
    gap: 8,
  },
  skillOption: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  skillSelected: {
    borderColor: '#059669',
    backgroundColor: 'rgba(5, 150, 105, 0.1)',
  },
  skillError: {
    borderColor: '#EF4444',
  },
  skillLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  skillLabelSelected: {
    color: '#059669',
  },
  skillDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  skillDescriptionSelected: {
    color: '#047857',
  },
});

export default SkillSelector;