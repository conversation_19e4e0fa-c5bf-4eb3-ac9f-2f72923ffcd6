import { FormErrors, ProfileFormData, UserType } from './types';

export const validateProfileForm = (formData: ProfileFormData, userType: UserType): FormErrors => {
  const errors: FormErrors = {};

  if (!formData.fullName.trim() || formData.fullName.trim().length < 2) {
    errors.fullName = 'Full name is required (at least 2 characters)';
  }

  if ((userType === 'worker') && !formData.primarySkill) {
    errors.primarySkill = 'Primary skill is required for workers';
  }

  if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = 'Please enter a valid email address';
  }

  return errors;
};