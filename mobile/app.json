{"expo": {"name": "mobile", "slug": "mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "mobile", "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"supportsTablet": true, "bundleIdentifier": "com.anonymous.mobile"}, "android": {"usesCleartextTraffic": true, "networkSecurityConfig": "./network_security_config.xml", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.anonymous.mobile"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": [["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-router", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow this app to use your location."}]]}}