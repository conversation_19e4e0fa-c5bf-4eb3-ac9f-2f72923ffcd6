{"name": "mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@ozgaar/types": "workspace:*", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/elements": "^2.6.3", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.25", "@reduxjs/toolkit": "^2.8.2", "@supabase/supabase-js": "^2.55.0", "@tanstack/react-query": "^5.85.3", "expo": "~53.0.20", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "i18n-js": "^4.5.1", "i18next": "^25.3.6", "lucide-react-native": "^0.539.0", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.6.1", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-localize": "^3.5.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/node": "^20.19.11", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}