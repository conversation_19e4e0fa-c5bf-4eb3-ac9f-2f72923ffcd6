import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import { authHelpers } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/redux';
import { setViewMode } from '@/store/userSlice';

type UserType = 'worker' | 'poster';

const UserTypeSelectionScreen = () => {
  const [selectedType, setSelectedType] = useState<UserType | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { refreshUser } = useAuth();
  const dispatch = useAppDispatch();

  const handleContinue = async () => {
    if (!selectedType) {
      Alert.alert('Selection Required', 'Please select how you want to use Ozgaar');
      return;
    }

    setIsLoading(true);
    try {
      // Update user profile with selected type
      const response: any = await authHelpers.upsertUserProfile({
        user_type: selectedType,
      });

      if (response.error) {
        throw new Error('Failed to update user type:' ,response.error.message);
      }

      // Refresh user data
      await refreshUser();

      // Navigate to appropriate next screen
      if (selectedType === 'worker') {
        dispatch(setViewMode('worker'));
        router.push('/(auth)/worker-profile-creation');
      } else {
        dispatch(setViewMode('poster'));
        router.push('/(tabs)');
      }
    } catch (error) {
      console.error('Error updating user type:', error);
      Alert.alert('Error', 'Failed to update user type. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>How do you want to use Ozgaar?</Text>
        <Text style={styles.subtitle}>You can change this later in settings</Text>

        <View style={styles.optionsContainer}>
          <TouchableOpacity
            style={[
              styles.optionButton,
              selectedType === 'worker' && styles.selectedOption,
            ]}
            onPress={() => setSelectedType('worker')}
          >
            <Text style={styles.optionEmoji}>👷</Text>
            <Text style={styles.optionTitle}>Find Work</Text>
            <Text style={styles.optionDescription}>
              Browse and apply for jobs in your area
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.optionButton,
              selectedType === 'poster' && styles.selectedOption,
            ]}
            onPress={() => setSelectedType('poster')}
          >
            <Text style={styles.optionEmoji}>💼</Text>
            <Text style={styles.optionTitle}>Hire Workers</Text>
            <Text style={styles.optionDescription}>
              Post jobs and find skilled workers
            </Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={[styles.continueButton, !selectedType && styles.disabledButton]}
          onPress={handleContinue}
          disabled={!selectedType || isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <Text style={styles.continueButtonText}>Continue</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 40,
  },
  optionsContainer: {
    gap: 16,
    marginBottom: 40,
  },
  optionButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  selectedOption: {
    borderColor: '#059669',
    backgroundColor: '#ECFDF5',
  },
  optionEmoji: {
    fontSize: 40,
    marginBottom: 12,
  },
  optionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  optionDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  continueButton: {
    backgroundColor: '#059669',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#D1D5DB',
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});

export default UserTypeSelectionScreen;