import React, { useState, useEffect, useMemo, useCallback } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView
} from 'react-native'
import { ArrowLeft, Lock, Save, User, Briefcase, MapPin } from 'lucide-react-native'
import { router } from 'expo-router'
import { authHelpers } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuth'
import ProfilePhotoUpload from '@/components/ProfilePhotoUpload'
import { useRoleDetection } from '@/hooks/useRoleDetection'
import { useDynamicForm, commonValidationRules } from '@/hooks/useDynamicForm'
import { useAutoSave } from '@/hooks/useAutoSave'
import SkillSelector from '@/components/SkillSelector'
import BusinessTypeSelector from '@/components/BusinessTypeSelector'

type SkillCategory = 'electrical' | 'plumbing' | 'carpentry' | 'cooking' | 'cleaning' | 'driving' | 'delivery' | 'security' | 'gardening' | 'tutoring'

interface ProfileFormData {
  full_name: string
  email: string
  phone_number: string
  profile_image_url: string | null
  
  // Worker-specific fields
  primary_skill_category?: SkillCategory | ''
  years_of_experience?: string
  hourly_rate?: string
  daily_rate?: string
  bio?: string
  currently_available?: boolean
  
  // Job Poster-specific fields
  company_name?: string
  business_type?: string
  hiring_radius_km?: string
  poster_bio?: string
}

const EditProfileScreen = () => {
  const { user, refreshUser } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [profileImageUrl, setProfileImageUrl] = useState<string | null>(null)

  // Use custom hooks for role detection and dynamic form management
  const { detectedRole, isDetecting } = useRoleDetection()

  // Memoize initial form data to prevent infinite re-renders
  const initialFormData = useMemo(() => ({
    full_name: user?.full_name || '',
    email: user?.email || '',
    phone_number: user?.phone || '',
    profile_image_url: user?.profile_image_url || null,
    primary_skill_category: (user?.primary_skill_category as SkillCategory) || '',
    years_of_experience: user?.years_of_experience?.toString() || '',
    hourly_rate: user?.hourly_rate?.toString() || '',
    daily_rate: user?.daily_rate?.toString() || '',
    bio: user?.description || '',
    currently_available: user?.currently_available || false,
    company_name: user?.company_name || '',
    business_type: user?.business_type || '',
    hiring_radius_km: user?.travel_radius_km?.toString() || '10',
    poster_bio: user?.poster_bio || ''
  }), [user])

  const validationRules = useMemo(() => ({
    email: commonValidationRules.email,
    hourly_rate: commonValidationRules.rate,
    daily_rate: commonValidationRules.rate,
    years_of_experience: commonValidationRules.experience,
    bio: commonValidationRules.bio,
    poster_bio: commonValidationRules.bio
  }), [])

  const {
    formData,
    errors,
    updateField,
    validateForm,
    resetForm,
    isDirty
  } = useDynamicForm<ProfileFormData>(initialFormData, validationRules)

  useEffect(() => {
    setProfileImageUrl(user?.profile_image_url || null)
  }, [user])

  useEffect(() => {
    setHasUnsavedChanges(isDirty)
  }, [isDirty])

  // Auto-save functionality
  const { clearSavedData, restoreData, hasSavedData } = useAutoSave({
    key: `profile_edit_${user?.id}`,
    data: formData,
    enabled: isDirty,
    onSave: () => {
      console.log('Profile draft auto-saved')
    }
  })

  // Check for saved draft on mount
  const checkForDraft = useCallback(async () => {
    if (!user?.id) return

    const hasData = await hasSavedData()
    if (hasData) {
      Alert.alert(
        'Draft Found',
        'We found a saved draft of your profile changes. Would you like to restore it?',
        [
          {
            text: 'Discard',
            style: 'destructive',
            onPress: () => clearSavedData()
          },
          {
            text: 'Restore',
            onPress: async () => {
              const savedData = await restoreData()
              if (savedData) {
                // Update form with saved data
                Object.keys(savedData).forEach(key => {
                  updateField(key as keyof ProfileFormData, savedData[key])
                })
              }
            }
          }
        ]
      )
    }
  }, [user?.id, hasSavedData, clearSavedData, restoreData, updateField])

  useEffect(() => {
    checkForDraft()
  }, [checkForDraft])

  // Handle back navigation with unsaved changes warning
  const handleBackPress = () => {
    if (hasUnsavedChanges) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Are you sure you want to leave?',
        [
          { text: 'Stay', style: 'cancel' },
          { 
            text: 'Leave', 
            style: 'destructive',
            onPress: () => router.back()
          }
        ]
      )
    } else {
      router.back()
    }
  }

  const handleSaveProfile = async () => {
    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      // Prepare update data based on detected role
      const updateData: any = {
        full_name: formData.full_name.trim(),
        email: formData.email.trim() || undefined,
      }

      // Add role-specific fields
      if (detectedRole === 'worker' || detectedRole === 'both') {
        if (formData.primary_skill_category) {
          updateData.primary_skill_category = formData.primary_skill_category
        }
        if (formData.years_of_experience) {
          updateData.years_of_experience = parseInt(formData.years_of_experience)
        }
        if (formData.hourly_rate) {
          updateData.hourly_rate = parseFloat(formData.hourly_rate)
        }
        if (formData.daily_rate) {
          updateData.daily_rate = parseFloat(formData.daily_rate)
        }
        if (formData.bio) {
          updateData.description = formData.bio.trim()
        }
        updateData.currently_available = formData.currently_available
      }

      if (detectedRole === 'poster' || detectedRole === 'both') {
        if (formData.company_name) {
          updateData.company_name = formData.company_name.trim()
        }
        if (formData.business_type) {
          updateData.business_type = formData.business_type
        }
        if (formData.hiring_radius_km) {
          updateData.travel_radius_km = parseInt(formData.hiring_radius_km)
        }
        if (formData.poster_bio) {
          updateData.poster_bio = formData.poster_bio.trim()
        }
      }

      const response = await authHelpers.upsertUserProfile(updateData)

      if (response.error) {
        const errorMessage = typeof response.error === 'string' ? response.error :
          (response.error as any)?.message || 'Failed to update profile'
        Alert.alert('Update Failed', errorMessage)
        return
      }

      // Refresh user data
      await refreshUser()
      
      // Reset form dirty state and clear auto-saved draft
      resetForm()
      setHasUnsavedChanges(false)
      await clearSavedData()

      Alert.alert(
        'Profile Updated!',
        'Your profile has been updated successfully.',
        [
          {
            text: 'OK',
            onPress: () => router.back()
          }
        ]
      )
    } catch (error) {
      console.error('Profile update error:', error)
      Alert.alert('Error', 'Failed to update profile. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  if (isDetecting) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#059669" />
          <Text style={styles.loadingText}>Loading profile...</Text>
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        style={styles.container} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBackPress}
          >
            <ArrowLeft size={24} color="#374151" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Profile</Text>
          <TouchableOpacity
            style={[
              styles.saveButton,
              (!hasUnsavedChanges || isLoading) && styles.saveButtonDisabled
            ]}
            onPress={handleSaveProfile}
            disabled={!hasUnsavedChanges || isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Save size={20} color="#fff" />
            )}
          </TouchableOpacity>
        </View>

        <ScrollView 
          contentContainerStyle={styles.scrollContainer} 
          showsVerticalScrollIndicator={false}
        >
          {/* Profile Photo */}
          <View style={styles.photoSection}>
            <ProfilePhotoUpload
              currentImageUrl={profileImageUrl}
              onImageUploaded={(imageUrl) => {
                setProfileImageUrl(imageUrl)
                updateField('profile_image_url', imageUrl)
              }}
              onImageDeleted={() => {
                setProfileImageUrl(null)
                updateField('profile_image_url', null)
              }}
              size={120}
            />
          </View>

          {/* Basic Information Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <User size={20} color="#374151" />
              <Text style={styles.sectionTitle}>Basic Information</Text>
            </View>

            {/* Full Name - Locked */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Full Name</Text>
              <View style={styles.lockedInputContainer}>
                <TextInput
                  style={[styles.input, styles.lockedInput]}
                  value={formData.full_name}
                  editable={false}
                />
                <Lock size={16} color="#9CA3AF" style={styles.lockIcon} />
              </View>
              <Text style={styles.helperText}>Contact support to change your name</Text>
            </View>

            {/* Phone Number - Locked */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Phone Number</Text>
              <View style={styles.lockedInputContainer}>
                <TextInput
                  style={[styles.input, styles.lockedInput]}
                  value={formData.phone_number}
                  editable={false}
                />
                <Lock size={16} color="#9CA3AF" style={styles.lockIcon} />
              </View>
              <Text style={styles.helperText}>Contact support to change your phone number</Text>
            </View>

            {/* Email */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Email (Optional)</Text>
              <TextInput
                style={[styles.input, errors.email && styles.inputError]}
                placeholder="Enter your email address"
                value={formData.email}
                onChangeText={(text) => updateField('email', text)}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
              />
              {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
            </View>
          </View>

          {/* Worker-Specific Fields */}
          {(detectedRole === 'worker' || detectedRole === 'both') && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Briefcase size={20} color="#374151" />
                <Text style={styles.sectionTitle}>Professional Details</Text>
              </View>

              {/* Primary Skill */}
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Primary Skill</Text>
                <SkillSelector
                  selectedSkills={ formData.primary_skill_category ? [formData.primary_skill_category] : []}
                  onSkillsChange={(skill:any) => updateField('primary_skill_category', skill)}
                  error={errors.primary_skill_category}
                />
              </View>

              {/* Years of Experience */}
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Years of Experience</Text>
                <TextInput
                  style={[styles.input, errors.years_of_experience && styles.inputError]}
                  placeholder="e.g., 5"
                  value={formData.years_of_experience}
                  onChangeText={(text) => updateField('years_of_experience', text)}
                  keyboardType="numeric"
                />
                {errors.years_of_experience && (
                  <Text style={styles.errorText}>{errors.years_of_experience}</Text>
                )}
              </View>

              {/* Hourly Rate */}
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Hourly Rate (₹)</Text>
                <TextInput
                  style={[styles.input, errors.hourly_rate && styles.inputError]}
                  placeholder="e.g., 500"
                  value={formData.hourly_rate}
                  onChangeText={(text) => updateField('hourly_rate', text)}
                  keyboardType="numeric"
                />
                {errors.hourly_rate && (
                  <Text style={styles.errorText}>{errors.hourly_rate}</Text>
                )}
              </View>

              {/* Daily Rate */}
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Daily Rate (₹)</Text>
                <TextInput
                  style={[styles.input, errors.daily_rate && styles.inputError]}
                  placeholder="e.g., 2000"
                  value={formData.daily_rate}
                  onChangeText={(text) => updateField('daily_rate', text)}
                  keyboardType="numeric"
                />
                {errors.daily_rate && (
                  <Text style={styles.errorText}>{errors.daily_rate}</Text>
                )}
              </View>

              {/* Bio */}
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Bio (100 words max)</Text>
                <TextInput
                  style={[styles.textArea, errors.bio && styles.inputError]}
                  placeholder="Tell potential employers about your skills and experience..."
                  value={formData.bio}
                  onChangeText={(text) => updateField('bio', text)}
                  multiline
                  numberOfLines={4}
                  maxLength={500}
                />
                <Text style={styles.characterCount}>
                  {formData.bio?.length || 0}/500 characters
                </Text>
                {errors.bio && <Text style={styles.errorText}>{errors.bio}</Text>}
              </View>

              {/* Availability Toggle */}
              <View style={styles.inputGroup}>
                <View style={styles.toggleContainer}>
                  <Text style={styles.label}>Currently Available for Work</Text>
                  <TouchableOpacity
                    style={[
                      styles.toggle,
                      formData.currently_available && styles.toggleActive
                    ]}
                    onPress={() => updateField('currently_available', !formData.currently_available)}
                  >
                    <View style={[
                      styles.toggleThumb,
                      formData.currently_available && styles.toggleThumbActive
                    ]} />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          )}

          {/* Job Poster-Specific Fields */}
          {(detectedRole === 'poster' || detectedRole === 'both') && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <MapPin size={20} color="#374151" />
                <Text style={styles.sectionTitle}>Business Details</Text>
              </View>

              {/* Company Name */}
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Company/Business Name</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Enter your company or business name"
                  value={formData.company_name}
                  onChangeText={(text) => updateField('company_name', text)}
                />
              </View>

              {/* Business Type */}
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Business Type</Text>
                <BusinessTypeSelector
                  value={formData.business_type}
                  onValueChange={(type) => updateField('business_type', type)}
                  error={errors.business_type}
                />
              </View>

              {/* Hiring Radius */}
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Preferred Hiring Radius (km)</Text>
                <TextInput
                  style={styles.input}
                  placeholder="e.g., 10"
                  value={formData.hiring_radius_km}
                  onChangeText={(text) => updateField('hiring_radius_km', text)}
                  keyboardType="numeric"
                />
              </View>

              {/* Poster Bio */}
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Business Bio (150 words max)</Text>
                <TextInput
                  style={[styles.textArea, errors.poster_bio && styles.inputError]}
                  placeholder="Tell workers about your business and what you're looking for..."
                  value={formData.poster_bio}
                  onChangeText={(text) => updateField('poster_bio', text)}
                  multiline
                  numberOfLines={4}
                  maxLength={500}
                />
                <Text style={styles.characterCount}>
                  {formData.poster_bio?.length || 0}/500 characters
                </Text>
                {errors.poster_bio && <Text style={styles.errorText}>{errors.poster_bio}</Text>}
              </View>
            </View>
          )}

        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  saveButton: {
    backgroundColor: '#059669',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#D1D5DB',
  },
  scrollContainer: {
    paddingBottom: 32,
  },
  photoSection: {
    alignItems: 'center',
    paddingVertical: 32,
    backgroundColor: '#fff',
    marginBottom: 16,
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 16,
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginLeft: 8,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  lockedInputContainer: {
    position: 'relative',
  },
  lockedInput: {
    backgroundColor: '#F3F4F6',
    color: '#6B7280',
  },
  lockIcon: {
    position: 'absolute',
    right: 16,
    top: 14,
  },
  inputError: {
    borderColor: '#EF4444',
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  helperText: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    minHeight: 100,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'right',
    marginTop: 4,
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  toggle: {
    width: 50,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#D1D5DB',
    padding: 2,
    justifyContent: 'center',
  },
  toggleActive: {
    backgroundColor: '#059669',
  },
  toggleThumb: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  toggleThumbActive: {
    transform: [{ translateX: 22 }],
  },
})

export default EditProfileScreen
