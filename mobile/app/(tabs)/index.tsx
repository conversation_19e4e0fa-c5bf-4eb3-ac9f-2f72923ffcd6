import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
  RefreshControl,
  Alert,
  ListRenderItem,
  AppState,
  AppStateStatus
} from "react-native";
import { Bell, MapPin, Filter, Briefcase, WifiOff } from "lucide-react-native";
import { LinearGradient } from 'expo-linear-gradient';

import JobCard from "../../components/JobCard";
import TrustBadge from "../../components/TrustBadge";
import JobCardSkeleton from "../../components/JobCardSkeleton";
import EnhancedEmptyState from "../../components/EnhancedEmptyState";
import { Job as UIJob } from "../../types";
import LanguageSelectorIcon from "@/components/LanguageSelectorIcon";
import { jobService, Job as APIJob } from "../../services/jobService";
import { locationService, LocationData } from "../../services/locationService";
import { useAuth } from "../../hooks/useAuth";
import { NetworkState, networkService } from "../../services/networkService";
import { jobCacheService } from "../../services/jobCacheService";

interface JobFeedState {
  jobs: APIJob[];
  loading: boolean;
  refreshing: boolean;
  loadingMore: boolean;
  backgroundRefreshing: boolean;
  error: string | null;
  networkError: boolean;
  locationError: boolean;
  hasMore: boolean;
  page: number;
  lastRefresh: number;
}

interface FilterState {
  urgency: string | null;
  skillCategory: string | null;
  radiusKm: number;
  mySkillsOnly: boolean;
}

const SearchTab = () => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [userLocation, setUserLocation] = useState<LocationData | null>(null);
  const [locationLoading, setLocationLoading] = useState(false);
  const [networkState, setNetworkState] = useState<NetworkState>({
    isConnected: false,
    isInternetReachable: false,
    type: 'unknown'
  });
  const [jobFeed, setJobFeed] = useState<JobFeedState>({
    jobs: [],
    loading: true,
    refreshing: false,
    loadingMore: false,
    backgroundRefreshing: false,
    error: null,
    networkError: false,
    locationError: false,
    hasMore: true,
    page: 1,
    lastRefresh: 0
  });
  const [filters, setFilters] = useState<FilterState>({
    urgency: null,
    skillCategory: null,
    radiusKm: 25,
    mySkillsOnly: false
  });

  // Refs for background refresh and cleanup
  const backgroundRefreshInterval = useRef<NodeJS.Timeout | null>(null);
  const networkListener = useRef<{ unsubscribe: () => void } | null>(null);
  const appStateListener = useRef<any>(null);
  const isComponentMounted = useRef(true);

  // Convert APIJob to UIJob format for JobCard component
  const convertAPIJobToUIJob = useCallback((apiJob: APIJob): UIJob => {
    return {
      id: apiJob.id,
      title: apiJob.title,
      description: apiJob.description,
      skill_category: apiJob.skill_category as any, // Type assertion for now
      skill_subcategories: [], // Not available in API response
      location: {
        latitude: apiJob.latitude || 0,
        longitude: apiJob.longitude || 0,
        address: apiJob.address || ''
      },
      budget_min: apiJob.budget_min,
      budget_max: apiJob.budget_max,
      urgency: apiJob.urgency === 'normal' ? 'medium' : apiJob.urgency as any,
      preferred_start_time: undefined,
      estimated_duration_hours: apiJob.estimated_duration_hours || undefined,
      requirements: apiJob.requirements ? [apiJob.requirements] : [],
      is_active: apiJob.status === 'active',
      poster_id: apiJob.poster_id,
      created_at: apiJob.created_at,
      applications_count: 0 // Not available in API response
    };
  }, []);

  // Get user location on component mount
  useEffect(() => {
    getUserLocation();
  }, []);

  // Load jobs when location or filters change
  useEffect(() => {
    if (userLocation) {
      loadJobs(true);
    }
  }, [userLocation, filters]);

  // Initialize network monitoring and app state listeners
  useEffect(() => {
    isComponentMounted.current = true;

    // Network state listener
    networkListener.current = networkService.addListener((state) => {
      if (!isComponentMounted.current) return;

      setNetworkState(state);

      // Auto-retry when network comes back online
      if (state.isConnected && state.isInternetReachable && jobFeed.networkError) {
        console.log('Network restored, retrying job load...');
        loadJobs(true);
      }
    });

    // App state listener for background refresh
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (!isComponentMounted.current) return;

      if (nextAppState === 'active') {
        // App came to foreground, check if we need to refresh
        const timeSinceLastRefresh = Date.now() - jobFeed.lastRefresh;
        if (timeSinceLastRefresh > 60000) { // 1 minute
          performBackgroundRefresh();
        }
      }
    };

    appStateListener.current = AppState.addEventListener('change', handleAppStateChange);

    // Start background refresh interval
    startBackgroundRefresh();

    return () => {
      isComponentMounted.current = false;
      networkListener.current?.unsubscribe();
      appStateListener.current?.remove();
      stopBackgroundRefresh();
    };
  }, []);

  // Clear expired cache periodically
  useEffect(() => {
    const clearExpiredCache = async () => {
      try {
        await jobCacheService.clearExpiredCache();
      } catch (error) {
        console.error('Error clearing expired cache:', error);
      }
    };

    clearExpiredCache();
    const interval = setInterval(clearExpiredCache, 5 * 60 * 1000); // Every 5 minutes

    return () => clearInterval(interval);
  }, []);

  // Background refresh functions
  const startBackgroundRefresh = () => {
    // Refresh every 2 minutes when app is active
    backgroundRefreshInterval.current = setInterval(() => {
      if (isComponentMounted.current && !jobFeed.loading && !jobFeed.refreshing) {
        performBackgroundRefresh();
      }
    }, 2 * 60 * 1000);
  };

  const stopBackgroundRefresh = () => {
    if (backgroundRefreshInterval.current) {
      clearInterval(backgroundRefreshInterval.current);
      backgroundRefreshInterval.current = null;
    }
  };

  const performBackgroundRefresh = async () => {
    if (!networkState.isConnected || !networkState.isInternetReachable) {
      return;
    }

    try {
      setJobFeed(prev => ({ ...prev, backgroundRefreshing: true }));

      // Check cache first
      const cacheKey = { filters, location: userLocation };
      const cached = await jobCacheService.getCachedJobs(filters, userLocation);

      if (cached.needsRefresh) {
        // Fetch fresh data in background
        const params: any = {
          limit: 20,
          offset: 0
        };

        if (userLocation) {
          params.latitude = userLocation.latitude;
          params.longitude = userLocation.longitude;
          params.radius_km = filters.radiusKm;
        }

        if (filters.urgency) {
          params.urgency = filters.urgency;
        }

        if (filters.mySkillsOnly && user?.primary_skill_category) {
          params.skill_category = user.primary_skill_category;
        } else if (filters.skillCategory) {
          params.skill_category = filters.skillCategory;
        }

        const { data, error } = await jobService.getJobs(params);

        if (data?.jobs && !error) {
          // Check if there are newer jobs
          const hasNewer = await jobCacheService.hasNewerJobs(data.jobs, filters, userLocation);

          if (hasNewer) {
            // Update cache and UI
            await jobCacheService.cacheJobs(data.jobs, filters, userLocation);

            const sortedJobs = [...data.jobs].sort((a, b) => {
              if (a.urgency === 'urgent' && b.urgency !== 'urgent') return -1;
              if (b.urgency === 'urgent' && a.urgency !== 'urgent') return 1;
              return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
            });

            setJobFeed(prev => ({
              ...prev,
              jobs: sortedJobs,
              lastRefresh: Date.now(),
              backgroundRefreshing: false,
              error: null,
              networkError: false
            }));
          } else {
            setJobFeed(prev => ({
              ...prev,
              backgroundRefreshing: false,
              lastRefresh: Date.now()
            }));
          }
        }
      }
    } catch (error) {
      console.error('Background refresh error:', error);
      setJobFeed(prev => ({ ...prev, backgroundRefreshing: false }));
    }
  };

  const getUserLocation = useCallback(async () => {
    setLocationLoading(true);
    try {
      // Try to get cached location first for faster loading
      let location = await locationService.getCachedLocation();

      if (location) {
        setUserLocation(location);
      }

      // Then try to get fresh location
      const freshLocation = await locationService.getCurrentLocation(false);
      if (freshLocation) {
        setUserLocation(freshLocation);
      } else if (!location) {
        // If no location available, show error but continue with jobs without location
        setJobFeed(prev => ({
          ...prev,
          error: 'Location not available. Showing all jobs.'
        }));
        loadJobs(true);
      }
    } catch (error) {
      console.error('Error getting location:', error);
      setJobFeed(prev => ({
        ...prev,
        error: 'Location not available. Showing all jobs.'
      }));
      loadJobs(true);
    } finally {
      setLocationLoading(false);
    }
  }, []);

  const loadJobs = useCallback(async (reset: boolean = false) => {
    // Prevent multiple simultaneous requests
    if (!reset && (!jobFeed.hasMore || jobFeed.loading || jobFeed.loadingMore)) {
      return;
    }

    // Check network connectivity first
    const isOnline = await networkService.isOnline();

    if (!isOnline && reset) {
      // Try to load from cache when offline
      const cached = await jobCacheService.getCachedJobs(filters, userLocation);
      if (cached.jobs && cached.jobs.length > 0) {
        setJobFeed(prev => ({
          ...prev,
          jobs: cached.jobs || [],
          loading: false,
          error: null,
          networkError: false,
          hasMore: false, // Don't allow pagination when offline
          lastRefresh: Date.now()
        }));
        return;
      } else {
        setJobFeed(prev => ({
          ...prev,
          loading: false,
          networkError: true,
          error: 'No internet connection. Please check your network and try again.'
        }));
        return;
      }
    }

    if (reset) {
      // Check cache first for faster loading
      const cached = await jobCacheService.getCachedJobs(filters, userLocation);
      if (cached.jobs && cached.jobs.length > 0 && !cached.needsRefresh) {
        setJobFeed(prev => ({
          ...prev,
          jobs: cached.jobs || [],
          loading: false,
          error: null,
          networkError: false,
          locationError: false,
          hasMore: true,
          page: 2,
          lastRefresh: Date.now()
        }));

        // Still fetch fresh data in background if needed
        if (cached.needsRefresh) {
          performBackgroundRefresh();
        }
        return;
      }

      setJobFeed(prev => ({
        ...prev,
        loading: true,
        loadingMore: false,
        error: null,
        networkError: false,
        locationError: false,
        page: 1,
        jobs: []
      }));
    } else {
      setJobFeed(prev => ({
        ...prev,
        loadingMore: true,
        error: null,
        networkError: false
      }));
    }

    try {
      const params: any = {
        limit: 20,
        offset: reset ? 0 : (jobFeed.page - 1) * 20
      };

      // Add location parameters if available
      if (userLocation) {
        params.latitude = userLocation.latitude;
        params.longitude = userLocation.longitude;
        params.radius_km = filters.radiusKm;
      }

      // Add filter parameters
      if (filters.urgency) {
        params.urgency = filters.urgency;
      }

      // Handle skill category filtering
      if (filters.mySkillsOnly && user?.primary_skill_category) {
        params.skill_category = user.primary_skill_category;
      } else if (filters.skillCategory) {
        params.skill_category = filters.skillCategory;
      }

      const { data, error } = await jobService.getJobs(params);

      if (error) {
        setJobFeed(prev => ({
          ...prev,
          loading: false,
          loadingMore: false,
          error: error
        }));
        return;
      }

      if (data?.jobs) {
        // Sort jobs to prioritize urgent jobs at the top
        const sortedJobs = [...data.jobs].sort((a, b) => {
          // Urgent jobs first, then by creation date (newest first)
          if (a.urgency === 'urgent' && b.urgency !== 'urgent') return -1;
          if (b.urgency === 'urgent' && a.urgency !== 'urgent') return 1;
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });

        // Cache the jobs for offline access
        if (reset) {
          await jobCacheService.cacheJobs(sortedJobs, filters, userLocation);
        }

        setJobFeed(prev => ({
          ...prev,
          jobs: reset ? sortedJobs : [...prev.jobs, ...sortedJobs],
          loading: false,
          loadingMore: false,
          error: null,
          networkError: false,
          locationError: false,
          hasMore: data.jobs.length === 20,
          page: reset ? 2 : prev.page + 1,
          lastRefresh: Date.now()
        }));
      } else {
        setJobFeed(prev => ({
          ...prev,
          loading: false,
          loadingMore: false,
          jobs: reset ? [] : prev.jobs,
          hasMore: false,
          lastRefresh: Date.now()
        }));
      }
    } catch (error) {
      console.error('Error loading jobs:', error);

      // Determine error type
      const isNetworkError = !networkState.isConnected || !networkState.isInternetReachable;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const isLocationError = errorMessage?.includes('location') || errorMessage?.includes('Location');

      // Try to load cached data on error
      if (reset && isNetworkError) {
        const cached = await jobCacheService.getCachedJobs(filters, userLocation);
        if (cached.jobs && cached.jobs.length > 0) {
          setJobFeed(prev => ({
            ...prev,
            jobs: cached.jobs || [],
            loading: false,
            loadingMore: false,
            error: 'Showing cached jobs. Check your connection for updates.',
            networkError: true,
            locationError: false,
            hasMore: false
          }));
          return;
        }
      }

      setJobFeed(prev => ({
        ...prev,
        loading: false,
        loadingMore: false,
        error: isLocationError
          ? 'Location access required for nearby jobs. Enable location services or browse all jobs.'
          : isNetworkError
            ? 'No internet connection. Please check your network and try again.'
            : 'Failed to load jobs. Please try again.',
        networkError: isNetworkError,
        locationError: isLocationError
      }));
    }
  }, [userLocation, filters, jobFeed.page, jobFeed.hasMore, jobFeed.loading, jobFeed.loadingMore, networkState.isConnected, networkState.isInternetReachable, user?.primary_skill_category]);

  const handleRefresh = useCallback(async () => {
    setJobFeed(prev => ({ ...prev, refreshing: true }));
    await getUserLocation();
    await loadJobs(true);
    setJobFeed(prev => ({ ...prev, refreshing: false }));
  }, [loadJobs, getUserLocation]);

  const handleLoadMore = useCallback(() => {
    if (jobFeed.hasMore && !jobFeed.loading && !jobFeed.loadingMore) {
      loadJobs(false);
    }
  }, [jobFeed.hasMore, jobFeed.loading, jobFeed.loadingMore, loadJobs]);

  const keyExtractor = useCallback((item: APIJob) => item.id, []);

  const renderJobItem: ListRenderItem<APIJob> = useCallback(({ item }) => (
    <JobCard
      job={convertAPIJobToUIJob(item)}
      userLocation={userLocation}
      onClick={() => {
        // TODO: Navigate to job details
        console.log('Navigate to job:', item.id);
      }}
    />
  ), [userLocation, convertAPIJobToUIJob]);

  const renderFooter = useCallback(() => {
    if (!jobFeed.loadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#059669" />
        <Text style={styles.footerLoaderText}>Loading more jobs...</Text>
      </View>
    );
  }, [jobFeed.loadingMore]);

  const renderListHeader = useCallback(() => {
    const getFilterDescription = () => {
      const parts = [];

      if (filters.mySkillsOnly && user?.primary_skill_category) {
        parts.push(`${getSkillCategoryLabel(user.primary_skill_category)} jobs`);
      } else if (userLocation) {
        parts.push('Jobs near you');
      } else {
        parts.push('Available jobs');
      }

      if (filters.urgency === 'urgent') {
        parts.push('urgent only');
      }

      return parts.join(' • ');
    };

    return (
      <View style={styles.listHeader}>
        <Text style={styles.listTitle}>
          {getFilterDescription()}
        </Text>
        <Text style={styles.jobCount}>
          {jobFeed.jobs.length} job{jobFeed.jobs.length !== 1 ? 's' : ''} found
        </Text>
      </View>
    );
  }, [userLocation, jobFeed.jobs.length, filters.mySkillsOnly, filters.urgency, user?.primary_skill_category]);

  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const getSkillCategoryLabel = (category: string) => {
    const skillLabels: { [key: string]: string } = {
      'electrical': 'Electrical Work',
      'plumbing': 'Plumbing',
      'carpentry': 'Carpentry',
      'cooking': 'Cooking',
      'cleaning': 'Cleaning',
      'driving': 'Driving',
      'delivery': 'Delivery',
      'security': 'Security',
      'gardening': 'Gardening',
      'tutoring': 'Tutoring'
    };
    return skillLabels[category] || category.charAt(0).toUpperCase() + category.slice(1);
  };

  const handleSkillFilterToggle = () => {
    if (!user?.primary_skill_category) {
      Alert.alert(
        'Profile Incomplete',
        'Please complete your profile and set your primary skill to use this filter.',
        [{ text: 'OK' }]
      );
      return;
    }

    handleFilterChange({
      mySkillsOnly: !filters.mySkillsOnly,
      skillCategory: null // Clear any specific skill category filter
    });
  };

  const handleLocationChange = () => {
    Alert.alert(
      'Change Location',
      'Do you want to update your current location?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Update',
          onPress: () => {
            locationService.clearCache();
            getUserLocation();
          }
        }
      ]
    );
  };

  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <JobCardSkeleton count={5} />
    </View>
  );

  const renderErrorState = () => {
    const getErrorType = () => {
      if (jobFeed.networkError) return 'no_network';
      if (jobFeed.locationError) return 'location_error';
      return 'no_jobs';
    };

    return (
      <EnhancedEmptyState
        type={getErrorType()}
        message={jobFeed.error || undefined}
        primaryAction={{
          label: jobFeed.networkError ? 'Retry' : jobFeed.locationError ? 'Enable Location' : 'Try Again',
          onPress: () => {
            if (jobFeed.locationError) {
              getUserLocation();
            } else {
              loadJobs(true);
            }
          }
        }}
        secondaryAction={jobFeed.locationError ? {
          label: 'Browse All Jobs',
          onPress: () => {
            setUserLocation(null);
            loadJobs(true);
          }
        } : undefined}
        hasLocation={!!userLocation}
        currentRadius={filters.radiusKm}
      />
    );
  };

  const renderEmptyState = () => {
    const getEmptyStateType = () => {
      if (searchQuery.trim()) return 'search_no_results';
      if (filters.urgency || filters.mySkillsOnly || filters.skillCategory) return 'filter_no_results';
      return 'no_jobs';
    };

    return (
      <EnhancedEmptyState
        type={getEmptyStateType()}
        primaryAction={{
          label: 'Refresh Jobs',
          onPress: handleRefresh
        }}
        secondaryAction={
          getEmptyStateType() === 'filter_no_results' ? {
            label: 'Clear Filters',
            onPress: () => {
              setFilters({
                urgency: null,
                skillCategory: null,
                radiusKm: 25,
                mySkillsOnly: false
              });
              setSearchQuery('');
            }
          } : userLocation ? {
            label: 'Expand Search Area',
            onPress: () => {
              setFilters(prev => ({ ...prev, radiusKm: prev.radiusKm + 10 }));
            }
          } : {
            label: 'Enable Location',
            onPress: getUserLocation
          }
        }
        hasLocation={!!userLocation}
        currentRadius={filters.radiusKm}
        activeFilters={filters}
        searchQuery={searchQuery}
      />
    );
  };

  const renderJobsList = () => (
    <FlatList
      data={jobFeed.jobs}
      renderItem={renderJobItem}
      keyExtractor={keyExtractor}
      ListHeaderComponent={renderListHeader}
      ListFooterComponent={renderFooter}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      refreshControl={
        <RefreshControl
          refreshing={jobFeed.refreshing}
          onRefresh={handleRefresh}
          colors={['#059669']}
          tintColor="#059669"
        />
      }
      showsVerticalScrollIndicator={false}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      updateCellsBatchingPeriod={50}
      initialNumToRender={10}
      windowSize={10}
      contentContainerStyle={styles.listContainer}
    />
  );

  const renderHeader = () => (
    <>
      <LinearGradient colors={['#059669', '#3B82F6']} style={styles.header}>
        <View style={styles.headerTopRow}>
          <View>
            <Text style={styles.headerTitle}>Find Perfect Jobs</Text>
            <Text style={styles.headerSubtitle}>
              {userLocation ? 'Jobs near you' : 'Available opportunities'}
            </Text>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <TouchableOpacity style={styles.headerButton}>
              <Bell size={20} color="white" />
              <LanguageSelectorIcon />
            </TouchableOpacity>
          </View>
        </View>

        <TextInput
          placeholder="Search for jobs..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchInput}
          placeholderTextColor="rgba(255,255,255,0.7)"
        />

        <View style={styles.locationRow}>
          <MapPin size={16} color="white" />
          {locationLoading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text style={styles.locationText}>
              {userLocation
                ? `Current location: ${userLocation.address || 'GPS Location'}`
                : 'Location not available'
              }
            </Text>
          )}
          <TouchableOpacity onPress={handleLocationChange}>
            <Text style={styles.changeLocation}>Change</Text>
          </TouchableOpacity>
        </View>

        {/* Network Status Indicator */}
        {(!networkState.isConnected || !networkState.isInternetReachable) && (
          <View style={styles.networkStatusRow}>
            <WifiOff size={14} color="#FEF3C7" />
            <Text style={styles.networkStatusText}>
              {jobFeed.backgroundRefreshing
                ? 'Showing cached jobs • Updating when online'
                : 'Offline • Showing cached jobs'
              }
            </Text>
          </View>
        )}

        {/* Background refresh indicator */}
        {jobFeed.backgroundRefreshing && networkState.isConnected && (
          <View style={styles.networkStatusRow}>
            <ActivityIndicator size="small" color="#FEF3C7" />
            <Text style={styles.networkStatusText}>Checking for new jobs...</Text>
          </View>
        )}
      </LinearGradient>

      <View style={[styles.card, styles.trustCard]}>
        <View style={{flex: 1}}>
          <Text style={styles.cardTitle}>Your Trust Profile</Text>
          <View style={styles.trustBadges}>
            <TrustBadge type="verified" text="Phone Verified" />
            <TrustBadge type="pending" text="ID Pending" />
          </View>
        </View>
        <TouchableOpacity><Text style={styles.linkText}>View Details</Text></TouchableOpacity>
      </View>

      <View style={styles.filtersContainer}>
        <TouchableOpacity style={styles.filterButton}>
          <Filter size={16} />
          <Text>Filters</Text>
        </TouchableOpacity>

        {/* Skill Category Filter */}
        <TouchableOpacity
          style={[
            styles.filterButton,
            filters.mySkillsOnly && styles.activeFilter
          ]}
          onPress={handleSkillFilterToggle}
        >
          <Briefcase size={14} color={filters.mySkillsOnly ? "white" : "#6B7280"} />
          <Text style={filters.mySkillsOnly ? styles.activeFilterText : undefined}>
            {filters.mySkillsOnly
              ? `My Skills${user?.primary_skill_category ? ` (${getSkillCategoryLabel(user.primary_skill_category)})` : ''}`
              : 'My Skills'
            }
          </Text>
        </TouchableOpacity>

        {/* Urgency Filter */}
        <TouchableOpacity
          style={[
            styles.filterButton,
            filters.urgency === 'urgent' && styles.activeFilter
          ]}
          onPress={() => handleFilterChange({
            urgency: filters.urgency === 'urgent' ? null : 'urgent'
          })}
        >
          <Text style={filters.urgency === 'urgent' ? styles.activeFilterText : undefined}>
            Urgent Jobs
          </Text>
        </TouchableOpacity>

        {/* Location Filter */}
        <TouchableOpacity
          style={[
            styles.filterButton,
            userLocation && styles.activeFilter
          ]}
          onPress={getUserLocation}
        >
          <Text style={userLocation ? styles.activeFilterText : undefined}>
            Near Me
          </Text>
        </TouchableOpacity>
      </View>
    </>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.tabContent}>
        {renderHeader()}

        {/* Content based on state */}
        {jobFeed.loading && renderLoadingState()}
        {jobFeed.error && !jobFeed.loading && renderErrorState()}
        {!jobFeed.loading && !jobFeed.error && jobFeed.jobs.length === 0 && renderEmptyState()}
        {!jobFeed.loading && !jobFeed.error && jobFeed.jobs.length > 0 && renderJobsList()}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F3F4F6'
  },
  tabContent: {
    flex: 1
  },
  header: {
    padding: 24,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24
  },
  headerTopRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white'
  },
  headerSubtitle: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 14
  },
  headerButton: {
    padding: 8,
    display: 'flex',
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center'
  },
  searchInput: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    color: 'white',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  locationText: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 14,
    flex: 1
  },
  changeLocation: {
    color: 'white',
    fontWeight: 'bold'
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 16
  },
  trustCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 16
  },
  cardTitle: {
    fontWeight: '600',
    marginBottom: 8
  },
  trustBadges: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap'
  },
  linkText: {
    color: '#059669',
    fontWeight: '500'
  },
  filtersContainer: {
    flexDirection: 'row',
    gap: 8,
    padding: 16
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: 'white',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 999,
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  activeFilter: {
    backgroundColor: '#059669',
    borderColor: '#059669'
  },
  activeFilterText: {
    color: 'white',
    fontWeight: '500'
  },
  listContainer: {
    paddingBottom: 32
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 8
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600'
  },
  jobCount: {
    fontSize: 14,
    color: '#6B7280'
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center'
  },
  errorTitle: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center'
  },
  errorMessage: {
    marginTop: 8,
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20
  },
  emptyTitle: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center'
  },
  emptyMessage: {
    marginTop: 8,
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20
  },
  retryButton: {
    marginTop: 24,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: '#059669',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '500',
    fontSize: 14
  },
  loadMoreButton: {
    marginTop: 16,
    marginHorizontal: 16,
    backgroundColor: 'white',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center'
  },
  loadMoreText: {
    color: '#059669',
    fontWeight: '500',
    fontSize: 14
  },
  footerLoader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    gap: 8
  },
  footerLoaderText: {
    color: '#6B7280',
    fontSize: 14,
    fontWeight: '500'
  },
  loadingContainer: {
    flex: 1,
    paddingTop: 16
  },
  networkStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 8,
    paddingHorizontal: 4
  },
  networkStatusText: {
    color: '#FEF3C7',
    fontSize: 12,
    fontWeight: '500',
    opacity: 0.9
  }
});

export default SearchTab;
