import React, { useState } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  StyleSheet
} from 'react-native'
import { router } from 'expo-router'
import {
  ArrowLeft,
  ArrowRight,
  MapPin,
  DollarSign,
  CheckCircle,
  Briefcase,
  Eye
} from 'lucide-react-native'
import LocationPicker from '../../components/LocationPicker'
import BudgetInput, { BudgetType } from '../../components/BudgetInput'
import AutoCompleteInput from '../../components/AutoCompleteInput'
import { jobService, JobFormData as ServiceJobFormData } from '../../services/jobService'

interface LocationData {
  latitude: number | null
  longitude: number | null
  address: string
  landmark: string
  accuracy?: number
  source: 'gps' | 'manual' | 'search'
}

interface BudgetData {
  type: BudgetType
  minAmount: string
  maxAmount: string
}

interface JobFormData {
  // Step 1: Basic Information
  title: string
  description: string
  skillCategory: string

  // Step 2: Location & Requirements
  location: LocationData
  requirements: string
  estimatedDurationHours: string
  preferredGender: 'any' | 'male' | 'female'
  minExperienceYears: string

  // Step 3: Budget & Urgency
  budget: BudgetData
  urgency: 'normal' | 'high' | 'urgent'
  jobType: 'one_time' | 'recurring' | 'permanent'
}

interface ValidationErrors {
  title?: string
  description?: string
  skillCategory?: string
  address?: string
  budgetMin?: string
  budgetMax?: string
}

interface StepConfig {
  title: string
  subtitle: string
  icon: React.ReactNode
}

const SKILL_CATEGORIES = [
  { value: 'electrical', label: 'Electrical Work', icon: '⚡' },
  { value: 'plumbing', label: 'Plumbing', icon: '🔧' },
  { value: 'carpentry', label: 'Carpentry', icon: '🔨' },
  { value: 'cooking', label: 'Cooking', icon: '👨‍🍳' },
  { value: 'cleaning', label: 'Cleaning', icon: '🧹' },
  { value: 'driving', label: 'Driving', icon: '🚗' },
  { value: 'delivery', label: 'Delivery', icon: '📦' },
  { value: 'security', label: 'Security', icon: '🛡️' },
  { value: 'gardening', label: 'Gardening', icon: '🌱' },
  { value: 'tutoring', label: 'Tutoring', icon: '📚' }
]

const URGENCY_LEVELS = [
  { value: 'normal', label: 'Normal', description: 'Standard timeline', color: '#10B981' },
  { value: 'high', label: 'High Priority', description: 'Needed soon', color: '#F59E0B' },
  { value: 'urgent', label: 'Urgent', description: 'Immediate need', color: '#EF4444', premium: true }
]

const PostJobScreen = () => {
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<ValidationErrors>({})

  const [formData, setFormData] = useState<JobFormData>({
    title: '',
    description: '',
    skillCategory: '',
    location: {
      latitude: null,
      longitude: null,
      address: '',
      landmark: '',
      source: 'manual'
    },
    requirements: '',
    estimatedDurationHours: '',
    preferredGender: 'any',
    minExperienceYears: '0',
    budget: {
      type: 'fixed',
      minAmount: '',
      maxAmount: ''
    },
    urgency: 'normal',
    jobType: 'one_time'
  })

  const stepConfigs: StepConfig[] = [
    {
      title: 'Job Details',
      subtitle: 'Tell us about the work you need done',
      icon: <Briefcase size={24} color="#3B82F6" />
    },
    {
      title: 'Location & Requirements',
      subtitle: 'Where and what kind of worker do you need?',
      icon: <MapPin size={24} color="#3B82F6" />
    },
    {
      title: 'Budget & Timeline',
      subtitle: 'Set your budget and urgency level',
      icon: <DollarSign size={24} color="#3B82F6" />
    }
  ]

  // Character count for description (max 500 as per US-003)
  const descriptionCharCount = formData.description.length
  const maxDescriptionLength = 500

  // Validation functions
  const validateStep1 = (): boolean => {
    const newErrors: ValidationErrors = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Job title is required'
    } else if (formData.title.length < 5) {
      newErrors.title = 'Job title must be at least 5 characters'
    } else if (formData.title.length > 200) {
      newErrors.title = 'Job title cannot exceed 200 characters'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Job description is required'
    } else if (formData.description.length < 20) {
      newErrors.description = 'Description must be at least 20 characters'
    } else if (formData.description.length > maxDescriptionLength) {
      newErrors.description = `Description cannot exceed ${maxDescriptionLength} characters`
    }

    if (!formData.skillCategory) {
      newErrors.skillCategory = 'Please select a skill category'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const validateStep2 = (): boolean => {
    const newErrors: ValidationErrors = {}

    if (!formData.location.address.trim()) {
      newErrors.address = 'Job location is required'
    } else if (formData.location.address.length < 10) {
      newErrors.address = 'Please provide a more detailed address'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const validateStep3 = (): boolean => {
    const newErrors: ValidationErrors = {}

    if (!formData.budget.minAmount.trim()) {
      newErrors.budgetMin = 'Minimum budget is required'
    } else {
      const minBudget = parseFloat(formData.budget.minAmount)
      if (isNaN(minBudget) || minBudget <= 0) {
        newErrors.budgetMin = 'Please enter a valid minimum budget'
      }
    }

    if (!formData.budget.maxAmount.trim()) {
      newErrors.budgetMax = 'Maximum budget is required'
    } else {
      const maxBudget = parseFloat(formData.budget.maxAmount)
      const minBudget = parseFloat(formData.budget.minAmount)
      if (isNaN(maxBudget) || maxBudget <= 0) {
        newErrors.budgetMax = 'Please enter a valid maximum budget'
      } else if (maxBudget < minBudget) {
        newErrors.budgetMax = 'Maximum budget must be greater than minimum budget'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Navigation functions
  const handleNext = () => {
    let isValid = false

    switch (currentStep) {
      case 1:
        isValid = validateStep1()
        break
      case 2:
        isValid = validateStep2()
        break
      case 3:
        isValid = validateStep3()
        break
    }

    if (isValid) {
      if (currentStep < 3) {
        setCurrentStep(currentStep + 1)
        setErrors({}) // Clear errors when moving to next step
      } else {
        handleSubmit()
      }
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
      setErrors({}) // Clear errors when going back
    } else {
      router.back()
    }
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    try {
      // Transform form data to service format
      const jobData: ServiceJobFormData = {
        title: formData.title,
        description: formData.description,
        skillCategory: formData.skillCategory,
        location: formData.location,
        budget: formData.budget,
        urgency: formData.urgency,
        requirements: formData.requirements,
        estimatedDurationHours: formData.estimatedDurationHours,
        preferredGender: formData.preferredGender,
        jobType: formData.jobType
      }

      // Submit job to backend API
      const result = await jobService.createJob(jobData)

      if (result.error) {
        Alert.alert(
          'Failed to Post Job',
          result.error,
          [{ text: 'OK' }]
        )
        return
      }

      Alert.alert(
        'Job Posted Successfully!',
        'Your job has been posted and will be visible to workers within 5 minutes.',
        [
          {
            text: 'View Jobs',
            onPress: () => router.replace('/(tabs)/jobs')
          }
        ]
      )
    } catch (error) {
      console.error('Submit job error:', error)
      Alert.alert(
        'Error',
        'Failed to post job. Please check your connection and try again.',
        [{ text: 'OK' }]
      )
    } finally {
      setIsLoading(false)
    }
  }

  // Note: Title suggestions are now handled by the AutoCompleteInput component

  // Step Progress Indicator
  const StepIndicator = () => (
    <View style={styles.stepIndicator}>
      {[1, 2, 3].map((step) => (
        <View key={step} style={styles.stepContainer}>
          <View style={[
            styles.stepCircle,
            currentStep >= step && styles.stepCircleActive,
            currentStep > step && styles.stepCircleComplete
          ]}>
            {currentStep > step ? (
              <CheckCircle size={16} color="white" />
            ) : (
              <Text style={[
                styles.stepNumber,
                currentStep >= step && styles.stepNumberActive
              ]}>
                {step}
              </Text>
            )}
          </View>
          {step < 3 && (
            <View style={[
              styles.stepLine,
              currentStep > step && styles.stepLineComplete
            ]} />
          )}
        </View>
      ))}
    </View>
  )

  // Step 1: Job Details
  const renderStep1 = () => (
    <ScrollView style={styles.stepContent} showsVerticalScrollIndicator={false}>
      <View style={styles.stepHeader}>
        <Briefcase size={32} color="#3B82F6" />
        <Text style={styles.stepTitle}>{stepConfigs[0].title}</Text>
        <Text style={styles.stepSubtitle}>{stepConfigs[0].subtitle}</Text>
      </View>

      {/* Job Title */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Job Title *</Text>
        <AutoCompleteInput
          value={formData.title}
          onChangeText={(text) => {
            setFormData(prev => ({ ...prev, title: text }))
            if (errors.title) setErrors(prev => ({ ...prev, title: undefined }))
          }}
          placeholder="e.g., Electrician for Home Wiring"
          maxLength={200}
          error={errors.title}
          skillCategory={formData.skillCategory}
          onSuggestionSelect={(suggestion) => {
            // Optional: Track suggestion selection for analytics
            console.log('Selected suggestion:', suggestion)
          }}
          showSuggestions={true}
          minCharsForSuggestions={2}
          maxSuggestions={5}
        />
      </View>

      {/* Skill Category */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Skill Category *</Text>
        <Text style={styles.helperText}>Choose the main skill required for this job</Text>
        <View style={styles.skillGrid}>
          {SKILL_CATEGORIES.map((skill) => (
            <TouchableOpacity
              key={skill.value}
              style={[
                styles.skillOption,
                formData.skillCategory === skill.value && styles.skillSelected,
                errors.skillCategory && styles.skillError
              ]}
              onPress={() => {
                setFormData(prev => ({ ...prev, skillCategory: skill.value }))
                if (errors.skillCategory) setErrors(prev => ({ ...prev, skillCategory: undefined }))
              }}
            >
              <Text style={styles.skillIcon}>{skill.icon}</Text>
              <Text style={[
                styles.skillLabel,
                formData.skillCategory === skill.value && styles.skillLabelSelected
              ]}>
                {skill.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        {errors.skillCategory && <Text style={styles.errorText}>{errors.skillCategory}</Text>}
      </View>

      {/* Job Description */}
      <View style={styles.inputGroup}>
        <View style={styles.labelRow}>
          <Text style={styles.label}>Job Description *</Text>
          <Text style={[
            styles.charCount,
            descriptionCharCount > maxDescriptionLength * 0.9 && styles.charCountWarning,
            descriptionCharCount >= maxDescriptionLength && styles.charCountError
          ]}>
            {descriptionCharCount}/{maxDescriptionLength}
          </Text>
        </View>
        <Text style={styles.helperText}>
          Describe what work needs to be done, when, and any specific requirements
        </Text>
        <TextInput
          style={[
            styles.textArea,
            errors.description && styles.inputError
          ]}
          placeholder="e.g., Need an electrician to install 3 ceiling fans in my home. Work should be completed by this weekend. All materials will be provided."
          value={formData.description}
          onChangeText={(text) => {
            if (text.length <= maxDescriptionLength) {
              setFormData(prev => ({ ...prev, description: text }))
              if (errors.description) setErrors(prev => ({ ...prev, description: undefined }))
            }
          }}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
        {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
      </View>
    </ScrollView>
  )

  // Step 2: Location & Requirements
  const renderStep2 = () => (
    <ScrollView style={styles.stepContent} showsVerticalScrollIndicator={false}>
      <View style={styles.stepHeader}>
        <MapPin size={32} color="#3B82F6" />
        <Text style={styles.stepTitle}>{stepConfigs[1].title}</Text>
        <Text style={styles.stepSubtitle}>{stepConfigs[1].subtitle}</Text>
      </View>

      {/* Job Location */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Job Location *</Text>
        <Text style={styles.helperText}>Where should the worker come to do the job?</Text>
        <LocationPicker
          value={formData.location}
          onChange={(location) => {
            setFormData(prev => ({ ...prev, location }))
            if (errors.address) setErrors(prev => ({ ...prev, address: undefined }))
          }}
          error={errors.address}
          placeholder="Enter full address or use GPS"
          required
        />
      </View>

      {/* Additional Requirements */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Additional Requirements (Optional)</Text>
        <Text style={styles.helperText}>Any specific skills, tools, or qualifications needed?</Text>
        <TextInput
          style={styles.textArea}
          placeholder="e.g., Must have own tools, experience with specific brands, safety certification required"
          value={formData.requirements}
          onChangeText={(text) => setFormData(prev => ({ ...prev, requirements: text }))}
          multiline
          numberOfLines={3}
          textAlignVertical="top"
          maxLength={500}
        />
      </View>

      {/* Estimated Duration */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Estimated Duration (Optional)</Text>
        <Text style={styles.helperText}>How many hours do you think this job will take?</Text>
        <TextInput
          style={styles.input}
          placeholder="e.g., 4"
          value={formData.estimatedDurationHours}
          onChangeText={(text) => setFormData(prev => ({ ...prev, estimatedDurationHours: text }))}
          keyboardType="numeric"
          maxLength={3}
        />
      </View>

      {/* Preferred Gender */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Worker Preference (Optional)</Text>
        <Text style={styles.helperText}>Do you have any preference for the worker?</Text>
        <View style={styles.genderOptions}>
          {[
            { value: 'any', label: 'No Preference' },
            { value: 'male', label: 'Male Worker' },
            { value: 'female', label: 'Female Worker' }
          ].map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.genderOption,
                formData.preferredGender === option.value && styles.genderSelected
              ]}
              onPress={() => setFormData(prev => ({ ...prev, preferredGender: option.value as any }))}
            >
              <Text style={[
                styles.genderLabel,
                formData.preferredGender === option.value && styles.genderLabelSelected
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </ScrollView>
  )

  // Step 3: Budget & Timeline
  const renderStep3 = () => (
    <ScrollView style={styles.stepContent} showsVerticalScrollIndicator={false}>
      <View style={styles.stepHeader}>
        <DollarSign size={32} color="#3B82F6" />
        <Text style={styles.stepTitle}>{stepConfigs[2].title}</Text>
        <Text style={styles.stepSubtitle}>{stepConfigs[2].subtitle}</Text>
      </View>

      {/* Budget Input */}
      <View style={styles.inputGroup}>
        <BudgetInput
          value={formData.budget}
          onChange={(budget) => {
            setFormData(prev => ({ ...prev, budget }))
            // Clear errors when budget changes
            if (errors.budgetMin || errors.budgetMax) {
              setErrors(prev => ({
                ...prev,
                budgetMin: undefined,
                budgetMax: undefined
              }))
            }
          }}
          skillCategory={formData.skillCategory}
          location={formData.location.address}
          error={{
            minAmount: errors.budgetMin,
            maxAmount: errors.budgetMax
          }}
          showMarketGuidance={true}
        />
      </View>

      {/* Urgency Level */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Urgency Level</Text>
        <Text style={styles.helperText}>How quickly do you need this job done?</Text>

        <View style={styles.urgencyContainer}>
          {URGENCY_LEVELS.map((level) => (
            <TouchableOpacity
              key={level.value}
              style={[
                styles.urgencyOption,
                formData.urgency === level.value && styles.urgencySelected,
                { borderColor: level.color }
              ]}
              onPress={() => setFormData(prev => ({ ...prev, urgency: level.value as any }))}
            >
              <View style={styles.urgencyHeader}>
                <Text style={[
                  styles.urgencyLabel,
                  formData.urgency === level.value && { color: level.color }
                ]}>
                  {level.label}
                </Text>
                {level.premium && (
                  <View style={styles.premiumBadge}>
                    <Text style={styles.premiumText}>Premium</Text>
                  </View>
                )}
              </View>
              <Text style={styles.urgencyDescription}>{level.description}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </ScrollView>
  )

  // Main render
  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="#374151" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Post a Job</Text>
          <TouchableOpacity style={styles.previewButton}>
            <Eye size={20} color="#3B82F6" />
          </TouchableOpacity>
        </View>

        {/* Step Indicator */}
        <StepIndicator />

        {/* Step Content */}
        <View style={styles.content}>
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
        </View>

        {/* Navigation Buttons */}
        <View style={styles.navigationContainer}>
          <TouchableOpacity
            style={[styles.navButton, styles.backNavButton]}
            onPress={handleBack}
          >
            <ArrowLeft size={20} color="#6B7280" />
            <Text style={styles.backNavText}>
              {currentStep === 1 ? 'Cancel' : 'Back'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, styles.nextNavButton]}
            onPress={handleNext}
            disabled={isLoading}
          >
            <Text style={styles.nextNavText}>
              {currentStep === 3 ? 'Post Job' : 'Next'}
            </Text>
            <ArrowRight size={20} color="white" />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827'
  },
  previewButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#EBF4FF'
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    backgroundColor: 'white',
    marginBottom: 1
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E5E7EB',
    alignItems: 'center',
    justifyContent: 'center'
  },
  stepCircleActive: {
    backgroundColor: '#3B82F6'
  },
  stepCircleComplete: {
    backgroundColor: '#10B981'
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280'
  },
  stepNumberActive: {
    color: 'white'
  },
  stepLine: {
    width: 40,
    height: 2,
    backgroundColor: '#E5E7EB',
    marginHorizontal: 8
  },
  stepLineComplete: {
    backgroundColor: '#10B981'
  },
  content: {
    flex: 1
  },
  stepContent: {
    flex: 1,
    backgroundColor: 'white'
  },
  stepHeader: {
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6'
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginTop: 12,
    marginBottom: 4
  },
  stepSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center'
  },
  inputGroup: {
    paddingHorizontal: 20,
    paddingVertical: 16
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 4
  },
  helperText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: 'white'
  },
  inputError: {
    borderColor: '#EF4444'
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: 'white',
    minHeight: 100
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444',
    marginTop: 4
  },
  labelRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4
  },
  charCount: {
    fontSize: 12,
    color: '#6B7280'
  },
  charCountWarning: {
    color: '#F59E0B'
  },
  charCountError: {
    color: '#EF4444'
  },
  skillGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12
  },
  skillOption: {
    width: '47%',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    backgroundColor: 'white'
  },
  skillSelected: {
    borderColor: '#3B82F6',
    backgroundColor: '#EBF4FF'
  },
  skillError: {
    borderColor: '#EF4444'
  },
  skillIcon: {
    fontSize: 24,
    marginBottom: 8
  },
  skillLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    textAlign: 'center'
  },
  skillLabelSelected: {
    color: '#3B82F6'
  },
  suggestionsContainer: {
    marginTop: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: 'white'
  },
  suggestionItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6'
  },
  suggestionText: {
    fontSize: 14,
    color: '#374151'
  },
  budgetRow: {
    flexDirection: 'row',
    gap: 16
  },
  budgetInput: {
    flex: 1
  },
  budgetLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8
  },
  currencyInput: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: 'white'
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
    paddingLeft: 16
  },
  budgetField: {
    flex: 1,
    paddingHorizontal: 8,
    paddingVertical: 12,
    fontSize: 16
  },
  urgencyContainer: {
    gap: 12
  },
  urgencyOption: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    backgroundColor: 'white'
  },
  urgencySelected: {
    backgroundColor: '#F0F9FF'
  },
  urgencyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4
  },
  urgencyLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151'
  },
  urgencyDescription: {
    fontSize: 14,
    color: '#6B7280'
  },
  premiumBadge: {
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12
  },
  premiumText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#D97706'
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB'
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8
  },
  backNavButton: {
    backgroundColor: '#F3F4F6'
  },
  nextNavButton: {
    backgroundColor: '#3B82F6'
  },
  backNavText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280'
  },
  nextNavText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white'
  },
  genderOptions: {
    flexDirection: 'row',
    gap: 12
  },
  genderOption: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    backgroundColor: 'white'
  },
  genderSelected: {
    borderColor: '#3B82F6',
    backgroundColor: '#EBF4FF'
  },
  genderLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151'
  },
  genderLabelSelected: {
    color: '#3B82F6'
  }
})

export default PostJobScreen