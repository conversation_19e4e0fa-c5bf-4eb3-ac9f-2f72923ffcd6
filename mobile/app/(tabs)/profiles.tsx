import React, { useState } from "react";
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, SafeAreaView } from "react-native";
import PersonaCard from "../../components/PersonaCard";
import { WorkerPersona } from "../../types";

// Sample data
const samplePersonas: WorkerPersona[] = [
    { id: "1", user_id: "user1", title: "Rajesh - Master Electrician", skill_category: "electrical", skill_subcategories: ["Home Wiring", "Repairs"], description: "15+ years experienced electrician specializing in residential electrical work.", experience_years: 15, hourly_rate: 200, daily_rate: 1500, is_rate_negotiable: true, availability_pattern: { monday: [], tuesday: [], wednesday: [], thursday: [], friday: [], saturday: [], sunday: [] }, travel_radius_km: 10, is_active: true, total_jobs_completed: 156, average_rating: 4.7, created_at: "2024-01-10T08:00:00Z", updated_at: "2024-01-15T12:00:00Z" }
];

const ProfilesTab = () => {
    const [selectedPersona, setSelectedPersona] = useState<string | null>(null);

    return (
        <SafeAreaView style={styles.container}>
            <ScrollView style={styles.tabContent}>
                <View style={styles.pageHeader}>
                    <Text style={styles.pageTitle}>Your Worker Personas</Text>
                    <Text style={styles.pageSubtitle}>Manage your professional profiles</Text>
                </View>
                <View style={styles.listContainer}>
                    <TouchableOpacity style={styles.primaryButton}>
                        <Text style={styles.primaryButtonText}>Create New Persona</Text>
                    </TouchableOpacity>
                    {samplePersonas.map(p => 
                        <PersonaCard 
                            key={p.id} 
                            persona={p} 
                            isActive={selectedPersona === p.id} 
                            onClick={() => setSelectedPersona(p.id)} 
                        />
                    )}
                </View>
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: '#F3F4F6' },
    tabContent: { flex: 1 },
    pageHeader: { backgroundColor: 'white', padding: 24, borderBottomWidth: 1, borderBottomColor: '#E5E7EB' },
    pageTitle: { fontSize: 24, fontWeight: 'bold' },
    pageSubtitle: { color: '#6B7280', marginTop: 4 },
    listContainer: { paddingHorizontal: 16, gap: 16, paddingBottom: 16, paddingTop: 16 },
    primaryButton: { backgroundColor: '#059669', padding: 12, borderRadius: 8, alignItems: 'center' },
    primaryButtonText: { color: 'white', fontWeight: 'bold' },
});

export default ProfilesTab;
