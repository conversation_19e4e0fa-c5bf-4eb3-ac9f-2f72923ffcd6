import React from 'react'
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StyleSheet,
  Alert
} from 'react-native'
import { router } from 'expo-router'
import {
  ArrowLeft,
  Edit3,
  MapPin,
  Clock,
  DollarSign,
  Calendar,
  User,
  AlertCircle,
  CheckCircle,
  Send,
  Eye
} from 'lucide-react-native'

interface JobPreviewData {
  title: string
  description: string
  skillCategory: string
  location: {
    address: string
    landmark: string
  }
  budget: {
    type: 'fixed' | 'hourly' | 'daily'
    minAmount: string
    maxAmount: string
  }
  urgency: 'normal' | 'high' | 'urgent'
  requirements: string
  estimatedDurationHours: string
  preferredGender: 'any' | 'male' | 'female'
}

const JobPreviewScreen = () => {
  // const params = useLocalSearchParams() // TODO: Use for real job data

  // In a real implementation, this would come from navigation params or state
  // For now, we'll use mock data
  const jobData: JobPreviewData = {
    title: 'Electrician for Home Wiring',
    description: 'Need an experienced electrician to install 3 ceiling fans and fix some electrical outlets in my home. Work should be completed by this weekend. All materials will be provided.',
    skillCategory: 'electrical',
    location: {
      address: 'Sector 15, Gurgaon, Haryana',
      landmark: 'Near Metro Station'
    },
    budget: {
      type: 'fixed',
      minAmount: '1200',
      maxAmount: '1800'
    },
    urgency: 'normal',
    requirements: 'Must have own tools and safety equipment. Experience with ceiling fan installation required.',
    estimatedDurationHours: '4',
    preferredGender: 'any'
  }

  const handlePublishJob = () => {
    Alert.alert(
      'Publish Job?',
      'Your job will be visible to workers within 5 minutes. You can edit or delete it later if needed.',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Publish Job',
          onPress: () => {
            // TODO: Submit job to backend
            Alert.alert(
              'Job Published!',
              'Your job has been posted successfully. Workers will start seeing it within 5 minutes.',
              [
                {
                  text: 'View My Jobs',
                  onPress: () => router.replace('/(tabs)/jobs')
                }
              ]
            )
          }
        }
      ]
    )
  }

  const handleEditJob = () => {
    router.back()
  }

  const formatBudget = (budget: JobPreviewData['budget']): string => {
    const min = parseInt(budget.minAmount)
    const max = parseInt(budget.maxAmount)
    const suffix = budget.type === 'hourly' ? '/hour' : budget.type === 'daily' ? '/day' : ''

    if (min === max) {
      return `₹${min.toLocaleString('en-IN')}${suffix}`
    } else {
      return `₹${min.toLocaleString('en-IN')} - ₹${max.toLocaleString('en-IN')}${suffix}`
    }
  }

  const getUrgencyColor = (urgency: string): string => {
    switch (urgency) {
      case 'urgent': return '#EF4444'
      case 'high': return '#F59E0B'
      default: return '#10B981'
    }
  }

  const getUrgencyLabel = (urgency: string): string => {
    switch (urgency) {
      case 'urgent': return 'URGENT'
      case 'high': return 'HIGH PRIORITY'
      default: return 'NORMAL'
    }
  }

  const getSkillCategoryIcon = (category: string): string => {
    const icons: { [key: string]: string } = {
      'electrical': '⚡',
      'plumbing': '🔧',
      'carpentry': '🔨',
      'cooking': '👨‍🍳',
      'cleaning': '🧹',
      'driving': '🚗',
      'delivery': '📦',
      'security': '🛡️',
      'gardening': '🌱',
      'tutoring': '📚'
    }
    return icons[category] || '🔧'
  }

  const getSkillCategoryLabel = (category: string): string => {
    const labels: { [key: string]: string } = {
      'electrical': 'Electrical Work',
      'plumbing': 'Plumbing',
      'carpentry': 'Carpentry',
      'cooking': 'Cooking',
      'cleaning': 'Cleaning',
      'driving': 'Driving',
      'delivery': 'Delivery',
      'security': 'Security',
      'gardening': 'Gardening',
      'tutoring': 'Tutoring'
    }
    return labels[category] || category
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleEditJob}>
          <ArrowLeft size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Job Preview</Text>
        <TouchableOpacity style={styles.editButton} onPress={handleEditJob}>
          <Edit3 size={20} color="#3B82F6" />
        </TouchableOpacity>
      </View>

      {/* Preview Notice */}
      <View style={styles.previewNotice}>
        <Eye size={16} color="#3B82F6" />
        <Text style={styles.previewText}>This is how your job will appear to workers</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Job Card - Worker View */}
        <View style={styles.jobCard}>
          {/* Job Header */}
          <View style={styles.jobHeader}>
            <View style={styles.jobTitleRow}>
              <Text style={styles.jobTitle}>{jobData.title}</Text>
              <View style={[
                styles.urgencyBadge,
                { backgroundColor: getUrgencyColor(jobData.urgency) }
              ]}>
                <Text style={styles.urgencyText}>{getUrgencyLabel(jobData.urgency)}</Text>
              </View>
            </View>

            <View style={styles.categoryRow}>
              <Text style={styles.categoryIcon}>{getSkillCategoryIcon(jobData.skillCategory)}</Text>
              <Text style={styles.categoryLabel}>{getSkillCategoryLabel(jobData.skillCategory)}</Text>
            </View>
          </View>

          {/* Budget */}
          <View style={styles.budgetSection}>
            <DollarSign size={20} color="#10B981" />
            <Text style={styles.budgetAmount}>{formatBudget(jobData.budget)}</Text>
            <Text style={styles.budgetType}>
              {jobData.budget.type === 'fixed' ? 'Fixed Amount' :
               jobData.budget.type === 'hourly' ? 'Per Hour' : 'Per Day'}
            </Text>
          </View>

          {/* Location */}
          <View style={styles.locationSection}>
            <MapPin size={18} color="#6B7280" />
            <View style={styles.locationText}>
              <Text style={styles.locationAddress}>{jobData.location.address}</Text>
              {jobData.location.landmark && (
                <Text style={styles.locationLandmark}>{jobData.location.landmark}</Text>
              )}
            </View>
          </View>

          {/* Description */}
          <View style={styles.descriptionSection}>
            <Text style={styles.sectionTitle}>Job Description</Text>
            <Text style={styles.descriptionText}>{jobData.description}</Text>
          </View>

          {/* Requirements */}
          {jobData.requirements && (
            <View style={styles.requirementsSection}>
              <Text style={styles.sectionTitle}>Requirements</Text>
              <Text style={styles.requirementsText}>{jobData.requirements}</Text>
            </View>
          )}

          {/* Job Details */}
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>Job Details</Text>

            {jobData.estimatedDurationHours && (
              <View style={styles.detailRow}>
                <Clock size={16} color="#6B7280" />
                <Text style={styles.detailLabel}>Estimated Duration:</Text>
                <Text style={styles.detailValue}>{jobData.estimatedDurationHours} hours</Text>
              </View>
            )}

            {jobData.preferredGender !== 'any' && (
              <View style={styles.detailRow}>
                <User size={16} color="#6B7280" />
                <Text style={styles.detailLabel}>Worker Preference:</Text>
                <Text style={styles.detailValue}>
                  {jobData.preferredGender === 'male' ? 'Male Worker' : 'Female Worker'}
                </Text>
              </View>
            )}

            <View style={styles.detailRow}>
              <Calendar size={16} color="#6B7280" />
              <Text style={styles.detailLabel}>Posted:</Text>
              <Text style={styles.detailValue}>Just now</Text>
            </View>
          </View>

          {/* Worker Action Buttons (Preview) */}
          <View style={styles.workerActions}>
            <TouchableOpacity style={styles.applyButton} disabled>
              <Text style={styles.applyButtonText}>Apply for Job</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.contactButton} disabled>
              <Text style={styles.contactButtonText}>Contact Employer</Text>
            </TouchableOpacity>
          </View>

          {/* Preview Disclaimer */}
          <View style={styles.previewDisclaimer}>
            <AlertCircle size={14} color="#6B7280" />
            <Text style={styles.disclaimerText}>
              Preview only - Workers will see actual contact options after you publish
            </Text>
          </View>
        </View>

        {/* Publishing Guidelines */}
        <View style={styles.guidelinesCard}>
          <Text style={styles.guidelinesTitle}>Before Publishing</Text>
          <View style={styles.checklistItem}>
            <CheckCircle size={16} color="#10B981" />
            <Text style={styles.checklistText}>Job title is clear and specific</Text>
          </View>
          <View style={styles.checklistItem}>
            <CheckCircle size={16} color="#10B981" />
            <Text style={styles.checklistText}>Description includes all necessary details</Text>
          </View>
          <View style={styles.checklistItem}>
            <CheckCircle size={16} color="#10B981" />
            <Text style={styles.checklistText}>Budget is competitive for your area</Text>
          </View>
          <View style={styles.checklistItem}>
            <CheckCircle size={16} color="#10B981" />
            <Text style={styles.checklistText}>Location is accurate and detailed</Text>
          </View>
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.editActionButton} onPress={handleEditJob}>
          <Edit3 size={20} color="#6B7280" />
          <Text style={styles.editActionText}>Edit Job</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.publishButton} onPress={handlePublishJob}>
          <Send size={20} color="white" />
          <Text style={styles.publishButtonText}>Publish Job</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827'
  },
  editButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#EBF4FF'
  },
  previewNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    backgroundColor: '#EFF6FF',
    borderBottomWidth: 1,
    borderBottomColor: '#DBEAFE',
    gap: 8
  },
  previewText: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500'
  },
  content: {
    flex: 1,
    paddingHorizontal: 20
  },
  jobCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  jobHeader: {
    marginBottom: 16
  },
  jobTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8
  },
  jobTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    flex: 1,
    marginRight: 12
  },
  urgencyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12
  },
  urgencyText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white'
  },
  categoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  categoryIcon: {
    fontSize: 18
  },
  categoryLabel: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500'
  },
  budgetSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0FDF4',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 16,
    gap: 8
  },
  budgetAmount: {
    fontSize: 18,
    fontWeight: '600',
    color: '#059669'
  },
  budgetType: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 'auto'
  },
  locationSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    gap: 8
  },
  locationText: {
    flex: 1
  },
  locationAddress: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '500'
  },
  locationLandmark: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2
  },
  descriptionSection: {
    marginBottom: 16
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8
  },
  descriptionText: {
    fontSize: 15,
    color: '#4B5563',
    lineHeight: 22
  },
  requirementsSection: {
    marginBottom: 16
  },
  requirementsText: {
    fontSize: 15,
    color: '#4B5563',
    lineHeight: 22
  },
  detailsSection: {
    marginBottom: 20
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8
  },
  detailLabel: {
    fontSize: 14,
    color: '#6B7280',
    flex: 1
  },
  detailValue: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500'
  },
  workerActions: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12
  },
  applyButton: {
    flex: 1,
    backgroundColor: '#E5E7EB',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center'
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#9CA3AF'
  },
  contactButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  contactButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#9CA3AF'
  },
  previewDisclaimer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6'
  },
  disclaimerText: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center'
  },
  guidelinesCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  guidelinesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16
  },
  checklistItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12
  },
  checklistText: {
    fontSize: 14,
    color: '#4B5563',
    flex: 1
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12
  },
  editActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    gap: 8
  },
  editActionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280'
  },
  publishButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 8,
    backgroundColor: '#3B82F6',
    gap: 8
  },
  publishButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white'
  }
})

export default JobPreviewScreen
