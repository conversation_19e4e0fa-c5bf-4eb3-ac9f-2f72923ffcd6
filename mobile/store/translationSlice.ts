import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { LanguageMetadata } from '../lib/i18n';
import type { Slice } from '@reduxjs/toolkit';

export interface TranslationState {
  currentLanguage: string;
  availableLanguages: LanguageMetadata[];
  isLoading: boolean;
}

const initialState: TranslationState = {
  currentLanguage: 'en',
  availableLanguages: [],
  isLoading: true,
};

export const translationSlice: Slice<TranslationState> = createSlice({
  name: 'translation',
  initialState,
  reducers: {
    setCurrentLanguage: (state, action: PayloadAction<string>) => {
      state.currentLanguage = action.payload;
    },
    setAvailableLanguages: (state, action: PayloadAction<LanguageMetadata[]>) => {
      state.availableLanguages = action.payload;
    },
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    initializeTranslation: (state, action: PayloadAction<{
      currentLanguage: string;
      availableLanguages: LanguageMetadata[];
    }>) => {
      state.currentLanguage = action.payload.currentLanguage;
      state.availableLanguages = action.payload.availableLanguages;
      state.isLoading = false;
    },
  },
});

export const { setCurrentLanguage, setAvailableLanguages, setIsLoading, initializeTranslation } = translationSlice.actions;

export default translationSlice.reducer;