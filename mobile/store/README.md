# Redux Toolkit Setup

This document explains how Redux Toolkit has been implemented in the Ozgaar mobile application.

## Overview

Redux Toolkit has been integrated to manage global state in the application, replacing parts of the Context API for better performance and scalability.

## Store Structure

The Redux store is configured with the following slices:

1. **Auth Slice** - Manages authentication state (user, session, loading)
2. **Translation Slice** - Manages language and translation state
3. **User Slice** - Manages user UI state (view mode, selected persona, preferences)

## Implementation Details

### Store Configuration

The store is configured in `store/index.ts` with:
- `auth` reducer for authentication state
- `translation` reducer for language state
- `user` reducer for user UI state
- Redux Persist configuration for state persistence
- Middleware configuration to handle non-serializable values

### Auth Slice

The auth slice manages:
- `session` - User session data
- `user` - Current user object
- `loading` - Loading state

Actions include:
- `setSession` - Set the current session
- `setUser` - Set the current user
- `setLoading` - Set loading state
- `signOut` - Clear auth state

### Translation Slice

The translation slice manages:
- `currentLanguage` - Currently selected language
- `availableLanguages` - List of available languages
- `isLoading` - Loading state for translations

Actions include:
- `setCurrentLanguage` - Set the current language
- `setAvailableLanguages` - Set available languages
- `setIsLoading` - Set loading state
- `initializeTranslation` - Initialize translation state

### User Slice

The user slice manages UI-related user state:
- `viewMode` - Current view mode ('worker', 'poster', 'both', or null)
- `selectedPersona` - ID of the currently selected worker persona
- `preferences` - User preferences for notifications, privacy, and UI

Actions include:
- `setViewMode` - Set the current view mode
- `setSelectedPersona` - Set the selected worker persona
- `setNotificationPreferences` - Update notification preferences
- `setPrivacyPreferences` - Update privacy preferences
- `setUIPreferences` - Update UI preferences
- `setPreferences` - Update all preferences at once
- `resetUserState` - Reset user state to initial values

## Redux Persist

Redux Persist has been configured to persist user state between app sessions:
- Auth state: User data is persisted (but not session data for security)
- Translation state: Current language is persisted
- User state: View mode, selected persona, and preferences are persisted

## Usage

To use Redux state in components:

```typescript
import { useAppSelector, useAppDispatch } from '../hooks/redux';

// Select state
const { user, loading } = useAppSelector(state => state.auth);

// Dispatch actions
const dispatch = useAppDispatch();
dispatch(setUser(updatedUser));
```

To use the user state:

```typescript
import { useUserState } from '../hooks/useUserState';

// Use the custom hook
const { viewMode, selectedPersona, setViewMode, setSelectedPersona } = useUserState();
```

## Migration from Context API

The Context API providers (`AuthProvider` and `TranslationProvider`) have been updated to use Redux for state management while maintaining the same API interface. This allows for a gradual migration without breaking existing components.

## Benefits

1. **Improved Performance** - Redux Toolkit uses Immer for efficient immutable updates
2. **Better Debugging** - Redux DevTools integration for state inspection
3. **Scalability** - Easier to manage complex state as the application grows
4. **Middleware Support** - Built-in support for async operations and side effects
5. **State Persistence** - User preferences and state persist between app sessions