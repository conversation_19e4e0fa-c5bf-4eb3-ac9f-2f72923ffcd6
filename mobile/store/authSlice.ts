import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { User } from '@ozgaar/types';
import type { Slice } from '@reduxjs/toolkit';

export interface AuthState {
  session: {
    user: User | null;
  } | null;
  user: User | null;
  loading: boolean;
}

const initialState: AuthState = {
  session: null,
  user: null,
  loading: true,
};

export const authSlice: Slice<AuthState> = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setSession: (state, action: PayloadAction<AuthState['session']>) => {
      state.session = action.payload;
      state.user = action.payload?.user || null;
    },
    setUser: (state, action: PayloadAction<User | null>) => {
      state.user = action.payload;
      if (state.session) {
        state.session.user = action.payload;
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    signOut: (state) => {
      state.session = null;
      state.user = null;
      state.loading = false;
    },
  },
});

export const { setSession, setUser, setLoading, signOut } = authSlice.actions;

export default authSlice.reducer;