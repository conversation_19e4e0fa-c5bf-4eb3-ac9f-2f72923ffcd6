import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { Slice } from '@reduxjs/toolkit';

// Define the user state structure
export interface UserState {
  viewMode: 'worker' | 'poster' | null;
  selectedPersona: string | null; // ID of the selected worker persona
  preferences: {
    notifications: {
      jobApplications: boolean;
      jobInvites: boolean;
      messages: boolean;
      updates: boolean;
    };
    privacy: {
      profileVisibility: 'public' | 'connections' | 'private';
      showContactInfo: boolean;
    };
    ui: {
      theme: 'light' | 'dark' | 'system';
      language: string;
    };
  };
  // Add other UI-related user state here
}

const initialState: UserState = {
  viewMode: null,
  selectedPersona: null,
  preferences: {
    notifications: {
      jobApplications: true,
      jobInvites: true,
      messages: true,
      updates: true,
    },
    privacy: {
      profileVisibility: 'public',
      showContactInfo: true,
    },
    ui: {
      theme: 'system',
      language: 'en',
    },
  },
};

export const userSlice: Slice<UserState> = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // Set the user's view mode (worker, poster, or null)
    setViewMode: (state, action: PayloadAction<'worker' | 'poster' | null>) => {
      state.viewMode = action.payload;
    },
    
    // Set the selected worker persona
    setSelectedPersona: (state, action: PayloadAction<string | null>) => {
      state.selectedPersona = action.payload;
    },
    
    // Update notification preferences
    setNotificationPreferences: (state, action: PayloadAction<Partial<UserState['preferences']['notifications']>>) => {
      state.preferences.notifications = {
        ...state.preferences.notifications,
        ...action.payload,
      };
    },
    
    // Update privacy preferences
    setPrivacyPreferences: (state, action: PayloadAction<Partial<UserState['preferences']['privacy']>>) => {
      state.preferences.privacy = {
        ...state.preferences.privacy,
        ...action.payload,
      };
    },
    
    // Update UI preferences
    setUIPreferences: (state, action: PayloadAction<Partial<UserState['preferences']['ui']>>) => {
      state.preferences.ui = {
        ...state.preferences.ui,
        ...action.payload,
      };
    },
    
    // Update all preferences at once
    setPreferences: (state, action: PayloadAction<Partial<UserState['preferences']>>) => {
      state.preferences = {
        ...state.preferences,
        ...action.payload,
      };
    },
    
    // Reset user state to initial values
    resetUserState: (state) => {
      state.viewMode = null;
      state.selectedPersona = null;
      state.preferences = initialState.preferences;
    },
  },
});

export const {
  setViewMode,
  setSelectedPersona,
  setNotificationPreferences,
  setPrivacyPreferences,
  setUIPreferences,
  setPreferences,
  resetUserState,
} = userSlice.actions;

export default userSlice.reducer;