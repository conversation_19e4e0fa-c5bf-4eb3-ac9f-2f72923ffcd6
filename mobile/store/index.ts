import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import authReducer from './authSlice';
import translationReducer from './translationSlice';
import userReducer from './userSlice';

// Configure persist for each reducer
const authPersistConfig = {
  key: 'auth',
  storage: AsyncStorage,
  whitelist: ['user'] // Only persist the user data, not the session which contains sensitive data
};

const translationPersistConfig = {
  key: 'translation',
  storage: AsyncStorage,
  whitelist: ['currentLanguage'] // Only persist the current language
};

const userPersistConfig = {
  key: 'user',
  storage: AsyncStorage,
  whitelist: ['viewMode', 'selectedPersona', 'preferences'] // Persist user UI preferences
};

// Create persisted reducers
const persistedAuthReducer = persistReducer(authPersistConfig, authReducer);
const persistedTranslationReducer = persistReducer(translationPersistConfig, translationReducer);
const persistedUserReducer = persistReducer(userPersistConfig, userReducer);

export const store = configureStore({
  reducer: {
    auth: persistedAuthReducer,
    translation: persistedTranslationReducer,
    user: persistedUserReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {auth: AuthState, translation: TranslationState, user: UserState}
export type AppDispatch = typeof store.dispatch;

export const persistor = persistStore(store);

export default store;