import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { Search, Check } from 'lucide-react-native';
import { apiClient } from '@/lib/supabase';

interface SkillCategory {
  id: string;
  code: string;
  name: string;
  description?: string;
}

interface SkillSelectorProps {
  selectedSkills: string[];
  onSkillsChange: (skills: string[]) => void;
  error?: string;
}

const SkillSelector: React.FC<SkillSelectorProps> = ({
  selectedSkills,
  onSkillsChange,
  error,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [availableSkills, setAvailableSkills] = useState<SkillCategory[]>([]);
  const [filteredSkills, setFilteredSkills] = useState<SkillCategory[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isModalVisible && availableSkills.length === 0) {
      fetchSkills();
    }
  }, [isModalVisible]);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredSkills(availableSkills);
    } else {
      const filtered = availableSkills.filter(skill =>
        skill.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (skill.description && skill.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredSkills(filtered);
    }
  }, [searchQuery, availableSkills]);

  const fetchSkills = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would fetch from your API
      // For now, we'll use mock data
      const mockSkills: SkillCategory[] = [
        { id: '1', code: 'electrical', name: 'Electrical Work', description: 'Wiring, repairs, installations' },
        { id: '2', code: 'plumbing', name: 'Plumbing', description: 'Pipes, fittings, water systems' },
        { id: '3', code: 'carpentry', name: 'Carpentry', description: 'Wood work, furniture, repairs' },
        { id: '4', code: 'cooking', name: 'Cooking', description: 'Chef, home cooking, catering' },
        { id: '5', code: 'cleaning', name: 'Cleaning', description: 'House cleaning, office cleaning' },
        { id: '6', code: 'driving', name: 'Driving', description: 'Personal driver, delivery driver' },
        { id: '7', code: 'delivery', name: 'Delivery', description: 'Package delivery, food delivery' },
        { id: '8', code: 'security', name: 'Security', description: 'Security guard, watchman' },
        { id: '9', code: 'gardening', name: 'Gardening', description: 'Garden maintenance, landscaping' },
        { id: '10', code: 'tutoring', name: 'Tutoring', description: 'Teaching, home tutoring' },
      ];
      
      setAvailableSkills(mockSkills);
      setFilteredSkills(mockSkills);
    } catch (error) {
      console.error('Error fetching skills:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleSkill = (skillCode: string) => {
    if (selectedSkills.includes(skillCode)) {
      onSkillsChange(selectedSkills.filter(skill => skill !== skillCode));
    } else {
      onSkillsChange([...selectedSkills, skillCode]);
    }
  };

  const isSelected = (skillCode: string) => {
    return selectedSkills.includes(skillCode);
  };

  return (
    <View>
      <TouchableOpacity
        style={[styles.selectorButton, error && styles.selectorButtonError]}
        onPress={() => setIsModalVisible(true)}
      >
        <Text style={styles.selectorButtonText}>
          {selectedSkills.length > 0 
            ? `${selectedSkills.length} skill${selectedSkills.length > 1 ? 's' : ''} selected` 
            : 'Select skills'}
        </Text>
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Skills</Text>
            <TouchableOpacity 
              style={styles.closeButton}
              onPress={() => setIsModalVisible(false)}
            >
              <Text style={styles.closeButtonText}>Done</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <Search size={20} color="#6B7280" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search skills..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus
            />
          </View>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#059669" />
              <Text style={styles.loadingText}>Loading skills...</Text>
            </View>
          ) : (
            <ScrollView style={styles.skillsList}>
              {filteredSkills.map((skill) => (
                <TouchableOpacity
                  key={skill.code}
                  style={styles.skillItem}
                  onPress={() => toggleSkill(skill.code)}
                >
                  <View style={styles.skillInfo}>
                    <Text style={styles.skillName}>{skill.name}</Text>
                    {skill.description && (
                      <Text style={styles.skillDescription}>{skill.description}</Text>
                    )}
                  </View>
                  {isSelected(skill.code) && (
                    <View style={styles.checkContainer}>
                      <Check size={20} color="#059669" />
                    </View>
                  )}
                </TouchableOpacity>
              ))}
              
              {filteredSkills.length === 0 && (
                <View style={styles.emptyState}>
                  <Text style={styles.emptyStateText}>No skills found</Text>
                </View>
              )}
            </ScrollView>
          )}
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  selectorButton: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  selectorButtonError: {
    borderColor: '#EF4444',
  },
  selectorButtonText: {
    fontSize: 16,
    color: '#374151',
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 16,
    color: '#059669',
    fontWeight: '500',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    margin: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  skillsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  skillItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  skillInfo: {
    flex: 1,
  },
  skillName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 4,
  },
  skillDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  checkContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#ECFDF5',
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#6B7280',
  },
});

export default SkillSelector;