import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { 
  Wifi, 
  MapPin, 
  Filter, 
  RefreshCw, 
  Search, 
  AlertCircle,
  WifiOff,
  Briefcase
} from 'lucide-react-native';

interface EmptyStateProps {
  type: 'no_jobs' | 'no_network' | 'location_error' | 'filter_no_results' | 'search_no_results';
  title?: string;
  message?: string;
  primaryAction?: {
    label: string;
    onPress: () => void;
    icon?: React.ReactNode;
  };
  secondaryAction?: {
    label: string;
    onPress: () => void;
  };
  // Context-specific props
  currentRadius?: number;
  hasLocation?: boolean;
  activeFilters?: {
    urgency?: string | null;
    skillCategory?: string | null;
    mySkillsOnly?: boolean;
  };
  searchQuery?: string;
}

const EmptyStateIcon: React.FC<{ type: EmptyStateProps['type'] }> = ({ type }) => {
  const iconProps = { size: 48, color: '#6B7280' };
  
  switch (type) {
    case 'no_network':
      return <WifiOff {...iconProps} color="#EF4444" />;
    case 'location_error':
      return <AlertCircle {...iconProps} color="#F59E0B" />;
    case 'filter_no_results':
      return <Filter {...iconProps} />;
    case 'search_no_results':
      return <Search {...iconProps} />;
    default:
      return <Briefcase {...iconProps} />;
  }
};

const EnhancedEmptyState: React.FC<EmptyStateProps> = ({
  type,
  title,
  message,
  primaryAction,
  secondaryAction,
  currentRadius = 25,
  hasLocation = false,
  activeFilters = {},
  searchQuery
}) => {
  const getDefaultContent = () => {
    switch (type) {
      case 'no_network':
        return {
          title: 'No Internet Connection',
          message: 'Please check your internet connection and try again. Jobs will be loaded automatically when connection is restored.'
        };
      
      case 'location_error':
        return {
          title: 'Location Access Required',
          message: 'We need location access to show you nearby jobs. Please enable location services in your device settings.'
        };
      
      case 'filter_no_results':
        const filterDescriptions = [];
        if (activeFilters.urgency) {
          filterDescriptions.push(`${activeFilters.urgency} priority jobs`);
        }
        if (activeFilters.mySkillsOnly) {
          filterDescriptions.push('jobs matching your skills');
        }
        if (activeFilters.skillCategory) {
          filterDescriptions.push(`${activeFilters.skillCategory} jobs`);
        }
        
        return {
          title: 'No Jobs Match Your Filters',
          message: `No ${filterDescriptions.join(' and ')} found${hasLocation ? ` within ${currentRadius}km` : ''}. Try adjusting your filters or expanding your search area.`
        };
      
      case 'search_no_results':
        return {
          title: 'No Search Results',
          message: `No jobs found for "${searchQuery}". Try different keywords or check your spelling.`
        };
      
      default: // 'no_jobs'
        return {
          title: hasLocation ? 'No Jobs Near You' : 'No Jobs Available',
          message: hasLocation 
            ? `No jobs found within ${currentRadius}km of your location. Try expanding your search radius or check back later for new opportunities.`
            : 'No jobs available at the moment. Check back later for new opportunities or enable location services to see nearby jobs.'
        };
    }
  };

  const defaultContent = getDefaultContent();
  const displayTitle = title || defaultContent.title;
  const displayMessage = message || defaultContent.message;

  const getDefaultActions = () => {
    switch (type) {
      case 'no_network':
        return {
          primary: {
            label: 'Retry',
            icon: <RefreshCw size={16} color="white" />
          }
        };
      
      case 'location_error':
        return {
          primary: {
            label: 'Enable Location',
            icon: <MapPin size={16} color="white" />
          },
          secondary: {
            label: 'Browse All Jobs'
          }
        };
      
      case 'filter_no_results':
        return {
          primary: {
            label: 'Clear Filters',
            icon: <Filter size={16} color="white" />
          },
          secondary: {
            label: hasLocation ? 'Expand Search Area' : 'Refresh Jobs'
          }
        };
      
      case 'search_no_results':
        return {
          primary: {
            label: 'Clear Search',
            icon: <Search size={16} color="white" />
          },
          secondary: {
            label: 'Browse All Jobs'
          }
        };
      
      default:
        return {
          primary: {
            label: 'Refresh Jobs',
            icon: <RefreshCw size={16} color="white" />
          },
          secondary: hasLocation ? {
            label: 'Expand Search Area'
          } : {
            label: 'Enable Location'
          }
        };
    }
  };

  const defaultActions = getDefaultActions();

  return (
    <View style={styles.container}>
      <EmptyStateIcon type={type} />
      
      <Text style={styles.title}>{displayTitle}</Text>
      <Text style={styles.message}>{displayMessage}</Text>
      
      <View style={styles.actionsContainer}>
        {primaryAction && (
          <TouchableOpacity 
            style={styles.primaryButton} 
            onPress={primaryAction.onPress}
          >
            {primaryAction.icon || defaultActions.primary?.icon}
            <Text style={styles.primaryButtonText}>
              {primaryAction.label}
            </Text>
          </TouchableOpacity>
        )}
        
        {secondaryAction && (
          <TouchableOpacity 
            style={styles.secondaryButton} 
            onPress={secondaryAction.onPress}
          >
            <Text style={styles.secondaryButtonText}>
              {secondaryAction.label}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 48,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 8,
  },
  message: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  actionsContainer: {
    width: '100%',
    gap: 12,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: '#059669',
    paddingHorizontal: 24,
    paddingVertical: 14,
    borderRadius: 8,
  },
  primaryButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  secondaryButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  secondaryButtonText: {
    color: '#059669',
    fontWeight: '500',
    fontSize: 16,
  },
});

export default EnhancedEmptyState;
