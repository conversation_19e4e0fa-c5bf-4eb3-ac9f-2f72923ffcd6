import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert
} from 'react-native'
import { Info, DollarSign } from 'lucide-react-native'

interface MarketRateGuidance {
  skillCategory: string
  location?: string
  suggestedHourlyMin: number
  suggestedHourlyMax: number
  suggestedDailyMin: number
  suggestedDailyMax: number
  marketAverage: number
  confidence: 'high' | 'medium' | 'low'
}

interface RateInputProps {
  hourlyRate?: string
  dailyRate?: string
  onHourlyRateChange: (rate: string) => void
  onDailyRateChange: (rate: string) => void
  isNegotiable: boolean
  onNegotiableChange: (negotiable: boolean) => void
  marketGuidance?: MarketRateGuidance
  errors?: {
    hourlyRate?: string
    dailyRate?: string
  }
}

const RateInput: React.FC<RateInputProps> = ({
  hourlyRate,
  dailyRate,
  onHourlyRateChange,
  onDailyRateChange,
  isNegotiable,
  onNegotiableChange,
  marketGuidance,
  errors
}) => {
  const [showGuidance, setShowGuidance] = useState(false)

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount)
  }

  const validateRate = (rate: string, type: 'hourly' | 'daily'): string | null => {
    if (!rate) return null
    
    const numRate = parseFloat(rate)
    if (isNaN(numRate) || numRate <= 0) {
      return 'Please enter a valid rate'
    }

    if (marketGuidance) {
      const min = type === 'hourly' ? marketGuidance.suggestedHourlyMin : marketGuidance.suggestedDailyMin
      const max = type === 'hourly' ? marketGuidance.suggestedHourlyMax : marketGuidance.suggestedDailyMax

      if (numRate < min * 0.5) {
        return `Rate seems too low. Market range: ${formatCurrency(min)} - ${formatCurrency(max)}`
      }
      if (numRate > max * 2) {
        return `Rate seems too high. Market range: ${formatCurrency(min)} - ${formatCurrency(max)}`
      }
    }

    return null
  }

  const getWarningForRate = (rate: string, type: 'hourly' | 'daily'): string | null => {
    if (!rate || !marketGuidance) return null
    
    const numRate = parseFloat(rate)
    if (isNaN(numRate)) return null

    const min = type === 'hourly' ? marketGuidance.suggestedHourlyMin : marketGuidance.suggestedDailyMin
    const max = type === 'hourly' ? marketGuidance.suggestedHourlyMax : marketGuidance.suggestedDailyMax

    if (numRate < min) {
      return 'Below market average - consider increasing'
    }
    if (numRate > max) {
      return 'Above market average - may reduce applications'
    }

    return null
  }

  const showMarketGuidance = () => {
    if (!marketGuidance) return

    Alert.alert(
      'Market Rate Guidance',
      `For ${marketGuidance.skillCategory} in your area:\n\n` +
      `Hourly: ${formatCurrency(marketGuidance.suggestedHourlyMin)} - ${formatCurrency(marketGuidance.suggestedHourlyMax)}\n` +
      `Daily: ${formatCurrency(marketGuidance.suggestedDailyMin)} - ${formatCurrency(marketGuidance.suggestedDailyMax)}\n\n` +
      `Market Average: ${formatCurrency(marketGuidance.marketAverage)}/hour\n` +
      `Confidence: ${marketGuidance.confidence.toUpperCase()}`,
      [{ text: 'OK' }]
    )
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Your Rates (Optional)</Text>
        {marketGuidance && (
          <TouchableOpacity
            style={styles.guidanceButton}
            onPress={showMarketGuidance}
          >
            <Info size={16} color="#3B82F6" />
            <Text style={styles.guidanceText}>Market Rates</Text>
          </TouchableOpacity>
        )}
      </View>

      <Text style={styles.subtitle}>
        Set your rates to help employers understand your pricing
      </Text>

      {/* Hourly Rate */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Hourly Rate</Text>
        <View style={[styles.inputContainer, errors?.hourlyRate && styles.inputError]}>
          <View style={styles.currencyPrefix}>
            <Text style={styles.currencySymbol}>₹</Text>
          </View>
          <TextInput
            style={styles.input}
            placeholder="0"
            value={hourlyRate}
            onChangeText={onHourlyRateChange}
            keyboardType="numeric"
            maxLength={6}
          />
          <Text style={styles.suffix}>/hour</Text>
        </View>
        {errors?.hourlyRate && (
          <Text style={styles.errorText}>{errors.hourlyRate}</Text>
        )}
        {!errors?.hourlyRate && hourlyRate && (
          <Text style={styles.warningText}>
            {getWarningForRate(hourlyRate, 'hourly')}
          </Text>
        )}
        {marketGuidance && (
          <Text style={styles.hintText}>
            Market range: {formatCurrency(marketGuidance.suggestedHourlyMin)} - {formatCurrency(marketGuidance.suggestedHourlyMax)}
          </Text>
        )}
      </View>

      {/* Daily Rate */}
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Daily Rate</Text>
        <View style={[styles.inputContainer, errors?.dailyRate && styles.inputError]}>
          <View style={styles.currencyPrefix}>
            <Text style={styles.currencySymbol}>₹</Text>
          </View>
          <TextInput
            style={styles.input}
            placeholder="0"
            value={dailyRate}
            onChangeText={onDailyRateChange}
            keyboardType="numeric"
            maxLength={7}
          />
          <Text style={styles.suffix}>/day</Text>
        </View>
        {errors?.dailyRate && (
          <Text style={styles.errorText}>{errors.dailyRate}</Text>
        )}
        {!errors?.dailyRate && dailyRate && (
          <Text style={styles.warningText}>
            {getWarningForRate(dailyRate, 'daily')}
          </Text>
        )}
        {marketGuidance && (
          <Text style={styles.hintText}>
            Market range: {formatCurrency(marketGuidance.suggestedDailyMin)} - {formatCurrency(marketGuidance.suggestedDailyMax)}
          </Text>
        )}
      </View>

      {/* Negotiable Toggle */}
      <TouchableOpacity
        style={styles.negotiableToggle}
        onPress={() => onNegotiableChange(!isNegotiable)}
      >
        <View style={[styles.checkbox, isNegotiable && styles.checkboxChecked]}>
          {isNegotiable && <Text style={styles.checkmark}>✓</Text>}
        </View>
        <Text style={styles.negotiableText}>My rates are negotiable</Text>
      </TouchableOpacity>

      <Text style={styles.footerText}>
        💡 Tip: Setting competitive rates increases your chances of getting hired
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 24
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827'
  },
  guidanceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: '#EFF6FF',
    borderRadius: 6,
    gap: 4
  },
  guidanceText: {
    fontSize: 12,
    color: '#3B82F6',
    fontWeight: '500'
  },
  subtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16
  },
  inputGroup: {
    marginBottom: 16
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 6
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 48
  },
  inputError: {
    borderColor: '#EF4444'
  },
  currencyPrefix: {
    paddingRight: 8
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280'
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
    paddingVertical: 0
  },
  suffix: {
    fontSize: 14,
    color: '#6B7280',
    paddingLeft: 8
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4
  },
  warningText: {
    fontSize: 12,
    color: '#F59E0B',
    marginTop: 4
  },
  hintText: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4
  },
  negotiableToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 12
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center'
  },
  checkboxChecked: {
    backgroundColor: '#3B82F6',
    borderColor: '#3B82F6'
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold'
  },
  negotiableText: {
    fontSize: 14,
    color: '#374151'
  },
  footerText: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic'
  }
})

export default RateInput
