import { Picker } from "@react-native-picker/picker";
import { Globe } from "lucide-react-native";
import { SupportedLanguage } from "@/types";
import { View, StyleSheet } from "react-native";

interface LanguageSelectorProps {
  value: SupportedLanguage;
  onChange: (language: SupportedLanguage) => void;
  className?: string;
}

const languages: Record<SupportedLanguage, string> = {
  english: "English",
  hindi: "हिंदी",
  tamil: "தமிழ்",
  telugu: "తెలుగు",
  bengali: "বাংলা",
  marathi: "मराठी",
  gujarati: "ગુજરાતી",
  kannada: "ಕನ್ನಡ"
};

const LanguageSelector = ({ value, onChange, className }: LanguageSelectorProps) => {
  return (
    <View style={styles.container}>
      <Globe style={styles.icon} size={20} color="black" />
      <Picker
        selectedValue={value}
        style={styles.picker}
        onValueChange={(itemValue) => onChange(itemValue as SupportedLanguage)}
      >
        {Object.entries(languages).map(([code, name]) => (
          <Picker.Item key={code} label={name} value={code} />
        ))}
      </Picker>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
  },
  icon: {
    paddingLeft: 10,
  },
  picker: {
    flex: 1,
  },
});

export default LanguageSelector;
