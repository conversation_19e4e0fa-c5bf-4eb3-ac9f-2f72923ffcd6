import React, { useState } from 'react';
import { TouchableOpacity, StyleSheet, Modal, View, TouchableWithoutFeedback } from 'react-native';
import { Globe } from 'lucide-react-native';
import { Picker } from "@react-native-picker/picker";
import { SupportedLanguage } from "@/types";

interface LanguageSelectorIconProps {
  value?: SupportedLanguage;
  onChange?: (language: SupportedLanguage) => void;
}

const languages: Record<SupportedLanguage, string> = {
  english: "English",
  hindi: "हिंदी",
  tamil: "தமிழ்",
  telugu: "తెలుగు",
  bengali: "বাংলা",
  marathi: "मराठी",
  gujarati: "ગુજરાતી",
  kannada: "ಕನ್ನಡ"
};

const LanguageSelectorIcon = ({ value = 'english', onChange }: LanguageSelectorIconProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = (selectedLanguage: SupportedLanguage) => {
    onChange?.(selectedLanguage);
    setIsOpen(false);
  };

  return (
    <>
      <TouchableOpacity 
        style={styles.container}
        onPress={() => setIsOpen(true)}
      >
        <Globe size={20} color="white" />
      </TouchableOpacity>

      <Modal
        visible={isOpen}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsOpen(false)}
      >
        <TouchableWithoutFeedback onPress={() => setIsOpen(false)}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback>
              <View style={styles.modalContent}>
                <Picker
                  selectedValue={value}
                  style={styles.picker}
                  onValueChange={handleLanguageChange}
                >
                  {Object.entries(languages).map(([code, name]) => (
                    <Picker.Item key={code} label={name} value={code} />
                  ))}
                </Picker>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    width: '80%',
    maxHeight: '40%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  picker: {
    width: '100%',
  },
});

export default LanguageSelectorIcon;
