import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator
} from 'react-native'
import {
  DollarSign,
  Clock,
  Calendar,
  AlertTriangle,
  Info,
  TrendingUp
} from 'lucide-react-native'
import { jobService } from '../services/jobService'

export type BudgetType = 'fixed' | 'hourly' | 'daily'

interface BudgetData {
  type: BudgetType
  minAmount: string
  maxAmount: string
}

interface MarketRateGuidance {
  isRealistic: boolean
  severity: 'info' | 'warning' | 'error'
  message: string
  suggestedMin?: number
  suggestedMax?: number
  marketAverage?: number
}

interface BudgetInputProps {
  value: BudgetData
  onChange: (budget: BudgetData) => void
  skillCategory?: string
  location?: string
  error?: {
    minAmount?: string
    maxAmount?: string
  }
  showMarketGuidance?: boolean
}

const BudgetInput: React.FC<BudgetInputProps> = ({
  value,
  onChange,
  skillCategory,
  location,
  error,
  showMarketGuidance = true
}) => {
  const [marketGuidance, setMarketGuidance] = useState<MarketRateGuidance | null>(null)
  const [isLoadingGuidance, setIsLoadingGuidance] = useState(false)

  const budgetTypes = [
    {
      type: 'fixed' as BudgetType,
      label: 'Fixed Amount',
      description: 'One-time payment for the entire job',
      icon: <DollarSign size={18} color="#3B82F6" />,
      example: 'e.g., ₹1,500 total',
      color: '#3B82F6'
    },
    {
      type: 'hourly' as BudgetType,
      label: 'Hourly Rate',
      description: 'Payment per hour of work',
      icon: <Clock size={18} color="#10B981" />,
      example: 'e.g., ₹200/hour',
      color: '#10B981'
    },
    {
      type: 'daily' as BudgetType,
      label: 'Daily Rate',
      description: 'Payment per day of work',
      icon: <Calendar size={18} color="#F59E0B" />,
      example: 'e.g., ₹1,200/day',
      color: '#F59E0B'
    }
  ]

  // Validate budget against market rates using backend API
  const validateBudgetAgainstMarket = async (
    budgetType: BudgetType,
    minAmount: number,
    maxAmount: number,
    category?: string,
    location?: string
  ): Promise<MarketRateGuidance> => {
    try {
      if (!category) {
        return {
          isRealistic: true,
          severity: 'info',
          message: 'Please select a skill category for market rate guidance.'
        }
      }

      const result = await jobService.validateBudgetRange(
        category,
        budgetType,
        minAmount,
        maxAmount,
        location
      )

      if (result.error || !result.data) {
        console.warn('Market rate validation failed:', result.error)
        // Fall back to basic validation
        return {
          isRealistic: true,
          severity: 'info',
          message: 'Unable to validate against market rates. Your budget will be accepted as provided.'
        }
      }

      // Transform API response to component format
      return {
        isRealistic: result.data.isValid,
        severity: result.data.severity,
        message: result.data.message,
        suggestedMin: result.data.suggestions?.recommended_min,
        suggestedMax: result.data.suggestions?.recommended_max,
        marketAverage: result.data.suggestions?.market_avg
      }

    } catch (error) {
      console.error('Budget validation error:', error)
      return {
        isRealistic: true,
        severity: 'info',
        message: 'Unable to validate against market rates. Your budget will be accepted as provided.'
      }
    }
  }

  // Validate budget when values change
  useEffect(() => {
    if (showMarketGuidance && value.minAmount && value.maxAmount && skillCategory) {
      const minAmount = parseFloat(value.minAmount)
      const maxAmount = parseFloat(value.maxAmount)

      if (!isNaN(minAmount) && !isNaN(maxAmount) && minAmount > 0 && maxAmount > 0) {
        setIsLoadingGuidance(true)
        validateBudgetAgainstMarket(value.type, minAmount, maxAmount, skillCategory, location)
          .then(guidance => {
            setMarketGuidance(guidance)
            setIsLoadingGuidance(false)
          })
          .catch(() => {
            setIsLoadingGuidance(false)
          })
      } else {
        setMarketGuidance(null)
      }
    }
  }, [value.type, value.minAmount, value.maxAmount, skillCategory, showMarketGuidance, location])

  const handleBudgetTypeChange = (newType: BudgetType) => {
    onChange({
      ...value,
      type: newType
    })
  }

  const handleMinAmountChange = (text: string) => {
    // Only allow numbers and decimal point
    const cleanText = text.replace(/[^0-9.]/g, '')
    onChange({
      ...value,
      minAmount: cleanText
    })
  }

  const handleMaxAmountChange = (text: string) => {
    // Only allow numbers and decimal point
    const cleanText = text.replace(/[^0-9.]/g, '')
    onChange({
      ...value,
      maxAmount: cleanText
    })
  }

  const formatCurrency = (amount: number): string => {
    return `₹${amount.toLocaleString('en-IN')}`
  }

  const getGuidanceIcon = (severity: 'info' | 'warning' | 'error') => {
    switch (severity) {
      case 'error':
        return <AlertTriangle size={16} color="#EF4444" />
      case 'warning':
        return <AlertTriangle size={16} color="#F59E0B" />
      case 'info':
        return <Info size={16} color="#3B82F6" />
    }
  }

  const getGuidanceColor = (severity: 'info' | 'warning' | 'error') => {
    switch (severity) {
      case 'error': return '#FEF2F2'
      case 'warning': return '#FFFBEB'
      case 'info': return '#EFF6FF'
    }
  }

  const getGuidanceTextColor = (severity: 'info' | 'warning' | 'error') => {
    switch (severity) {
      case 'error': return '#DC2626'
      case 'warning': return '#D97706'
      case 'info': return '#2563EB'
    }
  }

  return (
    <View style={styles.container}>
      {/* Budget Type Selection */}
      <View style={styles.typeSelection}>
        <Text style={styles.label}>Budget Type *</Text>
        <Text style={styles.helperText}>Choose how you want to pay for this job</Text>

        <View style={styles.typeOptions}>
          {budgetTypes.map((type) => (
            <TouchableOpacity
              key={type.type}
              style={[
                styles.typeOption,
                value.type === type.type && styles.typeOptionSelected,
                { borderColor: value.type === type.type ? type.color : '#D1D5DB' }
              ]}
              onPress={() => handleBudgetTypeChange(type.type)}
            >
              <View style={styles.typeHeader}>
                {type.icon}
                <Text style={[
                  styles.typeLabel,
                  value.type === type.type && { color: type.color }
                ]}>
                  {type.label}
                </Text>
              </View>
              <Text style={styles.typeDescription}>{type.description}</Text>
              <Text style={styles.typeExample}>{type.example}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Budget Range Input */}
      <View style={styles.rangeSection}>
        <Text style={styles.label}>Budget Range *</Text>
        <Text style={styles.helperText}>
          Set your minimum and maximum budget for this {value.type === 'fixed' ? 'job' : value.type === 'hourly' ? 'hour' : 'day'}
        </Text>

        <View style={styles.rangeInputs}>
          <View style={styles.rangeInput}>
            <Text style={styles.rangeLabel}>Minimum</Text>
            <View style={[styles.currencyInput, error?.minAmount && styles.inputError]}>
              <Text style={styles.currencySymbol}>₹</Text>
              <TextInput
                style={styles.amountInput}
                placeholder="500"
                value={value.minAmount}
                onChangeText={handleMinAmountChange}
                keyboardType="numeric"
                maxLength={7}
              />
              <Text style={styles.rateSuffix}>
                {value.type === 'hourly' ? '/hr' : value.type === 'daily' ? '/day' : ''}
              </Text>
            </View>
            {error?.minAmount && <Text style={styles.errorText}>{error.minAmount}</Text>}
          </View>

          <View style={styles.rangeInput}>
            <Text style={styles.rangeLabel}>Maximum</Text>
            <View style={[styles.currencyInput, error?.maxAmount && styles.inputError]}>
              <Text style={styles.currencySymbol}>₹</Text>
              <TextInput
                style={styles.amountInput}
                placeholder="1500"
                value={value.maxAmount}
                onChangeText={handleMaxAmountChange}
                keyboardType="numeric"
                maxLength={7}
              />
              <Text style={styles.rateSuffix}>
                {value.type === 'hourly' ? '/hr' : value.type === 'daily' ? '/day' : ''}
              </Text>
            </View>
            {error?.maxAmount && <Text style={styles.errorText}>{error.maxAmount}</Text>}
          </View>
        </View>
      </View>

      {/* Market Rate Guidance */}
      {showMarketGuidance && (
        <View style={styles.guidanceSection}>
          {isLoadingGuidance ? (
            <View style={styles.loadingGuidance}>
              <ActivityIndicator size="small" color="#3B82F6" />
              <Text style={styles.loadingText}>Checking market rates...</Text>
            </View>
          ) : marketGuidance ? (
            <View style={[
              styles.guidanceCard,
              { backgroundColor: getGuidanceColor(marketGuidance.severity) }
            ]}>
              <View style={styles.guidanceHeader}>
                {getGuidanceIcon(marketGuidance.severity)}
                <Text style={[
                  styles.guidanceTitle,
                  { color: getGuidanceTextColor(marketGuidance.severity) }
                ]}>
                  Market Rate Guidance
                </Text>
              </View>
              <Text style={[
                styles.guidanceMessage,
                { color: getGuidanceTextColor(marketGuidance.severity) }
              ]}>
                {marketGuidance.message}
              </Text>
              {marketGuidance.suggestedMin && marketGuidance.suggestedMax && (
                <View style={styles.suggestionRow}>
                  <TrendingUp size={14} color={getGuidanceTextColor(marketGuidance.severity)} />
                  <Text style={[
                    styles.suggestionText,
                    { color: getGuidanceTextColor(marketGuidance.severity) }
                  ]}>
                    Market range: {formatCurrency(marketGuidance.suggestedMin)} - {formatCurrency(marketGuidance.suggestedMax)}
                  </Text>
                </View>
              )}
            </View>
          ) : null}
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16
  },
  typeSelection: {
    marginBottom: 24
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 4
  },
  helperText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12
  },
  typeOptions: {
    gap: 12
  },
  typeOption: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    backgroundColor: 'white'
  },
  typeOptionSelected: {
    backgroundColor: '#F8FAFC'
  },
  typeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
    gap: 8
  },
  typeLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151'
  },
  typeDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4
  },
  typeExample: {
    fontSize: 12,
    color: '#9CA3AF',
    fontStyle: 'italic'
  },
  rangeSection: {
    marginBottom: 20
  },
  rangeInputs: {
    flexDirection: 'row',
    gap: 16
  },
  rangeInput: {
    flex: 1
  },
  rangeLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8
  },
  currencyInput: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: 'white'
  },
  inputError: {
    borderColor: '#EF4444'
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
    paddingLeft: 16
  },
  amountInput: {
    flex: 1,
    paddingHorizontal: 8,
    paddingVertical: 12,
    fontSize: 16,
    color: '#111827'
  },
  rateSuffix: {
    fontSize: 14,
    color: '#6B7280',
    paddingRight: 16
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4
  },
  guidanceSection: {
    marginTop: 8
  },
  loadingGuidance: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8
  },
  loadingText: {
    fontSize: 14,
    color: '#6B7280'
  },
  guidanceCard: {
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  guidanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8
  },
  guidanceTitle: {
    fontSize: 14,
    fontWeight: '500'
  },
  guidanceMessage: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8
  },
  suggestionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6
  },
  suggestionText: {
    fontSize: 12,
    fontWeight: '500'
  }
})

export default BudgetInput