import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useAppSelector, useAppDispatch } from '../hooks/redux';
import { setUser } from '../store/authSlice';

const ReduxExample = () => {
  const dispatch = useAppDispatch();
  const authState = useAppSelector(state => state.auth);
  const translationState = useAppSelector(state => state.translation);
  const { user, loading } = authState;
  const { currentLanguage } = translationState;

  const handleUpdateUser = () => {
    if (user) {
      dispatch(setUser({
        ...user,
        full_name: `${user.full_name} (Updated)`
      }));
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Redux Toolkit Example</Text>
      
      <View style={styles.section}>
        <Text style={styles.label}>Auth State:</Text>
        <Text style={styles.value}>{loading ? 'Loading...' : user ? 'Authenticated' : 'Not Authenticated'}</Text>
      </View>
      
      {user && (
        <View style={styles.section}>
          <Text style={styles.label}>User Name:</Text>
          <Text style={styles.value}>{user.full_name}</Text>
        </View>
      )}
      
      <View style={styles.section}>
        <Text style={styles.label}>Current Language:</Text>
        <Text style={styles.value}>{currentLanguage}</Text>
      </View>
      
      {user && (
        <TouchableOpacity style={styles.button} onPress={handleUpdateUser}>
          <Text style={styles.buttonText}>Update User Name</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  section: {
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  value: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
  },
  button: {
    backgroundColor: '#059669',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ReduxExample;