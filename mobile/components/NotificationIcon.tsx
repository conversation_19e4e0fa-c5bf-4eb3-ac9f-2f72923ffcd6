import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { Bell } from 'lucide-react-native';

const NotificationIcon = () => {
  return (
    <TouchableOpacity style={styles.container}>
      <Bell size={24} color="black" />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 8,
  },
});

export default NotificationIcon;
