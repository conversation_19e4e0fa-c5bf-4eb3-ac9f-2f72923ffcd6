import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator
} from 'react-native'
import { 
  CheckCircle, 
  Circle, 
  ArrowRight, 
  Star, 
  Users, 
  Eye,
  TrendingUp,
  Lock,
  Unlock
} from 'lucide-react-native'
import { apiClient } from '../lib/supabase'
import { router } from 'expo-router'

interface ProfileCompletenessData {
  currentProfile: {
    completenessScore: number
    completenessLevel: 'draft' | 'basic' | 'good' | 'complete'
    isValid: boolean
    platformAccess: {
      canApplyToJobs: boolean
      canReceiveJobInvites: boolean
      searchVisibilityMultiplier: number
      hasFullAccess: boolean
    }
  }
  validation: {
    errors: string[]
    warnings: string[]
    suggestions: string[]
    missingFields: string[]
  }
  requirements: Record<string, { minScore: number; description: string; benefits: string[] }>
  nextSteps: string[]
  benefits: string[]
}

interface ProfileCompletenessDashboardProps {
  onNavigateToProfile?: () => void
}

const ProfileCompletenessDashboard: React.FC<ProfileCompletenessDashboardProps> = ({
  onNavigateToProfile
}) => {
  const [data, setData] = useState<ProfileCompletenessData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchProfileCompleteness()
  }, [])

  const fetchProfileCompleteness = async () => {
    try {
      setLoading(true)
      const response = await apiClient.get('/auth/profile-completeness')
      
      if (response.success && response.data) {
        setData(response.data as ProfileCompletenessData)
      } else {
        setError(response.error || 'Failed to load profile completeness')
      }
    } catch (err) {
      console.error('Profile completeness error:', err)
      setError('Failed to load profile completeness')
    } finally {
      setLoading(false)
    }
  }

  const getCompletenessColor = (level: string) => {
    switch (level) {
      case 'complete': return '#22C55E'
      case 'good': return '#3B82F6'
      case 'basic': return '#F59E0B'
      default: return '#EF4444'
    }
  }

  const getCompletenessIcon = (level: string) => {
    switch (level) {
      case 'complete': return '🎉'
      case 'good': return '👍'
      case 'basic': return '⚡'
      default: return '📝'
    }
  }

  const handleNavigateToProfile = () => {
    if (onNavigateToProfile) {
      onNavigateToProfile()
    } else {
      router.push('/(auth)/worker-profile-creation')
    }
  }

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3B82F6" />
        <Text style={styles.loadingText}>Loading profile completeness...</Text>
      </View>
    )
  }

  if (error || !data) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error || 'Failed to load data'}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchProfileCompleteness}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    )
  }

  const { currentProfile, validation, requirements, nextSteps, benefits } = data
  const completenessColor = getCompletenessColor(currentProfile.completenessLevel)
  const completenessIcon = getCompletenessIcon(currentProfile.completenessLevel)

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Main Completeness Card */}
      <View style={styles.mainCard}>
        <View style={styles.scoreHeader}>
          <View style={styles.scoreInfo}>
            <Text style={styles.scoreIcon}>{completenessIcon}</Text>
            <View>
              <Text style={styles.scoreText}>{currentProfile.completenessScore}%</Text>
              <Text style={[styles.levelText, { color: completenessColor }]}>
                {currentProfile.completenessLevel.toUpperCase()}
              </Text>
            </View>
          </View>
          <TouchableOpacity style={styles.editButton} onPress={handleNavigateToProfile}>
            <Text style={styles.editButtonText}>Edit Profile</Text>
            <ArrowRight size={16} color="#3B82F6" />
          </TouchableOpacity>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBackground}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  width: `${Math.max(currentProfile.completenessScore, 5)}%`,
                  backgroundColor: completenessColor 
                }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>
            Profile Completeness
          </Text>
        </View>

        {/* Platform Access Status */}
        <View style={styles.accessContainer}>
          <Text style={styles.accessTitle}>Platform Access</Text>
          <View style={styles.accessItems}>
            <View style={styles.accessItem}>
              {currentProfile.platformAccess.canApplyToJobs ? (
                <Unlock size={16} color="#22C55E" />
              ) : (
                <Lock size={16} color="#EF4444" />
              )}
              <Text style={[
                styles.accessText,
                { color: currentProfile.platformAccess.canApplyToJobs ? '#22C55E' : '#EF4444' }
              ]}>
                Apply to Jobs
              </Text>
            </View>
            <View style={styles.accessItem}>
              {currentProfile.platformAccess.canReceiveJobInvites ? (
                <Unlock size={16} color="#22C55E" />
              ) : (
                <Lock size={16} color="#EF4444" />
              )}
              <Text style={[
                styles.accessText,
                { color: currentProfile.platformAccess.canReceiveJobInvites ? '#22C55E' : '#EF4444' }
              ]}>
                Receive Job Invites
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Visibility Boost */}
      <View style={styles.visibilityCard}>
        <View style={styles.visibilityHeader}>
          <Eye size={20} color="#3B82F6" />
          <Text style={styles.visibilityTitle}>Search Visibility</Text>
        </View>
        <Text style={styles.visibilityMultiplier}>
          {currentProfile.platformAccess.searchVisibilityMultiplier}x
        </Text>
        <Text style={styles.visibilityDescription}>
          Your profile appears {currentProfile.platformAccess.searchVisibilityMultiplier}x more often in search results
        </Text>
      </View>

      {/* Next Steps */}
      {nextSteps.length > 0 && (
        <View style={styles.nextStepsCard}>
          <Text style={styles.cardTitle}>Next Steps to Improve</Text>
          {nextSteps.map((step, index) => (
            <View key={index} style={styles.nextStepItem}>
              <Circle size={12} color="#F59E0B" />
              <Text style={styles.nextStepText}>{step}</Text>
            </View>
          ))}
          <TouchableOpacity style={styles.improveButton} onPress={handleNavigateToProfile}>
            <TrendingUp size={16} color="#FFFFFF" />
            <Text style={styles.improveButtonText}>Improve Profile</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Benefits for Next Level */}
      <View style={styles.benefitsCard}>
        <Text style={styles.cardTitle}>Benefits at Your Level</Text>
        {benefits.map((benefit, index) => (
          <View key={index} style={styles.benefitItem}>
            <Star size={12} color="#22C55E" />
            <Text style={styles.benefitText}>{benefit}</Text>
          </View>
        ))}
      </View>

      {/* Completeness Levels Guide */}
      <View style={styles.levelsCard}>
        <Text style={styles.cardTitle}>Completeness Levels</Text>
        {Object.entries(requirements).map(([level, req]) => (
          <View 
            key={level} 
            style={[
              styles.levelItem,
              currentProfile.completenessLevel === level && styles.levelItemActive
            ]}
          >
            <View style={styles.levelHeader}>
              <Text style={[
                styles.levelName,
                currentProfile.completenessLevel === level && styles.levelNameActive
              ]}>
                {level.toUpperCase()}
              </Text>
              <Text style={styles.levelScore}>{req.minScore}%+</Text>
            </View>
            <Text style={styles.levelDescription}>{req.description}</Text>
            {currentProfile.completenessLevel === level && (
              <View style={styles.currentLevelBadge}>
                <CheckCircle size={12} color="#22C55E" />
                <Text style={styles.currentLevelText}>Current Level</Text>
              </View>
            )}
          </View>
        ))}
      </View>

      {/* Call to Action */}
      {currentProfile.completenessScore < 100 && (
        <View style={styles.ctaCard}>
          <Users size={24} color="#3B82F6" />
          <Text style={styles.ctaTitle}>Get More Job Opportunities</Text>
          <Text style={styles.ctaDescription}>
            Complete profiles get 3x more job applications. Take 2 minutes to boost your visibility!
          </Text>
          <TouchableOpacity style={styles.ctaButton} onPress={handleNavigateToProfile}>
            <Text style={styles.ctaButtonText}>Complete Profile Now</Text>
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.bottomSpacing} />
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    paddingHorizontal: 20
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40
  },
  loadingText: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 12
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444',
    textAlign: 'center',
    marginBottom: 16
  },
  retryButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500'
  },
  mainCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginVertical: 10,
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  scoreHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20
  },
  scoreInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12
  },
  scoreIcon: {
    fontSize: 32
  },
  scoreText: {
    fontSize: 28,
    fontWeight: '700',
    color: '#111827'
  },
  levelText: {
    fontSize: 14,
    fontWeight: '600'
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#EFF6FF',
    borderRadius: 8
  },
  editButtonText: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500'
  },
  progressContainer: {
    marginBottom: 20
  },
  progressBackground: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
    minWidth: 8
  },
  progressText: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center'
  },
  accessContainer: {
    marginTop: 16
  },
  accessTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12
  },
  accessItems: {
    flexDirection: 'row',
    gap: 20
  },
  accessItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6
  },
  accessText: {
    fontSize: 12,
    fontWeight: '500'
  },
  visibilityCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center'
  },
  visibilityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8
  },
  visibilityTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111827'
  },
  visibilityMultiplier: {
    fontSize: 24,
    fontWeight: '700',
    color: '#3B82F6',
    marginBottom: 4
  },
  visibilityDescription: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center'
  },
  nextStepsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12
  },
  nextStepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8
  },
  nextStepText: {
    fontSize: 13,
    color: '#374151',
    flex: 1
  },
  improveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F59E0B',
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 8,
    gap: 6
  },
  improveButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500'
  },
  benefitsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8
  },
  benefitText: {
    fontSize: 13,
    color: '#374151',
    flex: 1
  },
  levelsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  levelItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#F9FAFB'
  },
  levelItemActive: {
    backgroundColor: '#EFF6FF',
    borderWidth: 1,
    borderColor: '#3B82F6'
  },
  levelHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4
  },
  levelName: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280'
  },
  levelNameActive: {
    color: '#3B82F6'
  },
  levelScore: {
    fontSize: 12,
    color: '#9CA3AF'
  },
  levelDescription: {
    fontSize: 11,
    color: '#6B7280'
  },
  currentLevelBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: 6
  },
  currentLevelText: {
    fontSize: 10,
    color: '#22C55E',
    fontWeight: '500'
  },
  ctaCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginVertical: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center'
  },
  ctaTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4
  },
  ctaDescription: {
    fontSize: 13,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 16
  },
  ctaButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8
  },
  ctaButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600'
  },
  bottomSpacing: {
    height: 20
  }
})

export default ProfileCompletenessDashboard
