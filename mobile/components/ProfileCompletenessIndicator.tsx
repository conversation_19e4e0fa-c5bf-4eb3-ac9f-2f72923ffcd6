import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { CheckCircle, AlertCircle } from 'lucide-react-native';

interface ProfileCompletenessIndicatorProps {
  completenessScore: number;
  missingFields: string[];
  suggestions: string[];
}

const ProfileCompletenessIndicator: React.FC<ProfileCompletenessIndicatorProps> = ({
  completenessScore,
  missingFields,
  suggestions,
}) => {
  const getCompletenessLevel = (score: number) => {
    if (score >= 90) return { level: 'Complete', color: '#10B981' };
    if (score >= 70) return { level: 'Good', color: '#3B82F6' };
    if (score >= 50) return { level: 'Fair', color: '#F59E0B' };
    return { level: 'Needs Work', color: '#EF4444' };
  };

  const { level, color } = getCompletenessLevel(completenessScore);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Profile Completeness</Text>
        <View style={[styles.scoreBadge, { backgroundColor: `${color}20` }]}>
          <Text style={[styles.scoreText, { color }]}>{completenessScore}%</Text>
          <Text style={[styles.levelText, { color }]}>{level}</Text>
        </View>
      </View>

      <View style={styles.progressBarContainer}>
        <View style={styles.progressBarBackground} />
        <View 
          style={[
            styles.progressBarFill, 
            { 
              width: `${completenessScore}%`, 
              backgroundColor: color 
            }
          ]} 
        />
      </View>

      {missingFields.length > 0 && (
        <View style={styles.missingFieldsContainer}>
          <View style={styles.missingFieldsHeader}>
            <AlertCircle size={20} color="#F59E0B" />
            <Text style={styles.missingFieldsTitle}>Incomplete Fields</Text>
          </View>
          {missingFields.map((field, index) => (
            <Text key={index} style={styles.missingFieldItem}>• {field}</Text>
          ))}
        </View>
      )}

      {suggestions.length > 0 && (
        <View style={styles.suggestionsContainer}>
          <View style={styles.suggestionsHeader}>
            <CheckCircle size={20} color="#10B981" />
            <Text style={styles.suggestionsTitle}>Suggestions</Text>
          </View>
          {suggestions.map((suggestion, index) => (
            <Text key={index} style={styles.suggestionItem}>• {suggestion}</Text>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  scoreBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  scoreText: {
    fontSize: 14,
    fontWeight: '700',
    marginRight: 6,
  },
  levelText: {
    fontSize: 12,
    fontWeight: '600',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 16,
  },
  progressBarBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#E5E7EB',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  missingFieldsContainer: {
    backgroundColor: '#FFFBEB',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  missingFieldsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  missingFieldsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#92400E',
    marginLeft: 8,
  },
  missingFieldItem: {
    fontSize: 14,
    color: '#92400E',
    marginBottom: 4,
  },
  suggestionsContainer: {
    backgroundColor: '#ECFDF5',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  suggestionsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  suggestionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#065F46',
    marginLeft: 8,
  },
  suggestionItem: {
    fontSize: 14,
    color: '#065F46',
    marginBottom: 4,
  },
  improveButton: {
    backgroundColor: '#059669',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  improveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProfileCompletenessIndicator;