import React from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';

interface JobCardSkeletonProps {
  count?: number;
}

const SkeletonPlaceholder: React.FC<{ width: number | string; height: number; style?: any }> = ({ 
  width, 
  height, 
  style 
}) => {
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: false,
        }),
      ])
    );
    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['#E5E7EB', '#F3F4F6'],
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          backgroundColor,
          borderRadius: 4,
        },
        style,
      ]}
    />
  );
};

const SingleJobCardSkeleton: React.FC = () => (
  <View style={styles.card}>
    {/* Header with title and urgency badge */}
    <View style={styles.cardHeader}>
      <View style={styles.titleSection}>
        <SkeletonPlaceholder width="70%" height={20} style={{ marginBottom: 8 }} />
        <SkeletonPlaceholder width="40%" height={16} />
      </View>
      <SkeletonPlaceholder width={60} height={24} style={{ borderRadius: 12 }} />
    </View>

    {/* Description */}
    <View style={styles.descriptionSection}>
      <SkeletonPlaceholder width="100%" height={16} style={{ marginBottom: 4 }} />
      <SkeletonPlaceholder width="80%" height={16} />
    </View>

    {/* Skills badges */}
    <View style={styles.skillsSection}>
      <SkeletonPlaceholder width={80} height={24} style={{ borderRadius: 12, marginRight: 8 }} />
      <SkeletonPlaceholder width={60} height={24} style={{ borderRadius: 12, marginRight: 8 }} />
      <SkeletonPlaceholder width={70} height={24} style={{ borderRadius: 12 }} />
    </View>

    {/* Footer with budget, location, and time */}
    <View style={styles.footerSection}>
      <View style={styles.footerLeft}>
        <SkeletonPlaceholder width={80} height={16} style={{ marginBottom: 4 }} />
        <SkeletonPlaceholder width={100} height={14} />
      </View>
      <SkeletonPlaceholder width={60} height={14} />
    </View>
  </View>
);

const JobCardSkeleton: React.FC<JobCardSkeletonProps> = ({ count = 3 }) => {
  return (
    <View style={styles.container}>
      {Array.from({ length: count }, (_, index) => (
        <SingleJobCardSkeleton key={index} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  titleSection: {
    flex: 1,
    marginRight: 12,
  },
  descriptionSection: {
    marginBottom: 12,
  },
  skillsSection: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  footerSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  footerLeft: {
    flex: 1,
  },
});

export default JobCardSkeleton;
