import { Star, MapPin, Clock, IndianRupee } from "lucide-react-native";
import { WorkerPersona } from "../types";
import { View, Text, StyleSheet, Image, TouchableOpacity } from "react-native";
import TrustBadge from "./TrustBadge";

interface PersonaCardProps {
  persona: WorkerPersona;
  className?: any;
  onClick?: () => void;
  isActive?: boolean;
}

const Badge = ({ children, style, textStyle }: { children: React.ReactNode, style?: any, textStyle?: any }) => (
  <View style={[styles.badgeBase, style]}>
    <Text style={[styles.badgeText, textStyle]}>{children}</Text>
  </View>
);

const PersonaCard = ({ persona, className, onClick, isActive }: PersonaCardProps) => {
  const getSkillCategoryLabel = (category: string) => {
    return category.replace('_', ' ').split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatRate = () => {
    if (persona.hourly_rate && persona.daily_rate) {
      return `₹${persona.hourly_rate}/hr • ₹${persona.daily_rate}/day`;
    } else if (persona.hourly_rate) {
      return `₹${persona.hourly_rate}/hour`;
    } else if (persona.daily_rate) {
      return `₹${persona.daily_rate}/day`;
    }
    return "Rate negotiable";
  };

  const getTrustLevel = (rating: number): 'high' | 'medium' | 'low' => {
    if (rating >= 4.5) return 'high';
    if (rating >= 3.5) return 'medium';
    return 'low';
  };

  return (
    <TouchableOpacity 
      style={[
        styles.card, 
        isActive && styles.activeCard,
        className
      ]}
      onPress={onClick}
    >
      <View style={styles.cardHeader}>
        <View style={styles.avatar}>
          {persona.profile_image_url ? (
            <Image source={{ uri: persona.profile_image_url }} style={styles.avatarImage} />
          ) : (
            <Text style={styles.avatarFallback}>
              {persona.title.split(' ').map(n => n[0]).join('').toUpperCase()}
            </Text>
          )}
        </View>
        
        <View style={{ flex: 1 }}>
          <Text style={styles.title} numberOfLines={1}>
            {persona.title}
          </Text>
          <Text style={styles.mutedText}>
            {getSkillCategoryLabel(persona.skill_category)}
          </Text>
          <View style={styles.ratingContainer}>
            <View style={styles.rating}>
              <Star size={12} color="#f59e0b" fill="#f59e0b" />
              <Text style={styles.ratingText}>{persona.average_rating.toFixed(1)}</Text>
            </View>
            <TrustBadge 
              type={getTrustLevel(persona.average_rating)} 
              text={`${persona.total_jobs_completed} jobs`} 
            />
          </View>
        </View>
        
        {!persona.is_active && (
          <Badge>Inactive</Badge>
        )}
      </View>
      
      <View style={styles.cardContent}>
        <Text style={styles.mutedText} numberOfLines={2}>
          {persona.description}
        </Text>
        
        <View style={styles.skillsContainer}>
          {persona.skill_subcategories.slice(0, 3).map((skill, index) => (
            <Badge key={index} style={styles.skillBadge} textStyle={styles.skillBadgeText}>
              {skill}
            </Badge>
          ))}
          {persona.skill_subcategories.length > 3 && (
            <Badge style={styles.skillBadge} textStyle={styles.skillBadgeText}>
              +{persona.skill_subcategories.length - 3}
            </Badge>
          )}
        </View>
        
        <View style={{ gap: 8 }}>
          <View style={styles.infoRow}>
            <MapPin size={16} color="#64748b" />
            <Text style={styles.mutedText}>Within {persona.travel_radius_km}km radius</Text>
          </View>
          
          <View style={styles.infoRow}>
            <IndianRupee size={16} color="#059669" />
            <Text style={styles.rateText}>{formatRate()}</Text>
            {persona.is_rate_negotiable && (
              <Badge>Negotiable</Badge>
            )}
          </View>
          
          <View style={styles.infoRow}>
            <Clock size={12} color="#64748b" />
            <Text style={styles.smallMutedText}>{persona.experience_years} years experience</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    marginVertical: 8,
  },
  activeCard: {
    borderColor: '#059669',
    borderWidth: 2,
  },
  cardHeader: {
    paddingBottom: 12,
    paddingHorizontal: 16,
    paddingTop: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#059669',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 24,
  },
  avatarFallback: {
    color: 'white',
    fontWeight: '600',
  },
  title: {
    fontWeight: '600',
    color: '#0f172a',
    fontSize: 16,
  },
  mutedText: {
    fontSize: 14,
    color: '#64748b',
  },
  smallMutedText: {
    fontSize: 12,
    color: '#64748b',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 4,
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '500',
  },
  cardContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    gap: 12,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  badgeBase: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 9999,
  },
  badgeText: {
    fontSize: 12,
    color: '#334155',
  },
  skillBadge: {
    backgroundColor: '#f1f5f9',
    borderColor: '#e2e8f0',
  },
  skillBadgeText: {
    color: '#334155'
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  rateText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#059669',
  }
});

export default PersonaCard;
