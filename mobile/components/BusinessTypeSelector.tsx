import React, { useState } from 'react'
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  SafeAreaView
} from 'react-native'
import { Check, ChevronDown } from 'lucide-react-native'

const businessTypeOptions: { value: string; label: string; icon: string }[] = [
  { value: 'restaurant', label: 'Restaurant/Food Service', icon: '🍽️' },
  { value: 'retail', label: 'Retail Store', icon: '🏪' },
  { value: 'construction', label: 'Construction Company', icon: '🏗️' },
  { value: 'healthcare', label: 'Healthcare/Medical', icon: '🏥' },
  { value: 'education', label: 'Educational Institution', icon: '🎓' },
  { value: 'hospitality', label: 'Hotel/Hospitality', icon: '🏨' },
  { value: 'manufacturing', label: 'Manufacturing', icon: '🏭' },
  { value: 'logistics', label: 'Logistics/Transport', icon: '🚛' },
  { value: 'technology', label: 'Technology/IT', icon: '💻' },
  { value: 'agriculture', label: 'Agriculture/Farming', icon: '🌾' },
  { value: 'automotive', label: 'Automotive', icon: '🚗' },
  { value: 'beauty', label: 'Beauty/Salon', icon: '💄' },
  { value: 'fitness', label: 'Fitness/Gym', icon: '💪' },
  { value: 'real_estate', label: 'Real Estate', icon: '🏠' },
  { value: 'consulting', label: 'Consulting/Services', icon: '💼' },
  { value: 'other', label: 'Other', icon: '🏢' }
]

interface BusinessTypeSelectorProps {
  value?: string
  onValueChange: (businessType: string) => void
  placeholder?: string
  error?: string
}

const BusinessTypeSelector: React.FC<BusinessTypeSelectorProps> = ({
  value,
  onValueChange,
  placeholder = 'Select business type',
  error
}) => {
  const [isOpen, setIsOpen] = useState(false)

  const selectedType = businessTypeOptions.find(type => type.value === value)

  const handleSelect = (businessType: string) => {
    onValueChange(businessType)
    setIsOpen(false)
  }

  return (
    <View>
      <TouchableOpacity
        style={[styles.selector, error && styles.selectorError]}
        onPress={() => setIsOpen(true)}
      >
        <View style={styles.selectorContent}>
          {selectedType ? (
            <View style={styles.selectedOption}>
              <Text style={styles.typeIcon}>{selectedType.icon}</Text>
              <Text style={styles.selectedText}>{selectedType.label}</Text>
            </View>
          ) : (
            <Text style={styles.placeholderText}>{placeholder}</Text>
          )}
        </View>
        <ChevronDown size={20} color="#6B7280" />
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      <Modal
        visible={isOpen}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsOpen(false)}
      >
        <View style={styles.modalOverlay}>
          <SafeAreaView style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Business Type</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setIsOpen(false)}
              >
                <Text style={styles.closeButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.optionsList}>
              {businessTypeOptions.map((type) => (
                <TouchableOpacity
                  key={type.value}
                  style={[
                    styles.option,
                    value === type.value && styles.selectedOptionItem
                  ]}
                  onPress={() => handleSelect(type.value)}
                >
                  <View style={styles.optionContent}>
                    <Text style={styles.typeIcon}>{type.icon}</Text>
                    <Text style={[
                      styles.optionText,
                      value === type.value && styles.selectedOptionText
                    ]}>
                      {type.label}
                    </Text>
                  </View>
                  {value === type.value && (
                    <Check size={20} color="#059669" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </SafeAreaView>
        </View>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  selectorError: {
    borderColor: '#EF4444',
  },
  selectorContent: {
    flex: 1,
  },
  selectedOption: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  selectedText: {
    fontSize: 16,
    color: '#374151',
  },
  placeholderText: {
    fontSize: 16,
    color: '#9CA3AF',
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  closeButtonText: {
    fontSize: 16,
    color: '#6B7280',
  },
  optionsList: {
    paddingHorizontal: 20,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  selectedOptionItem: {
    backgroundColor: '#F0FDF4',
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionText: {
    fontSize: 16,
    color: '#374151',
  },
  selectedOptionText: {
    color: '#059669',
    fontWeight: '500',
  },
})

export default BusinessTypeSelector
