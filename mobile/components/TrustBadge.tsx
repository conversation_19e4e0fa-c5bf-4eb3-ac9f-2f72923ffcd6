import { Shield, CheckCircle, Clock, AlertTriangle } from "lucide-react-native";
import { View, Text, StyleSheet } from "react-native";

interface TrustBadgeProps {
  type: 'verified' | 'pending' | 'high' | 'medium' | 'low';
  text: string;
  className?: any;
}

const TrustBadge = ({ type, text, className }: TrustBadgeProps) => {
  const getIcon = () => {
    const iconProps = { size: 12, color: getVariantClasses().color };
    switch (type) {
      case 'verified':
        return <CheckCircle {...iconProps} />;
      case 'pending':
        return <Clock {...iconProps} />;
      case 'high':
        return <Shield {...iconProps} />;
      case 'medium':
        return <Shield {...iconProps} />;
      case 'low':
        return <AlertTriangle {...iconProps} />;
      default:
        return <CheckCircle {...iconProps} />;
    }
  };

  const getVariantClasses = () => {
    switch (type) {
      case 'verified':
        return { backgroundColor: '#d1fae5', color: '#065f46', borderColor: '#6ee7b7' }; // Green
      case 'pending':
        return { backgroundColor: '#fef3c7', color: '#92400e', borderColor: '#fcd34d' }; // Amber
      case 'high':
        return { backgroundColor: '#d1fae5', color: '#065f46', borderColor: '#6ee7b7' }; // Green
      case 'medium':
        return { backgroundColor: '#fef9c3', color: '#b45309', borderColor: '#fde047' }; // Yellow
      case 'low':
        return { backgroundColor: '#fee2e2', color: '#991b1b', borderColor: '#fca5a5' }; // Red
      default:
        return { backgroundColor: '#d1fae5', color: '#065f46', borderColor: '#6ee7b7' };
    }
  };
  
  const variantStyles = getVariantClasses();

  return (
    <View style={[
      styles.badge,
      { 
        backgroundColor: variantStyles.backgroundColor,
        borderColor: variantStyles.borderColor,
      },
      className
    ]}>
      {getIcon()}
      <Text style={[styles.text, { color: variantStyles.color }]}>
        {text}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 9999,
    borderWidth: 1,
  },
  text: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
});

export default TrustBadge;
