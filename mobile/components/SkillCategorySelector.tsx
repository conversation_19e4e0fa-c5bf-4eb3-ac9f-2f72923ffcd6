// =====================================================
// SKILL CATEGORY SELECTOR COMPONENT
// =====================================================
// Component for selecting primary skill category with grid layout
// Task: US-002.2 - Create Skill Categories Master Data System

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { SkillCategoryData, SkillCategoriesResponse } from '../types';
import { MaterialIcons } from '@expo/vector-icons';

interface SkillCategorySelectorProps {
  selectedCategory?: SkillCategoryData | null;
  onCategorySelect: (category: SkillCategoryData) => void;
  disabled?: boolean;
  style?: any;
}

// Icon mapping for skill categories (Material Icons names)
const CATEGORY_ICONS: Record<string, any> = {
  'electrical': 'flash-on',
  'plumbing': 'plumbing',
  'carpentry': 'build',
  'cooking': 'restaurant',
  'cleaning': 'cleaning-services',
  'driving': 'directions-car',
  'delivery': 'local-shipping',
  'security': 'security',
  'gardening': 'local-florist',
  'tutoring': 'school'
};

const SkillCategorySelector: React.FC<SkillCategorySelectorProps> = ({
  selectedCategory,
  onCategorySelect,
  disabled = false,
  style
}) => {
  const [categories, setCategories] = useState<SkillCategoryData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSkillCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Replace with actual API endpoint when backend is deployed
      const response = await fetch('/api/skills/categories');

      if (!response.ok) {
        throw new Error('Failed to fetch skill categories');
      }

      const data: SkillCategoriesResponse = await response.json();

      if (data.success && data.data) {
        setCategories(data.data);
      } else {
        throw new Error(data.message || data.error || 'Failed to fetch skill categories');
      }
    } catch (err) {
      console.error('Error fetching skill categories:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');

      // For development, use mock data
      setCategories(getMockCategories());
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchSkillCategories();
  }, [fetchSkillCategories]);

  const getMockCategories = (): SkillCategoryData[] => {
    return [
      { id: '1', code: 'electrical', name: 'Electrical Work', icon_name: 'bolt', display_order: 1, is_active: true, created_at: '', updated_at: '' },
      { id: '2', code: 'plumbing', name: 'Plumbing', icon_name: 'water-drop', display_order: 2, is_active: true, created_at: '', updated_at: '' },
      { id: '3', code: 'carpentry', name: 'Carpentry', icon_name: 'hammer', display_order: 3, is_active: true, created_at: '', updated_at: '' },
      { id: '4', code: 'cooking', name: 'Cooking', icon_name: 'restaurant', display_order: 4, is_active: true, created_at: '', updated_at: '' },
      { id: '5', code: 'cleaning', name: 'Cleaning', icon_name: 'cleaning-services', display_order: 5, is_active: true, created_at: '', updated_at: '' },
      { id: '6', code: 'driving', name: 'Driving', icon_name: 'directions-car', display_order: 6, is_active: true, created_at: '', updated_at: '' },
      { id: '7', code: 'delivery', name: 'Delivery', icon_name: 'local-shipping', display_order: 7, is_active: true, created_at: '', updated_at: '' },
      { id: '8', code: 'security', name: 'Security', icon_name: 'security', display_order: 8, is_active: true, created_at: '', updated_at: '' },
      { id: '9', code: 'gardening', name: 'Gardening', icon_name: 'local-florist', display_order: 9, is_active: true, created_at: '', updated_at: '' },
      { id: '10', code: 'tutoring', name: 'Tutoring', icon_name: 'school', display_order: 10, is_active: true, created_at: '', updated_at: '' }
    ];
  };

  const handleCategoryPress = (category: SkillCategoryData) => {
    if (disabled) return;
    onCategorySelect(category);
  };

  const handleRetry = () => {
    fetchSkillCategories();
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent, style]}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading skill categories...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centerContent, style]}>
        <Text style={styles.errorText}>Failed to load skill categories</Text>
        <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>Select Your Primary Skill</Text>
      <Text style={styles.subtitle}>Choose the main service you want to offer</Text>
      
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.grid}>
          {categories.map((category) => {
            const isSelected = selectedCategory?.id === category.id;
            const iconName = CATEGORY_ICONS[category.code] || 'work';
            
            return (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryCard,
                  isSelected && styles.categoryCardSelected,
                  disabled && styles.categoryCardDisabled
                ]}
                onPress={() => handleCategoryPress(category)}
                disabled={disabled}
                activeOpacity={0.7}
              >
                <View style={styles.iconContainer}>
                  <MaterialIcons
                    name={iconName}
                    size={32}
                    color={isSelected ? '#FFFFFF' : '#007AFF'}
                  />
                </View>
                <Text style={[
                  styles.categoryName,
                  isSelected && styles.categoryNameSelected,
                  disabled && styles.categoryNameDisabled
                ]}>
                  {category.name}
                </Text>
                {category.description && (
                  <Text style={[
                    styles.categoryDescription,
                    isSelected && styles.categoryDescriptionSelected
                  ]}>
                    {category.description}
                  </Text>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
    textAlign: 'center',
  },
  scrollContent: {
    paddingBottom: 20,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryCard: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E5E5',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  categoryCardSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  categoryCardDisabled: {
    opacity: 0.5,
  },
  iconContainer: {
    marginBottom: 12,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 4,
  },
  categoryNameSelected: {
    color: '#FFFFFF',
  },
  categoryNameDisabled: {
    color: '#999',
  },
  categoryDescription: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    lineHeight: 16,
  },
  categoryDescriptionSelected: {
    color: '#E5E5E5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SkillCategorySelector;
