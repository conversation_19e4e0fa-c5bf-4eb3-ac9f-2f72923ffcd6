import { MapPin, Clock, IndianRupee, Users, Navigation, AlertCircle } from "lucide-react-native";
import { Job } from "../types";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import TrustBadge from "./TrustBadge";
import locationService from "../services/locationService";

interface JobCardProps {
  job: Job & {
    distance_km?: number;
    is_urgent?: boolean;
    hours_since_posted?: number;
    is_new?: boolean;
    budget_display?: string;
  };
  className?: any;
  onClick?: () => void;
  userLocation?: { latitude: number; longitude: number } | null;
}

const Badge = ({ children, style }: { children: React.ReactNode, style?: any }) => (
  <View style={[styles.badgeBase, style]}>
    <Text style={styles.badgeText}>{children}</Text>
  </View>
);

const JobCard = ({ job, className, onClick, userLocation }: JobCardProps) => {
  const getUrgencyBadge = (urgency: Job['urgency']) => {
    switch (urgency) {
      case 'urgent':
        return (
          <View style={[styles.urgencyBadge, styles.urgentBadge]}>
            <AlertCircle size={12} color="#DC2626" />
            <Text style={[styles.urgencyText, { color: '#DC2626' }]}>Urgent</Text>
          </View>
        );
      case 'high':
        return (
          <View style={[styles.urgencyBadge, styles.highBadge]}>
            <Clock size={12} color="#EA580C" />
            <Text style={[styles.urgencyText, { color: '#EA580C' }]}>High Priority</Text>
          </View>
        );
      case 'medium':
        return (
          <View style={[styles.urgencyBadge, styles.mediumBadge]}>
            <Text style={[styles.urgencyText, { color: '#059669' }]}>Medium</Text>
          </View>
        );
      case 'low':
        return (
          <View style={[styles.urgencyBadge, styles.lowBadge]}>
            <Text style={[styles.urgencyText, { color: '#6B7280' }]}>Low</Text>
          </View>
        );
      default:
        return null;
    }
  };

  const formatBudget = () => {
    if (job.budget_display) {
      return job.budget_display;
    }
    if (job.budget_min && job.budget_max) {
      return `₹${job.budget_min} - ₹${job.budget_max}`;
    } else if (job.budget_min) {
      return `₹${job.budget_min}+`;
    }
    return "Budget negotiable";
  };

  const getSkillCategoryLabel = (category: string) => {
    return category.replace('_', ' ').split(' ').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatTimeAgo = (createdAt: string) => {
    const now = new Date();
    const created = new Date(createdAt);
    const diffInHours = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60));

    if (job.hours_since_posted !== undefined) {
      const hours = job.hours_since_posted;
      if (hours < 1) return 'Just posted';
      if (hours < 24) return `${hours}h ago`;
      const days = Math.floor(hours / 24);
      return `${days}d ago`;
    }

    if (diffInHours < 1) return 'Just posted';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const days = Math.floor(diffInHours / 24);
    return `${days}d ago`;
  };

  const calculateDistance = () => {
    if (job.distance_km !== undefined) {
      return locationService.formatDistance(job.distance_km);
    }

    if (userLocation && job.location) {
      const distance = locationService.calculateDistance(
        userLocation.latitude,
        userLocation.longitude,
        job.location.latitude,
        job.location.longitude
      );
      return locationService.formatDistance(distance);
    }

    return null;
  };

  const distance = calculateDistance();
  const isUrgent = job.is_urgent || job.urgency === 'urgent' || job.urgency === 'high';
  const isNew = job.is_new || (job.hours_since_posted !== undefined && job.hours_since_posted <= 2);

  return (
    <TouchableOpacity
      style={[
        styles.card,
        isUrgent && styles.urgentCard,
        className
      ]}
      onPress={onClick}
      activeOpacity={0.7}
    >
      {/* New indicator for recently posted jobs */}
      {isNew && (
        <View style={styles.newIndicator}>
          <Text style={styles.newIndicatorText}>NEW</Text>
        </View>
      )}

      <View style={styles.cardHeader}>
        <View style={{ flex: 1 }}>
          <Text style={styles.title} numberOfLines={2}>
            {job.title}
          </Text>
          <Text style={styles.mutedText}>
            {getSkillCategoryLabel(job.skill_category)}
          </Text>
        </View>
        {getUrgencyBadge(job.urgency)}
      </View>
      
      <View style={styles.cardContent}>
        <Text style={styles.mutedText} numberOfLines={2}>
          {job.description}
        </Text>
        
        <View style={styles.skillsContainer}>
          {job.skill_subcategories.slice(0, 3).map((skill, index) => (
            <Badge key={index}>
              {skill}
            </Badge>
          ))}
          {job.skill_subcategories.length > 3 && (
            <Badge>
              +{job.skill_subcategories.length - 3} more
            </Badge>
          )}
        </View>
        
        {/* Enhanced info section with distance and time */}
        <View style={styles.infoSection}>
          {/* Location and Distance Row */}
          <View style={styles.infoRow}>
            <MapPin size={16} color="#64748b" />
            <Text style={[styles.mutedText, { flex: 1 }]} numberOfLines={1}>
              {job.location.address}
            </Text>
            {distance && (
              <View style={styles.distanceContainer}>
                <Navigation size={12} color="#059669" />
                <Text style={styles.distanceText}>{distance}</Text>
              </View>
            )}
          </View>

          {/* Budget and Applications Row */}
          <View style={styles.infoRowSpaced}>
            <View style={styles.infoRow}>
              <IndianRupee size={16} color="#059669" />
              <Text style={styles.budgetText}>{formatBudget()}</Text>
            </View>

            <View style={styles.infoRow}>
              <Users size={12} color="#64748b" />
              <Text style={styles.smallMutedText}>{job.applications_count} applied</Text>
            </View>
          </View>

          {/* Time and Duration Row */}
          <View style={styles.infoRowSpaced}>
            <View style={styles.infoRow}>
              <Clock size={12} color="#64748b" />
              <Text style={styles.smallMutedText}>{formatTimeAgo(job.created_at)}</Text>
            </View>

            {job.estimated_duration_hours && (
              <View style={styles.infoRow}>
                <Clock size={12} color="#64748b" />
                <Text style={styles.smallMutedText}>Est. {job.estimated_duration_hours}h</Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderLeftWidth: 4,
    borderLeftColor: '#059669', // trust-primary
    marginVertical: 6,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    position: 'relative',
    // Thumb-friendly minimum height
    minHeight: 120,
  },
  urgentCard: {
    borderLeftColor: '#DC2626', // Red for urgent jobs
    borderColor: '#FEE2E2',
    backgroundColor: '#FFFBFB',
  },
  newIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#059669',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    zIndex: 1,
  },
  newIndicatorText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  cardHeader: {
    paddingBottom: 12,
    paddingHorizontal: 16,
    paddingTop: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    gap: 12,
  },
  title: {
    fontWeight: '600',
    color: '#0f172a',
    fontSize: 16,
    lineHeight: 22,
  },
  mutedText: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
  smallMutedText: {
    fontSize: 12,
    color: '#64748b',
    lineHeight: 16,
  },
  cardContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    gap: 12,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  badgeBase: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 9999,
  },
  badgeText: {
    fontSize: 12,
    color: '#334155',
  },
  urgencyBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
  },
  urgentBadge: {
    backgroundColor: '#FEE2E2',
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  highBadge: {
    backgroundColor: '#FED7AA',
    borderWidth: 1,
    borderColor: '#FDBA74',
  },
  mediumBadge: {
    backgroundColor: '#D1FAE5',
    borderWidth: 1,
    borderColor: '#A7F3D0',
  },
  lowBadge: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  urgencyText: {
    fontSize: 11,
    fontWeight: '500',
  },
  infoSection: {
    gap: 8,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  infoRowSpaced: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: '#F0FDF4',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  distanceText: {
    fontSize: 12,
    color: '#059669',
    fontWeight: '500',
  },
  budgetText: {
    color: '#059669',
    fontWeight: '600',
    fontSize: 14,
  },
});

export default JobCard;
