import React, { useState } from 'react'
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Image
} from 'react-native'
import { Camera, Upload, User, X } from 'lucide-react-native'
import * as ImagePicker from 'expo-image-picker'
import * as ImageManipulator from 'expo-image-manipulator'
import { apiClient } from '../lib/supabase'

interface ProfilePhotoUploadProps {
  currentImageUrl?: string | null
  onImageUploaded?: (imageUrl: string) => void
  onImageDeleted?: () => void
  size?: number
  showDeleteButton?: boolean
}

const ProfilePhotoUpload: React.FC<ProfilePhotoUploadProps> = ({
  currentImageUrl,
  onImageUploaded,
  onImageDeleted,
  size = 120,
  showDeleteButton = true
}) => {
  const [isUploading, setIsUploading] = useState(false)
  const [localImageUri, setLocalImageUri] = useState<string | null>(null)

  const requestPermissions = async () => {
    const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync()
    const { status: libraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync()
    
    if (cameraStatus !== 'granted' || libraryStatus !== 'granted') {
      Alert.alert(
        'Permissions Required',
        'Camera and photo library permissions are required to upload profile photos.',
        [{ text: 'OK' }]
      )
      return false
    }
    return true
  }

  const processImage = async (uri: string) => {
    try {
      // Crop and resize image to square format
      const manipulatedImage = await ImageManipulator.manipulateAsync(
        uri,
        [
          { resize: { width: 400, height: 400 } }
        ],
        {
          compress: 0.8,
          format: ImageManipulator.SaveFormat.JPEG,
          base64: false
        }
      )
      
      return manipulatedImage.uri
    } catch (error) {
      console.error('Image processing error:', error)
      throw new Error('Failed to process image')
    }
  }

  const uploadImage = async (imageUri: string) => {
    try {
      setIsUploading(true)

      // Create FormData for file upload
      const formData = new FormData()
      
      // Get file info
      const filename = imageUri.split('/').pop() || 'profile.jpg'
      const match = /\.(\w+)$/.exec(filename)
      const type = match ? `image/${match[1]}` : 'image/jpeg'

      formData.append('profileImage', {
        uri: imageUri,
        name: filename,
        type
      } as any)

      // Upload to backend
      const response = await fetch(`${apiClient.apiBaseUrl}/auth/upload-profile-image`, {
        method: 'POST',
        headers: {
          ...await apiClient.getPublicAuthHeaders(),
          'Content-Type': 'multipart/form-data'
        },
        body: formData
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Upload failed')
      }

      const imageUrl = result.data?.profile_image_url
      if (imageUrl) {
        setLocalImageUri(imageUri)
        onImageUploaded?.(imageUrl)
      }

      Alert.alert('Success', 'Profile photo uploaded successfully!')

    } catch (error) {
      console.error('Upload error:', error)
      Alert.alert(
        'Upload Failed',
        error instanceof Error ? error.message : 'Failed to upload image. Please try again.'
      )
    } finally {
      setIsUploading(false)
    }
  }

  const handleImagePicker = () => {
    Alert.alert(
      'Select Photo',
      'Choose how you want to add your profile photo',
      [
        {
          text: 'Camera',
          onPress: () => openCamera()
        },
        {
          text: 'Photo Library',
          onPress: () => openImageLibrary()
        },
        {
          text: 'Cancel',
          style: 'cancel'
        }
      ]
    )
  }

  const openCamera = async () => {
    const hasPermission = await requestPermissions()
    if (!hasPermission) return

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      })

      if (!result.canceled && result.assets[0]) {
        const processedUri = await processImage(result.assets[0].uri)
        await uploadImage(processedUri)
      }
    } catch (error) {
      console.error('Camera error:', error)
      Alert.alert('Error', 'Failed to take photo. Please try again.')
    }
  }

  const openImageLibrary = async () => {
    const hasPermission = await requestPermissions()
    if (!hasPermission) return

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      })

      if (!result.canceled && result.assets[0]) {
        const processedUri = await processImage(result.assets[0].uri)
        await uploadImage(processedUri)
      }
    } catch (error) {
      console.error('Image library error:', error)
      Alert.alert('Error', 'Failed to select photo. Please try again.')
    }
  }

  const handleDeleteImage = async () => {
    Alert.alert(
      'Delete Photo',
      'Are you sure you want to remove your profile photo?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsUploading(true)
              
              const response = await apiClient.delete('/auth/profile-image')
              
              if (!response.success) {
                throw new Error(response.error || 'Failed to delete image')
              }

              setLocalImageUri(null)
              onImageDeleted?.()
              Alert.alert('Success', 'Profile photo removed successfully!')

            } catch (error) {
              console.error('Delete error:', error)
              Alert.alert('Error', 'Failed to delete photo. Please try again.')
            } finally {
              setIsUploading(false)
            }
          }
        }
      ]
    )
  }

  const displayImageUri = localImageUri || currentImageUrl
  const containerSize = { width: size, height: size }

  return (
    <View style={styles.container}>
      <View style={[styles.imageContainer, containerSize]}>
        {displayImageUri ? (
          <Image source={{ uri: displayImageUri }} style={[styles.image, containerSize]} />
        ) : (
          <View style={[styles.placeholder, containerSize]}>
            <User size={size * 0.4} color="#9CA3AF" />
          </View>
        )}
        
        {isUploading && (
          <View style={[styles.loadingOverlay, containerSize]}>
            <ActivityIndicator size="large" color="#3B82F6" />
          </View>
        )}

        {showDeleteButton && displayImageUri && !isUploading && (
          <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteImage}>
            <X size={16} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </View>

      <TouchableOpacity
        style={[styles.uploadButton, isUploading && styles.uploadButtonDisabled]}
        onPress={handleImagePicker}
        disabled={isUploading}
      >
        {displayImageUri ? (
          <>
            <Camera size={20} color="#3B82F6" />
            <Text style={styles.uploadButtonText}>Change Photo</Text>
          </>
        ) : (
          <>
            <Upload size={20} color="#3B82F6" />
            <Text style={styles.uploadButtonText}>Add Photo</Text>
          </>
        )}
      </TouchableOpacity>

      <Text style={styles.helperText}>
        {displayImageUri ? 'Tap to change your profile photo' : 'Add a profile photo (optional)'}
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginVertical: 20
  },
  imageContainer: {
    borderRadius: 60,
    overflow: 'hidden',
    position: 'relative',
    marginBottom: 12
  },
  image: {
    borderRadius: 60
  },
  placeholder: {
    backgroundColor: '#F3F4F6',
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderStyle: 'dashed'
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center'
  },
  deleteButton: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#EF4444',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF'
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F8FAFC',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    gap: 8
  },
  uploadButtonDisabled: {
    opacity: 0.5
  },
  uploadButtonText: {
    color: '#3B82F6',
    fontSize: 14,
    fontWeight: '500'
  },
  helperText: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 8,
    maxWidth: 200
  }
})

export default ProfilePhotoUpload
