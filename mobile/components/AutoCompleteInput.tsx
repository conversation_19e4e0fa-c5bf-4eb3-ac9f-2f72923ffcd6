import React, { useState, useEffect, useRef, useCallback } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  ActivityIndicator
} from 'react-native'
import {
  Search,
  X,
  Lightbulb,
  Clock,
  TrendingUp
} from 'lucide-react-native'
import { jobService } from '../services/jobService'

interface Suggestion {
  id: string
  text: string
  category?: string
  popularity?: number
  type: 'recent' | 'popular' | 'ai_generated' | 'template'
}

interface AutoCompleteInputProps {
  value: string
  onChangeText: (text: string) => void
  placeholder?: string
  maxLength?: number
  error?: string
  skillCategory?: string
  onSuggestionSelect?: (suggestion: Suggestion) => void
  showSuggestions?: boolean
  minCharsForSuggestions?: number
  maxSuggestions?: number
  style?: any
}

const AutoCompleteInput: React.FC<AutoCompleteInputProps> = ({
  value,
  onChangeText,
  placeholder = "Enter job title",
  maxLength = 200,
  error,
  skillCategory,
  onSuggestionSelect,
  showSuggestions = true,
  minCharsForSuggestions = 2,
  maxSuggestions = 5,
  style
}) => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<TextInput>(null)

  // Fallback mock suggestions for when API is unavailable
  const getMockSuggestions = useCallback((query: string, category?: string): Suggestion[] => {
    const baseSuggestions: { [key: string]: Suggestion[] } = {
      'electrical': [
        { id: '1', text: 'Electrician for Home Wiring', type: 'popular', popularity: 95 },
        { id: '2', text: 'AC Installation and Repair', type: 'popular', popularity: 88 },
        { id: '3', text: 'Ceiling Fan Installation', type: 'popular', popularity: 82 },
        { id: '4', text: 'Light Fixture Installation', type: 'template', popularity: 75 },
        { id: '5', text: 'Emergency Electrical Repair', type: 'ai_generated', popularity: 55 }
      ],
      'plumbing': [
        { id: '8', text: 'Plumber for Pipe Repair', type: 'popular', popularity: 92 },
        { id: '9', text: 'Bathroom Plumbing Installation', type: 'popular', popularity: 85 },
        { id: '10', text: 'Kitchen Sink Installation', type: 'template', popularity: 78 },
        { id: '11', text: 'Toilet Installation and Repair', type: 'template', popularity: 72 }
      ],
      'carpentry': [
        { id: '13', text: 'Carpenter for Custom Furniture', type: 'popular', popularity: 90 },
        { id: '14', text: 'Kitchen Cabinet Installation', type: 'popular', popularity: 87 },
        { id: '15', text: 'Door and Window Frame Repair', type: 'template', popularity: 80 }
      ],
      'cooking': [
        { id: '18', text: 'Cook for Home Meals', type: 'popular', popularity: 94 },
        { id: '19', text: 'Chef for Party Catering', type: 'popular', popularity: 89 },
        { id: '20', text: 'Daily Meal Preparation', type: 'template', popularity: 81 }
      ],
      'cleaning': [
        { id: '22', text: 'House Cleaning Service', type: 'popular', popularity: 96 },
        { id: '23', text: 'Deep Cleaning for Home', type: 'popular', popularity: 91 },
        { id: '24', text: 'Office Cleaning Service', type: 'template', popularity: 84 }
      ]
    }

    const defaultSuggestions: Suggestion[] = [
      { id: '26', text: 'General Helper Needed', type: 'template', popularity: 70 },
      { id: '27', text: 'Skilled Worker Required', type: 'template', popularity: 65 },
      { id: '28', text: 'Professional Service Needed', type: 'ai_generated', popularity: 60 }
    ]

    let categorySuggestions = baseSuggestions[category || ''] || defaultSuggestions

    // Filter suggestions based on query
    const filtered = categorySuggestions.filter(suggestion =>
      suggestion.text.toLowerCase().includes(query.toLowerCase())
    )

    return filtered.slice(0, maxSuggestions)
  }, [maxSuggestions])

  // Get suggestions from backend API
  const getSuggestions = useCallback(async (query: string, category?: string): Promise<Suggestion[]> => {
    try {
      const result = await jobService.getJobTitleSuggestions(query, category)

      if (result.error || !result.data) {
        console.warn('Failed to get title suggestions:', result.error)
        // Fall back to mock data if API fails
        return getMockSuggestions(query, category)
      }

      // Transform API response to component format
      return result.data.map(suggestion => ({
        id: suggestion.id,
        text: suggestion.title,
        category: suggestion.category,
        popularity: suggestion.popularity,
        type: suggestion.type as 'recent' | 'popular' | 'ai_generated' | 'template'
      })).slice(0, maxSuggestions)

    } catch (error) {
      console.error('Get suggestions error:', error)
      // Fall back to mock data on error
      return getMockSuggestions(query, category)
    }
  }, [maxSuggestions, getMockSuggestions])

  // Fetch suggestions when input changes
  useEffect(() => {
    if (showSuggestions && value.length >= minCharsForSuggestions) {
      setIsLoading(true)
      getSuggestions(value, skillCategory)
        .then(results => {
          setSuggestions(results)
          setShowDropdown(results.length > 0 && isFocused)
          setIsLoading(false)
        })
        .catch(() => {
          setIsLoading(false)
        })
    } else {
      setSuggestions([])
      setShowDropdown(false)
    }
  }, [value, skillCategory, showSuggestions, minCharsForSuggestions, maxSuggestions, isFocused, getSuggestions])

  const handleSuggestionPress = (suggestion: Suggestion) => {
    onChangeText(suggestion.text)
    setShowDropdown(false)
    inputRef.current?.blur()
    onSuggestionSelect?.(suggestion)
  }

  const handleFocus = () => {
    setIsFocused(true)
    if (suggestions.length > 0) {
      setShowDropdown(true)
    }
  }

  const handleBlur = () => {
    setIsFocused(false)
    // Delay hiding dropdown to allow suggestion selection
    setTimeout(() => setShowDropdown(false), 150)
  }

  const clearInput = () => {
    onChangeText('')
    setShowDropdown(false)
    inputRef.current?.focus()
  }

  const getSuggestionIcon = (type: Suggestion['type']) => {
    switch (type) {
      case 'recent':
        return <Clock size={14} color="#6B7280" />
      case 'popular':
        return <TrendingUp size={14} color="#10B981" />
      case 'ai_generated':
        return <Lightbulb size={14} color="#F59E0B" />
      case 'template':
        return <Search size={14} color="#3B82F6" />
      default:
        return <Search size={14} color="#6B7280" />
    }
  }

  const getSuggestionTypeLabel = (type: Suggestion['type']) => {
    switch (type) {
      case 'recent': return 'Recent'
      case 'popular': return 'Popular'
      case 'ai_generated': return 'AI Suggested'
      case 'template': return 'Template'
      default: return ''
    }
  }

  const renderSuggestion = ({ item }: { item: Suggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleSuggestionPress(item)}
    >
      <View style={styles.suggestionContent}>
        <View style={styles.suggestionHeader}>
          {getSuggestionIcon(item.type)}
          <Text style={styles.suggestionText}>{item.text}</Text>
        </View>
        <View style={styles.suggestionMeta}>
          <Text style={styles.suggestionType}>{getSuggestionTypeLabel(item.type)}</Text>
          {item.popularity && item.popularity > 80 && (
            <View style={styles.popularBadge}>
              <Text style={styles.popularText}>Popular</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  )

  return (
    <View style={[styles.container, style]}>
      {/* Input Field */}
      <View style={[styles.inputContainer, error && styles.inputError, isFocused && styles.inputFocused]}>
        <Search size={20} color="#6B7280" />
        <TextInput
          ref={inputRef}
          style={styles.input}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          maxLength={maxLength}
          onFocus={handleFocus}
          onBlur={handleBlur}
          autoCorrect={false}
          autoCapitalize="words"
        />
        {value.length > 0 && (
          <TouchableOpacity onPress={clearInput} style={styles.clearButton}>
            <X size={18} color="#6B7280" />
          </TouchableOpacity>
        )}
        {isLoading && (
          <ActivityIndicator size="small" color="#3B82F6" style={styles.loadingIndicator} />
        )}
      </View>

      {/* Error Message */}
      {error && <Text style={styles.errorText}>{error}</Text>}

      {/* Suggestions Dropdown */}
      {showDropdown && (
        <View style={styles.dropdown}>
          {suggestions.length > 0 ? (
            <FlatList
              data={suggestions}
              renderItem={renderSuggestion}
              keyExtractor={(item) => item.id}
              style={styles.suggestionsList}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.noSuggestions}>
              <Text style={styles.noSuggestionsText}>No suggestions found</Text>
              <Text style={styles.noSuggestionsSubtext}>Try a different search term</Text>
            </View>
          )}
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1000
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    gap: 12
  },
  inputError: {
    borderColor: '#EF4444'
  },
  inputFocused: {
    borderColor: '#3B82F6',
    shadowColor: '#3B82F6',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
    paddingVertical: 0
  },
  clearButton: {
    padding: 4
  },
  loadingIndicator: {
    marginLeft: 8
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444',
    marginTop: 4
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderTopWidth: 0,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    zIndex: 1001
  },
  suggestionsList: {
    maxHeight: 200
  },
  suggestionItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6'
  },
  suggestionContent: {
    flex: 1
  },
  suggestionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 8
  },
  suggestionText: {
    fontSize: 15,
    color: '#374151',
    fontWeight: '500',
    flex: 1
  },
  suggestionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  suggestionType: {
    fontSize: 12,
    color: '#6B7280'
  },
  popularBadge: {
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10
  },
  popularText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#D97706'
  },
  noSuggestions: {
    padding: 20,
    alignItems: 'center'
  },
  noSuggestionsText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
    marginBottom: 4
  },
  noSuggestionsSubtext: {
    fontSize: 12,
    color: '#9CA3AF'
  }
})

export default AutoCompleteInput