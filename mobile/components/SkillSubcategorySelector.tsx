// =====================================================
// SKILL SUBCATEGORY SELECTOR COMPONENT
// =====================================================
// Component for multi-selecting skill subcategories
// Task: US-002.2 - Create Skill Categories Master Data System

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TextInput
} from 'react-native';
import { SkillSubcategoryData, SkillSubcategoriesResponse } from '../types';
import { MaterialIcons } from '@expo/vector-icons';

interface SkillSubcategorySelectorProps {
  categoryCode: string;
  selectedSubcategories: SkillSubcategoryData[];
  onSubcategoriesChange: (subcategories: SkillSubcategoryData[]) => void;
  disabled?: boolean;
  maxSelections?: number;
  style?: any;
}

const SkillSubcategorySelector: React.FC<SkillSubcategorySelectorProps> = ({
  categoryCode,
  selectedSubcategories,
  onSubcategoriesChange,
  disabled = false,
  maxSelections = 5,
  style
}) => {
  const [subcategories, setSubcategories] = useState<SkillSubcategoryData[]>([]);
  const [filteredSubcategories, setFilteredSubcategories] = useState<SkillSubcategoryData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const fetchSkillSubcategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Replace with actual API endpoint when backend is deployed
      const response = await fetch(`/api/skills/subcategories?categoryCode=${categoryCode}`);

      if (!response.ok) {
        throw new Error('Failed to fetch skill subcategories');
      }

      const data: SkillSubcategoriesResponse = await response.json();

      if (data.success && data.data) {
        setSubcategories(data.data);
      } else {
        throw new Error(data.message || data.error || 'Failed to fetch skill subcategories');
      }
    } catch (err) {
      console.error('Error fetching skill subcategories:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');

      // For development, use mock data
      setSubcategories(getMockSubcategories(categoryCode));
    } finally {
      setLoading(false);
    }
  }, [categoryCode]);

  useEffect(() => {
    if (categoryCode) {
      fetchSkillSubcategories();
    }
  }, [categoryCode, fetchSkillSubcategories]);

  useEffect(() => {
    // Filter subcategories based on search query
    if (searchQuery.trim()) {
      const filtered = subcategories.filter(sub =>
        sub.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (sub.description && sub.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredSubcategories(filtered);
    } else {
      setFilteredSubcategories(subcategories);
    }
  }, [searchQuery, subcategories]);

  const getMockSubcategories = (code: string): SkillSubcategoryData[] => {
    const mockData: Record<string, SkillSubcategoryData[]> = {
      'electrical': [
        { id: '1', category_id: '1', code: 'house_wiring', name: 'House Wiring', display_order: 1, is_active: true, created_at: '', updated_at: '' },
        { id: '2', category_id: '1', code: 'appliance_repair', name: 'Appliance Repair', display_order: 2, is_active: true, created_at: '', updated_at: '' },
        { id: '3', category_id: '1', code: 'fan_installation', name: 'Fan Installation', display_order: 3, is_active: true, created_at: '', updated_at: '' },
        { id: '4', category_id: '1', code: 'light_fitting', name: 'Light Fitting', display_order: 4, is_active: true, created_at: '', updated_at: '' },
        { id: '5', category_id: '1', code: 'switch_board', name: 'Switch Board Work', display_order: 5, is_active: true, created_at: '', updated_at: '' },
      ],
      'plumbing': [
        { id: '6', category_id: '2', code: 'pipe_repair', name: 'Pipe Repair', display_order: 1, is_active: true, created_at: '', updated_at: '' },
        { id: '7', category_id: '2', code: 'bathroom_fitting', name: 'Bathroom Fitting', display_order: 2, is_active: true, created_at: '', updated_at: '' },
        { id: '8', category_id: '2', code: 'kitchen_plumbing', name: 'Kitchen Plumbing', display_order: 3, is_active: true, created_at: '', updated_at: '' },
        { id: '9', category_id: '2', code: 'water_heater', name: 'Water Heater', display_order: 4, is_active: true, created_at: '', updated_at: '' },
      ],
      'cooking': [
        { id: '10', category_id: '4', code: 'north_indian', name: 'North Indian', display_order: 1, is_active: true, created_at: '', updated_at: '' },
        { id: '11', category_id: '4', code: 'south_indian', name: 'South Indian', display_order: 2, is_active: true, created_at: '', updated_at: '' },
        { id: '12', category_id: '4', code: 'chinese', name: 'Chinese', display_order: 3, is_active: true, created_at: '', updated_at: '' },
        { id: '13', category_id: '4', code: 'catering', name: 'Catering', display_order: 4, is_active: true, created_at: '', updated_at: '' },
      ]
    };
    
    return mockData[code] || [];
  };

  const handleSubcategoryToggle = (subcategory: SkillSubcategoryData) => {
    if (disabled) return;

    const isSelected = selectedSubcategories.some(s => s.id === subcategory.id);
    
    if (isSelected) {
      // Remove from selection
      const updated = selectedSubcategories.filter(s => s.id !== subcategory.id);
      onSubcategoriesChange(updated);
    } else {
      // Add to selection (check max limit)
      if (selectedSubcategories.length >= maxSelections) {
        // Could show a toast or alert here
        return;
      }
      const updated = [...selectedSubcategories, subcategory];
      onSubcategoriesChange(updated);
    }
  };

  const handleRetry = () => {
    fetchSkillSubcategories();
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent, style]}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading subcategories...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centerContent, style]}>
        <Text style={styles.errorText}>Failed to load subcategories</Text>
        <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>Select Your Specializations</Text>
      <Text style={styles.subtitle}>
        Choose up to {maxSelections} specific skills ({selectedSubcategories.length}/{maxSelections} selected)
      </Text>

      {/* Search Input */}
      <View style={styles.searchContainer}>
        <MaterialIcons name="search" size={20} color="#666" />
        <TextInput
          style={styles.searchInput}
          placeholder="Search specializations..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          editable={!disabled}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <MaterialIcons name="close" size={20} color="#666" />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {filteredSubcategories.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>
              {searchQuery ? 'No matching specializations found' : 'No specializations available'}
            </Text>
          </View>
        ) : (
          <View style={styles.subcategoriesList}>
            {filteredSubcategories.map((subcategory) => {
              const isSelected = selectedSubcategories.some(s => s.id === subcategory.id);
              const canSelect = !isSelected && selectedSubcategories.length < maxSelections;
              
              return (
                <TouchableOpacity
                  key={subcategory.id}
                  style={[
                    styles.subcategoryItem,
                    isSelected && styles.subcategoryItemSelected,
                    disabled && styles.subcategoryItemDisabled,
                    !canSelect && !isSelected && styles.subcategoryItemMaxed
                  ]}
                  onPress={() => handleSubcategoryToggle(subcategory)}
                  disabled={disabled || (!canSelect && !isSelected)}
                  activeOpacity={0.7}
                >
                  <View style={styles.subcategoryContent}>
                    <View style={[
                      styles.checkbox,
                      isSelected && styles.checkboxSelected
                    ]}>
                      {isSelected && (
                        <MaterialIcons name="check" size={16} color="#FFFFFF" />
                      )}
                    </View>
                    <View style={styles.subcategoryInfo}>
                      <Text style={[
                        styles.subcategoryName,
                        isSelected && styles.subcategoryNameSelected,
                        disabled && styles.subcategoryNameDisabled
                      ]}>
                        {subcategory.name}
                      </Text>
                      {subcategory.description && (
                        <Text style={[
                          styles.subcategoryDescription,
                          isSelected && styles.subcategoryDescriptionSelected
                        ]}>
                          {subcategory.description}
                        </Text>
                      )}
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 16,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#1a1a1a',
  },
  scrollContent: {
    paddingBottom: 20,
  },
  subcategoriesList: {
    gap: 12,
  },
  subcategoryItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  subcategoryItemSelected: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  subcategoryItemDisabled: {
    opacity: 0.5,
  },
  subcategoryItemMaxed: {
    opacity: 0.3,
  },
  subcategoryContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#E5E5E5',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  subcategoryInfo: {
    flex: 1,
  },
  subcategoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a1a1a',
    marginBottom: 2,
  },
  subcategoryNameSelected: {
    color: '#007AFF',
  },
  subcategoryNameDisabled: {
    color: '#999',
  },
  subcategoryDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  subcategoryDescriptionSelected: {
    color: '#0056CC',
  },
  emptyState: {
    padding: 40,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SkillSubcategorySelector;
