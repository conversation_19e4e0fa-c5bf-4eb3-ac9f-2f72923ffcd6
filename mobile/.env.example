# Backend API Configuration
# Point to your Node.js backend server
EXPO_PUBLIC_API_BASE_URL=http://localhost:3000/api

# For production, use your deployed backend URL:
# EXPO_PUBLIC_API_BASE_URL=https://your-backend-domain.com/api

# Environment
EXPO_PUBLIC_ENV=development

# App Configuration
EXPO_PUBLIC_APP_NAME=Ozgaar
EXPO_PUBLIC_APP_VERSION=1.0.0

# Regional Settings
EXPO_PUBLIC_DEFAULT_COUNTRY_CODE=+91
EXPO_PUBLIC_DEFAULT_LANGUAGE=hindi

# Feature Flags
EXPO_PUBLIC_ENABLE_ANALYTICS=true
EXPO_PUBLIC_ENABLE_CRASH_REPORTING=true
