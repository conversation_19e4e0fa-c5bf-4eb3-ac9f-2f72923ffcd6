# Test Environment Setup Guide

## Overview

This document outlines the complete test environment setup for the Ozgaar platform, including Supabase test database configuration, mock external services, and comprehensive seed data for testing all user scenarios.

## Environment Structure

### Test Environments

| Environment | Purpose | Database | External Services |
|-------------|---------|----------|-------------------|
| **Local Test** | Developer unit testing | Local PostgreSQL | Mock services |
| **CI Test** | Automated testing in GitHub Actions | GitHub Actions PostgreSQL service | Mock services |
| **Integration Test** | End-to-end testing | Supabase test project | Real services (test keys) |

## 1. Local Test Environment Setup

### 1.1 Prerequisites

```bash
# Install required tools
npm install -g @supabase/cli
brew install postgresql  # macOS
# or
sudo apt-get install postgresql  # Ubuntu
```

### 1.2 Supabase Local Development

```bash
# Initialize Supabase in project
supabase init

# Start local Supabase (includes PostgreSQL, Auth, Storage, Edge Functions)
supabase start

# Apply database migrations
supabase db reset

# Generate TypeScript types
supabase gen types typescript --local > src/types/database.types.ts
```

### 1.3 Environment Variables

```bash
# .env.test.local
EXPO_PUBLIC_SUPABASE_URL=http://localhost:54321
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-local-anon-key
DATABASE_URL=postgresql://postgres:postgres@localhost:54322/postgres

# Mock service endpoints
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=mock-api-key
EXPO_PUBLIC_SMS_PROVIDER_URL=http://localhost:3001/mock-sms
EXPO_PUBLIC_TRANSLATION_API_URL=http://localhost:3002/mock-translate
```

## 2. Database Test Configuration

### 2.1 Test Database Schema

```sql
-- supabase/migrations/20250814000001_test_setup.sql

-- Create test-specific functions
CREATE OR REPLACE FUNCTION reset_test_data()
RETURNS void AS $$
BEGIN
  -- Clear all test data in correct order due to foreign keys
  DELETE FROM reviews;
  DELETE FROM job_applications;
  DELETE FROM jobs;
  DELETE FROM worker_personas;
  DELETE FROM users WHERE phone LIKE '+91555%'; -- Test phone numbers
  
  -- Reset sequences
  ALTER SEQUENCE IF EXISTS users_id_seq RESTART WITH 1;
  ALTER SEQUENCE IF EXISTS worker_personas_id_seq RESTART WITH 1;
  ALTER SEQUENCE IF EXISTS jobs_id_seq RESTART WITH 1;
END;
$$ LANGUAGE plpgsql;

-- Create test data insertion function
CREATE OR REPLACE FUNCTION seed_test_data()
RETURNS void AS $$
BEGIN
  -- Insert test users
  INSERT INTO users (id, phone, full_name, preferred_language, location, user_type, is_verified) VALUES
  ('550e8400-e29b-41d4-a716-446655440001', '+91**********', 'Test Worker Rajesh', 'hindi', ST_GeomFromText('POINT(77.2090 28.6139)', 4326), 'worker', true),
  ('550e8400-e29b-41d4-a716-446655440002', '+915551234502', 'Test Poster Priya', 'english', ST_GeomFromText('POINT(77.2100 28.6140)', 4326), 'poster', true),
  ('550e8400-e29b-41d4-a716-446655440003', '+915551234503', 'Test Multi-skill Amit', 'hindi', ST_GeomFromText('POINT(77.2110 28.6141)', 4326), 'both', true);
  
  -- Insert test worker personas
  INSERT INTO worker_personas (id, user_id, title, skill_category, experience_years, daily_rate, availability_pattern, travel_radius_km, is_active) VALUES
  ('650e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'Expert Electrician', 'electrical', 5, 800, '{"monday": [{"start": "09:00", "end": "18:00"}], "tuesday": [{"start": "09:00", "end": "18:00"}]}', 15, true),
  ('650e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003', 'Professional Driver', 'driving', 3, 600, '{"monday": [{"start": "06:00", "end": "22:00"}]}', 25, true),
  ('650e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003', 'Home Cook', 'cooking', 2, 400, '{"sunday": [{"start": "10:00", "end": "14:00"}]}', 10, true);
  
  -- Insert test jobs
  INSERT INTO jobs (id, poster_id, title, description, skill_category, location, budget_min, budget_max, status, expires_at) VALUES
  ('750e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', 'AC Repair Urgently Needed', 'AC not working, need immediate repair', 'electrical', ST_GeomFromText('POINT(77.2095 28.6138)', 4326), 500, 1000, 'active', NOW() + INTERVAL '7 days'),
  ('750e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440002', 'Weekend Cooking Service', 'Need cook for family gathering', 'cooking', ST_GeomFromText('POINT(77.2105 28.6142)', 4326), 300, 500, 'active', NOW() + INTERVAL '3 days');
END;
$$ LANGUAGE plpgsql;
```

### 2.2 Test Data Seed Script

```javascript
// scripts/seed-test-data.js
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function seedTestData() {
  try {
    // Reset existing test data
    await supabase.rpc('reset_test_data');
    console.log('✅ Test data reset completed');
    
    // Seed fresh test data
    await supabase.rpc('seed_test_data');
    console.log('✅ Test data seeded successfully');
    
    // Verify data insertion
    const { data: users } = await supabase
      .from('users')
      .select('*')
      .like('phone', '+91555%');
    
    console.log(`✅ Created ${users.length} test users`);
    
    const { data: personas } = await supabase
      .from('worker_personas')
      .select('*');
    
    console.log(`✅ Created ${personas.length} test personas`);
    
  } catch (error) {
    console.error('❌ Error seeding test data:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  seedTestData();
}

module.exports = { seedTestData };
```

## 3. Mock External Services

### 3.1 Google Maps API Mock

```javascript
// tests/mocks/google-maps-mock.js
class MockGoogleMapsAPI {
  static responses = {
    geocode: {
      results: [{
        formatted_address: "Test Address, Delhi, India",
        geometry: {
          location: { lat: 28.6139, lng: 77.2090 }
        }
      }]
    },
    distanceMatrix: {
      rows: [{
        elements: [{
          distance: { text: "5.2 km", value: 5200 },
          duration: { text: "15 mins", value: 900 },
          status: "OK"
        }]
      }]
    }
  };

  static mockGeocoding(address) {
    return Promise.resolve(this.responses.geocode);
  }

  static mockDistanceMatrix(origins, destinations) {
    return Promise.resolve(this.responses.distanceMatrix);
  }
}

module.exports = MockGoogleMapsAPI;
```

### 3.2 SMS Provider Mock

```javascript
// tests/mocks/sms-provider-mock.js
class MockSMSProvider {
  static sentMessages = [];

  static async sendOTP(phoneNumber, otp) {
    // Simulate SMS sending delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    this.sentMessages.push({
      to: phoneNumber,
      message: `Your Ozgaar OTP is: ${otp}`,
      timestamp: new Date(),
      success: true
    });
    
    return {
      success: true,
      messageId: `mock_msg_${Date.now()}`
    };
  }

  static getLastOTP(phoneNumber) {
    const lastMessage = this.sentMessages
      .filter(msg => msg.to === phoneNumber)
      .pop();
    
    if (lastMessage) {
      const otpMatch = lastMessage.message.match(/(\d{6})/);
      return otpMatch ? otpMatch[1] : null;
    }
    return null;
  }

  static reset() {
    this.sentMessages = [];
  }
}

module.exports = MockSMSProvider;
```

## 4. Test Configuration Files

### 4.1 Jest Configuration

```javascript
// jest.config.js
module.exports = {
  preset: '@testing-library/react-native',
  setupFilesAfterEnv: [
    '<rootDir>/tests/setup/jest-setup.js'
  ],
  testEnvironment: 'node',
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.(test|spec).{js,jsx,ts,tsx}',
    '<rootDir>/tests/**/*.(test|spec).{js,jsx,ts,tsx}'
  ],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@tests/(.*)$': '<rootDir>/tests/$1'
  },
  coverageDirectory: 'coverage',
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/types/**/*'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

### 4.2 Test Setup File

```javascript
// tests/setup/jest-setup.js
import 'react-native-gesture-handler/jestSetup';
import { jest } from '@jest/globals';
import MockSMSProvider from '../mocks/sms-provider-mock';
import MockGoogleMapsAPI from '../mocks/google-maps-mock';

// Mock external services
jest.mock('@react-native-google-signin/google-signin', () => ({
  GoogleSignin: {
    configure: jest.fn(),
    hasPlayServices: jest.fn(() => Promise.resolve(true)),
  }
}));

jest.mock('react-native-maps', () => {
  const MapView = () => null;
  MapView.Marker = () => null;
  return MapView;
});

// Setup global test utilities
global.mockSMSProvider = MockSMSProvider;
global.mockGoogleMaps = MockGoogleMapsAPI;

// Setup test database
beforeAll(async () => {
  const { seedTestData } = require('../../scripts/seed-test-data');
  await seedTestData();
});

// Clean up after each test
afterEach(() => {
  MockSMSProvider.reset();
  jest.clearAllMocks();
});
```

## 5. Integration Test Environment

### 5.1 Detox E2E Configuration

```javascript
// .detoxrc.js
module.exports = {
  testRunner: 'jest',
  runnerConfig: 'e2e/jest.config.js',
  configurations: {
    'android.emu.debug': {
      type: 'android.emulator',
      device: {
        avdName: 'Pixel_4_API_29'
      },
      app: {
        binaryPath: 'android/app/build/outputs/apk/debug/app-debug.apk',
        build: 'cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug'
      }
    },
    'android.emu.release': {
      type: 'android.emulator',
      device: {
        avdName: 'Pixel_4_API_29'
      },
      app: {
        binaryPath: 'android/app/build/outputs/apk/release/app-release.apk',
        build: 'cd android && ./gradlew assembleRelease assembleAndroidTest -DtestBuildType=release'
      }
    }
  }
};
```

### 5.2 E2E Test Example

```javascript
// e2e/auth-flow.e2e.js
describe('Authentication Flow', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  it('should complete phone registration successfully', async () => {
    // Test phone input
    await element(by.id('phone-input')).typeText('**********');
    await element(by.id('send-otp-button')).tap();
    
    // Wait for OTP screen
    await waitFor(element(by.id('otp-input')))
      .toBeVisible()
      .withTimeout(5000);
    
    // Get OTP from mock SMS provider (in real test environment)
    const otp = '123456'; // In real tests, get from mock service
    await element(by.id('otp-input')).typeText(otp);
    await element(by.id('verify-otp-button')).tap();
    
    // Verify navigation to language selection
    await expect(element(by.id('language-selection'))).toBeVisible();
  });

  it('should create worker persona successfully', async () => {
    // Assuming user is logged in
    await element(by.id('create-persona-button')).tap();
    
    await element(by.id('persona-title-input')).typeText('Test Electrician');
    await element(by.id('skill-category-dropdown')).tap();
    await element(by.text('Electrical')).tap();
    await element(by.id('experience-input')).typeText('5');
    await element(by.id('daily-rate-input')).typeText('800');
    
    await element(by.id('save-persona-button')).tap();
    
    // Verify persona appears in list
    await expect(element(by.text('Test Electrician'))).toBeVisible();
  });
});
```

## 6. Package.json Scripts

```json
{
  "scripts": {
    "test:unit": "jest",
    "test:unit:watch": "jest --watch",
    "test:integration": "jest --config jest.integration.config.js",
    "test:e2e:android": "detox test --configuration android.emu.debug",
    "test:e2e:android:release": "detox test --configuration android.emu.release",
    "db:setup:test": "supabase db reset --local",
    "db:seed:test": "node scripts/seed-test-data.js",
    "test:coverage": "jest --coverage",
    "test:ci": "npm run test:unit && npm run test:integration"
  }
}
```

This comprehensive test environment setup ensures all critical testing scenarios are covered with proper isolation, realistic data, and reliable mock services.