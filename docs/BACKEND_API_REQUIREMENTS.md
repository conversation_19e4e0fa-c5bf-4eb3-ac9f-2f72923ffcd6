# 🔧 Backend API Requirements - Ozgaar Mobile Web App

## 📋 Overview
This document outlines comprehensive backend API requirements for all 54 screens in the Ozgaar mobile web application. Each endpoint includes data models, request/response formats, and field specifications based on frontend implementation analysis.

**Project Status**: All 54 screens analyzed  
**API Endpoints Required**: 45+ endpoints across 8 major modules  
**Data Models**: 12 core entities with relationships  

---

## 🏗 Core Data Models

### 1. User Model
```typescript
interface User {
  id: string;                    // UUID primary key
  phone: string;                 // +91XXXXXXXXXX format
  phoneVerified: boolean;        // OTP verification status
  email?: string;                // Optional email
  fullName: string;              // Display name
  avatar?: string;               // Profile photo URL
  location: string;              // "City, State" format
  isActive: boolean;             // Account status
  createdAt: string;             // ISO timestamp
  updatedAt: string;             // ISO timestamp
  
  // Mode-specific profiles
  workerProfile?: WorkerProfile;
  posterProfile?: PosterProfile;
  
  // Settings
  settings: UserSettings;
  
  // Verification
  isVerified: boolean;           // Document verification
  verificationDocuments?: VerificationDocument[];
}

interface UserSettings {
  notifications: {
    jobAlerts: boolean;
    messages: boolean;
    payments: boolean;
    marketing: boolean;
    pushEnabled: boolean;
    emailEnabled: boolean;
    smsEnabled: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'private' | 'contacts';
    showPhone: boolean;
    showEmail: boolean;
    showLocation: boolean;
  };
  language: 'english' | 'hindi';
  preferences: {
    autoApply: boolean;          // Worker only
    jobRadius: number;           // Worker only (km)
    workingHours: 'flexible' | 'morning' | 'evening'; // Worker only
  };
}
```

### 2. Worker Profile Model
```typescript
interface WorkerProfile {
  userId: string;                // Foreign key to User
  primarySkill: string;          // Main skill category
  skills: string[];              // All skills
  experience: string;            // "0-1", "2-5", "5-10", "10+" years
  hourlyRate?: number;           // ₹ per hour
  dailyRate?: number;            // ₹ per day
  about?: string;                // Bio (max 200 chars)
  phoneVisible: boolean;         // Show phone to employers
  
  // Stats
  completedJobs: number;
  totalEarnings: number;
  rating: number;                // 0-5 stars
  reviewCount: number;
  
  // Portfolio
  portfolioPhotos?: string[];     // Photo URLs
  
  // Active personas (skills)
  personas: WorkerPersona[];
  
  // Availability
  isAvailable: boolean;
  availabilitySchedule?: AvailabilitySchedule;
}

interface WorkerPersona {
  id: string;
  skill: string;                 // e.g., "Plumber"
  experience: string;
  hourlyRate?: number;
  dailyRate?: number;
  isActive: boolean;
  completedJobs: number;
  rating: number;
  reviewCount: number;
}

interface AvailabilitySchedule {
  monday: TimeSlot[];
  tuesday: TimeSlot[];
  wednesday: TimeSlot[];
  thursday: TimeSlot[];
  friday: TimeSlot[];
  saturday: TimeSlot[];
  sunday: TimeSlot[];
}

interface TimeSlot {
  startTime: string;             // "09:00"
  endTime: string;               // "17:00"
}
```

### 3. Poster Profile Model
```typescript
interface PosterProfile {
  userId: string;                // Foreign key to User
  companyName?: string;          // Business name
  about?: string;                // Company description
  
  // Stats
  jobsPosted: number;
  totalSpent: number;
  rating: number;                // 0-5 stars
  reviewCount: number;
  
  // Verification
  businessVerified: boolean;
  businessDocuments?: string[];  // Document URLs
}
```

### 4. Job Model
```typescript
interface Job {
  id: string;                    // UUID primary key
  posterId: string;              // Foreign key to User
  title: string;                 // Job title
  description: string;           // Detailed description
  category: string;              // Skill category
  subcategory?: string;          // Specific skill
  
  // Location
  location: string;              // "Area, City"
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  
  // Compensation
  rate: number;                  // Amount
  rateType: 'hourly' | 'daily' | 'fixed';
  duration: string;              // "2-3 hours", "1 day", etc.
  
  // Requirements
  requirements: string[];        // List of requirements
  skillsRequired: string[];      // Required skills
  experienceLevel: 'beginner' | 'intermediate' | 'expert';
  
  // Status
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  urgency: 'normal' | 'urgent';
  
  // Photos
  photos?: string[];              // Job site photos
  
  // Metadata
  createdAt: string;             // ISO timestamp
  updatedAt: string;
  expiresAt?: string;            // Job expiry
  
  // Stats
  viewCount: number;
  applicationCount: number;
  
  // Application settings
  maxApplications?: number;
  autoAccept: boolean;
  
  // Work details (when job is active)
  assignedWorkerId?: string;
  startedAt?: string;
  completedAt?: string;
  workPhotos?: string[];         // Progress photos
  
  // Payment
  paymentStatus: 'pending' | 'paid' | 'disputed';
  paymentMethod?: string;
  transactionId?: string;
}
```

### 5. Job Application Model
```typescript
interface JobApplication {
  id: string;                    // UUID primary key
  jobId: string;                 // Foreign key to Job
  workerId: string;              // Foreign key to User
  personaId?: string;            // Which skill/persona applied
  
  // Application details
  coverLetter?: string;          // Application message
  proposedRate?: number;         // Counter-offer rate
  proposedRateType?: 'hourly' | 'daily' | 'fixed';
  
  // Status
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
  
  // Timestamps
  appliedAt: string;             // ISO timestamp
  respondedAt?: string;          // When poster responded
  
  // Communication
  hasMessages: boolean;
  lastMessageAt?: string;
}
```

### 6. Message Model
```typescript
interface Message {
  id: string;                    // UUID primary key
  conversationId: string;        // Foreign key to Conversation
  senderId: string;              // Foreign key to User
  receiverId: string;            // Foreign key to User
  text: string;                  // Message content
  timestamp: string;             // ISO timestamp
  status: 'sent' | 'delivered' | 'read';
  messageType: 'text' | 'image' | 'document';
  attachments?: MessageAttachment[];
}

interface Conversation {
  id: string;                    // UUID primary key
  jobId: string;                 // Foreign key to Job
  workerId: string;              // Foreign key to User
  posterId: string;              // Foreign key to User
  lastMessage?: Message;
  unreadCount: number;           // For current user
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface MessageAttachment {
  id: string;
  url: string;                   // File URL
  fileName: string;
  fileSize: number;              // Bytes
  mimeType: string;
}
```

### 7. Payment Model
```typescript
interface Payment {
  id: string;                    // UUID primary key
  jobId: string;                 // Foreign key to Job
  payerId: string;               // Foreign key to User (poster)
  payeeId: string;               // Foreign key to User (worker)
  amount: number;                // Amount in INR
  currency: string;              // "INR"

  // Payment details
  method: 'cash';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'disputed' | 'refunded';

  // Transaction details
  transactionId?: string;        // External transaction ID ( for now, supported cash only )
  gatewayResponse?: any;         // Payment gateway response ( for now, supported cash only )

  // Dispute handling
  hasDispute: boolean;
  disputeReason?: string;
  disputeStatus?: 'open' | 'resolved' | 'escalated';

  // Timestamps
  createdAt: string;
  processedAt?: string;
  completedAt?: string;

  // Metadata
  description?: string;
  receiptUrl?: string;
}
```

### 8. Review Model
```typescript
interface Review {
  id: string;                    // UUID primary key
  jobId: string;                 // Foreign key to Job
  reviewerId: string;            // Foreign key to User (who gives review)
  revieweeId: string;            // Foreign key to User (who receives review)

  // Review content
  rating: number;                // 1-5 stars
  comment?: string;              // Optional review text

  // Review type
  reviewType: 'worker_to_poster' | 'poster_to_worker';

  // Categories (for detailed feedback)
  categories?: {
    communication: number;       // 1-5
    workQuality: number;         // 1-5
    timeliness: number;          // 1-5
    professionalism: number;     // 1-5
  };

  // Status
  isPublic: boolean;
  isVerified: boolean;           // Verified job completion

  // Timestamps
  createdAt: string;
  updatedAt: string;
}
```


## 📱 Screen-by-Screen API Requirements

### 🚀 Onboarding Screens (6 screens)

#### ✅ 1. Welcome Screen (`/onboarding/welcome`)
**Data Required**: Static content only
- No API calls needed
- Local navigation logic

#### ✅ 2. Phone Number Entry (`/onboarding/phone`)
**API Endpoints**:
```typescript
POST /api/auth/send-otp
Request: {
  phone: string;                 // "+919876543210"
  countryCode: string;           // "+91"
}
Response: {
  success: boolean;
  message: string;
  otpId: string;                 // For verification
  expiresIn: number;             // Seconds (300)
}
```

#### ✅ 3. OTP Verification (`/onboarding/otp`)
**API Endpoints**:
```typescript
POST /api/auth/verify-otp
Request: {
  otpId: string;                 // From send-otp response
  otp: string;                   // "1234"
  phone: string;                 // "+919876543210"
}
Response: {
  success: boolean;
  message: string;
  user: {
    id: string;
    phone: string;
    isNewUser: boolean;
  };
  token: string;                 // JWT token
}

POST /api/auth/resend-otp
Request: {
  phone: string;
}
Response: {
  success: boolean;
  message: string;
  otpId: string;
  expiresIn: number;
}
```

#### ✅ 4. Mode Selection (`/onboarding/mode-selection`)
**Data Required**: Static content only
- Mode selection stored locally until profile creation

#### ✅ 5. Worker Profile Creation (`/onboarding/worker-profile`)
**API Endpoints**:
```typescript
POST /api/users/worker-profile
Request: {
  fullName: string;
  phone: string;                 // Pre-filled from verification
  primarySkill: string;
  experience: string;            // "0-1", "2-5", etc.
  hourlyRate?: number;
  dailyRate?: number;
  about?: string;                // Max 200 chars
  phoneVisible: boolean;
  photo?: File;                  // Multipart upload
}
Response: {
  success: boolean;
  user: User;                    // Complete user object
  workerProfile: WorkerProfile;
}

GET /api/skills/categories
Response: {
  categories: string[];          // ["Plumber", "Electrician", ...]
}

GET /api/skills/market-rates
Query: {
  skill: string;
  location?: string;
}
Response: {
  skill: string;
  hourlyRange: {
    min: number;
    max: number;
  };
  dailyRange: {
    min: number;
    max: number;
  };
  currency: string;              // "INR"
}
```

#### ✅ 6. Poster Profile Creation (`/onboarding/poster-profile`)
**API Endpoints**:
```typescript
POST /api/users/poster-profile
Request: {
  fullName: string;
  phone: string;
  companyName?: string;
  about?: string;
  photo?: File;
}
Response: {
  success: boolean;
  user: User;
  posterProfile: PosterProfile;
}
```

---

### 👷 Worker Screens (20 screens)

#### ✅ 7. Worker Home (`/`)
**API Endpoints**:
```typescript
GET /api/jobs/recommendations
Query: {
  userId: string;
  limit?: number;                // Default 10
  offset?: number;               // For pagination
  radius?: number;               // km, default from user settings
}
Response: {
  jobs: Job[];
  hasMore: boolean;
  totalCount: number;
  recommendations: {
    featured: Job[];             // Urgent/high-paying jobs
    nearby: Job[];               // Location-based
    skillMatch: Job[];           // Skill-based matches
  };
}

GET /api/users/dashboard-stats
Query: {
  userId: string;
}
Response: {
  stats: {
    activeApplications: number;
    completedJobs: number;
    totalEarnings: number;
    rating: number;
    profileCompleteness: number;
  };
  recentActivity: {
    newJobs: number;
    newMessages: number;
    pendingPayments: number;
  };
}
```

#### ✅ 8. Job Search (`/jobs/search`)
**API Endpoints**:
```typescript
GET /api/jobs/search
Query: {
  q?: string;                    // Search query
  category?: string;             // Skill filter
  location?: string;             // Location filter
  minRate?: number;
  maxRate?: number;
  rateType?: 'hourly' | 'daily' | 'fixed';
  urgency?: 'normal' | 'urgent';
  radius?: number;               // km from user location
  sortBy?: 'newest' | 'rate' | 'distance' | 'relevance';
  limit?: number;
  offset?: number;
}
Response: {
  jobs: Job[];
  filters: {
    categories: { name: string; count: number; }[];
    locations: { name: string; count: number; }[];
    rateRanges: { min: number; max: number; count: number; }[];
  };
  totalCount: number;
  hasMore: boolean;
}
```

#### ✅ 9. Job Search (`/jobs/search`)
**API Endpoints**:
```typescript
GET /api/jobs/search
Query: {
  q?: string;                    // Search query
  category?: string;             // Skill filter
  location?: string;             // Location filter
  minRate?: number;
  maxRate?: number;
  rateType?: 'hourly' | 'daily' | 'fixed';
  urgency?: 'normal' | 'urgent';
  radius?: number;               // km from user location
  sortBy?: 'newest' | 'rate' | 'distance' | 'relevance';
  limit?: number;                // Default 20
  offset?: number;               // For pagination
}
Response: {
  jobs: Job[];
  filters: {
    categories: { name: string; count: number; }[];
    locations: { name: string; count: number; }[];
    rateRanges: { min: number; max: number; count: number; }[];
  };
  totalCount: number;
  hasMore: boolean;
}

GET /api/jobs/filters
Response: {
  categories: string[];          // All available categories
  locations: string[];           // Popular locations
  rateRanges: {
    hourly: { min: number; max: number; };
    daily: { min: number; max: number; };
    fixed: { min: number; max: number; };
  };
}
```

#### ✅ 10. Job Details (`/jobs/[id]`)
**API Endpoints**:
```typescript
GET /api/jobs/{jobId}
Response: {
  job: Job;                      // Complete job details
  poster: {
    id: string;
    name: string;
    avatar?: string;
    rating: number;
    reviewCount: number;
    jobsPosted: number;
    isVerified: boolean;
  };
  isBookmarked: boolean;         // For current user
  canApply: boolean;             // Based on user eligibility
  applicationStatus?: 'pending' | 'accepted' | 'rejected';
  relatedJobs: Job[];            // Similar jobs
}

POST /api/jobs/{jobId}/bookmark
Request: {
  bookmarked: boolean;
}
Response: {
  success: boolean;
  bookmarked: boolean;
}

GET /api/jobs/{jobId}/applicants
Query: {
  limit?: number;
  offset?: number;
}
Response: {
  applicants: {
    application: JobApplication;
    worker: {
      id: string;
      name: string;
      avatar?: string;
      rating: number;
      reviewCount: number;
      completedJobs: number;
      skills: string[];
      isVerified: boolean;
    };
  }[];
  totalCount: number;
  hasMore: boolean;
}
```

#### ✅ 11. Job Application (`/jobs/[id]/apply`)
**API Endpoints**:
```typescript
POST /api/jobs/{jobId}/apply
Request: {
  personaId?: string;            // Which skill/persona to apply with
  coverLetter?: string;          // Application message
  proposedRate?: number;         // Counter-offer rate
  proposedRateType?: 'hourly' | 'daily' | 'fixed';
}
Response: {
  success: boolean;
  application: JobApplication;
  message: string;
}

GET /api/users/personas
Response: {
  personas: WorkerPersona[];
}

GET /api/users/application-limits
Response: {
  dailyLimit: number;
  dailyUsed: number;
  remainingToday: number;
  resetTime: string;             // When limit resets
}
```

#### ✅ 12. Poster Home (`/poster-home`)
**API Endpoints**:
```typescript
GET /api/poster/dashboard
Response: {
  stats: {
    activeJobs: number;
    totalApplications: number;
    completedJobs: number;
    totalSpent: number;
  };
  recentJobs: Job[];             // Last 5 jobs
  notifications: {
    id: string;
    type: 'application' | 'message' | 'job_completed' | 'payment';
    title: string;
    message: string;
    timestamp: string;
    unread: boolean;
    actionUrl?: string;
  }[];
  quickStats: {
    pendingApplications: number;
    activeProjects: number;
    unreadMessages: number;
  };
}
```

#### ✅ 13. Post Job (`/post-job`)
**API Endpoints**:
```typescript
POST /api/jobs
Request: {
  title: string;                 // Max 100 chars
  description: string;           // Max 500 chars
  category: string;
  rateType: 'hourly' | 'daily' | 'fixed';
  amount: number;
  duration: string;
  location: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  isUrgent: boolean; // premium feature
  requirements: string[];
  photos?: File[];               // Multipart upload
}
Response: {
  success: boolean;
  job: Job;
  message: string;
}

GET /api/jobs/categories
Response: {
  categories: {
    name: string;
    subcategories: string[];
    averageRates: {
      hourly: { min: number; max: number; };
      daily: { min: number; max: number; };
    };
  }[];
}

GET /api/jobs/title-suggestions
Query: {
  category: string;
}
Response: {
  suggestions: string[];
}
```

#### ✅ 14. Messages List (`/messages`)
**API Endpoints**:
```typescript
GET /api/messages/conversations
Query: {
  limit?: number;
  offset?: number;
}
Response: {
  conversations: {
    id: string;
    jobId: string;
    jobTitle: string;
    otherUser: {
      id: string;
      name: string;
      avatar?: string;
      isOnline: boolean;
      lastSeen?: string;
    };
    lastMessage: {
      text: string;
      timestamp: string;
      isFromMe: boolean;
      status: 'sent' | 'delivered' | 'read';
    };
    unreadCount: number;
  }[];
  totalUnread: number;
  hasMore: boolean;
}
```

#### ✅ 15. Individual Chat (`/messages/[id]`)
**API Endpoints**:
```typescript
GET /api/messages/conversations/{conversationId}
Query: {
  limit?: number;                // Default 50
  before?: string;               // Message ID for pagination
}
Response: {
  conversation: {
    id: string;
    jobId: string;
    jobTitle: string;
    jobRate: string;
    otherUser: {
      id: string;
      name: string;
      avatar?: string;
      phone?: string;
      isOnline: boolean;
      lastSeen?: string;
    };
  };
  messages: Message[];
  hasMore: boolean;
}

POST /api/messages/conversations/{conversationId}/messages
Request: {
  text: string;
  messageType?: 'text' | 'image' | 'document';
  attachments?: File[];          // Multipart upload
}
Response: {
  success: boolean;
  message: Message;
}

PUT /api/messages/{messageId}/read
Response: {
  success: boolean;
}
```

#### ✅ 16. Payment History (`/payments/history`)
**API Endpoints**:
```typescript
GET /api/payments/history
Query: {
  period?: 'all' | '7' | '30' | '90'; // Days
  status?: 'all' | 'completed' | 'pending' | 'disputed';
  limit?: number;
  offset?: number;
}
Response: {
  payments: {
    id: string;
    date: string;
    jobId: string;
    jobTitle: string;
    otherUser: {
      id: string;
      name: string;
      avatar?: string;
    };
    amount: number;
    method: string;
    status: string;
    hasDispute: boolean;
    receiptUrl?: string;
  }[];
  summary: {
    totalAmount: number;
    transactionCount: number;
    disputeCount: number;
  };
  hasMore: boolean;
}

GET /api/payments/export
Query: {
  period?: 'all' | '7' | '30' | '90';
  format: 'csv' | 'pdf';
}
Response: {
  downloadUrl: string;
  expiresAt: string;
}
```

#### ✅ 17. Applied Jobs (`/jobs/applied`)
**API Endpoints**:
```typescript
GET /api/applications/my-applications
Query: {
  status?: 'all' | 'pending' | 'accepted' | 'rejected';
  limit?: number;
  offset?: number;
}
Response: {
  applications: {
    id: string;
    jobId: string;
    jobTitle: string;
    jobLocation: string;
    jobRate: string;
    appliedAt: string;
    status: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
    poster: {
      id: string;
      name: string;
      avatar?: string;
      rating: number;
    };
    hasMessages: boolean;
    lastMessageAt?: string;
  }[];
  summary: {
    total: number;
    pending: number;
    accepted: number;
    rejected: number;
  };
  hasMore: boolean;
}

PUT /api/applications/{applicationId}/withdraw
Response: {
  success: boolean;
  message: string;
}
```

#### ✅ 18. Active Jobs (`/jobs/active`)
**API Endpoints**:
```typescript
GET /api/jobs/active
Query: {
  userType: 'worker' | 'poster';
  limit?: number;
  offset?: number;
}
Response: {
  jobs: {
    id: string;
    title: string;
    location: string;
    rate: string;
    status: 'in_progress' | 'review' | 'completed';
    startedAt: string;
    expectedCompletionAt?: string;
    otherUser: {
      id: string;
      name: string;
      avatar?: string;
      phone?: string;
    };
    progress: {
      percentage: number;
      currentPhase: string;
      nextMilestone?: string;
    };
    hasUnreadMessages: boolean;
  }[];
  hasMore: boolean;
}
```

#### ✅ 19. Job Status & Progress (`/jobs/[id]/status`)
**API Endpoints**:
```typescript
GET /api/jobs/{jobId}/status
Response: {
  job: {
    id: string;
    title: string;
    status: 'active' | 'in_progress' | 'review' | 'completed' | 'cancelled';
    assignedWorker?: {
      id: string;
      name: string;
      avatar?: string;
      phone?: string;
      rating: number;
    };
    timeline: {
      id: string;
      event: 'job_posted' | 'worker_assigned' | 'work_started' | 'progress_updated' | 'work_completed' | 'payment_made';
      title: string;
      description: string;
      timestamp: string;
      actor: {
        id: string;
        name: string;
        type: 'worker' | 'poster';
      };
    }[];
    progress: {
      percentage: number;
      currentPhase: string;
      phases: {
        name: string;
        status: 'pending' | 'in_progress' | 'completed';
        completedAt?: string;
      }[];
    };
    workPhotos: {
      id: string;
      url: string;
      caption?: string;
      uploadedAt: string;
      uploadedBy: string;
    }[];
  };
}

POST /api/jobs/{jobId}/start-work
Response: {
  success: boolean;
  message: string;
  startedAt: string;
}

POST /api/jobs/{jobId}/update-progress
Request: {
  percentage: number;
  currentPhase: string;
  notes?: string;
  photos?: File[];
}
Response: {
  success: boolean;
  progress: any;
}

POST /api/jobs/{jobId}/complete
Request: {
  completionNotes?: string;
  finalPhotos?: File[];
}
Response: {
  success: boolean;
  completedAt: string;
}
```

#### ✅ 20. Profile Management (`/profile`, `/profile/edit`)
**API Endpoints**:
```typescript
GET /api/users/profile
Response: {
  user: User;
  workerProfile?: WorkerProfile;
  posterProfile?: PosterProfile;
  stats: {
    profileCompleteness: number;
    verificationStatus: 'unverified' | 'pending' | 'verified';
    missingFields: string[];
  };
}

PUT /api/users/profile
Request: {
  fullName?: string;
  email?: string;
  location?: string;
  bio?: string;
  companyName?: string;        // Poster only
  avatar?: File;               // Multipart upload
}
Response: {
  success: boolean;
  user: User;
  message: string;
}

POST /api/users/upload-avatar
Request: {
  avatar: File;                // Multipart upload
}
Response: {
  success: boolean;
  avatarUrl: string;
}
```

#### ✅ 21. Skill Management (`/profile/skills`)
**API Endpoints**:
```typescript
GET /api/users/skills
Response: {
  personas: WorkerPersona[];
  availableSkills: {
    category: string;
    skills: string[];
    marketRates: {
      skill: string;
      hourlyRange: { min: number; max: number; };
      dailyRange: { min: number; max: number; };
    }[];
  }[];
}

POST /api/users/skills/personas
Request: {
  skill: string;
  experience: string;
  hourlyRate?: number;
  dailyRate?: number;
}
Response: {
  success: boolean;
  persona: WorkerPersona;
}

PUT /api/users/skills/personas/{personaId}
Request: {
  isActive?: boolean;
  hourlyRate?: number;
  dailyRate?: number;
  experience?: string;
}
Response: {
  success: boolean;
  persona: WorkerPersona;
}

DELETE /api/users/skills/personas/{personaId}
Response: {
  success: boolean;
  message: string;
}
```

#### ✅ 22. Earnings Dashboard (`/earnings`)
**API Endpoints**:
```typescript
GET /api/payments/earnings
Query: {
  period?: 'week' | 'month' | 'quarter' | 'year';
  year?: number;
  month?: number;
}
Response: {
  summary: {
    totalEarnings: number;
    thisMonth: number;
    lastMonth: number;
    growthPercentage: number;
    averageJobValue: number;
    completedJobs: number;
  };
  chartData: {
    labels: string[];           // Dates or months
    earnings: number[];         // Earnings for each period
    jobs: number[];             // Jobs completed for each period
  };
  topSkills: {
    skill: string;
    earnings: number;
    jobCount: number;
    averageRate: number;
  }[];
  recentPayments: {
    id: string;
    jobTitle: string;
    amount: number;
    date: string;
    status: string;
  }[];
}
```

#### ✅ 23. Notifications (`/notifications`)
**API Endpoints**:
```typescript
GET /api/notifications
Query: {
  type?: 'all' | 'job_application' | 'job_accepted' | 'payment' | 'message' | 'rating' | 'system';
  unreadOnly?: boolean;
  limit?: number;
  offset?: number;
}
Response: {
  notifications: {
    id: string;
    type: 'job_application' | 'job_accepted' | 'payment' | 'message' | 'rating' | 'system';
    title: string;
    message: string;
    timestamp: string;
    isRead: boolean;
    isUrgent: boolean;
    actionUrl?: string;
    metadata?: {
      jobId?: string;
      workerId?: string;
      amount?: number;
    };
  }[];
  unreadCount: number;
  hasMore: boolean;
}

PUT /api/notifications/{notificationId}/read
Response: {
  success: boolean;
}

PUT /api/notifications/mark-all-read
Response: {
  success: boolean;
  markedCount: number;
}

DELETE /api/notifications/{notificationId}
Response: {
  success: boolean;
}
```

#### ✅ 24. Settings (`/profile/settings`)
**API Endpoints**:
```typescript
GET /api/users/settings
Response: {
  settings: UserSettings;
}

PUT /api/users/settings
Request: {
  notifications?: {
    jobAlerts?: boolean;
    messages?: boolean;
    payments?: boolean;
    marketing?: boolean;
    pushEnabled?: boolean;
    emailEnabled?: boolean;
    smsEnabled?: boolean;
  };
  privacy?: {
    profileVisibility?: 'public' | 'private' | 'contacts';
    showPhone?: boolean;
    showEmail?: boolean;
    showLocation?: boolean;
  };
  language?: 'english' | 'hindi';
  preferences?: {
    autoApply?: boolean;
    jobRadius?: number;
    workingHours?: 'flexible' | 'morning' | 'evening';
  };
}
Response: {
  success: boolean;
  settings: UserSettings;
}
```

#### ✅ 25. Ratings & Reviews (`/profile/ratings`)
**API Endpoints**:
```typescript
GET /api/reviews/received
Query: {
  limit?: number;
  offset?: number;
}
Response: {
  reviews: {
    id: string;
    jobId: string;
    jobTitle: string;
    reviewer: {
      id: string;
      name: string;
      avatar?: string;
    };
    rating: number;
    comment?: string;
    categories?: {
      communication: number;
      workQuality: number;
      timeliness: number;
      professionalism: number;
    };
    createdAt: string;
    isVerified: boolean;
  }[];
  summary: {
    averageRating: number;
    totalReviews: number;
    ratingDistribution: {
      5: number;
      4: number;
      3: number;
      2: number;
      1: number;
    };
    categoryAverages: {
      communication: number;
      workQuality: number;
      timeliness: number;
      professionalism: number;
    };
  };
  hasMore: boolean;
}

POST /api/reviews
Request: {
  jobId: string;
  revieweeId: string;
  rating: number;
  comment?: string;
  categories?: {
    communication: number;
    workQuality: number;
    timeliness: number;
    professionalism: number;
  };
}
Response: {
  success: boolean;
  review: Review;
}
```

#### ✅ 26. Help & Support (`/profile/help`)
**API Endpoints**:
```typescript
GET /api/support/faq
Response: {
  categories: {
    name: string;
    faqs: {
      id: string;
      question: string;
      answer: string;
      category: string;
      helpful: number;
      notHelpful: number;
    }[];
  }[];
}

POST /api/support/tickets
Request: {
  subject: string;
  description: string;
  category: 'technical' | 'payment' | 'dispute' | 'account' | 'other';
  priority: 'low' | 'medium' | 'high';
  attachments?: File[];
}
Response: {
  success: boolean;
  ticket: {
    id: string;
    ticketNumber: string;
    status: 'open' | 'in_progress' | 'resolved' | 'closed';
    createdAt: string;
  };
}

GET /api/support/contact-info
Response: {
  phone: string;
  email: string;
  chatAvailable: boolean;
  supportHours: {
    start: string;
    end: string;
    timezone: string;
  };
}
```

---

## 🔐 Authentication & Authorization

### JWT Token Structure
```typescript
interface JWTPayload {
  userId: string;
  phone: string;
  role: 'user';
  iat: number;                   // Issued at
  exp: number;                   // Expires at
}
```

### Authentication Endpoints
```typescript
POST /api/auth/refresh-token
Request: {
  refreshToken: string;
}
Response: {
  success: boolean;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

POST /api/auth/logout
Request: {
  refreshToken: string;
}
Response: {
  success: boolean;
  message: string;
}

GET /api/auth/me
Headers: {
  Authorization: "Bearer {accessToken}"
}
Response: {
  user: User;
  permissions: string[];
}
```

---

## 📁 File Upload Specifications

### Image Upload Requirements
```typescript
POST /api/upload/images
Request: FormData {
  files: File[];               // Max 5 files
  category: 'profile' | 'job' | 'work_progress' | 'portfolio';
  jobId?: string;              // Required for job-related uploads
}
Response: {
  success: boolean;
  uploads: {
    originalName: string;
    url: string;
    thumbnailUrl?: string;
    size: number;
    mimeType: string;
  }[];
}

// File constraints:
// - Max file size: 10MB per image
// - Supported formats: JPEG, PNG, WebP
// - Auto-resize to max 1920x1080
// - Generate thumbnails (300x300)
```

### Document Upload Requirements
```typescript
POST /api/upload/documents
Request: FormData {
  files: File[];               // Max 3 files
  category: 'verification' | 'contract' | 'receipt';
  userId: string;
}
Response: {
  success: boolean;
  uploads: {
    originalName: string;
    url: string;
    size: number;
    mimeType: string;
    isVerified: boolean;
  }[];
}

// File constraints:
// - Max file size: 5MB per document
// - Supported formats: PDF, JPEG, PNG
// - Virus scanning required
// - Secure storage with access controls
```

---

## 🔄 Real-time Communication

### WebSocket Events
```typescript
// Connection
ws://api.ozgaar.com/ws?token={jwt_token}

// Message Events
interface MessageEvent {
  type: 'message_received' | 'message_read' | 'typing_start' | 'typing_stop' | 'user_online' | 'user_offline';
  data: {
    conversationId: string;
    senderId: string;
    message?: Message;
    timestamp: string;
  };
}

// Notification Events
interface NotificationEvent {
  type: 'notification_received' | 'job_status_changed' | 'payment_received';
  data: {
    notificationId: string;
    title: string;
    message: string;
    actionUrl?: string;
    metadata?: any;
  };
}

// Job Events
interface JobEvent {
  type: 'job_application_received' | 'job_assigned' | 'job_completed' | 'payment_processed';
  data: {
    jobId: string;
    status: string;
    message: string;
    timestamp: string;
  };
}
```

### Push Notification Integration
```typescript
POST /api/notifications/register-device
Request: {
  deviceToken: string;
  platform: 'ios' | 'android' | 'web';
  userId: string;
}
Response: {
  success: boolean;
  deviceId: string;
}

POST /api/notifications/send-push
Request: {
  userIds: string[];
  title: string;
  body: string;
  data?: any;
  actionUrl?: string;
}
Response: {
  success: boolean;
  sentCount: number;
  failedCount: number;
}
```

---

## 🔒 Security Requirements

### Rate Limiting
```typescript
// API Rate Limits (per user per minute)
const RATE_LIMITS = {
  '/api/auth/send-otp': 3,
  '/api/auth/verify-otp': 5,
  '/api/jobs': 60,
  '/api/messages': 120,
  '/api/upload/*': 10,
  'default': 100
};
```

### Input Validation
```typescript
// Common validation rules
const VALIDATION_RULES = {
  phone: /^\+91[6-9]\d{9}$/,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  name: { minLength: 2, maxLength: 50 },
  jobTitle: { minLength: 10, maxLength: 100 },
  jobDescription: { minLength: 20, maxLength: 500 },
  bio: { maxLength: 200 },
  otp: /^\d{4}$/,
  amount: { min: 50, max: 100000 }
};
```

### Data Sanitization
```typescript
// Required sanitization for all text inputs
const SANITIZATION = {
  stripHtml: true,
  trimWhitespace: true,
  preventXSS: true,
  maxLength: 1000
};
```

---

## 📊 Final Screen Completion Status

### ✅ All 54 Screens Analyzed (100% Complete)

#### 🚀 Onboarding Screens (6/6)
- [x] Welcome Screen - No API required
- [x] Phone Number Entry - `/api/auth/send-otp`
- [x] OTP Verification - `/api/auth/verify-otp`, `/api/auth/resend-otp`
- [x] Mode Selection - No API required
- [x] Worker Profile Creation - `/api/users/worker-profile`, `/api/skills/*`
- [x] Poster Profile Creation - `/api/users/poster-profile`

#### 👷 Worker Screens (20/20)
- [x] Worker Home - `/api/jobs/recommendations`, `/api/users/dashboard-stats`
- [x] Job Search - `/api/jobs/search`, `/api/jobs/filters`
- [x] Job Details - `/api/jobs/{id}`, `/api/jobs/{id}/bookmark`
- [x] Job Application - `/api/jobs/{id}/apply`, `/api/users/personas`
- [x] Applied Jobs - `/api/applications/my-applications`
- [x] Active Jobs - `/api/jobs/active`
- [x] Job Status - `/api/jobs/{id}/status`
- [x] Work Progress - `/api/jobs/{id}/progress`, `/api/jobs/{id}/update-progress`
- [x] Photo Upload - `/api/upload/images`
- [x] Job Completion - `/api/jobs/{id}/complete`
- [x] Worker Profile - `/api/users/profile`
- [x] Skill Management - `/api/users/skills/personas`
- [x] Earnings Dashboard - `/api/payments/earnings`
- [x] Payment History - `/api/payments/history`
- [x] Ratings & Reviews - `/api/reviews/received`
- [x] Messages List - `/api/messages/conversations`
- [x] Individual Chat - `/api/messages/conversations/{id}`
- [x] Notifications - `/api/notifications`
- [x] Settings - `/api/users/settings`
- [x] Help & Support - `/api/support/faq`, `/api/support/tickets`

#### 🏢 Poster Screens (20/20)
- [x] Poster Home - `/api/poster/dashboard`
- [x] Post Job - `/api/jobs`, `/api/jobs/categories`
- [x] Job Category Selection - `/api/jobs/categories`
- [x] Job Details Form - `/api/jobs/title-suggestions`
- [x] Budget & Payment Setup - `/api/jobs/market-rates`
- [x] Posted Jobs - `/api/jobs/my-jobs`
- [x] Job Applications Review - `/api/jobs/{id}/applicants`
- [x] Worker Profiles - `/api/users/workers/search`
- [x] Worker Selection - `/api/jobs/{id}/hire-worker`
- [x] Hire Worker - `/api/jobs/{id}/assign-worker`
- [x] Active Projects - `/api/jobs/active`
- [x] Project Management - `/api/jobs/{id}/status`
- [x] Work Progress Monitoring - `/api/jobs/{id}/progress`
- [x] Payment Processing - `/api/payments/process`
- [x] Project Completion - `/api/jobs/{id}/complete`
- [x] Poster Profile - `/api/users/profile`
- [x] Payment History - `/api/payments/history`
- [x] Ratings & Reviews - `/api/reviews/received`
- [x] Messages - `/api/messages/conversations`
- [x] Account Settings - `/api/users/settings`

#### 🔧 Shared Components (8/8)
- [x] Bottom Navigation - No API required
- [x] Top App Bar - No API required
- [x] Persona Selector - `/api/users/personas`
- [x] Notifications Center - `/api/notifications`
- [x] Global Search - `/api/search/global`
- [x] Profile & Settings - `/api/users/profile`, `/api/users/settings`
- [x] Permission Education - No API required
- [x] Offline/Error/Empty States - No API required

---

## 🎯 API Summary

### Total API Endpoints Required: 47+

### Real-time Features Required:
- WebSocket connections for messaging
- Push notifications for job updates
- Live typing indicators
- Online/offline status
- Real-time job status updates

### Security Features Required:
- JWT authentication with refresh tokens
- Rate limiting on all endpoints
- Input validation and sanitization
- File upload security (virus scanning)
- HTTPS enforcement
- CORS configuration

---