# Branding & Style Guide

## Visual Identity

**Brand Guidelines:** *[Ozgaar Brand Guidelines - to be created or linked if existing]*
- Must convey trustworthiness, local relevance, and accessibility
- Should feel familiar to WhatsApp users while establishing professional credibility
- Color choices must work across 8 Indian languages and various cultural contexts

## Color Palette

| Color Type | Hex Code | Usage |
|------------|----------|--------|
| Primary | #2E8B57 (Sea Green) | Trust actions, verification badges, primary CTAs - conveys growth and reliability |
| Secondary | #FF8C00 (Dark Orange) | Urgency indicators, job alerts, attention-requiring actions |
| Accent | #4169E1 (Royal Blue) | Information hierarchy, links, secondary actions |
| Success | #228B22 (Forest Green) | Completed jobs, successful payments, positive feedback |
| Warning | #FFD700 (Gold) | Pending verifications, important notices, skill certifications |
| Error | #DC143C (Crimson) | Failed actions, urgent problems, safety alerts |
| Neutral | #2F4F4F to #F5F5F5 | Text hierarchy, borders, background layers |

## Typography

### Font Families
- **Primary:** Noto Sans (supports all 8 Indian languages consistently)
- **Secondary:** Inter (for English-heavy screens, excellent readability)
- **Monospace:** JetBrains Mono (for numeric data, payment amounts)

### Type Scale

| Element | Size | Weight | Line Height |
|---------|------|--------|-------------|
| H1 | 28px | 700 | 1.2 |
| H2 | 24px | 600 | 1.3 |
| H3 | 20px | 600 | 1.4 |
| Body | 16px | 400 | 1.5 |
| Small | 14px | 400 | 1.4 |

## Iconography

**Icon Library:** Phosphor Icons with custom Ozgaar-specific additions
- Trust/verification icons (shields, checkmarks, locks)
- Trade-specific icons (wrench, paintbrush, cooking pot, etc.)
- Location icons optimized for hyperlocal context

**Usage Guidelines:** Icons must be immediately recognizable across cultural contexts; always pair with text labels in user's preferred language; use consistent visual weight; maintain 44px minimum touch targets

## Spacing & Layout

**Grid System:** 8px base grid system for consistent spacing across all screen sizes
- Mobile-first approach with 16px, 24px, 32px spacing intervals
- Content padding: 16px minimum from screen edges
- Component spacing: 8px between related elements, 24px between sections

**Spacing Scale:** 4px, 8px, 16px, 24px, 32px, 48px, 64px
- Optimized for thumb navigation and basic smartphone users
- Larger touch targets (minimum 44px) for accessibility
