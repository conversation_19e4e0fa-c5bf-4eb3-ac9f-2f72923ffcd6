# Wireframes & Mockups

**Primary Design Files:** *[To be determined - please specify your preferred design tool]*

## Key Screen Layouts

## Worker Dashboard (Home Screen)

**Purpose:** Immediate value delivery - show relevant local job opportunities and profile status
**Key Elements:**
- **Header**: Profile completion indicator (trust signal), language switcher, notifications badge
- **Quick Stats Bar**: Applications pending, jobs completed, current rating (visual progress)  
- **Job Feed**: Cards showing job type icon, distance, budget range, urgency indicator
- **Bottom Navigation**: Dashboard, Browse Jobs, Profile, Messages (with WhatsApp-familiar icons)

**Interaction Notes:** Swipe-to-refresh for job updates, tap job cards for quick preview before full details
**Design File Reference:** *[Screen: Worker-Dashboard-v1]*

## Job Application Card (Critical Component)

**Purpose:** Trust-building through transparency - show all key job information upfront
**Key Elements:**
- **Visual Header**: Job category icon, urgency badge (color-coded), verification checkmark
- **Job Details**: Title in local language, location with distance, budget range clearly stated
- **Trust Indicators**: Poster rating, previous hiring history, ID verification badge
- **Action Button**: Large "Apply Now" with application count shown ("12 people applied")

**Interaction Notes:** Expandable for full description, one-tap apply for verified profiles
**Design File Reference:** *[Component: Job-Card-v2]*

## Worker Profile Setup (Onboarding)

**Purpose:** Build comprehensive worker profiles for trust while keeping onboarding simple
**Key Elements:**
- **Progress Indicator**: Visual steps (5 steps max) with current position highlighted
- **Skill Selection**: Visual grid with icons for different trades (electrician, plumber, etc.)
- **Photo Upload**: Clear guidance with example good/bad photos for trust-building
- **Verification Options**: ID upload, phone verification, skill testing (optional but recommended)

**Interaction Notes:** Allow partial completion with prompts to finish later, auto-save progress
**Design File Reference:** *[Flow: Worker-Onboarding-v1]*

## Job Posting Flow (Job Poster)

**Purpose:** Streamlined job posting that prevents fake listings while staying simple
**Key Elements:**
- **Category Picker**: Large visual tiles for job types (domestic, technical, delivery, etc.)
- **Requirements Form**: Pre-filled suggestions, budget range slider with local market indicators
- **Location Selector**: Map interface with radius selector, address confirmation
- **Preview Screen**: Shows exactly how job appears to workers before publishing

**Interaction Notes:** Smart defaults based on category, budget suggestions to prevent lowball offers
**Design File Reference:** *[Flow: Job-Post-Flow-v1]*
