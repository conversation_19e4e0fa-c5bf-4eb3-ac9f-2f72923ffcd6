# Ozgaar UI/UX Specification

## Table of Contents

- [Ozgaar UI/UX Specification](#table-of-contents)
  - [Overall UX Goals & Principles](./overall-ux-goals-principles.md)
    - [Target User Personas](./overall-ux-goals-principles.md#target-user-personas)
    - [Usability Goals](./overall-ux-goals-principles.md#usability-goals)
    - [Design Principles](./overall-ux-goals-principles.md#design-principles)
    - [Change Log](./overall-ux-goals-principles.md#change-log)
  - [Information Architecture (IA)](./information-architecture-ia.md)
    - [Site Map / Screen Inventory](./information-architecture-ia.md#site-map-screen-inventory)
    - [Navigation Structure](./information-architecture-ia.md#navigation-structure)
  - [User Flows](./user-flows.md)
    - [Critical Flow 1: Worker Job Discovery & Application](./user-flows.md#critical-flow-1-worker-job-discovery-application)
    - [Critical Flow 2: Job Poster Hiring Process](./user-flows.md#critical-flow-2-job-poster-hiring-process)
  - [Wireframes & Mockups](./wireframes-mockups.md)
    - [Key Screen Layouts](./wireframes-mockups.md#key-screen-layouts)
    - [Worker Dashboard (Home Screen)](./wireframes-mockups.md#worker-dashboard-home-screen)
    - [Job Application Card (Critical Component)](./wireframes-mockups.md#job-application-card-critical-component)
    - [Worker Profile Setup (Onboarding)](./wireframes-mockups.md#worker-profile-setup-onboarding)
    - [Job Posting Flow (Job Poster)](./wireframes-mockups.md#job-posting-flow-job-poster)
  - [Component Library / Design System](./component-library-design-system.md)
    - [Core Components](./component-library-design-system.md#core-components)
    - [Trust Badge Component](./component-library-design-system.md#trust-badge-component)
    - [Job/Worker Card Component](./component-library-design-system.md#jobworker-card-component)
    - [Language-First Input Component](./component-library-design-system.md#language-first-input-component)
    - [Location Selector Component](./component-library-design-system.md#location-selector-component)
    - [Communication Hub Component](./component-library-design-system.md#communication-hub-component)
  - [Branding & Style Guide](./branding-style-guide.md)
    - [Visual Identity](./branding-style-guide.md#visual-identity)
    - [Color Palette](./branding-style-guide.md#color-palette)
    - [Typography](./branding-style-guide.md#typography)
      - [Font Families](./branding-style-guide.md#font-families)
      - [Type Scale](./branding-style-guide.md#type-scale)
    - [Iconography](./branding-style-guide.md#iconography)
    - [Spacing & Layout](./branding-style-guide.md#spacing-layout)
  - [Accessibility Requirements](./accessibility-requirements.md)
    - [Compliance Target](./accessibility-requirements.md#compliance-target)
    - [Key Requirements](./accessibility-requirements.md#key-requirements)
    - [Extended Accessibility Considerations](./accessibility-requirements.md#extended-accessibility-considerations)
    - [Testing Strategy](./accessibility-requirements.md#testing-strategy)
  - [Responsiveness Strategy](./responsiveness-strategy.md)
    - [Breakpoints](./responsiveness-strategy.md#breakpoints)
    - [Adaptation Patterns](./responsiveness-strategy.md#adaptation-patterns)
  - [Animation & Micro-interactions](./animation-micro-interactions.md)
    - [Motion Principles](./animation-micro-interactions.md#motion-principles)
    - [Key Animations](./animation-micro-interactions.md#key-animations)
  - [Performance Considerations](./performance-considerations.md)
    - [Performance Goals](./performance-considerations.md#performance-goals)
    - [Design Strategies](./performance-considerations.md#design-strategies)
  - [Next Steps](./next-steps.md)
    - [Immediate Actions](./next-steps.md#immediate-actions)
    - [Design Handoff Checklist](./next-steps.md#design-handoff-checklist)
