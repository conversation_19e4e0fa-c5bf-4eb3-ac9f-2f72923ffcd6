# User Flows

## Critical Flow 1: Worker Job Discovery & Application

**User Goal:** Find and successfully apply for relevant local work opportunities
**Entry Points:** App launch, browse jobs tab, push notification from job match
**Success Criteria:** Job application submitted with verified worker profile

**Flow Diagram:**
```mermaid
graph TD
    A[Worker Dashboard] --> B{New to App?}
    B -->|Yes| C[Complete Profile Setup]
    B -->|No| D[Browse Jobs Feed]
    C --> C1[Add Skills/Personas]
    C1 --> C2[Upload Photo/ID]
    C2 --> C3[Set Travel Radius]
    C3 --> D
    D --> E[Filter by Location/Skill]
    E --> F[View Job Card]
    F --> G{Job Details Interest?}
    G -->|Yes| H[View Full Job Posting]
    G -->|No| D
    H --> I{Meets Requirements?}
    I -->|Yes| J[Apply with Profile]
    I -->|No| K[Save for Later/Share]
    J --> L[Application Submitted]
    L --> M[Wait for Response/Chat]
```

**Edge Cases & Error Handling:**
- **Incomplete Profile**: Block job application with clear CTA to complete profile
- **Network Issues**: Cache job listings for offline browsing, queue applications for retry
- **No Local Jobs**: Show expanded radius options with travel time/cost estimates  
- **Fake Job Detection**: Flag suspicious postings (no location, unrealistic pay) for manual review
- **Application Overwhelm**: Limit applications per day for new users to maintain quality

**Notes:** This flow prioritizes immediate value (seeing local jobs) while ensuring profile completion for trust-building. The filtering step is crucial given the hyperlocal focus.

## Critical Flow 2: Job Poster Hiring Process

**User Goal:** Find and hire a reliable, verified worker for immediate or ongoing needs
**Entry Points:** App launch, "Post Job" tab, worker search, repeat hiring
**Success Criteria:** Worker hired with clear expectations and payment terms agreed

**Flow Diagram:**
```mermaid
graph TD
    A[Job Poster Dashboard] --> B{Hiring Intent?}
    B -->|Post New Job| C[Job Category Selection]
    B -->|Browse Workers| D[Worker Search/Filter]
    C --> C1[Job Requirements Form]
    C1 --> C2[Set Budget Range]
    C2 --> C3[Location & Timing]
    C3 --> C4[Job Preview]
    C4 --> C5[Publish Job]
    C5 --> C6[Review Applications]
    D --> D1[Filter by Skill/Rating/Distance]
    D1 --> D2[View Worker Profiles]
    D2 --> D3{Worker Suitable?}
    D3 -->|Yes| D4[Contact Worker]
    D3 -->|No| D1
    C6 --> E[Compare Worker Profiles]
    D4 --> F[Chat/Interview]
    E --> F
    F --> G{Hire Decision?}
    G -->|Yes| H[Send Job Offer]
    G -->|No| I[Decline with Reason]
    H --> J[Worker Accepts]
    J --> K[Work Scheduled]
```

**Edge Cases & Error Handling:**
- **No Applications**: Suggest budget/requirement adjustments, expand search radius
- **Worker No-Shows**: Automated rebooking with penalty system for unreliable workers
- **Budget Disputes**: Show local market rates, suggest negotiation ranges
- **Verification Concerns**: Highlight verification badges, show rating details
- **Communication Barriers**: Offer language translation in chat, voice message support

**Notes:** This flow balances quick hiring needs with trust verification requirements. The dual path (post job vs browse workers) accommodates different urgency levels.
