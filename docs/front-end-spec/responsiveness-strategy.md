# Responsiveness Strategy

## Breakpoints

| Breakpoint | Min Width | Max Width | Target Devices |
|------------|-----------|-----------|----------------|
| Mobile | 320px | 480px | Budget Android devices, older smartphones (primary target) |
| Large Mobile | 481px | 768px | Mid-range Android devices, larger budget phones |
| Tablet | 769px | 1024px | Android tablets, foldable devices (secondary consideration) |
| Desktop | 1025px | - | Web version for job posters, admin interfaces (future) |

## Adaptation Patterns

**Layout Changes:**
- **Mobile (320-480px)**: Single column layouts, stacked cards, full-width components
- **Large Mobile (481-768px)**: Introduce two-column job listings, side-by-side action buttons
- **Tablet**: Three-column layouts for job browsing, split-screen for job details + application
- **Desktop**: Multi-panel interfaces for job posters managing multiple listings

**Navigation Changes:**
- **Mobile**: Bottom tab navigation (thumb-friendly), hamburger menu for secondary functions
- **Large Mobile**: Same bottom tabs with larger touch targets, more visible secondary options
- **Tablet**: Side navigation possible for job posters, maintain bottom tabs for workers
- **Desktop**: Top navigation bar with full menu visibility

**Content Priority:**
- **Mobile**: Show only essential job information (title, location, pay, distance)
- **Large Mobile**: Add job description preview, poster ratings visible
- **Tablet**: Full job descriptions, multiple jobs visible simultaneously
- **Desktop**: Complete information density with filters, sorting, and bulk actions

**Interaction Changes:**
- **Mobile**: Swipe gestures for job browsing, large thumb-zone optimized buttons
- **Large Mobile**: Two-thumb interactions possible, side-by-side comparison options
- **Tablet**: Multi-touch gestures, drag-and-drop for job organization
- **Desktop**: Hover states, right-click contexts, keyboard shortcuts
