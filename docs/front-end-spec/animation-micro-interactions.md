# Animation & Micro-interactions

## Motion Principles

**Trust Through Transparency:** Animations should make system processes visible, especially for trust-critical actions like payment processing, verification status updates, and job application submissions

**Performance-Conscious Motion:** All animations optimized for budget Android devices (2GB RAM) and respectful of users' data plans - no decorative animations that don't serve functional purposes

**Cultural Familiarity:** Motion patterns that align with users' expectations from WhatsApp and other familiar apps, avoiding Western-centric interaction paradigms

**Accessibility-First:** All animations respect users' motion preferences and provide alternative feedback methods for users with vestibular disorders

## Key Animations

**Trust Verification Cascade** - When verification badges appear on profiles
- **Description:** Badges animate in sequence (phone → ID → skills) with gentle scale-in + checkmark draw animation
- **Duration:** 200ms per badge (staggered by 100ms)
- **Easing:** ease-out for confidence-building feel
- **Purpose:** Makes verification process feel thorough and trustworthy

**Job Application Success Flow** - After submitting job application
- **Description:** Submit button transforms to checkmark, followed by slide-up confirmation card with haptic feedback
- **Duration:** Button transform 300ms, card slide 400ms
- **Easing:** ease-out for button, spring (0.8 damping) for card
- **Purpose:** Provides clear confirmation that application was received

**Payment Security Pulse** - On payment-related screens
- **Description:** Subtle pulse animation on security badges and payment protection indicators
- **Duration:** 2000ms cycle, 50% opacity variation
- **Easing:** ease-in-out
- **Purpose:** Draws attention to payment protection without being distracting

**Location Loading Ripple** - During GPS location detection
- **Description:** Expanding concentric circles from location icon while searching for position
- **Duration:** 1500ms per ripple, continuous until location found
- **Easing:** ease-out for expanding circles
- **Purpose:** Shows active location searching, familiar pattern from maps apps

**Skill Confidence Meter** - When workers update their skill levels
- **Description:** Progress bar fills with color gradient from orange (learning) to green (expert)
- **Duration:** 800ms fill animation
- **Easing:** ease-in-out with slight overshoot at end
- **Purpose:** Gamifies skill development and shows progression visually

**Message Send Swoosh** - Chat message sending feedback
- **Description:** Message slides right with slight rotation while sending, returns to position when delivered
- **Duration:** Send: 250ms, delivery confirmation: 150ms
- **Easing:** ease-out for send, ease-in for return
- **Purpose:** Familiar WhatsApp-style feedback for communication confidence

**Job Match Celebration** - When worker gets matched with suitable job
- **Description:** Gentle confetti burst from job card with haptic feedback and success sound
- **Duration:** 1200ms total effect
- **Easing:** gravity simulation for confetti particles
- **Purpose:** Celebrates positive outcome and encourages continued engagement

**Network Retry Breathing** - During poor connectivity
- **Description:** Retry button gently scales up/down (breathing effect) when network request fails
- **Duration:** 2000ms cycle
- **Easing:** ease-in-out
- **Purpose:** Indicates system is actively retrying, reduces user anxiety about connection issues
