# Component Library / Design System

**Design System Approach:** Create a custom component library optimized for India's informal employment sector, prioritizing visual clarity, trust indicators, and multi-language support. Given the target users' basic smartphone literacy, we'll establish a system that emphasizes familiar patterns while introducing trust-building elements not found in standard design systems.

## Core Components

## Trust Badge Component

**Purpose:** Build user confidence through visual verification indicators across all interactions
**Variants:** 
- ID Verified (blue checkmark with shield)
- Phone Verified (green phone icon)
- Skill Certified (gold star with tool icon)
- Payment Protected (green rupee symbol with lock)

**States:** Verified, Pending Verification, Not Verified, Expired
**Usage Guidelines:** Always pair with explanatory text in local language; use consistently across profiles, job postings, and messaging; never fake verification status

## Job/Worker Card Component

**Purpose:** Standardized information display for quick scanning and decision-making
**Variants:**
- Job Listing Card (for workers browsing opportunities)
- Worker Profile Card (for job posters browsing candidates)  
- Application Status Card (for tracking applied jobs)
- Booking Confirmation Card (for scheduled work)

**States:** Default, Loading, Selected, Applied, Unavailable, Expired
**Usage Guidelines:** Always include distance indicator, budget/rate clearly visible, maximum 3 lines of description text; use color coding for urgency levels

## Language-First Input Component

**Purpose:** Accommodate users who prefer local languages and may have limited typing skills
**Variants:**
- Text Input with Language Toggle
- Voice Input Button (for descriptions)
- Suggested Tags/Chips (pre-written common phrases)
- Number Input (formatted for Indian numbering system)

**States:** Empty, Typing, Voice Recording, Validation Error, Success
**Usage Guidelines:** Default to user's selected language; provide visual feedback for voice input; include common phrase suggestions to reduce typing; format numbers with Indian comma placement

## Location Selector Component

**Purpose:** Hyperlocal job matching requires precise but user-friendly location input
**Variants:**
- Current Location Button (GPS-based)
- Map with Radius Selector
- Area/Neighborhood Dropdown
- Address Input with Auto-complete

**States:** Location Found, Searching, Permission Denied, Offline Mode, Manual Entry
**Usage Guidelines:** Always request location permission with clear benefit explanation; provide offline alternatives; show travel time/cost estimates; respect privacy with approximate locations

## Communication Hub Component

**Purpose:** Facilitate trust-building through transparent communication while maintaining familiarity
**Variants:**
- Chat Interface (WhatsApp-inspired)
- Voice Message Player
- Photo/Document Sharing
- Language Translation Toggle

**States:** Online, Offline, Typing, Message Sent, Message Read, Translation On/Off
**Usage Guidelines:** Mirror WhatsApp patterns for familiarity; provide clear indicators for message status; support mixed-language conversations; include auto-translation option
