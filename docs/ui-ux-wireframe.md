# Low‑Fidelity UX Flows: Onboarding, Worker Home & Apply, Poster Post‑a‑Job & Applicants

This document delivers complete, production-ready user flows in low-fidelity detail, covering screen sequences, key UI elements, states, and transitions. It’s structured to hand directly to design for wireframing and to product for validation. Technical implementation is intentionally excluded.

Contents
- A. Onboarding Flow (New user → Verified → Mode selection → First profile/persona)
- B. Worker Home & Apply Flow (Discovery → Apply → Status → Chat → Completion → Payment → Ratings)
- C. Poster Post‑a‑Job & Applicants Flow (Post → Manage → Accept → Status → Payment → Ratings)
- D. Edge Cases and Variant Paths (common across flows)
- E. Click/Navigation Map (screen-to-screen connections)

Notes
- Android, India-first. Bilingual labels at critical steps (English/Hindi).
- Mode visuals: Worker=Blue, Poster=Green. Urgent=Red accents.
- Bottom tabs: Home, Messages, My Jobs, Earnings/Payments, Profile.
- Persistent top app bar: Mode switch pill; Persona chip (worker-only); bell icon.

----------------------------------------

## A. Onboarding Flow

Goal: Verify identity with +91 phone, set initial mode, capture minimal profile so the user can either see jobs (Worker) or post/invite (Poster) in under 3 minutes.

A1. Welcome
- UI
  - Logo, tagline: “Find work. Hire fast. Nearby.”
  - Primary CTA: Get Started
  - Secondary: Continue in हिंदी
  - Links: Terms, Privacy
- Actions
  - Tap Get Started → A2 Phone Registration

A2. Phone Registration (+91 locked)
- UI
  - Phone field with mask: 98765-43210; +91 prefilled and locked
  - Real-time validation and helper text
  - CTA: Send OTP
  - Support text: “Standard SMS rates may apply. No charges by app.”
- States
  - Invalid input → inline error
  - SMS fail → “Try a different number” option
- Actions
  - Send OTP → A3 OTP

A3. OTP Verification (4-digit)
- UI
  - 4 OTP boxes; auto-advance; timer: Resend in 0:30
  - CTA: Verify (enabled when filled)
  - Alternate: Resend OTP (post-countdown), Change number
- States
  - Auto-verified via SIM → success banner, skip manual
  - Incorrect code → error + attempts left
  - Attempts exhausted → cooldown message
- Actions
  - Verify success → A4 Success
  - Change number → A2

A4. Verification Success
- UI
  - Green check animation
  - Copy: “Verified. Welcome!”
  - CTA: Continue
- Actions
  - Continue → A5 Mode Selection

A5. Mode Selection (first-time)
- UI
  - Two equal cards:
    - Worker: “Find jobs near you” (helmet icon)
    - Poster: “Hire trusted workers” (briefcase icon)
  - Note: “You can switch anytime”
- Actions
  - Pick Worker → A6 Worker Basic Profile
  - Pick Poster → A10 Poster Basic Profile

A6. Worker Basic Profile
- UI sections
  - Photo (circle crop)
  - Full name
  - Phone (pre-filled), visibility toggle
  - Primary skill (single-select)
  - Optional: Experience (years), Rate (hourly/daily), About (<=100 words)
  - Market rate hint per skill/location
  - Profile completeness meter
  - CTA: Save & Continue
- States
  - Missing required → inline blockers
- Actions
  - Save & Continue → A7 Persona Intro

A7. Persona Intro
- UI
  - Explainer card: What is a persona?
  - Benefits: “Apply with the right skills and rates”
  - CTA: Create My First Persona
- Actions
  - Create → A8 Persona Create (Wizard)

A8. Persona Create (Wizard: 2–3 steps)
- Step 1: Skill & Experience
  - Skill (preselected; changeable), Years of experience
  - Next
- Step 2: Rates & Availability
  - Rate type toggle (Daily/Hourly)
  - Suggested range
  - Availability toggle (Available/Busy)
  - Next
- Step 3: Portfolio (optional)
  - Add up to 3 photos; skip
  - CTA: Create Persona
- Edge
  - Duplicate skill persona prevention (later when adding more personas)
- Actions
  - Create Persona → A9 Worker Home (initialized)

A9. Worker Home First‑Run (Education overlays)
- UI
  - Job feed prefiltered by persona skill and 25km radius
  - Top bar: Mode pill (Worker), Persona chip, Location chip, bell
  - Bottom nav tabs
  - Coach marks:
    - Persona chip: “Switch skills here”
    - Apply button: “One-tap apply”
- Actions
  - Browse, open job → B2 Job Details
  - Switch persona (none extra yet)
  - Switch mode → C1 Poster Home (first-run guard if profile missing)

A10. Poster Basic Profile
- UI sections
  - Full name
  - Business name (optional)
  - Phone visibility toggle
  - Preferred categories to hire (multi-select optional)
  - CTA: Continue
- Actions
  - Continue → A11 Poster Home First‑Run

A11. Poster Home First‑Run (Education overlays)
- UI
  - Top bar: Mode pill (Poster), Location chip, bell
  - Tabs: Nearby Workers | My Jobs
  - Large primary CTA on app bar: Post a Job
  - Coach marks:
    - “Post a Job” button highlighted
    - Worker cards: “Invite or view profile”
- Actions
  - Tap Post a Job → C2 Job Post Wizard
  - Browse workers → C5 Worker Profile (Poster view)
  - Switch mode → A6 Worker Basic Profile (if not set up)

----------------------------------------

## B. Worker Home & Apply Flow

Goal: Let the worker quickly find relevant jobs, apply with the right persona, track status, coordinate via chat, verify completion with photo proof, confirm payment, and give/receive ratings.

B1. Worker Home (Jobs Feed)
- UI
  - Top: Mode pill (Worker), Persona chip (tap to switch), Location chip (tap to set radius)
  - Filter chips: My Skills / All Categories, Urgent, Radius, Sort (Distance/Newest)
  - Cards: Title, Rate (₹), Distance (km), Posted time, Urgent badge
  - Pull-to-refresh; infinite scroll; empty states with tips
- Actions
  - Tap card → B2 Job Details
  - Tap Apply on card → B3 Apply Drawer
  - Save job (bookmark)
  - Switch persona (if multiple) → feed refilters

B2. Job Details (Worker)
- UI
  - Header: Title, Poster rating, Posted time, Urgent
  - Rate & Type (Fixed/Hourly/Daily)
  - Map preview; distance
  - Duration estimate; description; requirements
  - Persona module:
    - Auto-suggested match with percentage
    - Dropdown to switch persona
    - Shows rate, availability, mini-portfolio
  - Primary CTA: Apply Now
  - Secondary: Save, Share
- Actions
  - Apply Now → B3 Apply Drawer
  - Back → B1

B3. Apply Drawer (Inline bottom sheet)
- UI
  - Selected persona card (+ switch)
  - Editable short message (pre-filled)
  - Availability confirmation
  - Warnings: budget vs rate mismatch; schedule conflicts
  - Daily application counter (e.g., 3 of 10 today)
  - CTA: Send Application
- States
  - Daily limit reached: show reset time
  - Incomplete profile: prompt to complete
- Actions
  - Send Application → success checkmark toast, disable Apply
  - Close → B2

B4. My Jobs (Applications tab)
- UI
  - Tabs: Applications | Active | Completed
  - Application list: Sent, Viewed, Shortlisted, Accepted/Rejected badges
  - Filters: Persona, Date
- Actions
  - Tap an item → B5 Job Status Detail

B5. Job Status Detail
- UI
  - Progress bar: Applied → Accepted → Started → Completed
  - Timeline with timestamps
  - Next action hint: “Waiting for poster to accept” or “Confirm Start”
  - If Accepted:
    - CTAs: Confirm Started, Open Chat
  - If Started:
    - CTA: Mark Work Completed (→ B7 Photo Proof)
    - “Started X hours ago” indicator
  - If Completed:
    - CTA: Confirm Payment Received (→ B8)
- Edge
  - Overdue alerts if stuck in Started
  - Dispute lock state display
- Actions
  - Open chat → B6 Chat
  - Mark Work Completed → B7 Photo Proof
  - Confirm Payment Received → B8 Worker Payment Confirm

B6. Chat (Worker)
- UI
  - WhatsApp-like thread
  - Header chip: Job name; tap → B5
  - Typing, read receipts
  - Quick replies: On my way, Running 15 min late, Work completed
  - Share Contact button
- States
  - Block/Report in overflow
  - Offline queued messages
- Actions
  - Back → previous

B7. Photo Proof (Completion)
- Flow
  - From B5 “Mark Work Completed”
  - Camera full-screen; instruction overlay: “Show completed work clearly”
  - GPS/timestamp indicators on preview
  - Retake, Submit
  - Quality feedback: too dark/blurry → suggest retake
- States
  - Pending upload banner on poor network
  - Poster approval pending; rejection reasons visible if rejected
- Actions
  - Submit → return to B5 with “Awaiting approval”

B8. Worker Payment Confirmation
- UI
  - Amount prominently displayed
  - Method select: Cash / UPI / Bank
  - CTA: Confirm Payment Received
  - Note (50 chars)
- States
  - If mismatch with poster’s entry → discrepancy banner + instructions
  - Reminder states if poster hasn’t confirmed
- Actions
  - Confirm → closes job officially when both parties confirm → B9 Ratings To Give

B9. Ratings To Give (Worker)
- UI
  - 1–5 stars with labels; optional 200-char comment
  - Reason dropdown if <2 stars
  - Countdown within 24h window
- Actions
  - Submit Rating → success toast → return to My Jobs Completed
  - Skip for now → reminder scheduled

B10. Earnings (Summary & History)
- UI
  - Summary: Today, Week, Month totals; monthly goal progress
  - Filters: 7/30/90 days, All time
  - Breakdown by persona/skill
  - List: Date, Job title, Amount, Method, Receipt
  - Export CSV
- Actions
  - Tap entry → Receipt view
  - Export → share sheet

----------------------------------------

## C. Poster Post‑a‑Job & Applicants Flow

Goal: Allow posters to create a job quickly, preview, publish, manage applicants, accept one, coordinate, confirm completion and payment, and rate the worker.

C1. Poster Home
- UI
  - Top: Mode pill (Poster), Location chip, bell
  - Primary CTA: Post a Job
  - Tabs: Nearby Workers | My Jobs
  - Nearby Workers: cards with persona, rate, distance, rating, availability
  - Filters: Skill, Rate range, Distance, Availability, Rating
- Actions
  - Post a Job → C2 Job Post Wizard
  - Tap worker card → C5 Worker Profile (Poster view)
  - Invite to Job from card → C6 Invite Flow

C2. Job Post Wizard (3 steps)
- Step 1: Basics
  - Title (with auto-suggest), Description (<=500 chars with counter), Category
  - Next
- Step 2: Pay & Time
  - Rate type: Fixed/Hourly/Daily; Amount (₹)
  - Duration: Hours/Days/Weeks
  - Budget warnings if too low vs market
  - Next
- Step 3: Location & Visibility
  - Auto-location map; manual address override; landmark helper
  - Urgency toggle (premium)
  - Preview button
- Preview screen
  - Exact worker-facing preview card + details
  - CTA: Publish
- Success
  - Confirmation page:
    - Job live in ~5 min; show “Views, Applications” counters (initially 0)
    - CTA: View Job, Invite Workers, Go to My Jobs

C3. My Jobs (Poster)
- UI
  - Tabs: Open | In Progress | Completed
  - Job item: Title, status, views, applications count
- Actions
  - Tap job → C4 Job Details & Applicants

C4. Job Details & Applicants
- UI
  - Job summary: Rate, Location, Duration, Urgency
  - Metrics: Views, Applications, Time since posting
  - Applicants list:
    - Card shows: Worker name, Persona skill, Rate, Availability, Rating -  count, short intro message, relevance match
    - Actions on each: View Profile, Message, Shortlist, Accept, Reject
  - Sorting: Match score, Time, Rating
- States
  - No applicants: tips to optimize job (better title, budget, renew)
- Actions
  - Message applicant → C7 Chat (Poster)
  - View profile → C5
  - Accept → confirmation → moves to In Progress; opens status page
  - Shortlist/Reject → update card state
  - Edit/Renew/Close job via overflow

C5. Worker Profile (Poster view)
- UI
  - Header: Photo, Name, Verified badge, Distance, Availability
  - Persona tabs (if multiple): Skill, Rate, Experience, Portfolio
  - Ratings & Reviews
  - Actions: Invite to Job, Message
- Actions
  - Invite to Job → C6
  - Message → C7

C6. Invite Worker to Job
- UI
  - Choose existing open job or Create new job inline
  - Optional message template
  - CTA: Send Invite
- States
  - Awaiting acceptance
- Actions
  - Send → success toast → back to C5 or C4

C7. Chat (Poster)
- UI
  - Same as worker chat; quick replies tailored (“Can you start today?”, “Share live location?”)
  - Header chip to Job Status
- Actions
  - Back → previous
  - Confirm start/complete via status link

C8. Job Status (Poster)
- UI
  - Progress: Applied → Accepted → Started → Completed
  - Actions by stage:
    - Post-accept: CTA Confirm Started
    - Post-start: CTA Confirm Completed (after photo proof approval)
  - Timestamps, overdue alerts
  - Dispute lock state if raised
- Actions
  - Approve/Reject Photo Proof when submitted (with reasons)
  - Open Chat

C9. Poster Payment Confirmation
- UI
  - Amount prominent; Method select (Cash/UPI/Bank)
  - Note (50 chars)
  - CTA: Confirm Payment Made
- States
  - Mismatch banner if worker entered different amount/method
  - Reminders if other party pending
- Actions
  - Confirm → job officially closed when both confirm → C10 Ratings To Give

C10. Ratings To Give (Poster)
- UI
  - 1–5 stars, labels; optional comment (<=200 chars)
  - Reason selection for <2 stars
- Actions
  - Submit → toast → back to Completed job list
  - Skip → reminder later

C11. Payments History (Poster)
- UI
  - Filters: 7/30/90/All
  - List: Date, Worker, Job title, Amount, Method; dispute markers
  - Export CSV
- Actions
  - Tap entry → Payment detail (receipt-like view)

----------------------------------------

## D. Edge Cases and Variant Paths

Shared
- Permissions education screens:
  - Location: “We use your location to show nearby jobs/workers”
  - Camera: “Needed to verify completed work”
  - Notifications: “We only send essential updates 7 AM–9 PM”
- Offline:
  - Top banner “Offline: actions will send later”
  - Cached feeds and chats with sent/delivered/failed statuses
- Do Not Disturb:
  - Queue non-urgent notifications; display next-send time in Notification Center
- App uninstalled (other party): Show “User unavailable” state in chat/job status

Worker-specific
- Daily apply limit reached:
  - Apply button disabled + banner: “Daily limit reached; resets at midnight”
- No persona when applying:
  - Inline prompt: “Create a persona to apply” → quick wizard
- Rate mismatch:
  - Warning with suggested edit or templated negotiation message
- Schedule conflict:
  - Availability checker suggests alternate time; allow proceed with warning
- Rejection:
  - Encourage feed tips: expand radius, adjust rate, enhance profile
- Photo proof rejected:
  - Show reason; CTA to retake; guidance text (e.g., “Ensure job site in frame”)

Poster-specific
- Budget too low:
  - Inline market guidance and “recommended range” pill; still allow post
- Duplicate job detection:
  - Side-by-side compare; CTA to edit existing instead
- No applicants:
  - Optimization checklist (title clarity, urgency toggle, rate tweak, renew timing)
- Disputes:
  - Locks status updates; opens Support sheet with case ID and expected SLA

----------------------------------------

## E. Click/Navigation Map

Onboarding
- A1 → A2 → A3 → A4 → A5 → (A6→A7→A8→A9) or (A10→A11)

Worker Core
- B1 ↔ B2 → B3 → (success) → B4 Applications → B5 Status → (B6 Chat | B7 Photo Proof → approval) → B8 Payment → B9 Rating → B10 Earnings
- B1 Persona chip → Persona selector sheet
- B1 Filters → refine feed; Location chip → radius settings

Poster Core
- C1 → C2 Wizard → Preview → Publish → (C3 My Jobs | C1 Invite workers)
- C3 → C4 Applicants → (C5 Worker Profile → C6 Invite) | (C7 Chat) | Accept → C8 Status → Photo Approval → C9 Payment → C10 Rating → C11 Payments History
- C1 Nearby Workers → C5 → C6 Invite

Cross‑Mode
- Mode pill (top bar) switches theme and home context instantly; if missing setup, redirect to basic profile for that mode before proceeding.

----------------------------------------

Deliverables for Wireframing (recommended sequence)
1) Onboarding: A1–A9 and A10–A11, incl. permission education
2) Worker Home & Apply: B1–B6, B7–B10
3) Poster Post‑a‑Job & Applicants: C1–C7, C8–C11
4) Edge-case sheets: Offline states, Disputes, Mismatch, Daily limits
5) Navigation skeleton: Bottom nav, top app bar variants, mode/persona sheets

## F. Wireframe Notes (per screen fidelity)
- Use card stacks for feeds, full-width primary CTAs, and bottom sheets for quick actions (Apply, Persona selection, Mode switch).

- Always anchor critical actions at bottom of the viewport for one-handed reachability.

- Status is color + label; never color-only.

- Keep content density high but scannable: larger title, ₹ rate bold, distance and time as compact chips.

- For bilingual labels, show English primary with small Hindi secondary where space allows for critical actions; full Hindi localization via Settings.

## G. What’s Next
- I can turn these flows into clickable low-fidelity wireframes (B/W) in batches:
  - Onboarding (5 screens)

  - Worker: Home, Job Details, Apply, My Jobs, Status, Chat, Photo Proof (7 screens)

  - Poster: Post-a-Job (wizard + preview), Applicants, Worker Profile, Status, Payments (7–8 screens)

After sign-off, proceed to mid-fidelity with typographic scale, iconography, and component library tokens (colors, spacing, elevation).

[1](https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/attachments/82055977/946699ea-0453-4c8f-8c37-d4aa6d7e2993/prd-continued.md)