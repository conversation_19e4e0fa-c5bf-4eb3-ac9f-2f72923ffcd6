# Core Workflows

Here are the critical user journeys illustrated as sequence diagrams, showing component interactions for the Ozgaar job platform:

## Workflow 1: Worker Registration and First Persona Creation

```mermaid
sequenceDiagram
    participant U as User
    participant App as Mobile App
    participant Auth as AuthenticationManager
    participant SB as Supabase Auth
    participant PM as PersonaManager
    participant DB as Supabase DB
    participant L<PERSON> as LocationManager
    participant Maps as Google Maps

    U->>App: Opens app, selects "Join as Worker"
    App->>Auth: Initialize registration
    Auth->>U: Show phone number input (+91 pre-filled)
    U->>Auth: Enters 10-digit phone number
    Auth->>SB: Send OTP request
    SB-->>U: Receives SMS with OTP
    U->>Auth: Enters OTP code
    Auth->>SB: Verify OTP
    SB->>Auth: Returns JWT token
    Auth->>App: Registration successful
    
    App->>U: Show language selection (8 options)
    U->>App: Selects preferred language (Hindi)
    App->>LM: Request location permission
    LM->>U: Show location permission dialog
    U->>LM: Grants location access
    LM->>Maps: Get current location
    Maps->>LM: Returns lat/lng + address
    LM->>DB: Update user location
    
    App->>PM: Initialize first persona creation
    PM->>U: Show persona creation form
    U->>PM: Fills persona details (Electrician, 5 years experience, ₹500/day)
    PM->>DB: Create worker_persona record
    DB->>PM: Returns persona ID
    PM->>App: Persona created successfully
    App->>U: Navigate to job dashboard
```

## Workflow 2: Job Matching and Application Process

```mermaid
sequenceDiagram
    participant W as Worker
    participant App as Mobile App
    participant JME as JobMatchingEngine
    participant JMS as JobMatchingService
    participant NS as NotificationService
    participant PM as PersonaManager
    participant DB as Supabase DB
    participant P as Job Poster

    Note over W,P: Worker opens app with active persona
    
    W->>App: Opens job dashboard
    App->>JME: Load matched jobs
    JME->>JMS: Request jobs for persona
    JMS->>DB: Query jobs within 10km radius
    DB->>JMS: Returns nearby electrical jobs
    JMS->>JMS: Calculate match scores (distance + skills)
    JMS->>JME: Returns ranked job matches
    JME->>W: Display job list with match scores
    
    Note over W,P: New urgent job posted
    
    P->>DB: Posts urgent electrical job
    DB->>NS: Trigger job notification
    NS->>JMS: Find matching workers nearby
    JMS->>DB: Query active electrical personas within 5km
    DB->>JMS: Returns 3 matching workers
    NS->>JME: Send real-time notification
    JME->>W: Shows push notification "Urgent job near you!"
    
    W->>JME: Taps notification, views job details
    JME->>W: Shows job: "AC Repair, ₹800, 2km away"
    W->>JME: Clicks "Apply Now"
    JME->>PM: Get active persona details
    PM->>JME: Returns persona info
    JME->>W: Show application form (rate: ₹800, message optional)
    W->>JME: Submits application
    JME->>DB: Create job_application record
    DB->>NS: Trigger application notification
    NS->>P: Notify poster of new application
    P->>App: Views application, accepts worker
    App->>DB: Update application status to 'accepted'
    DB->>NS: Trigger acceptance notification
    NS->>W: "Congratulations! Job accepted"
```
