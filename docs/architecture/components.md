# Components

Based on the React Native + Supabase architecture and multi-persona job platform requirements, here are the major logical components across the fullstack:

## Frontend Components (React Native)

**AuthenticationManager**
**Responsibility:** Handles phone-based authentication flow with +91 OTP verification for Indian users

**Key Interfaces:**
- Phone number input with validation
- OTP verification with retry logic
- Session persistence and refresh
- Language selection during onboarding

**Dependencies:** Supabase Auth, react-native-localize
**Technology Stack:** React Native, Supabase Auth SDK, AsyncStorage for session persistence

**LocationManager** 
**Responsibility:** Manages user location for hyper-local job matching within 5km radius

**Key Interfaces:**
- Current location detection
- Address geocoding and reverse geocoding
- Travel radius configuration per persona
- Location permission handling

**Dependencies:** Google Maps API, device location services
**Technology Stack:** React Native Geolocation, Google Maps SDK

**PersonaManager**
**Responsibility:** Core component managing multiple worker personas - electrician, driver, cook, etc.

**Key Interfaces:**
- Create/edit persona profiles
- Switch between active personas
- Manage persona-specific availability
- Upload persona-specific photos

**Dependencies:** Supabase Database, Supabase Storage
**Technology Stack:** React Hook Form, image picker, Supabase client

**JobMatchingEngine**
**Responsibility:** Displays matched jobs and handles job application workflow

**Key Interfaces:**
- Real-time job notifications
- Job filtering and search
- Application submission
- Job status tracking

**Dependencies:** Supabase Realtime, LocationManager
**Technology Stack:** Supabase subscriptions, push notifications, React Query for caching

**ReviewSystem**
**Responsibility:** Trust verification through bidirectional reviews between workers and job posters

**Key Interfaces:**
- Submit reviews after job completion
- Display persona-specific ratings
- Review moderation and verification
- Trust score calculations

**Dependencies:** Supabase Database, PersonaManager
**Technology Stack:** React Native rating components, form validation

## Backend Components (Supabase)

**UserService**
**Responsibility:** User management with phone-first authentication and multi-language support

**Key Interfaces:**
- User registration and phone verification
- Profile management
- Language preference handling
- Account linking (worker + poster capabilities)

**Dependencies:** Supabase Auth, database triggers
**Technology Stack:** PostgreSQL with RLS, Supabase Edge Functions

**JobMatchingService**
**Responsibility:** Core business logic for matching worker personas to nearby jobs based on skills, location, and availability

**Key Interfaces:**
- Calculate job match scores
- Geographic distance filtering
- Availability conflict detection
- Ranking algorithm based on ratings and proximity

**Dependencies:** PostgreSQL PostGIS, UserService
**Technology Stack:** Supabase Edge Functions, PostGIS for geo queries

**NotificationService**
**Responsibility:** Real-time job notifications and application status updates

**Key Interfaces:**
- Push job notifications to nearby workers
- Application status updates to job posters
- Batch notification processing
- Multi-language notification templates

**Dependencies:** Supabase Realtime, external push notification service
**Technology Stack:** Supabase Edge Functions, WebSocket subscriptions

**TrustVerificationService**
**Responsibility:** Manages trust scores, review verification, and fraud prevention

**Key Interfaces:**
- Review aggregation and scoring
- Fake review detection
- User verification workflows
- Trust score calculations

**Dependencies:** ReviewSystem, UserService
**Technology Stack:** PostgreSQL stored procedures, basic fraud detection rules

## Integration Components

**LanguageService**
**Responsibility:** Handles 8 Indian languages with real-time translation for job descriptions

**Key Interfaces:**
- Dynamic language switching
- Job description translation
- Cultural localization
- RTL text support

**Dependencies:** Translation API, react-native-localize
**Technology Stack:** i18n-js, Google Translate API integration

## Component Interaction Diagram

```mermaid
graph TB
    subgraph "React Native App"
        AUTH[AuthenticationManager]
        PERSONA[PersonaManager] 
        LOCATION[LocationManager]
        JOBS[JobMatchingEngine]
        REVIEWS[ReviewSystem]
        LANG[LanguageService]
    end
    
    subgraph "Supabase Backend"
        USER_SVC[UserService]
        MATCH_SVC[JobMatchingService]
        NOTIFY_SVC[NotificationService]
        TRUST_SVC[TrustVerificationService]
        DATA_MGR[PersonaDataManager]
        LOCATION_IDX[LocationIndexingService]
    end
    
    subgraph "External Services"
        MAPS[Google Maps API]
        TRANSLATE[Translation API]
        PUSH[Push Notifications]
    end
    
    AUTH --> USER_SVC
    PERSONA --> DATA_MGR
    LOCATION --> LOCATION_IDX
    LOCATION --> MAPS
    JOBS --> MATCH_SVC
    JOBS --> NOTIFY_SVC
    REVIEWS --> TRUST_SVC
    LANG --> TRANSLATE
    
    MATCH_SVC --> LOCATION_IDX
    NOTIFY_SVC --> PUSH
    USER_SVC --> TRUST_SVC
    
    style AUTH fill:#e1f5fe
    style PERSONA fill:#f3e5f5  
    style MATCH_SVC fill:#e8f5e8
```
