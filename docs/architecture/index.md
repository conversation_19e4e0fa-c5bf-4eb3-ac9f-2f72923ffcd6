# Ozgaar Fullstack Architecture Document

## Table of Contents

- [Ozgaar Fullstack Architecture Document](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Starter Template or Existing Project](./introduction.md#starter-template-or-existing-project)
    - [Change Log](./introduction.md#change-log)
  - [High Level Architecture](./high-level-architecture.md)
    - [Technical Summary](./high-level-architecture.md#technical-summary)
    - [Platform and Infrastructure Choice](./high-level-architecture.md#platform-and-infrastructure-choice)
    - [Repository Structure](./high-level-architecture.md#repository-structure)
    - [High Level Architecture Diagram](./high-level-architecture.md#high-level-architecture-diagram)
    - [Architectural Patterns](./high-level-architecture.md#architectural-patterns)
  - [Tech Stack](./tech-stack.md)
    - [Technology Stack Table](./tech-stack.md#technology-stack-table)
  - [Data Models](./data-models.md)
    - [User](./data-models.md#user)
    - [WorkerPersona](./data-models.md#workerpersona)
    - [Job](./data-models.md#job)
    - [JobApplication](./data-models.md#jobapplication)
    - [Review](./data-models.md#review)
  - [API Specification](./api-specification.md)
    - [Supabase Auto-Generated REST API](./api-specification.md#supabase-auto-generated-rest-api)
    - [Custom Edge Functions (Business Logic)](./api-specification.md#custom-edge-functions-business-logic)
    - [Real-time Subscriptions](./api-specification.md#real-time-subscriptions)
  - [Components](./components.md)
    - [Frontend Components (React Native)](./components.md#frontend-components-react-native)
    - [Backend Components (Supabase)](./components.md#backend-components-supabase)
    - [Integration Components](./components.md#integration-components)
    - [Component Interaction Diagram](./components.md#component-interaction-diagram)
  - [Core Workflows](./core-workflows.md)
    - [Workflow 1: Worker Registration and First Persona Creation](./core-workflows.md#workflow-1-worker-registration-and-first-persona-creation)
    - [Workflow 2: Job Matching and Application Process](./core-workflows.md#workflow-2-job-matching-and-application-process)
  - [Database Schema](./database-schema.md)
    - [Core Tables Schema](./database-schema.md#core-tables-schema)
  - [Frontend Architecture](./frontend-architecture.md)
    - [Component Architecture](./frontend-architecture.md#component-architecture)
    - [State Management Architecture](./frontend-architecture.md#state-management-architecture)
    - [Routing Architecture](./frontend-architecture.md#routing-architecture)
    - [Frontend Services Layer](./frontend-architecture.md#frontend-services-layer)
