# High Level Architecture

## Technical Summary

Ozgaar implements a **mobile-first, online-only** architecture using React Native for cross-platform mobile development and Supabase as the backend-as-a-service platform. The system prioritizes **hyper-local job matching** with **multi-persona worker profiles** while maintaining performance on 2G/3G networks common in Indian tier-2 markets.

The architecture follows a **serverless-first** approach leveraging Supabase's managed PostgreSQL, real-time subscriptions, and built-in authentication. Core business logic for job matching and persona management runs as Supabase Edge Functions, while the React Native app maintains direct API connectivity for all workflows.

The system supports **8 Indian languages** through internationalization libraries and implements **trust verification systems** without payment processing, focusing instead on profile verification, ratings, and review systems to establish worker credibility in the MVP phase.

## Platform and Infrastructure Choice

**Platform:** Supabase + Expo
**Key Services:** Supabase Database (PostgreSQL), Auth, Storage, Edge Functions, Real-time subscriptions
**Deployment Host and Regions:** Supabase global infrastructure (closest to India), Expo Application Services (EAS) for mobile app distribution

## Repository Structure

**Structure:** Monorepo with npm workspaces
**Monorepo Tool:** npm workspaces (simpler than Nx/Turborepo for small team)
**Package Organization:** Mobile app as primary focus, shared packages for types and utilities, future web app support

## High Level Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        MA[Mobile App<br/>React Native + Expo]
    end
    
    subgraph "Supabase Backend"
        AUTH[Supabase Auth<br/>Phone +91 OTP]
        DB[PostgreSQL<br/>Multi-persona data]
        STORAGE[Supabase Storage<br/>Photos/Files]
        REALTIME[Real-time<br/>Job notifications]
        FUNCTIONS[Edge Functions<br/>Matching logic]
    end
    
    subgraph "External Services"
        MAPS[Google Maps API<br/>Location services]
        SMS[SMS Provider<br/>OTP delivery]
        TRANSLATE[Translation API<br/>8 languages]
    end
    
    MA --> AUTH
    MA --> DB
    MA --> STORAGE
    MA --> REALTIME
    MA --> FUNCTIONS
    
    FUNCTIONS --> DB
    AUTH --> SMS
    MA --> MAPS
    MA --> TRANSLATE
    
    style MA fill:#e1f5fe
    style DB fill:#f3e5f5
    style AUTH fill:#e8f5e8
```

## Architectural Patterns

- **Backend-as-a-Service (BaaS):** Supabase handles infrastructure, focusing development on business logic - _Rationale:_ Accelerates MVP development and reduces operational complexity for small teams

- **Online-Only Mobile:** Direct API connectivity without offline data synchronization - _Rationale:_ Simplifies development while relying on Indian mobile network reliability

- **Multi-Tenant Data Model:** Single database with tenant isolation via Row Level Security - _Rationale:_ Simplifies data management while maintaining security between user contexts

- **Event-Driven Real-time:** Supabase real-time subscriptions for job notifications and matches - _Rationale:_ Critical for job platform user experience without building custom WebSocket infrastructure

- **Edge Functions for Business Logic:** Serverless functions for complex matching algorithms - _Rationale:_ Keeps mobile app lightweight while handling compute-intensive operations server-side

- **Internationalization (i18n) Pattern:** React Native i18n with remote configuration - _Rationale:_ Supports 8 Indian languages with ability to update translations without app releases
