# Tech Stack

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| **Frontend Language** | TypeScript | 5.x | Type safety for mobile app | Essential for large codebase, shared types, API contracts |
| **Frontend Framework** | React Native | 0.73.x | Cross-platform mobile development | Single codebase for iOS/Android, fast development |
| **Development Platform** | Expo | SDK 50+ | React Native development toolchain | Simplifies builds, OTA updates, managed workflow |
| **UI Component Library** | NativeBase | 3.x | Pre-built mobile UI components | Consistent design system, accessibility built-in |
| **State Management** | Zustand | 4.x | Lightweight state management | Simple API, TypeScript-first, less boilerplate than Redux |
| **Backend Platform** | Supabase | Latest | Backend-as-a-Service | Managed PostgreSQL, auth, real-time, file storage |
| **Database** | PostgreSQL | 15.x | Primary database (via Supabase) | Complex queries for personas/jobs, JSON support, reliability |
| **API Style** | Supabase Client | 2.x | Auto-generated REST/GraphQL | Built-in from Supabase, includes real-time subscriptions |
| **Authentication** | Supabase Auth | Built-in | Phone number authentication | +91 OTP support, JWT tokens, RLS integration |
| **File Storage** | Supabase Storage | Built-in | Profile photos, job images | Integrated with auth, CDN delivery, image transformations |
| **Cache/Local Storage** | @react-native-async-storage | 1.x | Session and preference storage | User sessions, language preferences |
| **Real-time** | Supabase Realtime | Built-in | Live job notifications | WebSocket-based, battery efficient |
| **Maps/Location** | Google Maps API | v3 | Hyper-local job matching | Accurate Indian locations, distance calculations |
| **Internationalization** | react-native-localize + i18n-js | Latest | 8 Indian languages support | Device language detection, RTL support |
| **Navigation** | React Navigation | 6.x | Mobile app navigation | Stack, tab, drawer navigation patterns |
| **Form Handling** | React Hook Form | 7.x | Job posting, profile forms | Performance optimized, validation support |
| **Testing Framework** | Jest + React Native Testing Library | Latest | Unit and integration testing | Standard RN testing, component testing |
| **E2E Testing** | Detox | 20.x | End-to-end mobile testing | Native iOS/Android E2E testing |
| **Code Quality** | ESLint + Prettier | Latest | Code formatting and linting | Consistent code style, catch errors early |
| **Build Tool** | Expo CLI / EAS Build | Latest | Mobile app builds | Managed build service, distribution |
| **CI/CD** | GitHub Actions + EAS | Latest | Automated testing and deployment | Free for open source, Expo integration |
| **Monitoring** | Supabase Analytics + Sentry | Latest | Error tracking, performance | Built-in analytics, crash reporting |
| **Environment Management** | Expo Constants | Built-in | Environment variables | Secure config management across environments |
