# Ozgaar Fullstack Architecture Document

This document outlines the complete fullstack architecture for **Ozgaar**, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for modern fullstack applications where these concerns are increasingly intertwined.

## Introduction

### Starter Template or Existing Project

Based on my analysis of your PRD for the Ozgaar MVP (Trust-First Job & Gig Platform), this is a **greenfield project** targeting the Indian market with specific requirements:

- **Target Platform:** Mobile-first (React Native) 
- **Target Markets:** Delhi NCR and Hyderabad initially  
- **Core Features:** Multi-persona worker profiles, hyper-local job matching, trust verification system
- **Technical Constraints:** Must work on 2G/3G networks, support 8 Indian languages, online-only architecture
- **MVP Scope:** Payment systems excluded from initial release

**Architecture Decision:** Building from scratch with React Native + Supabase stack. This combination provides:
- **Supabase Benefits:** Built-in auth, real-time subscriptions, PostgreSQL with REST/GraphQL APIs, file storage
- **React Native:** Cross-platform mobile development 
- **Rapid Development:** Supabase's backend-as-a-service accelerates MVP development timeline

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-14 | 1.0 | Initial fullstack architecture analysis | Business Analyst |

## High Level Architecture

### Technical Summary

Ozgaar implements a **mobile-first, online-only** architecture using React Native for cross-platform mobile development and Supabase as the backend-as-a-service platform. The system prioritizes **hyper-local job matching** with **multi-persona worker profiles** while maintaining performance on 2G/3G networks common in Indian tier-2 markets.

The architecture follows a **serverless-first** approach leveraging Supabase's managed PostgreSQL, real-time subscriptions, and built-in authentication. Core business logic for job matching and persona management runs as Supabase Edge Functions, while the React Native app maintains direct API connectivity for all workflows.

The system supports **8 Indian languages** through internationalization libraries and implements **trust verification systems** without payment processing, focusing instead on profile verification, ratings, and review systems to establish worker credibility in the MVP phase.

### Platform and Infrastructure Choice

**Platform:** Supabase + Expo
**Key Services:** Supabase Database (PostgreSQL), Auth, Storage, Edge Functions, Real-time subscriptions
**Deployment Host and Regions:** Supabase global infrastructure (closest to India), Expo Application Services (EAS) for mobile app distribution

### Repository Structure

**Structure:** Monorepo with npm workspaces
**Monorepo Tool:** npm workspaces (simpler than Nx/Turborepo for small team)
**Package Organization:** Mobile app as primary focus, shared packages for types and utilities, future web app support

### High Level Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        MA[Mobile App<br/>React Native + Expo]
    end
    
    subgraph "Supabase Backend"
        AUTH[Supabase Auth<br/>Phone +91 OTP]
        DB[PostgreSQL<br/>Multi-persona data]
        STORAGE[Supabase Storage<br/>Photos/Files]
        REALTIME[Real-time<br/>Job notifications]
        FUNCTIONS[Edge Functions<br/>Matching logic]
    end
    
    subgraph "External Services"
        MAPS[Google Maps API<br/>Location services]
        SMS[SMS Provider<br/>OTP delivery]
        TRANSLATE[Translation API<br/>8 languages]
    end
    
    MA --> AUTH
    MA --> DB
    MA --> STORAGE
    MA --> REALTIME
    MA --> FUNCTIONS
    
    FUNCTIONS --> DB
    AUTH --> SMS
    MA --> MAPS
    MA --> TRANSLATE
    
    style MA fill:#e1f5fe
    style DB fill:#f3e5f5
    style AUTH fill:#e8f5e8
```

### Architectural Patterns

- **Backend-as-a-Service (BaaS):** Supabase handles infrastructure, focusing development on business logic - _Rationale:_ Accelerates MVP development and reduces operational complexity for small teams

- **Online-Only Mobile:** Direct API connectivity without offline data synchronization - _Rationale:_ Simplifies development while relying on Indian mobile network reliability

- **Multi-Tenant Data Model:** Single database with tenant isolation via Row Level Security - _Rationale:_ Simplifies data management while maintaining security between user contexts

- **Event-Driven Real-time:** Supabase real-time subscriptions for job notifications and matches - _Rationale:_ Critical for job platform user experience without building custom WebSocket infrastructure

- **Edge Functions for Business Logic:** Serverless functions for complex matching algorithms - _Rationale:_ Keeps mobile app lightweight while handling compute-intensive operations server-side

- **Internationalization (i18n) Pattern:** React Native i18n with remote configuration - _Rationale:_ Supports 8 Indian languages with ability to update translations without app releases

## Tech Stack

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| **Frontend Language** | TypeScript | 5.x | Type safety for mobile app | Essential for large codebase, shared types, API contracts |
| **Frontend Framework** | React Native | 0.73.x | Cross-platform mobile development | Single codebase for iOS/Android, fast development |
| **Development Platform** | Expo | SDK 50+ | React Native development toolchain | Simplifies builds, OTA updates, managed workflow |
| **UI Component Library** | NativeBase | 3.x | Pre-built mobile UI components | Consistent design system, accessibility built-in |
| **State Management** | Zustand | 4.x | Lightweight state management | Simple API, TypeScript-first, less boilerplate than Redux |
| **Backend Platform** | Supabase | Latest | Backend-as-a-Service | Managed PostgreSQL, auth, real-time, file storage |
| **Database** | PostgreSQL | 15.x | Primary database (via Supabase) | Complex queries for personas/jobs, JSON support, reliability |
| **API Style** | Supabase Client | 2.x | Auto-generated REST/GraphQL | Built-in from Supabase, includes real-time subscriptions |
| **Authentication** | Supabase Auth | Built-in | Phone number authentication | +91 OTP support, JWT tokens, RLS integration |
| **File Storage** | Supabase Storage | Built-in | Profile photos, job images | Integrated with auth, CDN delivery, image transformations |
| **Cache/Local Storage** | @react-native-async-storage | 1.x | Session and preference storage | User sessions, language preferences |
| **Real-time** | Supabase Realtime | Built-in | Live job notifications | WebSocket-based, battery efficient |
| **Maps/Location** | Google Maps API | v3 | Hyper-local job matching | Accurate Indian locations, distance calculations |
| **Internationalization** | react-native-localize + i18n-js | Latest | 8 Indian languages support | Device language detection, RTL support |
| **Navigation** | React Navigation | 6.x | Mobile app navigation | Stack, tab, drawer navigation patterns |
| **Form Handling** | React Hook Form | 7.x | Job posting, profile forms | Performance optimized, validation support |
| **Testing Framework** | Jest + React Native Testing Library | Latest | Unit and integration testing | Standard RN testing, component testing |
| **E2E Testing** | Detox | 20.x | End-to-end mobile testing | Native iOS/Android E2E testing |
| **Code Quality** | ESLint + Prettier | Latest | Code formatting and linting | Consistent code style, catch errors early |
| **Build Tool** | Expo CLI / EAS Build | Latest | Mobile app builds | Managed build service, distribution |
| **CI/CD** | GitHub Actions + EAS | Latest | Automated testing and deployment | Free for open source, Expo integration |
| **Monitoring** | Supabase Analytics + Sentry | Latest | Error tracking, performance | Built-in analytics, crash reporting |
| **Environment Management** | Expo Constants | Built-in | Environment variables | Secure config management across environments |

## Data Models

### User
**Purpose:** Base user entity supporting both workers and job posters, with phone-based authentication for the Indian market.

**Key Attributes:**
- id: UUID - Primary identifier
- phone: string - +91 phone number (unique)
- email: string - Optional email address  
- full_name: string - User's full name
- preferred_language: enum - One of 8 supported Indian languages
- location: Point - Current/primary location for matching
- user_type: enum - 'worker', 'poster', 'both'
- is_verified: boolean - Phone verification status
- created_at: timestamp - Registration date
- last_active_at: timestamp - For matching algorithms

**TypeScript Interface:**
```typescript
interface User {
  id: string;
  phone: string;
  email?: string;
  full_name: string;
  preferred_language: SupportedLanguage;
  location: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  user_type: 'worker' | 'poster' | 'both';
  is_verified: boolean;
  created_at: string;
  last_active_at: string;
}

type SupportedLanguage = 'hindi' | 'english' | 'tamil' | 'telugu' | 'bengali' | 'marathi' | 'gujarati' | 'kannada';
```

**Relationships:**
- One-to-many with WorkerPersonas
- One-to-many with Jobs (as poster)
- One-to-many with JobApplications
- One-to-many with Reviews (given and received)

### WorkerPersona
**Purpose:** Core feature enabling multi-skilled workers to maintain separate professional identities for different services (electrician, driver, cook, etc.).

**Key Attributes:**
- id: UUID - Primary identifier
- user_id: UUID - Foreign key to User
- title: string - "Experienced Electrician", "Part-time Driver"
- skill_category: enum - Primary skill category
- skill_subcategories: array - Related skills
- description: text - Persona-specific bio
- experience_years: integer - Years of experience in this skill
- hourly_rate: decimal - Rate in INR
- daily_rate: decimal - Daily rate in INR
- is_rate_negotiable: boolean - Flexibility on pricing
- availability_pattern: JSON - Weekly availability schedule
- travel_radius_km: integer - Willing to travel distance
- is_active: boolean - Currently accepting jobs for this persona
- total_jobs_completed: integer - Performance metric
- average_rating: decimal - Persona-specific rating
- profile_image_url: string - Persona-specific photo

**TypeScript Interface:**
```typescript
interface WorkerPersona {
  id: string;
  user_id: string;
  title: string;
  skill_category: SkillCategory;
  skill_subcategories: string[];
  description: string;
  experience_years: number;
  hourly_rate?: number;
  daily_rate?: number;
  is_rate_negotiable: boolean;
  availability_pattern: WeeklyAvailability;
  travel_radius_km: number;
  is_active: boolean;
  total_jobs_completed: number;
  average_rating: number;
  profile_image_url?: string;
  created_at: string;
  updated_at: string;
}

type SkillCategory = 'electrical' | 'plumbing' | 'carpentry' | 'cooking' | 'cleaning' | 'driving' | 'delivery' | 'security' | 'gardening' | 'tutoring';

interface WeeklyAvailability {
  [day: string]: TimeSlot[];
}

interface TimeSlot {
  start: string; // "09:00"
  end: string;   // "17:00"
}
```

**Relationships:**
- Many-to-one with User
- One-to-many with JobApplications
- One-to-many with Reviews
- Many-to-many with Jobs (through applications)

### Job
**Purpose:** Job postings from employers seeking workers, with hyper-local matching and urgency levels.

**Key Attributes:**
- id: UUID - Primary identifier
- poster_id: UUID - Foreign key to User
- title: string - Job title
- description: text - Job description (supports multiple languages)
- skill_category: enum - Required skill category
- location: Point - Job location with address
- job_type: enum - 'one_time', 'recurring', 'permanent'
- urgency: enum - 'low', 'normal', 'high', 'urgent'
- budget_min: decimal - Minimum budget in INR
- budget_max: decimal - Maximum budget in INR
- estimated_duration_hours: integer - Expected job duration
- requirements: text - Specific requirements
- preferred_gender: enum - 'any', 'male', 'female'
- status: enum - 'active', 'paused', 'filled', 'cancelled', 'expired'
- applications_count: integer - Number of applications received
- views_count: integer - Number of views
- expires_at: timestamp - Job expiry date
- created_at: timestamp - Posted date

**TypeScript Interface:**
```typescript
interface Job {
  id: string;
  poster_id: string;
  title: string;
  description: string;
  skill_category: SkillCategory;
  location: {
    latitude: number;
    longitude: number;
    address: string;
    landmark?: string;
  };
  job_type: 'one_time' | 'recurring' | 'permanent';
  urgency: 'low' | 'normal' | 'high' | 'urgent';
  budget_min: number;
  budget_max: number;
  estimated_duration_hours: number;
  requirements?: string;
  preferred_gender: 'any' | 'male' | 'female';
  status: 'active' | 'paused' | 'filled' | 'cancelled' | 'expired';
  applications_count: number;
  views_count: number;
  expires_at: string;
  created_at: string;
  updated_at: string;
}
```

**Relationships:**
- Many-to-one with User (poster)
- One-to-many with JobApplications
- One-to-many with Reviews

### JobApplication
**Purpose:** Connection between worker personas and jobs, tracking application status and communication.

**Key Attributes:**
- id: UUID - Primary identifier
- job_id: UUID - Foreign key to Job
- worker_persona_id: UUID - Foreign key to WorkerPersona
- status: enum - Application status
- proposed_rate: decimal - Worker's proposed rate
- message: text - Application message
- applied_at: timestamp - Application timestamp
- responded_at: timestamp - When poster responded
- poster_response: text - Poster's response message

**TypeScript Interface:**
```typescript
interface JobApplication {
  id: string;
  job_id: string;
  worker_persona_id: string;
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
  proposed_rate?: number;
  message?: string;
  applied_at: string;
  responded_at?: string;
  poster_response?: string;
}
```

**Relationships:**
- Many-to-one with Job
- Many-to-one with WorkerPersona
- One-to-one with Review (after job completion)

### Review
**Purpose:** Trust verification system through ratings and reviews, supporting both worker and poster reviews.

**Key Attributes:**
- id: UUID - Primary identifier
- job_id: UUID - Foreign key to Job
- reviewer_id: UUID - User who wrote review
- reviewee_id: UUID - User being reviewed
- worker_persona_id: UUID - Specific persona being reviewed (if applicable)
- rating: integer - 1-5 star rating
- review_text: text - Written review
- review_type: enum - 'worker_review', 'poster_review'
- is_verified: boolean - Review verification status

**TypeScript Interface:**
```typescript
interface Review {
  id: string;
  job_id: string;
  reviewer_id: string;
  reviewee_id: string;
  worker_persona_id?: string;
  rating: 1 | 2 | 3 | 4 | 5;
  review_text?: string;
  review_type: 'worker_review' | 'poster_review';
  is_verified: boolean;
  created_at: string;
}
```

**Relationships:**
- Many-to-one with Job
- Many-to-one with User (reviewer)
- Many-to-one with User (reviewee)
- Many-to-one with WorkerPersona (optional)

## API Specification

Based on the Supabase client chosen in the Tech Stack, here are the API patterns and key endpoints that will be auto-generated by Supabase, along with custom Edge Functions for complex business logic.

### Supabase Auto-Generated REST API

Supabase automatically generates REST endpoints for all database tables with full CRUD operations:

**Base URL:** `https://your-project.supabase.co/rest/v1/`

**Key Endpoints:**

```yaml
# Users
GET    /users                    # List users with filtering
POST   /users                    # Create new user
GET    /users/{id}              # Get user by ID
PATCH  /users/{id}              # Update user
DELETE /users/{id}              # Delete user

# Worker Personas
GET    /worker_personas                           # List personas with filtering
POST   /worker_personas                           # Create new persona
GET    /worker_personas/{id}                     # Get persona by ID
PATCH  /worker_personas/{id}                     # Update persona
DELETE /worker_personas/{id}                     # Delete persona
GET    /worker_personas?user_id=eq.{user_id}     # Get user's personas

# Jobs
GET    /jobs                                      # List jobs with filtering
POST   /jobs                                      # Create new job
GET    /jobs/{id}                                # Get job by ID
PATCH  /jobs/{id}                                # Update job
DELETE /jobs/{id}                                # Delete job
GET    /jobs?status=eq.active&location=...       # Active jobs near location

# Job Applications
GET    /job_applications                         # List applications
POST   /job_applications                         # Apply to job
PATCH  /job_applications/{id}                   # Update application status

# Reviews
GET    /reviews                                  # List reviews
POST   /reviews                                  # Create review
GET    /reviews?reviewee_id=eq.{user_id}        # Get user's reviews
```

### Custom Edge Functions (Business Logic)

For complex operations that require custom logic, Supabase Edge Functions:

**Function 1: Job Matching Algorithm**
```typescript
// /functions/match-jobs/index.ts
interface MatchJobsRequest {
  worker_persona_id: string;
  max_distance_km?: number;
  limit?: number;
}

interface MatchedJob {
  job: Job;
  distance_km: number;
  match_score: number;
  match_reasons: string[];
}

// POST /functions/v1/match-jobs
// Returns: MatchedJob[] sorted by match score
```

**Function 2: Update Worker Location**
```typescript
// /functions/update-worker-location/index.ts
interface UpdateLocationRequest {
  user_id: string;
  latitude: number;
  longitude: number;
  address?: string;
}

// POST /functions/v1/update-worker-location
// Updates location and triggers nearby job notifications
```

### Real-time Subscriptions

Supabase provides WebSocket-based real-time subscriptions for live updates:

```typescript
// Job notifications for workers
const jobSubscription = supabase
  .channel('jobs')
  .on('postgres_changes', 
    { 
      event: 'INSERT', 
      schema: 'public', 
      table: 'jobs',
      filter: `skill_category=eq.${workerSkill}` 
    }, 
    handleNewJob
  )
  .subscribe();

// Application status updates for job posters
const applicationSubscription = supabase
  .channel('applications')
  .on('postgres_changes',
    {
      event: 'INSERT',
      schema: 'public', 
      table: 'job_applications',
      filter: `job_id=eq.${jobId}`
    },
    handleNewApplication
  )
  .subscribe();
```

## Components

Based on the React Native + Supabase architecture and multi-persona job platform requirements, here are the major logical components across the fullstack:

### Frontend Components (React Native)

**AuthenticationManager**
**Responsibility:** Handles phone-based authentication flow with +91 OTP verification for Indian users

**Key Interfaces:**
- Phone number input with validation
- OTP verification with retry logic
- Session persistence and refresh
- Language selection during onboarding

**Dependencies:** Supabase Auth, react-native-localize
**Technology Stack:** React Native, Supabase Auth SDK, AsyncStorage for session persistence

**LocationManager** 
**Responsibility:** Manages user location for hyper-local job matching within 5km radius

**Key Interfaces:**
- Current location detection
- Address geocoding and reverse geocoding
- Travel radius configuration per persona
- Location permission handling

**Dependencies:** Google Maps API, device location services
**Technology Stack:** React Native Geolocation, Google Maps SDK

**PersonaManager**
**Responsibility:** Core component managing multiple worker personas - electrician, driver, cook, etc.

**Key Interfaces:**
- Create/edit persona profiles
- Switch between active personas
- Manage persona-specific availability
- Upload persona-specific photos

**Dependencies:** Supabase Database, Supabase Storage
**Technology Stack:** React Hook Form, image picker, Supabase client

**JobMatchingEngine**
**Responsibility:** Displays matched jobs and handles job application workflow

**Key Interfaces:**
- Real-time job notifications
- Job filtering and search
- Application submission
- Job status tracking

**Dependencies:** Supabase Realtime, LocationManager
**Technology Stack:** Supabase subscriptions, push notifications, React Query for caching

**ReviewSystem**
**Responsibility:** Trust verification through bidirectional reviews between workers and job posters

**Key Interfaces:**
- Submit reviews after job completion
- Display persona-specific ratings
- Review moderation and verification
- Trust score calculations

**Dependencies:** Supabase Database, PersonaManager
**Technology Stack:** React Native rating components, form validation

### Backend Components (Supabase)

**UserService**
**Responsibility:** User management with phone-first authentication and multi-language support

**Key Interfaces:**
- User registration and phone verification
- Profile management
- Language preference handling
- Account linking (worker + poster capabilities)

**Dependencies:** Supabase Auth, database triggers
**Technology Stack:** PostgreSQL with RLS, Supabase Edge Functions

**JobMatchingService**
**Responsibility:** Core business logic for matching worker personas to nearby jobs based on skills, location, and availability

**Key Interfaces:**
- Calculate job match scores
- Geographic distance filtering
- Availability conflict detection
- Ranking algorithm based on ratings and proximity

**Dependencies:** PostgreSQL PostGIS, UserService
**Technology Stack:** Supabase Edge Functions, PostGIS for geo queries

**NotificationService**
**Responsibility:** Real-time job notifications and application status updates

**Key Interfaces:**
- Push job notifications to nearby workers
- Application status updates to job posters
- Batch notification processing
- Multi-language notification templates

**Dependencies:** Supabase Realtime, external push notification service
**Technology Stack:** Supabase Edge Functions, WebSocket subscriptions

**TrustVerificationService**
**Responsibility:** Manages trust scores, review verification, and fraud prevention

**Key Interfaces:**
- Review aggregation and scoring
- Fake review detection
- User verification workflows
- Trust score calculations

**Dependencies:** ReviewSystem, UserService
**Technology Stack:** PostgreSQL stored procedures, basic fraud detection rules

### Integration Components

**LanguageService**
**Responsibility:** Handles 8 Indian languages with real-time translation for job descriptions

**Key Interfaces:**
- Dynamic language switching
- Job description translation
- Cultural localization
- RTL text support

**Dependencies:** Translation API, react-native-localize
**Technology Stack:** i18n-js, Google Translate API integration

### Component Interaction Diagram

```mermaid
graph TB
    subgraph "React Native App"
        AUTH[AuthenticationManager]
        PERSONA[PersonaManager] 
        LOCATION[LocationManager]
        JOBS[JobMatchingEngine]
        REVIEWS[ReviewSystem]
        LANG[LanguageService]
    end
    
    subgraph "Supabase Backend"
        USER_SVC[UserService]
        MATCH_SVC[JobMatchingService]
        NOTIFY_SVC[NotificationService]
        TRUST_SVC[TrustVerificationService]
        DATA_MGR[PersonaDataManager]
        LOCATION_IDX[LocationIndexingService]
    end
    
    subgraph "External Services"
        MAPS[Google Maps API]
        TRANSLATE[Translation API]
        PUSH[Push Notifications]
    end
    
    AUTH --> USER_SVC
    PERSONA --> DATA_MGR
    LOCATION --> LOCATION_IDX
    LOCATION --> MAPS
    JOBS --> MATCH_SVC
    JOBS --> NOTIFY_SVC
    REVIEWS --> TRUST_SVC
    LANG --> TRANSLATE
    
    MATCH_SVC --> LOCATION_IDX
    NOTIFY_SVC --> PUSH
    USER_SVC --> TRUST_SVC
    
    style AUTH fill:#e1f5fe
    style PERSONA fill:#f3e5f5  
    style MATCH_SVC fill:#e8f5e8
```

## Core Workflows

Here are the critical user journeys illustrated as sequence diagrams, showing component interactions for the Ozgaar job platform:

### Workflow 1: Worker Registration and First Persona Creation

```mermaid
sequenceDiagram
    participant U as User
    participant App as Mobile App
    participant Auth as AuthenticationManager
    participant SB as Supabase Auth
    participant PM as PersonaManager
    participant DB as Supabase DB
    participant LM as LocationManager
    participant Maps as Google Maps

    U->>App: Opens app, selects "Join as Worker"
    App->>Auth: Initialize registration
    Auth->>U: Show phone number input (+91 pre-filled)
    U->>Auth: Enters 10-digit phone number
    Auth->>SB: Send OTP request
    SB-->>U: Receives SMS with OTP
    U->>Auth: Enters OTP code
    Auth->>SB: Verify OTP
    SB->>Auth: Returns JWT token
    Auth->>App: Registration successful
    
    App->>U: Show language selection (8 options)
    U->>App: Selects preferred language (Hindi)
    App->>LM: Request location permission
    LM->>U: Show location permission dialog
    U->>LM: Grants location access
    LM->>Maps: Get current location
    Maps->>LM: Returns lat/lng + address
    LM->>DB: Update user location
    
    App->>PM: Initialize first persona creation
    PM->>U: Show persona creation form
    U->>PM: Fills persona details (Electrician, 5 years experience, ₹500/day)
    PM->>DB: Create worker_persona record
    DB->>PM: Returns persona ID
    PM->>App: Persona created successfully
    App->>U: Navigate to job dashboard
```

### Workflow 2: Job Matching and Application Process

```mermaid
sequenceDiagram
    participant W as Worker
    participant App as Mobile App
    participant JME as JobMatchingEngine
    participant JMS as JobMatchingService
    participant NS as NotificationService
    participant PM as PersonaManager
    participant DB as Supabase DB
    participant P as Job Poster

    Note over W,P: Worker opens app with active persona
    
    W->>App: Opens job dashboard
    App->>JME: Load matched jobs
    JME->>JMS: Request jobs for persona
    JMS->>DB: Query jobs within 10km radius
    DB->>JMS: Returns nearby electrical jobs
    JMS->>JMS: Calculate match scores (distance + skills)
    JMS->>JME: Returns ranked job matches
    JME->>W: Display job list with match scores
    
    Note over W,P: New urgent job posted
    
    P->>DB: Posts urgent electrical job
    DB->>NS: Trigger job notification
    NS->>JMS: Find matching workers nearby
    JMS->>DB: Query active electrical personas within 5km
    DB->>JMS: Returns 3 matching workers
    NS->>JME: Send real-time notification
    JME->>W: Shows push notification "Urgent job near you!"
    
    W->>JME: Taps notification, views job details
    JME->>W: Shows job: "AC Repair, ₹800, 2km away"
    W->>JME: Clicks "Apply Now"
    JME->>PM: Get active persona details
    PM->>JME: Returns persona info
    JME->>W: Show application form (rate: ₹800, message optional)
    W->>JME: Submits application
    JME->>DB: Create job_application record
    DB->>NS: Trigger application notification
    NS->>P: Notify poster of new application
    P->>App: Views application, accepts worker
    App->>DB: Update application status to 'accepted'
    DB->>NS: Trigger acceptance notification
    NS->>W: "Congratulations! Job accepted"
```

## Database Schema

Based on the data models and PostgreSQL selection via Supabase, here's the concrete database schema for the Ozgaar platform:

### Core Tables Schema

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Custom types for the Indian job market
CREATE TYPE user_role_enum AS ENUM ('worker', 'poster', 'both');
CREATE TYPE supported_language_enum AS ENUM (
  'hindi', 'english', 'tamil', 'telugu', 'bengali', 
  'marathi', 'gujarati', 'kannada'
);
CREATE TYPE skill_category_enum AS ENUM (
  'electrical', 'plumbing', 'carpentry', 'cooking', 'cleaning',
  'driving', 'delivery', 'security', 'gardening', 'tutoring'
);
CREATE TYPE job_type_enum AS ENUM ('one_time', 'recurring', 'permanent');
CREATE TYPE urgency_enum AS ENUM ('low', 'normal', 'high', 'urgent');
CREATE TYPE job_status_enum AS ENUM ('active', 'paused', 'filled', 'cancelled', 'expired');
CREATE TYPE application_status_enum AS ENUM ('pending', 'accepted', 'rejected', 'withdrawn');
CREATE TYPE review_type_enum AS ENUM ('worker_review', 'poster_review');
CREATE TYPE gender_preference_enum AS ENUM ('any', 'male', 'female');

-- Users table (base entity for all platform users)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone VARCHAR(15) UNIQUE NOT NULL, -- +91 format for India
    email VARCHAR(255),
    full_name VARCHAR(100) NOT NULL,
    preferred_language supported_language_enum DEFAULT 'hindi',
    location GEOGRAPHY(POINT, 4326), -- PostGIS point for lat/lng
    address TEXT,
    user_type user_role_enum DEFAULT 'worker',
    is_verified BOOLEAN DEFAULT FALSE,
    profile_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_phone CHECK (phone ~ '^\+91[6-9][0-9]{9}$'),
    CONSTRAINT valid_email CHECK (email IS NULL OR email ~ '^[^@]+@[^@]+\.[^@]+$')
);

-- Worker personas table (core multi-persona feature)
CREATE TABLE worker_personas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(100) NOT NULL, -- "Experienced Electrician"
    skill_category skill_category_enum NOT NULL,
    skill_subcategories TEXT[], -- Array of related skills
    description TEXT,
    experience_years INTEGER DEFAULT 0 CHECK (experience_years >= 0),
    
    -- Pricing structure for Indian market
    hourly_rate DECIMAL(8,2) CHECK (hourly_rate >= 0),
    daily_rate DECIMAL(8,2) CHECK (daily_rate >= 0),
    monthly_rate DECIMAL(10,2) CHECK (monthly_rate >= 0),
    is_rate_negotiable BOOLEAN DEFAULT TRUE,
    
    -- Availability and location
    availability_pattern JSONB NOT NULL DEFAULT '{}', -- Weekly schedule
    travel_radius_km INTEGER DEFAULT 10 CHECK (travel_radius_km > 0),
    
    -- Performance metrics
    total_jobs_completed INTEGER DEFAULT 0 CHECK (total_jobs_completed >= 0),
    total_earnings DECIMAL(12,2) DEFAULT 0 CHECK (total_earnings >= 0),
    average_rating DECIMAL(3,2) DEFAULT 0 CHECK (average_rating >= 0 AND average_rating <= 5),
    total_reviews INTEGER DEFAULT 0 CHECK (total_reviews >= 0),
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_date TIMESTAMP WITH TIME ZONE,
    profile_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_rating CHECK (
      average_rating = 0 OR (total_reviews > 0 AND average_rating > 0)
    )
);

-- Jobs table (job postings from employers)
CREATE TABLE jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    poster_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    skill_category skill_category_enum NOT NULL,
    
    -- Location for hyper-local matching
    location GEOGRAPHY(POINT, 4326) NOT NULL,
    address TEXT NOT NULL,
    landmark TEXT,
    
    -- Job specifications
    job_type job_type_enum DEFAULT 'one_time',
    urgency urgency_enum DEFAULT 'normal',
    budget_min DECIMAL(10,2) NOT NULL CHECK (budget_min > 0),
    budget_max DECIMAL(10,2) NOT NULL CHECK (budget_max >= budget_min),
    estimated_duration_hours INTEGER CHECK (estimated_duration_hours > 0),
    
    -- Requirements and preferences
    requirements TEXT,
    preferred_gender gender_preference_enum DEFAULT 'any',
    min_experience_years INTEGER DEFAULT 0 CHECK (min_experience_years >= 0),
    min_rating DECIMAL(3,2) DEFAULT 0 CHECK (min_rating >= 0 AND min_rating <= 5),
    
    -- Status and metrics
    status job_status_enum DEFAULT 'active',
    applications_count INTEGER DEFAULT 0 CHECK (applications_count >= 0),
    views_count INTEGER DEFAULT 0 CHECK (views_count >= 0),
    
    -- Timestamps
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_expiry CHECK (expires_at > created_at)
);

-- Job applications (connects personas to jobs)
CREATE TABLE job_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    worker_persona_id UUID NOT NULL REFERENCES worker_personas(id) ON DELETE CASCADE,
    
    -- Application details
    status application_status_enum DEFAULT 'pending',
    proposed_rate DECIMAL(8,2),
    message TEXT,
    
    -- Response tracking
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    poster_response TEXT,
    
    -- Prevent duplicate applications
    UNIQUE(job_id, worker_persona_id)
);

-- Reviews table (bidirectional trust system)
CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reviewee_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    worker_persona_id UUID REFERENCES worker_personas(id) ON DELETE CASCADE,
    
    -- Review content
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    review_type review_type_enum NOT NULL,
    
    -- Verification
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate reviews
    UNIQUE(job_id, reviewer_id, review_type),
    
    -- Ensure reviewee consistency
    CONSTRAINT valid_reviewee CHECK (
      (review_type = 'worker_review' AND worker_persona_id IS NOT NULL) OR
      (review_type = 'poster_review' AND worker_persona_id IS NULL)
    )
);

-- Performance optimization indexes
CREATE INDEX idx_users_location ON users USING GIST (location);
CREATE INDEX idx_jobs_location ON jobs USING GIST (location);
CREATE INDEX idx_jobs_active_category ON jobs (skill_category, status, created_at) WHERE status = 'active';
CREATE INDEX idx_personas_active_category ON worker_personas (skill_category, is_active) WHERE is_active = TRUE;
CREATE INDEX idx_applications_job ON job_applications (job_id, status);
CREATE INDEX idx_reviews_reviewee_persona ON reviews (reviewee_id, worker_persona_id, rating);

-- Row Level Security policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE worker_personas ENABLE ROW LEVEL SECURITY;
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can manage own personas" ON worker_personas FOR ALL USING (user_id = auth.uid());
CREATE POLICY "Public can view active jobs" ON jobs FOR SELECT USING (status = 'active');

-- Auto-update triggers for ratings and counters
CREATE OR REPLACE FUNCTION update_persona_rating()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.worker_persona_id IS NOT NULL THEN
    UPDATE worker_personas 
    SET 
      average_rating = (
        SELECT ROUND(AVG(rating)::numeric, 2) 
        FROM reviews 
        WHERE worker_persona_id = NEW.worker_persona_id 
        AND review_type = 'worker_review'
      ),
      total_reviews = (
        SELECT COUNT(*) 
        FROM reviews 
        WHERE worker_persona_id = NEW.worker_persona_id 
        AND review_type = 'worker_review'
      ),
      updated_at = NOW()
    WHERE id = NEW.worker_persona_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_persona_rating
  AFTER INSERT OR UPDATE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_persona_rating();
```

## Frontend Architecture

### Component Architecture

**Component Organization:**
```
src/
├── components/           # Reusable UI components
│   ├── auth/            # Authentication components
│   ├── persona/         # Multi-persona components
│   ├── jobs/            # Job-related components
│   ├── reviews/         # Review and rating components
│   └── common/          # Shared components
├── screens/             # Screen components
│   ├── AuthScreens/
│   ├── PersonaScreens/
│   ├── JobScreens/
│   └── ProfileScreens/
├── navigation/          # Navigation configuration
├── services/            # API and business logic
├── stores/              # Zustand state stores
├── utils/               # Helper functions
└── types/               # TypeScript definitions
```

**Component Template:**
```typescript
// components/persona/PersonaCard.tsx
import React from 'react';
import { Box, Text, Image, Badge, HStack } from 'native-base';
import { WorkerPersona } from '../../types/database';

interface PersonaCardProps {
  persona: WorkerPersona;
  onPress: () => void;
  isActive?: boolean;
}

export const PersonaCard: React.FC<PersonaCardProps> = ({
  persona,
  onPress,
  isActive = false
}) => {
  return (
    <Box
      bg={isActive ? 'blue.50' : 'white'}
      borderWidth={isActive ? 2 : 1}
      borderColor={isActive ? 'blue.500' : 'gray.200'}
      rounded="lg"
      p={4}
      shadow={1}
      onTouchEnd={onPress}
    >
      <HStack space={3} alignItems="center">
        <Image
          source={{ uri: persona.profile_image_url }}
          alt={persona.title}
          size="md"
          rounded="full"
        />
        <Box flex={1}>
          <Text fontSize="lg" fontWeight="bold">
            {persona.title}
          </Text>
          <Text color="gray.600" fontSize="sm">
            {persona.experience_years} years experience
          </Text>
          <HStack space={2} mt={1}>
            <Badge colorScheme="green">
              {persona.skill_category}
            </Badge>
            <Badge variant="outline">
              ₹{persona.daily_rate}/day
            </Badge>
          </HStack>
        </Box>
      </HStack>
    </Box>
  );
};
```

### State Management Architecture

**State Structure:**
```typescript
// stores/authStore.ts
import { create } from 'zustand';
import { User } from '../types/database';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (phone: string) => Promise<void>;
  verifyOTP: (otp: string) => Promise<void>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,
  
  login: async (phone: string) => {
    set({ isLoading: true });
    try {
      await authService.sendOTP(phone);
      // Handle success
    } catch (error) {
      // Handle error
    } finally {
      set({ isLoading: false });
    }
  },
  
  verifyOTP: async (otp: string) => {
    // Implementation
  },
  
  logout: () => {
    set({ user: null, isAuthenticated: false });
  },
  
  updateProfile: async (updates: Partial<User>) => {
    // Implementation
  }
}));
```

**State Management Patterns:**
- Separate stores for different domains (auth, personas, jobs)
- Async actions with loading states
- Optimistic updates for better UX
- Persistent state for critical data (auth tokens, preferences)

### Routing Architecture

**Route Organization:**
```
navigation/
├── AuthNavigator.tsx     # Authentication flow
├── MainNavigator.tsx     # Main app navigation
├── PersonaNavigator.tsx  # Persona management
└── JobNavigator.tsx      # Job-related screens
```

**Protected Route Pattern:**
```typescript
// navigation/ProtectedRoute.tsx
import React from 'react';
import { useAuthStore } from '../stores/authStore';
import { AuthNavigator } from './AuthNavigator';
import { MainNavigator } from './MainNavigator';

export const ProtectedRoute: React.FC = () => {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  
  return isAuthenticated ? <MainNavigator /> : <AuthNavigator />;
};
```

### Frontend Services Layer

**API Client Setup:**
```typescript
// services/supabase.ts
import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});
```

**Service Example:**
```typescript
// services/personaService.ts
import { supabase } from './supabase';
import { WorkerPersona, CreatePersonaData } from '../types/database';

export const personaService = {
  async getUserPersonas(userId: string): Promise<WorkerPersona[]> {
    const { data, error } = await supabase
      .from('worker_personas')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },
  
  async createPersona(personaData: CreatePersonaData): Promise<WorkerPersona> {
    const { data, error } = await supabase
      .from('worker_personas')
      .insert(personaData)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },
  
  async updatePersonaAvailability(
    personaId: string, 
    availability: object
  ): Promise<void> {
    const { error } = await supabase
      .from('worker_personas')
      .update({ 
        availability_pattern: availability,
        updated_at: new Date().toISOString()
      })
      .eq('id', personaId);
    
    if (error) throw error;
  },
};
```

This completes the Frontend Architecture section. The architecture is designed for React Native with Expo, focusing on the multi-persona job matching platform requirements with clean separation of concerns and optimized performance for Indian mobile networks.