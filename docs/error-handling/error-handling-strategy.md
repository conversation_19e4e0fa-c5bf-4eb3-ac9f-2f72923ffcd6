# Comprehensive Error Handling Strategy

## Overview

This document defines a complete error handling strategy for the Ozgaar platform, covering error classification, user experience patterns, recovery mechanisms, logging, and monitoring across the entire technology stack.

## 1. Error Classification System

### 1.1 Error Categories

| Category | Scope | Impact | User Action Required | Auto-Recovery |
|----------|-------|--------|---------------------|---------------|
| **Network** | Connectivity issues | Medium | Retry or wait | Yes |
| **Authentication** | User session/login | High | Re-login | Partial |
| **Validation** | User input errors | Low | Correct input | No |
| **Business Logic** | App rule violations | Medium | Follow guidance | Partial |
| **External Service** | Third-party failures | Medium | Wait or alternative | Yes |
| **System** | Critical app failures | High | App restart | No |
| **Data Integrity** | Database inconsistencies | High | Contact support | No |

### 1.2 Error Severity Levels

```typescript
// src/types/errors.ts
export enum ErrorSeverity {
  CRITICAL = 'critical',    // App unusable, immediate attention
  HIGH = 'high',           // Major feature broken, user blocked
  MEDIUM = 'medium',       // Feature degraded, workaround available  
  LOW = 'low',             // Minor inconvenience, cosmetic
  INFO = 'info'            // Informational, no action needed
}

export enum ErrorCategory {
  NETWORK = 'network',
  AUTH = 'authentication',
  VALIDATION = 'validation',
  BUSINESS = 'business_logic',
  EXTERNAL = 'external_service',
  SYSTEM = 'system',
  DATA = 'data_integrity'
}

export interface AppError {
  code: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  message: string;
  details?: string;
  userMessage: LocalizedMessage;
  userAction?: UserAction;
  retryable: boolean;
  autoRetry: boolean;
  maxRetries?: number;
  telemetry: {
    timestamp: string;
    userId?: string;
    sessionId: string;
    context: Record<string, any>;
  };
}

interface LocalizedMessage {
  hindi: string;
  english: string;
  [key: string]: string;
}

interface UserAction {
  type: 'retry' | 'refresh' | 'navigate' | 'contact_support' | 'none';
  label: LocalizedMessage;
  action?: () => void;
}
```

## 2. Error Handling Patterns by Layer

### 2.1 Network Layer Error Handling

```typescript
// src/services/network/networkErrorHandler.ts
export class NetworkErrorHandler {
  private static retryConfig = {
    maxRetries: 3,
    baseDelay: 1000,    // 1 second
    maxDelay: 10000,    // 10 seconds
    backoffMultiplier: 2
  };

  static async handleNetworkError(error: any, context: string): Promise<AppError> {
    const errorCode = this.classifyNetworkError(error);
    
    switch (errorCode) {
      case 'NO_INTERNET':
        return {
          code: 'NET_001',
          category: ErrorCategory.NETWORK,
          severity: ErrorSeverity.MEDIUM,
          message: 'No internet connection detected',
          userMessage: {
            hindi: 'इंटरनेट कनेक्शन नहीं है',
            english: 'No internet connection'
          },
          userAction: {
            type: 'retry',
            label: {
              hindi: 'दोबारा कोशिश करें',
              english: 'Try Again'
            }
          },
          retryable: true,
          autoRetry: true,
          maxRetries: 5,
          telemetry: this.createTelemetry(context, error)
        };

      case 'TIMEOUT':
        return {
          code: 'NET_002',
          category: ErrorCategory.NETWORK,
          severity: ErrorSeverity.MEDIUM,
          message: 'Request timeout',
          userMessage: {
            hindi: 'नेटवर्क धीमा है, कृपया प्रतीक्षा करें',
            english: 'Network is slow, please wait'
          },
          userAction: {
            type: 'retry',
            label: {
              hindi: 'दोबारा कोशिश करें',
              english: 'Retry'
            }
          },
          retryable: true,
          autoRetry: true,
          maxRetries: 3,
          telemetry: this.createTelemetry(context, error)
        };

      case 'SERVER_ERROR':
        return {
          code: 'NET_003',
          category: ErrorCategory.NETWORK,
          severity: ErrorSeverity.HIGH,
          message: 'Server error occurred',
          userMessage: {
            hindi: 'सर्वर में समस्या है। कुछ देर बाद कोशिश करें।',
            english: 'Server issue. Please try again later.'
          },
          userAction: {
            type: 'contact_support',
            label: {
              hindi: 'सहायता चाहिए?',
              english: 'Need Help?'
            }
          },
          retryable: true,
          autoRetry: false,
          maxRetries: 1,
          telemetry: this.createTelemetry(context, error)
        };

      default:
        return this.createGenericNetworkError(error, context);
    }
  }

  private static classifyNetworkError(error: any): string {
    if (!navigator.onLine) return 'NO_INTERNET';
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('timeout')) return 'TIMEOUT';
    if (error.status >= 500) return 'SERVER_ERROR';
    if (error.status >= 400) return 'CLIENT_ERROR';
    return 'UNKNOWN_NETWORK';
  }

  static async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: string,
    customRetries?: number
  ): Promise<T> {
    const maxRetries = customRetries || this.retryConfig.maxRetries;
    let lastError: any;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        const appError = await this.handleNetworkError(error, context);
        
        // Don't retry if not retryable or if it's the last attempt
        if (!appError.retryable || attempt === maxRetries) {
          throw appError;
        }
        
        // Calculate delay with exponential backoff
        const delay = Math.min(
          this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt - 1),
          this.retryConfig.maxDelay
        );
        
        console.log(`Retry attempt ${attempt}/${maxRetries} after ${delay}ms delay`);
        await this.delay(delay);
      }
    }
    
    throw lastError;
  }

  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 2.2 Authentication Error Handling

```typescript
// src/services/auth/authErrorHandler.ts
export class AuthErrorHandler {
  static handleAuthError(error: any, context: string): AppError {
    const errorCode = this.classifyAuthError(error);
    
    switch (errorCode) {
      case 'INVALID_OTP':
        return {
          code: 'AUTH_001',
          category: ErrorCategory.AUTH,
          severity: ErrorSeverity.MEDIUM,
          message: 'Invalid OTP entered',
          userMessage: {
            hindi: 'गलत OTP डाला गया है',
            english: 'Incorrect OTP entered'
          },
          userAction: {
            type: 'retry',
            label: {
              hindi: 'दोबारा कोशिश करें',
              english: 'Try Again'
            }
          },
          retryable: true,
          autoRetry: false,
          maxRetries: 3,
          telemetry: this.createTelemetry(context, error)
        };

      case 'OTP_EXPIRED':
        return {
          code: 'AUTH_002',
          category: ErrorCategory.AUTH,
          severity: ErrorSeverity.MEDIUM,
          message: 'OTP has expired',
          userMessage: {
            hindi: 'OTP की समय सीमा खत्म हो गई',
            english: 'OTP has expired'
          },
          userAction: {
            type: 'retry',
            label: {
              hindi: 'नया OTP भेजें',
              english: 'Send New OTP'
            }
          },
          retryable: true,
          autoRetry: false,
          telemetry: this.createTelemetry(context, error)
        };

      case 'SESSION_EXPIRED':
        return {
          code: 'AUTH_003',
          category: ErrorCategory.AUTH,
          severity: ErrorSeverity.HIGH,
          message: 'User session has expired',
          userMessage: {
            hindi: 'आपका session खत्म हो गया है',
            english: 'Your session has expired'
          },
          userAction: {
            type: 'navigate',
            label: {
              hindi: 'दोबारा लॉगिन करें',
              english: 'Login Again'
            }
          },
          retryable: false,
          autoRetry: false,
          telemetry: this.createTelemetry(context, error)
        };

      case 'TOO_MANY_REQUESTS':
        return {
          code: 'AUTH_004',
          category: ErrorCategory.AUTH,
          severity: ErrorSeverity.MEDIUM,
          message: 'Too many OTP requests',
          userMessage: {
            hindi: 'बहुत सारे OTP भेजे गए। 1 घंटे बाद कोशिश करें।',
            english: 'Too many OTP requests. Try again after 1 hour.'
          },
          userAction: {
            type: 'none',
            label: {
              hindi: 'ठीक है',
              english: 'OK'
            }
          },
          retryable: false,
          autoRetry: false,
          telemetry: this.createTelemetry(context, error)
        };

      default:
        return this.createGenericAuthError(error, context);
    }
  }

  private static classifyAuthError(error: any): string {
    if (error.code === 'invalid_otp' || error.message?.includes('Invalid OTP')) {
      return 'INVALID_OTP';
    }
    if (error.code === 'otp_expired') return 'OTP_EXPIRED';
    if (error.code === 'session_expired' || error.status === 401) return 'SESSION_EXPIRED';
    if (error.status === 429) return 'TOO_MANY_REQUESTS';
    return 'UNKNOWN_AUTH';
  }

  // Auto session refresh mechanism
  static async handleSessionExpiry() {
    try {
      // Try to refresh the session
      const refreshed = await AuthService.refreshSession();
      if (refreshed) {
        return true; // Continue with original request
      }
    } catch (refreshError) {
      console.log('Session refresh failed:', refreshError);
    }
    
    // Redirect to login
    await AuthService.logout();
    NavigationService.navigateToLogin();
    return false;
  }
}
```

### 2.3 Business Logic Error Handling

```typescript
// src/services/business/businessErrorHandler.ts
export class BusinessErrorHandler {
  static handleBusinessError(error: any, context: string): AppError {
    const errorCode = this.classifyBusinessError(error, context);
    
    switch (errorCode) {
      case 'JOB_ALREADY_APPLIED':
        return {
          code: 'BIZ_001',
          category: ErrorCategory.BUSINESS,
          severity: ErrorSeverity.LOW,
          message: 'Already applied to this job',
          userMessage: {
            hindi: 'इस जॉब के लिए पहले से apply किया गया है',
            english: 'You have already applied to this job'
          },
          userAction: {
            type: 'navigate',
            label: {
              hindi: 'Application status देखें',
              english: 'View Application Status'
            }
          },
          retryable: false,
          autoRetry: false,
          telemetry: this.createTelemetry(context, error)
        };

      case 'JOB_EXPIRED':
        return {
          code: 'BIZ_002',
          category: ErrorCategory.BUSINESS,
          severity: ErrorSeverity.MEDIUM,
          message: 'Job posting has expired',
          userMessage: {
            hindi: 'यह जॉब अब उपलब्ध नहीं है',
            english: 'This job is no longer available'
          },
          userAction: {
            type: 'navigate',
            label: {
              hindi: 'और जॉब देखें',
              english: 'View More Jobs'
            }
          },
          retryable: false,
          autoRetry: false,
          telemetry: this.createTelemetry(context, error)
        };

      case 'INSUFFICIENT_PROFILE':
        return {
          code: 'BIZ_003',
          category: ErrorCategory.BUSINESS,
          severity: ErrorSeverity.MEDIUM,
          message: 'Profile incomplete for job application',
          userMessage: {
            hindi: 'जॉब apply करने के लिए प्रोफाइल complete करें',
            english: 'Complete your profile to apply for jobs'
          },
          userAction: {
            type: 'navigate',
            label: {
              hindi: 'प्रोफाइल complete करें',
              english: 'Complete Profile'
            }
          },
          retryable: false,
          autoRetry: false,
          telemetry: this.createTelemetry(context, error)
        };

      case 'MAX_PERSONAS_REACHED':
        return {
          code: 'BIZ_004',
          category: ErrorCategory.BUSINESS,
          severity: ErrorSeverity.LOW,
          message: 'Maximum persona limit reached',
          userMessage: {
            hindi: 'अधिकतम 5 प्रोफाइल बना सकते हैं',
            english: 'You can create maximum 5 profiles'
          },
          userAction: {
            type: 'navigate',
            label: {
              hindi: 'मौजूदा प्रोफाइल manage करें',
              english: 'Manage Existing Profiles'
            }
          },
          retryable: false,
          autoRetry: false,
          telemetry: this.createTelemetry(context, error)
        };

      default:
        return this.createGenericBusinessError(error, context);
    }
  }

  private static classifyBusinessError(error: any, context: string): string {
    if (error.code === 'duplicate_application') return 'JOB_ALREADY_APPLIED';
    if (error.code === 'job_expired' || error.message?.includes('expired')) return 'JOB_EXPIRED';
    if (error.code === 'profile_incomplete') return 'INSUFFICIENT_PROFILE';
    if (error.code === 'max_personas_exceeded') return 'MAX_PERSONAS_REACHED';
    
    // Context-specific classification
    if (context.includes('job_application') && error.status === 409) return 'JOB_ALREADY_APPLIED';
    if (context.includes('persona_creation') && error.status === 400) return 'MAX_PERSONAS_REACHED';
    
    return 'UNKNOWN_BUSINESS';
  }
}
```

## 3. User Experience Error Patterns

### 3.1 Error UI Components

```typescript
// src/components/errors/ErrorDisplay.tsx
import React from 'react';
import { Alert, Box, Button, Text, VStack, Icon } from 'native-base';
import { AppError } from '../../types/errors';
import { useTranslation } from '../../hooks/useTranslation';

interface ErrorDisplayProps {
  error: AppError;
  onRetry?: () => void;
  onDismiss?: () => void;
  compact?: boolean;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  onDismiss,
  compact = false
}) => {
  const { language } = useTranslation();
  const userMessage = error.userMessage[language] || error.userMessage.english;
  
  const getAlertStatus = () => {
    switch (error.severity) {
      case 'critical':
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      default:
        return 'info';
    }
  };

  const getErrorIcon = () => {
    switch (error.category) {
      case 'network':
        return 'wifi-off';
      case 'authentication':
        return 'lock';
      case 'validation':
        return 'alert-circle';
      case 'business_logic':
        return 'info';
      case 'external_service':
        return 'cloud-off';
      default:
        return 'alert-triangle';
    }
  };

  if (compact) {
    return (
      <Alert status={getAlertStatus()} variant="left-accent">
        <Alert.Icon />
        <Text fontSize="sm">{userMessage}</Text>
        {error.retryable && onRetry && (
          <Button size="xs" variant="ghost" onPress={onRetry}>
            {error.userAction?.label[language]}
          </Button>
        )}
      </Alert>
    );
  }

  return (
    <Box bg="white" p={4} rounded="lg" shadow={2}>
      <VStack space={3} alignItems="center">
        <Icon name={getErrorIcon()} size="xl" color="red.500" />
        
        <VStack space={2} alignItems="center">
          <Text fontSize="lg" fontWeight="bold" textAlign="center">
            {userMessage}
          </Text>
          
          {error.details && (
            <Text fontSize="sm" color="gray.600" textAlign="center">
              {error.details}
            </Text>
          )}
        </VStack>

        <VStack space={2} w="full">
          {error.retryable && onRetry && (
            <Button onPress={onRetry} variant="solid">
              {error.userAction?.label[language]}
            </Button>
          )}
          
          {onDismiss && (
            <Button onPress={onDismiss} variant="ghost">
              {language === 'hindi' ? 'ठीक है' : 'OK'}
            </Button>
          )}
          
          {error.severity === 'critical' && (
            <Button 
              onPress={() => ContactService.openSupport()}
              variant="outline"
            >
              {language === 'hindi' ? 'सहायता चाहिए' : 'Contact Support'}
            </Button>
          )}
        </VStack>
      </VStack>
    </Box>
  );
};
```

### 3.2 Loading States with Error Recovery

```typescript
// src/components/common/SafeAsyncComponent.tsx
import React, { useState, useEffect } from 'react';
import { Box, Spinner, Text } from 'native-base';
import { ErrorDisplay } from '../errors/ErrorDisplay';
import { AppError } from '../../types/errors';

interface SafeAsyncComponentProps<T> {
  asyncOperation: () => Promise<T>;
  onSuccess: (data: T) => React.ReactNode;
  onError?: (error: AppError) => React.ReactNode;
  loadingMessage?: string;
  retryable?: boolean;
  children?: React.ReactNode;
}

export function SafeAsyncComponent<T>({
  asyncOperation,
  onSuccess,
  onError,
  loadingMessage,
  retryable = true
}: SafeAsyncComponentProps<T>) {
  const [state, setState] = useState<{
    loading: boolean;
    data: T | null;
    error: AppError | null;
  }>({
    loading: true,
    data: null,
    error: null
  });

  const executeOperation = async () => {
    setState({ loading: true, data: null, error: null });
    
    try {
      const result = await asyncOperation();
      setState({ loading: false, data: result, error: null });
    } catch (error) {
      const appError = error as AppError;
      setState({ loading: false, data: null, error: appError });
      
      // Auto-retry for retryable errors
      if (appError.autoRetry && appError.maxRetries && appError.maxRetries > 0) {
        setTimeout(() => {
          executeOperation();
        }, 2000);
      }
    }
  };

  useEffect(() => {
    executeOperation();
  }, []);

  if (state.loading) {
    return (
      <Box flex={1} justifyContent="center" alignItems="center" p={4}>
        <Spinner size="lg" />
        {loadingMessage && (
          <Text mt={2} textAlign="center" color="gray.600">
            {loadingMessage}
          </Text>
        )}
      </Box>
    );
  }

  if (state.error) {
    if (onError) {
      return <>{onError(state.error)}</>;
    }
    
    return (
      <Box p={4}>
        <ErrorDisplay
          error={state.error}
          onRetry={retryable ? executeOperation : undefined}
        />
      </Box>
    );
  }

  if (state.data) {
    return <>{onSuccess(state.data)}</>;
  }

  return null;
}
```

## 4. Error Recovery Mechanisms

### 4.1 Automatic Recovery Strategies

```typescript
// src/services/recovery/autoRecoveryService.ts
export class AutoRecoveryService {
  private static recoveryStrategies = new Map<string, RecoveryStrategy>();

  static registerRecoveryStrategy(errorCode: string, strategy: RecoveryStrategy) {
    this.recoveryStrategies.set(errorCode, strategy);
  }

  static async attemptRecovery(error: AppError): Promise<boolean> {
    const strategy = this.recoveryStrategies.get(error.code);
    if (!strategy) return false;

    try {
      return await strategy.execute(error);
    } catch (recoveryError) {
      console.error('Recovery strategy failed:', recoveryError);
      return false;
    }
  }

  // Initialize recovery strategies
  static initialize() {
    // Network recovery
    this.registerRecoveryStrategy('NET_001', {
      execute: async (error: AppError) => {
        // Wait for network to come back
        return new Promise<boolean>(resolve => {
          const checkConnection = () => {
            if (navigator.onLine) {
              resolve(true);
            } else {
              setTimeout(checkConnection, 2000);
            }
          };
          checkConnection();
          
          // Timeout after 30 seconds
          setTimeout(() => resolve(false), 30000);
        });
      }
    });

    // Session recovery
    this.registerRecoveryStrategy('AUTH_003', {
      execute: async (error: AppError) => {
        return await AuthErrorHandler.handleSessionExpiry();
      }
    });

    // Data sync recovery
    this.registerRecoveryStrategy('DATA_001', {
      execute: async (error: AppError) => {
        try {
          await DataSyncService.forceSyncFromServer();
          return true;
        } catch {
          return false;
        }
      }
    });
  }
}

interface RecoveryStrategy {
  execute(error: AppError): Promise<boolean>;
}
```

### 4.2 Graceful Degradation

```typescript
// src/services/degradation/gracefulDegradationService.ts
export class GracefulDegradationService {
  static async handleServiceDegradation(serviceType: string, error: AppError) {
    switch (serviceType) {
      case 'google_maps':
        return this.handleMapsDegradation();
      
      case 'sms_service':
        return this.handleSMSDegradation();
        
      case 'translation_service':
        return this.handleTranslationDegradation();
        
      default:
        return false;
    }
  }

  private static async handleMapsDegradation(): Promise<boolean> {
    // Fallback to manual address input
    UIService.showMessage({
      title: 'Maps service unavailable',
      message: 'Please enter your address manually',
      type: 'info'
    });
    
    // Enable manual location input mode
    LocationService.enableManualMode();
    return true;
  }

  private static async handleSMSDegradation(): Promise<boolean> {
    // Fallback to alternative verification method
    UIService.showMessage({
      title: 'SMS service temporarily unavailable',
      message: 'We will try alternative verification methods',
      type: 'warning'
    });
    
    // Try backup SMS provider or alternative auth
    return await AuthService.tryAlternativeVerification();
  }

  private static async handleTranslationDegradation(): Promise<boolean> {
    // Fallback to cached translations or English
    TranslationService.enableOfflineMode();
    
    UIService.showMessage({
      title: 'Translation service unavailable',
      message: 'Some content may appear in English',
      type: 'info'
    });
    
    return true;
  }
}
```

## 5. Error Logging and Monitoring

### 5.1 Error Telemetry System

```typescript
// src/services/telemetry/errorTelemetryService.ts
export class ErrorTelemetryService {
  private static isInitialized = false;
  private static errorBuffer: AppError[] = [];
  private static readonly MAX_BUFFER_SIZE = 50;

  static initialize() {
    if (this.isInitialized) return;

    // Setup error event listeners
    window.addEventListener('error', this.handleGlobalError.bind(this));
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this));

    // Setup periodic flush
    setInterval(() => this.flushErrorBuffer(), 30000); // Every 30 seconds

    this.isInitialized = true;
  }

  static logError(error: AppError) {
    // Add to buffer
    this.errorBuffer.push({
      ...error,
      telemetry: {
        ...error.telemetry,
        deviceInfo: this.getDeviceInfo(),
        appVersion: this.getAppVersion(),
        networkInfo: this.getNetworkInfo()
      }
    });

    // Flush if buffer is full
    if (this.errorBuffer.length >= this.MAX_BUFFER_SIZE) {
      this.flushErrorBuffer();
    }

    // Log critical errors immediately
    if (error.severity === ErrorSeverity.CRITICAL) {
      this.sendImmediateAlert(error);
    }
  }

  private static async flushErrorBuffer() {
    if (this.errorBuffer.length === 0) return;

    const errors = [...this.errorBuffer];
    this.errorBuffer = [];

    try {
      await this.sendErrorsToServer(errors);
    } catch (sendError) {
      console.error('Failed to send error telemetry:', sendError);
      // Put errors back in buffer for next attempt
      this.errorBuffer.unshift(...errors.slice(-10)); // Keep only last 10
    }
  }

  private static async sendErrorsToServer(errors: AppError[]) {
    const supabase = createClient(
      process.env.EXPO_PUBLIC_SUPABASE_URL!,
      process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!
    );

    await supabase
      .from('error_logs')
      .insert(errors.map(error => ({
        error_code: error.code,
        category: error.category,
        severity: error.severity,
        message: error.message,
        user_id: error.telemetry.userId,
        session_id: error.telemetry.sessionId,
        context: error.telemetry.context,
        device_info: error.telemetry.deviceInfo,
        app_version: error.telemetry.appVersion,
        network_info: error.telemetry.networkInfo,
        created_at: error.telemetry.timestamp
      })));
  }

  private static async sendImmediateAlert(error: AppError) {
    // Send to monitoring service (Sentry, etc.)
    if (process.env.NODE_ENV === 'production') {
      // Sentry.captureException(error);
    }

    // Send to internal alerting
    try {
      await fetch('/api/alerts/critical', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error })
      });
    } catch (alertError) {
      console.error('Failed to send critical alert:', alertError);
    }
  }

  private static getDeviceInfo() {
    return {
      platform: Platform.OS,
      version: Platform.Version,
      model: Platform.constants?.Model || 'unknown',
      memory: DeviceInfo?.getTotalMemorySync?.() || 0
    };
  }

  private static getNetworkInfo() {
    return {
      type: NetInfo?.type || 'unknown',
      isConnected: NetInfo?.isConnected || false,
      isInternetReachable: NetInfo?.isInternetReachable || false
    };
  }
}
```

### 5.2 Error Analytics Dashboard Schema

```sql
-- Database schema for error analytics
CREATE TABLE error_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    error_code VARCHAR(50) NOT NULL,
    category error_category_enum NOT NULL,
    severity error_severity_enum NOT NULL,
    message TEXT NOT NULL,
    user_id UUID REFERENCES users(id),
    session_id VARCHAR(100) NOT NULL,
    context JSONB,
    device_info JSONB,
    app_version VARCHAR(20),
    network_info JSONB,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE error_category_enum AS ENUM (
    'network', 'authentication', 'validation', 'business_logic', 
    'external_service', 'system', 'data_integrity'
);

CREATE TYPE error_severity_enum AS ENUM ('critical', 'high', 'medium', 'low', 'info');

-- Indexes for analytics
CREATE INDEX idx_error_logs_category_severity ON error_logs(category, severity, created_at);
CREATE INDEX idx_error_logs_user_session ON error_logs(user_id, session_id);
CREATE INDEX idx_error_logs_code_frequency ON error_logs(error_code, created_at);

-- Error summary view
CREATE VIEW error_summary AS
SELECT 
    error_code,
    category,
    severity,
    COUNT(*) as occurrence_count,
    COUNT(DISTINCT user_id) as affected_users,
    AVG(EXTRACT(EPOCH FROM (resolved_at - created_at))) as avg_resolution_time,
    MAX(created_at) as last_occurrence
FROM error_logs
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY error_code, category, severity
ORDER BY occurrence_count DESC;
```

## 6. Error Testing Strategy

### 6.1 Error Scenario Testing

```typescript
// src/testing/errorScenarios.test.ts
describe('Error Handling Scenarios', () => {
  describe('Network Errors', () => {
    it('should handle no internet connection gracefully', async () => {
      // Mock no internet
      jest.spyOn(navigator, 'onLine', 'get').mockReturnValue(false);
      
      const result = await NetworkService.makeRequest('/api/jobs');
      
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe('NET_001');
      expect(result.error.userMessage.hindi).toContain('इंटरनेट');
    });

    it('should retry failed requests automatically', async () => {
      const mockFetch = jest.fn()
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockResolvedValueOnce({ ok: true, json: () => ({}) });
      
      global.fetch = mockFetch;
      
      await NetworkService.makeRequest('/api/jobs');
      
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Business Logic Errors', () => {
    it('should prevent duplicate job applications', async () => {
      const result = await JobService.applyToJob('job-123', 'persona-456');
      
      // Apply again
      const duplicateResult = await JobService.applyToJob('job-123', 'persona-456');
      
      expect(duplicateResult.error).toBeDefined();
      expect(duplicateResult.error.code).toBe('BIZ_001');
    });
  });
});
```

This comprehensive error handling strategy ensures robust error management across the entire Ozgaar platform with proper user experience, automatic recovery, and detailed monitoring capabilities.