# AI Frontend Development Prompt for Ozgaar Mobile Application

## High-Level Goal
Create a mobile-first React Native application for Ozgaar, a trust-first job and gig platform targeting the Indian market. The app should support multi-persona worker profiles, hyper-local job
matching, and work efficiently on 2G/3G networks while supporting 8 Indian languages.

## Detailed, Step-by-Step Instructions

### 1. Project Setup and Configuration
1. Initialize a new React Native project with Expo (SDK 50+)
2. Install required dependencies:
    - @supabase/supabase-js for backend integration
    - native-base for UI components
    - zustand for state management
    - react-navigation for navigation
    - react-hook-form for form handling
    - @react-native-async-storage/async-storage for local storage
    - react-native-localize and i18n-js for internationalization
3. Configure TypeScript with strict mode enabled
4. Set up ESLint and Prettier for code quality
5. Configure environment variables for Supabase connection

### 2. Authentication System
1. Create an AuthenticationManager component for phone-based authentication with +91 OTP verification
2. Implement phone number input with validation (10-digit Indian format)
3. Create OTP verification screen with retry logic
4. Implement session persistence using AsyncStorage
5. Add language selection during onboarding (8 Indian language options)
6. Create protected route system to redirect between auth and main app

### 3. User Profile and Persona Management
1. Build a PersonaManager component to handle multi-persona worker profiles
2. Create screens for:
    - User profile creation/editing
    - Persona creation (electrician, driver, cook, etc.)
    - Persona switching interface
    - Persona-specific availability scheduling
    - Profile image upload functionality
3. Implement forms with proper validation using React Hook Form
4. Create reusable PersonaCard components for displaying persona information

### 4. Location Services Integration
1. Implement a LocationManager component for hyper-local job matching
2. Integrate Google Maps API for:
    - Current location detection
    - Address geocoding/reverse geocoding
    - Location permission handling
3. Implement travel radius configuration (0-5km default)
4. Create location-aware components that work offline with cached data

### 5. Job Matching and Browsing
1. Build a JobMatchingEngine component for displaying matched jobs
2. Create screens for:
    - Worker dashboard with job recommendations
    - Job browsing with filtering (by skill, distance, pay)
    - Job details view with trust indicators
    - Job application submission
    - Application status tracking
3. Implement real-time job notifications using Supabase subscriptions
4. Create job cards with key information (skill, distance, pay, urgency)

### 6. Review and Trust System
1. Implement a ReviewSystem component for bidirectional reviews
2. Create screens for:
    - Review submission after job completion
    - Persona-specific rating display
    - Review moderation interface
3. Build trust score visualization components

### 7. Internationalization (i18n)
1. Set up react-native-localize with i18n-js for 8 Indian languages
2. Implement language switching functionality
3. Create translation JSON files for all supported languages
4. Ensure all UI components support right-to-left layouts where needed

### 8. State Management
1. Implement Zustand stores for:
    - Authentication state
    - User profile and personas
    - Job listings and applications
    - Location data
    - Language preferences
2. Add persistence for critical data using AsyncStorage
3. Implement optimistic updates for better UX

### 9. Navigation Structure
1. Set up React Navigation with:
    - Stack navigators for authentication flow
    - Bottom tab navigator for main app sections
    - Drawer navigator for settings (if needed)
2. Implement proper deep linking for job postings
3. Add breadcrumb navigation where appropriate

### 10. Performance Optimization
1. Implement lazy loading for job listings
2. Add progressive loading for images (show text content first)
3. Optimize for 2G/3G networks with data-conscious design
4. Implement proper error handling and offline states
5. Add skeleton screens for better perceived performance

### 11. UI Component Library
1. Create reusable components following the design system:
    - Trust Badge components (ID verified, phone verified, etc.)
    - Job/Worker Card components
    - Language-First Input components
    - Location Selector components
    - Communication Hub components (chat interface)
2. Implement proper accessibility features (WCAG 2.1 AA compliance)
3. Ensure all touch targets are minimum 44px for accessibility

### 12. Testing Infrastructure
1. Set up Jest for unit testing
2. Configure React Native Testing Library for component testing
3. Implement basic snapshot tests for key components
4. Set up Detox for end-to-end testing

## Code Examples, Data Structures & Constraints

### Tech Stack Requirements
- React Native with Expo (TypeScript)
- Supabase for backend services
- NativeBase for UI components
- Zustand for state management
- React Navigation for routing
- Google Maps API for location services

### Data Models (TypeScript interfaces)
```typescript
interface User {
id: string;
phone: string;
email?: string;
full_name: string;
preferred_language: SupportedLanguage;
location: {
    latitude: number;
    longitude: number;
    address?: string;
};
user_type: 'worker' | 'poster' | 'both';
is_verified: boolean;
created_at: string;
last_active_at: string;
}

type SupportedLanguage = 'hindi' | 'english' | 'tamil' | 'telugu' | 'bengali' | 'marathi' | 'gujarati' | 'kannada';

interface WorkerPersona {
id: string;
user_id: string;
title: string;
skill_category: SkillCategory;
skill_subcategories: string[];
description: string;
experience_years: number;
hourly_rate?: number;
daily_rate?: number;
is_rate_negotiable: boolean;
availability_pattern: WeeklyAvailability;
travel_radius_km: number;
is_active: boolean;
total_jobs_completed: number;
average_rating: number;
profile_image_url?: string;
created_at: string;
updated_at: string;
}

Design System Requirements

- Primary color: #2E8B57 (Sea Green) for trust indicators
- Secondary color: #FF8C00 (Dark Orange) for urgency
- Support all 8 Indian languages with proper font rendering
- Implement 8px grid system for consistent spacing
- Ensure all interactive elements have minimum 44px touch targets
- Maintain accessibility standards (WCAG 2.1 AA)

Constraints

- DO NOT implement payment systems (MVP phase)
- DO NOT include offline data synchronization (online-only architecture)
- All components must work on 2G/3G networks
- App must support devices with 2GB RAM
- Implement only for mobile (no tablet/desktop optimization required for MVP)
- Only use Supabase auto-generated APIs and specified Edge Functions

Define a Strict Scope

Files to Create

- src/components/ (all reusable UI components)
- src/screens/ (all screen components)
- src/navigation/ (navigation configuration)
- src/services/ (API and business logic)
- src/stores/ (Zustand state stores)
- src/utils/ (helper functions)
- src/types/ (TypeScript definitions)
- src/assets/ (static assets, translations)
- App.tsx (main application component)

Files to NOT Modify

- package.json (unless adding missing dependencies)
- app.json (Expo configuration)
- babel.config.js
- metro.config.js
- tsconfig.json
- Any native iOS/Android files

Visual and Brand Guidelines

Color Palette

- Primary: #2E8B57 (Sea Green) - Trust actions, verification badges
- Secondary: #FF8C00 (Dark Orange) - Urgency indicators, job alerts
- Accent: #4169E1 (Royal Blue) - Information hierarchy, links
- Success: #228B22 (Forest Green) - Completed jobs, successful payments
- Warning: #FFD700 (Gold) - Pending verifications, skill certifications
- Error: #DC143C (Crimson) - Failed actions, urgent problems

Typography

- Primary font: Noto Sans (supports all 8 Indian languages)
- Secondary font: Inter (for English-heavy screens)
- Monospace: JetBrains Mono (for numeric data)
- Type scale:
- H1: 28px, bold
- H2: 24px, semi-bold
- H3: 20px, semi-bold
- Body: 16px, regular
- Small: 14px, regular

Accessibility Requirements

- Minimum 4.5:1 color contrast ratios
- Support screen readers (VoiceOver, TalkBack)
- All images with descriptive alt text
- Proper heading hierarchy (H1-H6)
- Keyboard navigation support

Performance Considerations

Network Resilience

- Implement retry mechanisms for failed requests
- Cache last 50 viewed jobs for offline browsing
- Show data usage indicators for actions
- Provide clear feedback during network failures

Memory Management

- Lazy loading for job cards
- Image recycling for job listings
- Release unused images when app goes to background
- Batch state updates to minimize re-renders

Battery Optimization

- Request location only when needed
- Use native driver for animations
- Minimize background tasks
- Batch non-urgent notifications

IMPORTANT: This prompt should generate only the frontend mobile application code. Do not create backend services, database schemas, or deployment configurations. All AI-generated code will require
careful human review, testing, and refinement to be considered production-ready.
