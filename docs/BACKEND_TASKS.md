## Doc 2: Backend Task Plan (ordered, screen-mapped, acceptance criteria)

### How to use
- Each task maps to specific screens, API endpoints, DB entities, and specifies previous/next screen context based on the UI flow and checklists.   
- Execute in order; a task may iterate on the same screen until acceptance criteria are met, then proceed to “Next Screen.”   
- Keep SCREEN_CHECKLIST.md updated and tick backend-complete alongside frontend-complete per screen.   

***

### Phase 1: Auth + Profiles (Week 1)

1) Task BE-01: OTP Auth and Session
- Screens: A2 Phone Registration, A3 OTP Verification, A4 Success.   
- Endpoints: POST /api/auth/send-otp, POST /api/auth/verify-otp, POST /api/auth/resend-otp, GET /api/auth/me, POST /api/auth/refresh-token, POST /api/auth/logout.  
- Tables: otp_requests, user_profiles.    
- Previous Screen: A1 Welcome. Next Screen: A5 Mode Selection.  
- Acceptance: Valid +91 numbers, max attempts/rate limit enforced, new users created in auth + user_profiles with phoneVerified=true, JWT issued and /me returns user.    

2) Task BE-02: Mode Selection persistence
- Screens: A5 Mode Selection.   
- Endpoints: none (client-local), but user_settings.language and preferences initialized.  
- Tables: user_settings.    
- Previous: A3/A4 OTP. Next: A6 Worker Profile or A10 Poster Profile.  
- Acceptance: Settings row created with defaults; language set per UI selection.    

3) Task BE-03: Worker Profile + Personas v1
- Screens: A6 Worker Basic Profile, A7 Persona Intro, A8 Persona Create, A9 Worker Home First-Run.   
- Endpoints: POST /api/users/worker-profile, GET /api/skills/categories, GET /api/skills/market-rates, POST /api/users/skills/personas.  
- Tables: worker_profiles, worker_personas, job_categories, market_rates, uploads (profile avatar).    
- Previous: A5 Mode Selection. Next: B1 Worker Home.  
- Acceptance: Profile upserted; persona created; rate guidance returned; avatar upload stored to uploads and URL saved.    

4) Task BE-04: Poster Profile v1
- Screens: A10 Poster Basic Profile, A11 Poster Home First-Run.   
- Endpoints: POST /api/users/poster-profile.  
- Tables: poster_profiles, uploads (avatar).    
- Previous: A5 Mode Selection. Next: C1 Poster Home.  
- Acceptance: Poster profile created; green theme switch supported by mode; defaults seeded.   


### Phase 2: Jobs Core (Week 2)

5) Task BE-05: Worker Home Recommendations
- Screens: B1 Worker Home.   
- Endpoints: GET /api/jobs/recommendations, GET /api/users/dashboard-stats.  
- Tables: jobs, job_categories, worker_profiles, worker_personas.    
- Previous: A9 First-Run. Next: B2 Job Details.  
- Acceptance: Returns featured/nearby/skillMatch buckets with pagination; dashboard stats filled.  

6) Task BE-06: Job Search + Filters
- Screens: Global Search, B1/B2 integration.   
- Endpoints: GET /api/jobs/search, GET /api/jobs/filters.  
- Tables: jobs, job_categories, skills.    
- Previous: B1 Worker Home. Next: B2 Job Details.  
- Acceptance: Keyword/category/rate/distance sorting, counts for filter facets, hasMore pagination.  

7) Task BE-07: Job Details + Bookmark
- Screens: B2 Job Details.   
- Endpoints: GET /api/jobs/{id}, POST /api/jobs/{id}/bookmark.  
- Tables: jobs, job_photos, applications (status for current user), and a bookmarks store (table job_bookmarks).    
- Previous: B1/B1 list. Next: B3 Apply Drawer.  
- Acceptance: Poster summary + relatedJobs; canApply logic; bookmark toggles per user.  

1) Task BE-08: Apply Flow + Limits
- Screens: B3 Apply Drawer, B4 Applications Tab.   
- Endpoints: POST /api/jobs/{id}/apply, GET /api/users/personas, GET /api/users/application-limits, GET /api/applications/my-applications, PUT /api/applications/{id}/withdraw.  
- Tables: job_applications, worker_personas, jobs (counter trigger).    
- Previous: B2 Details. Next: B5 Job Status.  
- Acceptance: Persona select, counter-offer stored, daily limit enforced, application list with summary.  

1) Task BE-09: Poster Post Job Wizard
- Screens: C2 Job Post Wizard + Preview + Publish.   
- Endpoints: POST /api/jobs, GET /api/jobs/categories, GET /api/jobs/title-suggestions.  
- Tables: jobs, job_categories, uploads (job photos).    
- Previous: C1 Poster Home. Next: C3 My Jobs.  
- Acceptance: Validations per title/description/amount; preview data matches worker view fields exactly.  

1)  Task BE-10: Poster My Jobs + Applicants
- Screens: C3 My Jobs, C4 Job Details & Applicants.   
- Endpoints: GET /api/jobs/{id}/applicants, and list own jobs (GET /api/jobs/active?userType=poster or a dedicated /api/jobs/my-jobs).  
- Tables: jobs, job_applications.    
- Previous: C2 Publish. Next: C5 Worker Profile, C7 Chat, Accept/Reject.  
- Acceptance: Sort by match score/time/rating; accept/reject/shortlist state flows.  


### Phase 3: Messaging + Notifications (Week 3)

11) Task BE-11: Conversations + Messages
- Screens: Messages List, B6/C7 Chat.   
- Endpoints: GET /api/messages/conversations, GET /api/messages/conversations/{id}, POST /api/messages/conversations/{id}/messages, PUT /api/messages/{messageId}/read.  
- Tables: conversations, messages, message_attachments.    
- Previous: B5/C4 Status/Applicants. Next: B5/C8 Status deep link.  
- Acceptance: Thread pagination, delivery/read state, 500-char limit enforced server-side.  

12) Task BE-12: Notifications Center
- Screens: Notifications Center.   
- Endpoints: GET /api/notifications, PUT /api/notifications/{id}/read, PUT /api/notifications/mark-all-read, DELETE /api/notifications/{id}.  
- Tables: notifications, notification_devices (push registration).    
- Previous: Any screen. Next: Deep-link target (job/app/chat/payment).  
- Acceptance: Types filtering, unread counts, mark-all, registration endpoint for device tokens.  


### Phase 4: Execution, Proof, Payments, Reviews (Week 4)

13) Task BE-13: Job Lifecycle + Status Timeline
- Screens: B5 Job Status (Worker), C8 Job Status (Poster).   
- Endpoints: GET /api/jobs/{jobId}/status, POST /api/jobs/{jobId}/start-work, POST /api/jobs/{jobId}/update-progress, POST /api/jobs/{jobId}/complete.  
- Tables: jobs (started/completed, progress JSON via timeline if desired), job_photos (work progress).    
- Previous: B4 Applications accepted / C4 Accept. Next: B7 Photo Proof, C8 Approve Photo.  
- Acceptance: Timeline events emitted, phase/progress updated, status transitions validated.  

14) Task BE-14: Photo Proof + Uploads
- Screens: B7 Photo Proof (Worker), C8 Photo Approval (Poster).   
- Endpoints: POST /api/upload/images, approval in status endpoint flow.  
- Tables: uploads, job_photos (work_photos linkage).    
- Previous: B5 Started. Next: C9/B8 Payments.  
- Acceptance: Image constraints (size/types), thumbnails optional, metadata saved, approval/rejection reasons persisted.  

15) Task BE-15: Payment Confirmation + History + Earnings
- Screens: B8 Worker Payment Confirmation, C9 Poster Payment Confirmation, C11 Payments History, B10 Earnings.   
- Endpoints: GET /api/payments/history, GET /api/payments/earnings, GET /api/payments/export, POST /api/payments/process (future), job close on dual confirmation.  
- Tables: payments, jobs (payment_status), user_profiles (totals via denormalized counters optional).    
- Previous: C8 Approval. Next: B9/C10 Ratings.  
- Acceptance: Dual-confirm state persisted; mismatch banner resolved path; export returns URL.  

16) Task BE-16: Ratings & Reviews (both sides)
- Screens: B9 Ratings To Give (Worker), C10 Ratings To Give (Poster), Ratings Received.   
- Endpoints: GET /api/reviews/received, POST /api/reviews.  
- Tables: reviews.    
- Previous: Payments confirmed. Next: Completed lists.  
- Acceptance: One rating per pair per job, optional categories, average distribution computed.  


### Phase 5: Settings, Help, Advanced + Hardening (Week 5)

17) Task BE-17: Settings, Privacy, Language
- Screens: Profile & Settings.   
- Endpoints: GET/PUT /api/users/settings, GET/PUT /api/users/profile, POST /api/users/upload-avatar.  
- Tables: user_settings, user_profiles, uploads.    
- Previous: Any. Next: Return to previous.  
- Acceptance: All toggles persisted; privacy flags honored via RLS; language saved.  

18) Task BE-18: Help & Support
- Screens: Help & Support.   
- Endpoints: GET /api/support/faq, POST /api/support/tickets, GET /api/support/contact-info.  
- Tables: none required (proxy/service) or a tickets table if internal; optional.    
- Previous: Settings. Next: Back.  
- Acceptance: FAQs grouped by category; ticket created with SLA fields.  

19) Task BE-19: Poster Worker Discovery + Invite
- Screens: C1 Poster Home (Nearby Workers), C5 Worker Profile (Poster), C6 Invite Worker.   
- Endpoints: GET /api/poster/dashboard, GET /api/users/workers/search (if present), invite via job_invites or assign-worker.  
- Tables: worker_personas, worker_profiles, job_invites.    
- Previous: C1. Next: C4/C7/C8.  
- Acceptance: Filters by skill/rate/distance/availability; invite state pending→accepted/declined.  

20) Task BE-20: Assign/Hire Worker
- Screens: C4 Applicants actions, C5 Hire/Invite acceptance.   
- Endpoints: /api/jobs/{id}/assign-worker or equivalent accept action, updates job.assignedWorkerId and status.  
- Tables: jobs, job_applications, job_invites.    
- Previous: C4. Next: C8 Status.  
- Acceptance: Idempotent assignment; prevents over-assign; notifies worker.  

21) Task BE-21: Notifications: Push + WebSocket Events
- Screens: Notification bell counts and real-time chat/status.   
- Endpoints: POST /api/notifications/register-device, ws://... event shapes in doc.  
- Tables: notification_devices, notifications.    
- Previous: BE-11/12. Next: Ongoing.  
- Acceptance: Device tokens stored; events emitted on application/job status/message/payment.  

22) Task BE-22: Data Security, RLS, Rate Limits
- Screens: All (security layer).   
- Endpoints: N/A; enforce via DB RLS and API throttling per Security section.  
- Tables: otp_requests (rate audit), policies across all tables.    
- Previous: Core endpoints. Next: Continuous.  
- Acceptance: RLS policies pass integration tests; OTP endpoints respect limits; audit logs present.  

23) Task BE-23: Performance + Indexes + Caching
- Screens: All with lists.   
- Endpoints: N/A; improves latency.  
- Tables/Indexes: As per implementation (jobs status+created index, messages conv+ts, notifications unread, trigram on location).  
- Previous: Feature complete. Next: Launch readiness.  
- Acceptance: P95 response times within targets; slow queries use proper indexes; cache keys defined.  

24) Task BE-24: Exports + CSVs
- Screens: B10 Earnings export, C11 Payments export.   
- Endpoints: GET /api/payments/export.  
- Tables: payments.    
- Previous: BE-15. Next: Done.  
- Acceptance: Signed URL generated; expiresAt set; CSV schema matches UI columns.  


### Implementation guidance and sequencing

- Execute Phase 1→5 sequentially; unblock frontend flows earliest (Auth → Profile → Job discovery) before engagement and monetization layers.  
- For each task, create sub-tasks: DB migrations (supabase.sql slice), API handlers, RLS test, Integration test, Documentation snippet to BACKEND_API_REQUIREMENTS.    
- Keep SCREEN_CHECKLIST.md updated with “Backend Complete” next to each screen as endpoints go live for that screen.   


### Cross-check coverage to 54 screens

- Onboarding (A1–A11): BE-01..04 map to all onboarding screens and mode/profile setup.    
- Worker (B1–B10): BE-05..08, BE-11, BE-13..16 fully cover feed→apply→status→proof→payment→earnings→ratings.    
- Poster (C1–C11): BE-09..15, BE-19..20 cover post→applicants→assign→status→payment→history→ratings.    
- Shared: BE-11..12 (messages/notifications), BE-17 (settings), BE-18 (support), BE-21 (push), BE-22..23 (security/perf).    
