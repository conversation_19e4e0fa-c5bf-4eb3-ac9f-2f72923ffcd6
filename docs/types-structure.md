Key Type Management Instructions Added

  Each task now includes specific instructions for:
   - Creating API request/response types in packages/types/src/server/{feature}/
   - Creating database schema types in packages/types/src/server/database/
   - Following the naming convention I{TypeName} for interfaces
   - Exporting all new types from packages/types/src/server/index.ts
   - Using existing types from @ozgaar/types where applicable

  Folder Structure for Types

  The tasks now specify organizing types in the following structure:
   - packages/types/src/server/auth/ - Authentication types
   - packages/types/src/server/user/ - User-related types
   - packages/types/src/server/jobs/ - Job-related types
   - packages/types/src/server/worker/ - Worker persona types
   - packages/types/src/server/poster/ - Poster profile types
   - packages/types/src/server/applications/ - Job application types
   - packages/types/src/server/messages/ - Messaging types
   - packages/types/src/server/notifications/ - Notification types
   - packages/types/src/server/payments/ - Payment types
   - packages/types/src/server/reviews/ - Review types
   - packages/types/src/server/support/ - Support types
   - packages/types/src/server/discovery/ - Discovery types
   - packages/types/src/server/uploads/ - Upload types
   - packages/types/src/server/exports/ - Export types
   - packages/types/src/server/security/ - Security types
   - packages/types/src/server/performance/ - Performance types
   - packages/types/src/server/database/ - Database schema types