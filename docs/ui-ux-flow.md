# Android App UI/UX Specification: Worker–Poster Marketplace (India-first)

This document defines a complete screen system, navigation model, and UX flows for an Android app enabling users to work and hire. It translates the PRD user stories into a cohesive, production-ready UI/UX plan with clear information architecture, states, and transitions. It emphasizes clarity, speed, and Indian context (low literacy, dual-language, location-first, trust-forward).

Note: This focuses on UX and screen design details, not implementation.

***

## Design Principles

- Verify first, profile fast: Minimize friction while ensuring trust.
- One brain, two modes: Users can be both worker and poster; keep mode switching effortless and obvious.
- Persona-first for workers: Discovery, applying, and reputation tied to selected persona.
- Local and timely: Location prominence, recentness, urgency, and availability drive decisions.
- Essential-only notifications: Actionable and time-bounded.
- Familiar patterns: WhatsApp-like chat, Netflix-like persona cards, Zomato/Swiggy-like feed density.
- Bilingual clarity: English + Hindi labels where critical; numeric focus for rates and time.
- Accessibility: Large tappable areas, 44dp minimum targets, color + icon + text redundancy.

***

## Global Navigation & Structure

- Primary navigation: Bottom tab bar (5 tabs)
  - Home (mode-aware)
  - Messages
  - Jobs (My Jobs)
  - Earnings (for Workers) / Payments (for Posters)
  - Profile
- Global elements:
  - Mode Switcher: Persistent pill in top app bar: Worker | Poster, with color scheme shift (Worker=Blue, Poster=Green).
  - Persona Switcher (Worker only): Inline in top app bar under mode switch, shows current persona chip; tap to switch.
  - Notification bell (top-right) with badge; deep-links to relevant items.
- Color semantics:
  - Urgent = Red accents
  - Normal = Blue accents
  - Status: Applied=Grey, Accepted=Blue, Started=Orange, Completed=Green

***

## Onboarding & Access Flow

### 1. Welcome Screen
- Purpose: Introduce value, quick entry to verification.
- Elements:
  - App logo, tagline: “Find work. Hire fast. Nearby.”
  - Call-to-action: “Get Started”
  - Secondary: “Continue in हिंदी”
  - Link: Terms & Privacy
- Actions:
  - Tapping primary moves to Phone Registration.

### 2. Phone Registration (US-001)
- Layout:
  - Phone input with +91 locked, 10-digit mask with 98765-43210 formatting
  - Real-time validation and hints (English/Hindi based on area code)
  - “Send OTP” button
  - Microcopy: “No charges. Used to verify real people.”
- Edge states:
  - Error for invalid format, retries with countdown, cooldown messaging, SMS failure fallback (“Try different number”)
- Next: OTP Entry

### 3. OTP Verification
- Layout:
  - 6-digit OTP boxes with auto-advance
  - Countdown: “Resend in 0:45”
  - “Verify” button enabled when complete
  - Device-SIM auto-detect confirmation banner: “We auto-verified your phone”
- Success: Celebration animation, green check + “Verified”
- Fail/Retry: Clear error, attempt counter

### 4. Mode Introduction (First-Time)
- Purpose: Choose initial mode; changeable later.
- Screen: Two large cards
  - Worker: “Find jobs near you” (icon: hard hat)
  - Poster: “Hire trusted workers” (icon: briefcase)
- Microcopy: “You can switch anytime”
- Action: Pick one; navigates to the respective onboarding.

***

## Worker Mode Flows

### 5. Worker Initial Profile (US-002)
- Screen: “Tell us about you”
- Sections:
  - Photo (circle crop + auto-crop)
  - Full name
  - Contact number (pre-filled, visibility toggle public/private)
  - Primary skill category (single-select)
  - Optional: Experience (years), Rate (hourly/daily), short bio (<=100 words)
- Helpers:
  - Market rate guidance based on skill + location (“₹500–800/day typical for electricians in Delhi”)
  - Profile completeness meter with steps
- Actions:
  - Save & Continue to Persona Setup

### 6. Persona Creation: First Persona (US-002.1)
- Screen: “Create your first skill persona”
- Cards:
  - Skill category (pre-selected from prior screen, changeable)
  - Years of experience
  - Rate (daily or hourly), with suggestions
  - Availability toggle (Available/Busy)
  - Portfolio (add up to 3 photos—optional)
- Info:
  - Explanation card: “Personas help you apply with the right skills and rates.”
- CTA:
  - Create Persona
- Result:
  - Sets a default active persona; goes to Worker Home.

### 7. Worker Home: Jobs Feed (US-004)
- App bar:
  - Mode Switcher (Worker)
  - Persona Chip (current persona), tap to change
  - Location (current city), tap to adjust radius
- Content:
  - Feed within 25km radius; cards show:
    - Title, Rate, Distance, Posted time, Urgency badge
  - Filter strip:
    - My Skills / All Categories, Urgency filter, Radius, Sort (Distance / Newest)
  - Pull-to-refresh; infinite scroll
  - Empty states with tips (expand radius, add skills, toggle availability)
- Interactions:
  - Tap Job Card → Job Details
  - Apply Now (one-tap) from card or details (US-005)
  - Saved Jobs icon (bookmark)

### 8. Job Details (Worker)
- Header:
  - Title, Poster Rating, Posted time, Urgency
- Body:
  - Rate format (Fixed/Hourly/Daily)
  - Location map preview
  - Duration estimate (Hours/Days/Weeks)
  - Description, Requirements
  - Budget low-warning (if below market)
- Persona section:
  - Auto-suggested persona; dropdown to switch persona
  - Shows persona’s rate, availability, reviews snippet
- Actions:
  - Apply Now (primary)
  - Share, Save Job
- Post-apply:
  - Success toast + checkmark animation
  - Button disabled; status tag “Applied”

### 9. Apply Flow (Inline Drawer)
- Elements:
  - Selected Persona card
  - Auto-message (editable short message)
  - Availability confirmation
  - Rate mismatch warning if any
- CTA: Send Application
- Edge:
  - Daily limit reached → Info with reset time

### 10. Messages (US-006)
- List:
  - Active conversations (job title subtitle), badges for unread
  - Search conversations
- Chat thread:
  - WhatsApp-style bubbles
  - Typing indicator, read receipts
  - Message limit: 500 chars with counter
  - Quick replies: On my way, Running 15 min late, Work completed
  - “Share Contact” prominent button
  - Profanity filtered with asterisks
- Header:
  - Job link chip (tap → Job status view)
- Edge:
  - Block/Report in overflow menu

### 11. My Jobs (US-007)
- Tabs:
  - Applications (Sent/View/Shortlisted/Rejected)
  - Active (Accepted/Started)
  - Completed
- Each item:
  - Status badge with color
  - Timeline indicator (1/4, 2/4...)
- Job status details:
  - Progress bar (Applied → Accepted → Started → Completed)
  - Next action prompts (“Waiting for poster to confirm start”)
  - Timestamps, overdue alerts
  - CTA buttons for status changes with confirmation dialogs
- Edge:
  - Dispute lock state visuals

### 12. Work Completion: Photo Proof (US-008)
- Entry:
  - From Active job → Mark Work Completed → Launch Camera
- Camera:
  - Full-screen, clear instruction: “Show completed work clearly”
  - Timestamp overlay preview; GPS capture indicator
  - Retake option; file size indicator; quality check feedback
- Upload:
  - Pending upload banner for low network
- Poster review state:
  - Awaiting approval; rejection reasons displayed with tips

### 13. Payments & Earnings (Worker) (US-009, US-010)
- Payments tab:
  - Pending confirmations: nudges
  - Confirm “Payment Received” with amount and method (Cash/UPI/Bank)
  - Discrepancy state UI if mismatch
- Earnings tab:
  - Summary cards: Today / Week / Month totals
  - Filters: Last 7/30/90 days, All time
  - Breakdown by skill/persona
  - Average per job
  - Pending payments list
  - Export CSV CTA
  - Monthly goal progress bar
- Payment detail:
  - Receipt view with timestamp, method, notes
  - Dispute status if any

### 14. Ratings (Worker) (US-011)
- Ratings Received:
  - Overall average (1 decimal), total count
  - Breakdown bars (5★, 4★...)
  - Recent first; short text reviews
- Ratings To Give:
  - List of completed jobs awaiting ratings
  - 1–5 star component with labels + optional text (200 chars)
  - Reminder timers
- Persona filter:
  - View ratings per persona

### 15. Persona Management (Netflix-like) (US-002.1)
- Entry: Profile > Personas
- Grid of persona cards:
  - Card: Photo, Skill, Rate, Status (Active/Inactive), Rating chip
  - Add Persona card (+)
- Add/Edit Persona flow:
  - Wizard:
    - Skill selection
    - Experience
    - Rate (with guidance)
    - Availability schedule (simple weekly slots)
    - Portfolio upload
  - Duplicate-skill prevention message
- Actions:
  - Activate/Deactivate toggles
  - Set Default Persona action
- Conflict helpers:
  - Availability conflict detector tips

### 16. Worker Profile & Settings
- Profile overview:
  - Photo, Name, Verified badge, Primary contact
  - Availability toggle
  - Profile completeness meter
  - Summary stats: Jobs completed, Avg rating, Acceptance rate
- Sections:
  - Edit Profile (basic fields, photo)
  - Personas (manage)
  - Saved Jobs
  - Address & Location preferences (radius default)
  - Language (English/Hindi)
  - Notifications preferences (types)
  - Privacy (phone number visibility)
  - Help & Support
  - Legal (Terms, Privacy)
  - Logout
- Mode switch:
  - Persistent at top; also in overflow for redundancy

***

## Poster Mode Flows

### 17. Poster Initial Profile (US-003)
- If first-time poster:
  - Name, Business name (optional), Phone visibility
  - Preferred categories to hire (optional)
  - Payment method preference (for analytics only)
- CTA: Continue to Post a Job or Explore Workers

### 18. Poster Home: Nearby Workers Feed
- App bar:
  - Mode Switcher (Poster) with green theme
  - Location chip
  - CTA button on app bar: “Post a Job”
- Content tabs:
  - Nearby Workers (default)
  - My Jobs (posted)
- Worker feed cards:
  - Worker name, Persona skill, Rate, Distance, Rating -  Reviews count
  - Availability status
  - View Profile, Invite to Job CTA
- Filters:
  - Skill, Rate range, Distance, Availability, Rating

### 19. Worker Profile (as seen by Poster)
- Header:
  - Photo, Name, Verified badge, Distance, Availability
- Persona tabs (if multiple):
  - Skill-specific rates, experience, portfolio
- Ratings & Reviews
- Actions:
  - Invite to Job (select existing job or create new)
  - Message (creates connection only after invite accepted or if existing job)

### 20. Post a Job (US-003)
- Wizard (3 steps):
  1) Basics: Title, Description (counter), Category
  2) Pay & Time: Fixed/Hourly/Daily, Amount, Duration (Hours/Days/Weeks)
  3) Location & Visibility: Auto-location with map, Urgency toggle (premium), Preview
- Helpers:
  - Title suggestions based on category
  - Budget too low warnings
  - Duplicate job detection with option to edit existing
- Preview:
  - Exact worker-facing card preview
- Publish:
  - Confirmation screen with performance tracking chips: Views, Applications, Time to fill

### 21. Job Applicants & Management (Poster)
- Job Details:
  - Status: Open / Filled / Expired
  - Metrics: Views, Applications
  - Applicant list:
    - Cards with Persona info, Rate, Availability, Rating, Short message
    - Sort by match score / time / rating
  - Actions:
    - Accept, Reject, Shortlist
    - Message applicant (opens chat)
- Job actions:
  - Edit job, Renew, Close job
- Edge states:
  - No applications: optimization tips

### 22. Poster Messages (US-006)
- Same chat UI with poster context
- Quick replies tailored: “Can you start today?”, “Share location pin?”
- Share contact prominent

### 23. Poster My Jobs (US-007)
- Tabs:
  - Open, In Progress, Completed
- Each job item:
  - Status progress (Applied → Accepted → Started → Completed)
  - Overdue alerts
- Job status actions:
  - Confirm Started, Confirm Completed
- Dispute handling:
  - Lock state with support info

### 24. Payment Confirmation (Poster) (US-009)
- For each completed job:
  - Confirm “Payment Made” with amount & method
  - Add note (50 chars)
  - Reminders for pending confirmations
  - Amount mismatch flow with resolution prompt

### 25. Payments & Reports (Poster)
- History list:
  - Date, Worker, Job Title, Amount, Method
- Filters:
  - Time ranges
- Export CSV
- Dispute markers visible

### 26. Ratings (Poster) (US-011)
- To Rate:
  - List of recently completed jobs with CTA to rate workers
- Received:
  - Ratings from workers on poster (trust indicator)
- Breakdown and recent reviews

### 27. Poster Profile & Settings
- Business profile fields
- Hiring preferences (categories, typical rates)
- Notification settings (application updates, chat, disputes)
- Language, Help, Legal, Logout
- Mode switch

***

## Cross-Mode & Shared Screens

### 28. Mode Switcher Overlay (US-003.1)
- Tap mode pill → bottom sheet:
  - Worker (current) / Poster
  - Description beneath each: “Find jobs” / “Hire workers”
- If prerequisites missing:
  - Inline prompt to complete missing profile with “Complete Now”

### 29. Persona Selector Overlay (US-003.2)
- Worker apply: Bottom sheet with persona cards (image, skill, rate, availability)
- Auto-suggest match: Pre-selected with “Best match 92%” tag
- Quick switch without leaving apply flow

### 30. Notifications Center (US-012)
- Sections:
  - Applications
  - Job Status
  - Messages
  - Payments
  - Account
- Smart timing noted in header (“Quiet hours 9 PM–7 AM”)
- Grouping by job
- Tap-through to specific destination

### 31. Search (Global)
- Worker: Search jobs by title/skill/locality
- Poster: Search workers by skill/area
- Recent searches and quick chips

### 32. Error, Offline, and Empty States
- Offline mode:
  - Banner: “Working offline. Some actions will send later.”
  - Cached jobs/messages visually flagged
- Permission denials with rationale:
  - Location, Camera, Notifications
- Empty lists:
  - Clear next steps and shortcuts

***

## Information Architecture (Sitemaps)

Worker Mode
- Home
  - Job Feed
  - Job Details
  - Apply Drawer (Persona Selector)
- Messages
  - Threads
  - Chat
- My Jobs
  - Applications
  - Active (Status)
  - Completed
- Earnings
  - Summary
  - History
  - Payment Detail (Receipt)
- Profile
  - Edit Profile
  - Personas (Grid, Add/Edit)
  - Saved Jobs
  - Preferences (Language, Notifications, Privacy)
  - Help & Legal

Poster Mode
- Home
  - Nearby Workers
  - Worker Profile
  - Post a Job (Wizard, Preview)
- Messages
  - Threads
  - Chat
- My Jobs
  - Open
  - In Progress (Status)
  - Completed
- Payments
  - Pending Confirmations
  - History
- Profile
  - Business Profile
  - Preferences
  - Help & Legal

Shared
- Notifications Center
- Mode Switcher
- Search
- Error/Offline/Empty States

***

## Key Micro-interactions & Feedback

- Apply Now: Optimistic update → success toast → counter decrement (“3 of 10 today”)
- Status Change: Confirmation dialog with irreversible note; progress bar animates to next stage
- Photo Proof: Real-time GPS/timestamp validation indicators; retake loop with quality tip
- Payment Confirmations: Dual confirmation visual—shows who confirmed and who’s pending
- Ratings: Star hover animation; confetti for 5★ received
- Mode switch: Color scheme crossfade; home feed content swaps swiftly; state restoration on return

***

## Edge Cases & Safety

- Incomplete required fields: Inline blockers with “Complete to continue” CTA
- Duplicate applications: Disabled Apply button with status label
- High-demand queue: Inform expected delay before post visibility
- Disputes: Lock status with clear next steps; disable conflicting actions
- Abuse controls: Block/report in chat and profile; profanity filter; rate-limit messaging

***

## Content & Localization

- Dual-language critical strings (English/Hindi); switch in Settings
- Numeric prominence for rates/distance/time
- IST timestamps everywhere; relative time in feeds (“2 hours ago”)
- Indian currency format (₹ 1,500), separators, daily/hourly rates

***

## Accessibility

- Minimum 16sp body, 18–20sp headers
- 44dp touch targets, 8dp spacing rhythm
- Color + icon + text for status
- VoiceOver labels for all controls (“Apply Now, sends your electrician persona”)

***

## Success Metrics Tied to UI

- Onboarding completion time and drop-offs (Phone → Mode → Profile → Persona)
- Worker Apply conversion rate per persona
- Poster job fill time; view-to-application ratio
- Chat engagement after acceptance
- Photo proof approval rate on first attempt
- Dual confirmation completion time for payments
- Ratings completion within 24h
- Notification engagement rates by type and timing

***

## Example User Journeys

1) New Worker to First Application
- Welcome → Phone → Mode Select (Worker) → Worker Profile → First Persona → Home Feed → Job Details → Apply → My Jobs (Applications) → Messages when accepted → Start Job → Photo Proof → Payment Received → Rate Poster

2) New Poster to First Hire
- Welcome → Phone → Mode Select (Poster) → Poster Profile → Post a Job → Applicants → Accept → Messages → Confirm Start → Confirm Completion → Payment Made → Rate Worker

3) Multi-skilled Worker Adds Persona
- Profile → Personas → Add Persona (Plumber) → Set rate & availability → Home Feed switches recommendations → Apply with selected persona

***

## Screen Inventory (Checklist)

- Welcome
- Phone Registration
- OTP Verification
- Mode Introduction
- Worker Profile (basic)
- Persona Create (wizard)
- Worker Home (Jobs Feed)
- Job Details
- Apply Drawer + Persona Selector
- Messages List
- Chat Thread
- My Jobs (Applications/Active/Completed)
- Job Status Detail
- Photo Proof Capture
- Payment Confirmation (Worker)
- Earnings Summary & History
- Payment Receipt
- Ratings Received
- Ratings To Give
- Persona Management (grid)
- Persona Add/Edit
- Worker Profile & Settings
- Poster Profile (basic)
- Poster Home (Nearby Workers)
- Worker Profile (poster view)
- Post a Job (wizard + preview)
- Job Applicants & Management
- Poster Messages
- Poster My Jobs (status)
- Payment Confirmation (Poster)
- Payments History (Poster)
- Ratings (Poster: to give/received)
- Poster Profile & Settings
- Notifications Center
- Global Search
- Permission Education Screens
- Offline/Empty/Error Screens

***

## Visual Language Summary

- Worker theme: Blue primary, calm neutrals
- Poster theme: Green primary, confident accents
- Urgency red accents; status palette aligned to lifecycle
- Card-based density with strong typographic hierarchy
- Familiar bottom nav; sticky action CTAs where critical
- Icons: Material Design baseline with clear metaphors

***

If wireframes or interactive prototypes are needed next, this specification can be translated into low-fidelity flows per section, starting with Onboarding, Worker Home & Apply, and Poster Post-a-Job & Applicants, then iterating on edge-case screens (disputes, offline, mismatches).

[1](https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/attachments/82055977/946699ea-0453-4c8f-8c37-d4aa6d7e2993/prd-continued.md)