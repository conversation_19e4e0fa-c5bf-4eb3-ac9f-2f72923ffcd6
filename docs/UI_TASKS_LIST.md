## Task Breakdown

### **TASK 1: Project Setup & Global Components**
**Priority:** Critical  
**Estimated Time:** 2-3 hours  
**Screens Covered:** Bottom Navigation, Top App Bar, Global Theme  

**Scope:**
- Set up Android project structure with Material Design 3
- Implement global theme system (Worker=Blue, Poster=Green)
- Create bottom navigation with 5 tabs: Home, Messages, My Jobs, Earnings/Payments, Profile
- Create top app bar with mode switcher pill and notification bell
- Implement basic navigation between tabs

**Previous Screen:** N/A (Project start)  
**Next Screen:** A1. Welcome Screen  
**Implementation Notes:**
- Use Jetpack Compose for UI
- Set up ViewModel architecture
- Create color schemes for Worker/Poster modes
- Implement 44dp minimum touch targets
- Add accessibility labels

**Completion Criteria:**
- [ ] Project compiles and runs
- [ ] Bottom navigation switches between tabs
- [ ] Mode switcher pill changes theme colors
- [ ] Notification bell is positioned correctly
- Update SCREEN_CHECKLIST.md: Mark Bottom Navigation Bar, Top App Bar as complete

***

### **TASK 2: Onboarding Flow - Core Screens**
**Priority:** High  
**Estimated Time:** 4-5 hours  
**Screens Covered:** A1-A5 (Welcome → Phone → OTP → Success → Mode Selection)  

**Scope:**
- Welcome screen with logo, tagline, and "Get Started" CTA
- Phone registration with +91 locked, 10-digit validation
- OTP verification with 4-digit auto-advance boxes
- Success screen with animation
- Mode selection with Worker/Poster cards

**Previous Screen:** Bottom Navigation setup  
**Next Screen:** A6/A10 Profile Setup  
**Implementation Notes:**
- Use Indian phone number formatting (98765-43210)
- Implement OTP timer countdown (30 seconds)
- Add success animation (green checkmark)
- Create large card components for mode selection
- Add bilingual support indicators (English/Hindi)

**Completion Criteria:**
- [ ] Welcome screen navigates to phone registration
- [ ] Phone validation works with real-time feedback
- [ ] OTP boxes auto-advance and validate
- [ ] Success animation plays
- [ ] Mode selection cards are tappable and navigate correctly
- Update SCREEN_CHECKLIST.md: Mark A1-A5 as complete

***

### **TASK 3: Worker Onboarding - Profile & Persona Setup**
**Priority:** High  
**Estimated Time:** 3-4 hours  
**Screens Covered:** A6-A9 (Worker Profile → Persona Intro → Persona Wizard → First-Run Home)  

**Scope:**
- Worker basic profile form with photo upload, name, phone, skill selection
- Persona introduction screen with explanation
- 3-step persona creation wizard (Skill → Rates → Portfolio)
- Worker home with coach marks and education overlays

**Previous Screen:** A5. Mode Selection (Worker selected)  
**Next Screen:** B1. Worker Home (operational)  
**Implementation Notes:**
- Create photo upload with circle crop functionality
- Add skill category dropdown with Indian job categories
- Implement market rate guidance based on skill+location
- Create profile completeness meter
- Add coach mark overlays for first-time users
- Store persona data in local state/preferences

**Completion Criteria:**
- [ ] Profile form validates required fields
- [ ] Photo upload and crop works
- [ ] Persona wizard progresses through all steps
- [ ] Coach marks appear on first-run home
- [ ] Data persists between screens
- Update SCREEN_CHECKLIST.md: Mark A6-A9 as complete

***

### **TASK 4: Poster Onboarding - Profile Setup**
**Priority:** High  
**Estimated Time:** 2-3 hours  
**Screens Covered:** A10-A11 (Poster Profile → First-Run Home)  

**Scope:**
- Poster basic profile form (name, business name, categories)
- Poster home with nearby workers feed and coach marks
- "Post a Job" primary CTA in app bar

**Previous Screen:** A5. Mode Selection (Poster selected)  
**Next Screen:** C1. Poster Home (operational)  
**Implementation Notes:**
- Create business profile form
- Implement nearby workers mock data display
- Add coach marks for "Post a Job" button
- Create worker cards with persona, rate, distance, rating
- Switch to green theme for poster mode

**Completion Criteria:**
- [ ] Poster profile form saves data
- [ ] Green theme applies correctly
- [ ] Nearby workers feed displays mock data
- [ ] Coach marks highlight key features
- [ ] "Post a Job" CTA is prominent
- Update SCREEN_CHECKLIST.md: Mark A10-A11 as complete

***

### **TASK 5: Worker Home & Job Discovery**
**Priority:** High  
**Estimated Time:** 4-5 hours  
**Screens Covered:** B1-B2 (Jobs Feed → Job Details)  

**Scope:**
- Jobs feed with filtering, sorting, and search
- Job cards showing title, rate, distance, urgency
- Job details screen with full description, map preview
- Apply button and save functionality

**Previous Screen:** A9. Worker Home First-Run  
**Next Screen:** B3. Apply Drawer  
**Implementation Notes:**
- Create mock job data with Indian job categories
- Implement filter chips (My Skills, Urgency, Radius)
- Add pull-to-refresh and infinite scroll
- Create detailed job view with map preview
- Show persona matching suggestions
- Add distance calculation mock logic
- Implement bookmark/save functionality

**Completion Criteria:**
- [ ] Jobs feed loads and displays cards
- [ ] Filters work and update feed
- [ ] Job details show complete information
- [ ] Apply button is prominent and functional
- [ ] Save job functionality works
- Update SCREEN_CHECKLIST.md: Mark B1-B2 as complete

***

### **TASK 6: Worker Apply Flow & Job Management**
**Priority:** High  
**Estimated Time:** 4-5 hours  
**Screens Covered:** B3-B5 (Apply Drawer → My Jobs → Job Status)  

**Scope:**
- Apply drawer with persona selection and custom message
- My Jobs screen with Applications/Active/Completed tabs
- Job status detail with progress tracking

**Previous Screen:** B2. Job Details  
**Next Screen:** B6. Chat or B7. Photo Proof  
**Implementation Notes:**
- Create bottom sheet apply drawer
- Implement persona switcher in apply flow
- Add daily application limit counter (e.g., "3 of 10 today")
- Create tabbed My Jobs interface
- Add progress bar: Applied → Accepted → Started → Completed
- Show timestamps and next action hints
- Add status badges with appropriate colors

**Completion Criteria:**
- [ ] Apply drawer slides up from bottom
- [ ] Persona selection works within drawer
- [ ] My Jobs tabs switch correctly
- [ ] Job status shows progress visually
- [ ] Status updates trigger next actions
- Update SCREEN_CHECKLIST.md: Mark B3-B5 as complete

***

### **TASK 7: Worker Chat & Communication**
**Priority:** Medium  
**Estimated Time:** 3-4 hours  
**Screens Covered:** B6 (Chat Interface)  

**Scope:**
- WhatsApp-style chat interface
- Quick reply buttons
- Share contact functionality
- Job context header

**Previous Screen:** B5. Job Status Detail  
**Next Screen:** B5. Job Status Detail (return)  
**Implementation Notes:**
- Create chat bubble components
- Implement typing indicators and read receipts
- Add quick replies: "On my way", "Running 15 min late", "Work completed"
- Create job context chip in header
- Add character limit (500 chars) with counter
- Mock message delivery states

**Completion Criteria:**
- [ ] Chat bubbles render correctly
- [ ] Quick replies insert text
- [ ] Job header links back to status
- [ ] Character counter works
- [ ] Share contact button is functional
- Update SCREEN_CHECKLIST.md: Mark B6 as complete

***

### **TASK 8: Worker Work Completion & Payment**
**Priority:** Medium  
**Estimated Time:** 4-5 hours  
**Screens Covered:** B7-B8 (Photo Proof → Payment Confirmation)  

**Scope:**
- Camera interface for work completion proof
- Photo upload with GPS/timestamp indicators
- Payment confirmation with amount and method selection

**Previous Screen:** B5. Job Status Detail ("Mark Work Completed")  
**Next Screen:** B9. Ratings  
**Implementation Notes:**
- Create full-screen camera interface
- Add instruction overlay: "Show completed work clearly"
- Mock GPS/timestamp indicators
- Add photo quality feedback
- Create payment confirmation form
- Handle amount mismatch scenarios
- Add method selection (Cash/UPI/Bank)

**Completion Criteria:**
- [ ] Camera launches full-screen
- [ ] Photo preview shows indicators
- [ ] Upload status is visible
- [ ] Payment form validates input
- [ ] Method selection works
- Update SCREEN_CHECKLIST.md: Mark B7-B8 as complete

***

### **TASK 9: Worker Ratings & Earnings**
**Priority:** Medium  
**Estimated Time:** 3-4 hours  
**Screens Covered:** B9-B10 (Ratings → Earnings Dashboard)  

**Scope:**
- Rating interface with 1-5 stars and comments
- Earnings dashboard with summaries and history
- Export functionality mock

**Previous Screen:** B8. Payment Confirmation  
**Next Screen:** Return to My Jobs or Earnings tab  
**Implementation Notes:**
- Create 5-star rating component with labels
- Add optional comment field (200 chars)
- Create earnings summary cards (Today/Week/Month)
- Add breakdown by persona/skill
- Mock export CSV functionality
- Add monthly goal progress bar

**Completion Criteria:**
- [ ] Star rating component works
- [ ] Comment field has character counter
- [ ] Earnings cards display mock data
- [ ] Breakdown filters work
- [ ] Export button shows success message
- Update SCREEN_CHECKLIST.md: Mark B9-B10 as complete

***

### **TASK 10: Poster Job Creation**
**Priority:** High  
**Estimated Time:** 4-5 hours  
**Screens Covered:** C2 (Job Post Wizard + Preview)  

**Scope:**
- 3-step job posting wizard
- Job preview screen
- Publish confirmation

**Previous Screen:** C1. Poster Home  
**Next Screen:** C3. My Jobs  
**Implementation Notes:**
- Create multi-step wizard with progress indicator
- Step 1: Title, Description, Category
- Step 2: Rate type, Amount, Duration
- Step 3: Location, Urgency toggle
- Add auto-suggestions for job titles
- Create budget warning system
- Show exact worker-facing preview
- Add publish confirmation with metrics placeholder

**Completion Criteria:**
- [ ] All 3 wizard steps work
- [ ] Form validation prevents progression with errors
- [ ] Preview matches worker view exactly
- [ ] Publish creates job successfully
- [ ] Success confirmation shows metrics
- Update SCREEN_CHECKLIST.md: Mark C2 as complete

***

### **TASK 11: Poster Job Management & Applicants**
**Priority:** High  
**Estimated Time:** 5-6 hours  
**Screens Covered:** C3-C4 (My Jobs → Job Details & Applicants)  

**Scope:**
- Posted jobs list with tabs (Open/In Progress/Completed)
- Job applicants management interface
- Accept/Reject/Shortlist functionality

**Previous Screen:** C2. Job Post Wizard or C1. Poster Home  
**Next Screen:** C5. Worker Profile or C7. Chat  
**Implementation Notes:**
- Create tabbed interface for job states
- Show job metrics (views, applications)
- Create applicant cards with worker info
- Add sorting options (match score, time, rating)
- Implement Accept/Reject with confirmation dialogs
- Add optimization tips for jobs with no applicants
- Create match score algorithm mock

**Completion Criteria:**
- [ ] Job tabs switch correctly
- [ ] Applicant cards display worker info
- [ ] Accept/Reject updates job status
- [ ] Sorting changes order
- [ ] No applicants state shows tips
- Update SCREEN_CHECKLIST.md: Mark C3-C4 as complete

***

### **TASK 12: Poster Worker Profiles & Communication**
**Priority:** Medium  
**Estimated Time:** 4-5 hours  
**Screens Covered:** C5-C7 (Worker Profile → Invite Flow → Chat)  

**Scope:**
- Worker profile from poster perspective
- Invite worker to job flow
- Poster chat interface

**Previous Screen:** C4. Job Applicants or C1. Poster Home  
**Next Screen:** C8. Job Status  
**Implementation Notes:**
- Create worker profile view with persona tabs
- Show ratings, reviews, portfolio
- Add "Invite to Job" functionality
- Create job selection for invites
- Implement poster-specific chat quick replies
- Add "Can you start today?" type messages

**Completion Criteria:**
- [ ] Worker profile shows all personas
- [ ] Invite flow selects correct job
- [ ] Chat interface works with poster context
- [ ] Quick replies are poster-appropriate
- [ ] Profile navigation works correctly
- Update SCREEN_CHECKLIST.md: Mark C5-C7 as complete

***

### **TASK 13: Poster Job Status & Payment**
**Priority:** Medium  
**Estimated Time:** 4-5 hours  
**Screens Covered:** C8-C9 (Job Status → Payment Confirmation)  

**Scope:**
- Job progress tracking from poster side
- Photo proof approval/rejection
- Payment confirmation interface

**Previous Screen:** C7. Chat or C4. Job Applicants  
**Next Screen:** C10. Ratings  
**Implementation Notes:**
- Create poster-side status tracking
- Add photo proof review interface
- Implement approve/reject with reasons
- Create payment confirmation form
- Handle amount mismatch with worker
- Add dispute escalation mock

**Completion Criteria:**
- [ ] Status progress bar works
- [ ] Photo proof approval interface functions
- [ ] Payment confirmation validates
- [ ] Mismatch scenarios handled
- [ ] Dispute flow accessible
- Update SCREEN_CHECKLIST.md: Mark C8-C9 as complete

***

### **TASK 14: Poster Ratings & Payment History**
**Priority:** Medium  
**Estimated Time:** 3-4 hours  
**Screens Covered:** C10-C11 (Ratings → Payment History)  

**Scope:**
- Poster rating interface for workers
- Payment history and export functionality

**Previous Screen:** C9. Payment Confirmation  
**Next Screen:** Return to My Jobs  
**Implementation Notes:**
- Create rating form similar to worker version
- Add worker-specific context
- Create payment history list with filters
- Add export functionality
- Show dispute markers where relevant

**Completion Criteria:**
- [ ] Rating submission works
- [ ] Payment history displays correctly
- [ ] Filters work on history
- [ ] Export shows success
- [ ] Dispute markers visible
- Update SCREEN_CHECKLIST.md: Mark C10-C11 as complete

***

### **TASK 15: Shared Components & Navigation**
**Priority:** Medium  
**Estimated Time:** 4-5 hours  
**Screens Covered:** Mode Switcher, Persona Selector, Notifications, Search  

**Scope:**
- Mode switcher overlay/bottom sheet
- Persona selector for workers
- Notifications center
- Global search functionality

**Previous Screen:** Any screen with mode/persona access  
**Next Screen:** Relevant screens based on selection  
**Implementation Notes:**
- Create mode switcher bottom sheet
- Implement theme switching animation
- Create persona selector overlay
- Build notifications center with categories
- Add global search with different contexts (jobs for workers, workers for posters)
- Handle incomplete profile states

**Completion Criteria:**
- [ ] Mode switcher changes theme correctly
- [ ] Persona selector updates current persona
- [ ] Notifications categorize correctly
- [ ] Search returns relevant results
- [ ] Incomplete profile prompts work
- Update SCREEN_CHECKLIST.md: Mark shared components as complete

***

### **TASK 16: Profile & Settings Screens**
**Priority:** Low  
**Estimated Time:** 3-4 hours  
**Screens Covered:** Profile management for both modes  

**Scope:**
- User profile editing
- Settings and preferences
- Help and legal pages

**Previous Screen:** Profile tab from bottom navigation  
**Next Screen:** Updated profile or settings  
**Implementation Notes:**
- Create profile editing forms
- Add photo update functionality
- Implement language switching (English/Hindi indicators)
- Create notification preferences
- Add help and legal placeholder pages
- Handle mode-specific profile differences

**Completion Criteria:**
- [ ] Profile editing saves changes
- [ ] Settings preferences persist
- [ ] Language switching indicates change
- [ ] Help pages accessible
- [ ] Mode-specific content shows correctly
- Update SCREEN_CHECKLIST.md: Mark Profile & Settings as complete

***

### **TASK 17: Edge Cases & Error Handling**
**Priority:** Medium  
**Estimated Time:** 3-4 hours  
**Screens Covered:** Error states, offline mode, permission screens  

**Scope:**
- Offline state handling
- Permission education screens
- Error and empty states
- Loading states

**Previous Screen:** Any screen that can error  
**Next Screen:** Recovery or retry  
**Implementation Notes:**
- Create offline banner with queue indicators
- Build permission education screens for Location/Camera/Notifications
- Add empty state illustrations and tips
- Implement loading skeletons
- Create error retry mechanisms
- Add "User unavailable" states for uninstalled apps

**Completion Criteria:**
- [ ] Offline banner appears correctly
- [ ] Permission screens explain rationale
- [ ] Empty states provide helpful guidance
- [ ] Loading states prevent user confusion
- [ ] Error recovery works
- Update SCREEN_CHECKLIST.md: Mark all edge cases as complete

***

### **TASK 18: Final Integration & Polish**
**Priority:** Critical  
**Estimated Time:** 2-3 hours  
**Screens Covered:** All screens - final integration  

**Scope:**
- End-to-end flow testing
- UI polish and consistency
- Accessibility improvements
- Performance optimization

**Previous Screen:** All completed screens  
**Next Screen:** Production-ready app  
**Implementation Notes:**
- Test complete user journeys (onboarding → job application → completion)
- Ensure consistent theming across all screens
- Add accessibility labels and semantic markup
- Optimize image loading and transitions
- Fix any navigation inconsistencies
- Add final animations and micro-interactions

**Completion Criteria:**
- [ ] Complete onboarding flow works end-to-end
- [ ] Worker job application process is smooth
- [ ] Poster job posting and management works
- [ ] Mode switching preserves state
- [ ] All screens meet accessibility standards
- Update SCREEN_CHECKLIST.md: Mark final integration complete
- **FINAL VERIFICATION:** All 40 screens should be marked complete

***

## Execution Guidelines for AI Coding Agent

1. **Start with TASK 1** and work sequentially through tasks
2. **After each task completion**, update the SCREEN_CHECKLIST.md file as instructed
3. **Test each screen** before moving to the next task
4. **Use mock data** consistently across all screens - create realistic Indian job categories, worker names, and locations
5. **Maintain design consistency** - refer back to the original UI/UX specification for colors, typography, and interaction patterns
6. **Implement responsive design** - ensure all screens work on different Android screen sizes
7. **Add proper error handling** throughout all tasks
8. **Create reusable components** to maintain consistency and reduce code duplication

## Success Criteria
- All 40 screens implemented and functional
- Complete user flows work end-to-end
- Both Worker and Poster modes fully operational
- Consistent design language throughout
- Proper accessibility implementation
- SCREEN_CHECKLIST.md shows 100% completion

This task breakdown provides a systematic approach for building the complete Worker-Poster Marketplace app with proper tracking and quality assurance built into each step.

[1](https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/attachments/82055977/7dc0cf44-8343-4b9c-8055-967bc393375b/ui-ux-flow.md)
[2](https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/attachments/82055977/9339d05b-b71b-4cc9-9cd1-8eee172b4822/ui-ux-wireframe.md)