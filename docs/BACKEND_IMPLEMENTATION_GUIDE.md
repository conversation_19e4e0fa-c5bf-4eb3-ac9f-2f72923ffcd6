# 🚀 Backend Implementation Guide - Ozgaar Mobile Web App

## 📋 Quick Start for Backend Engineers

This guide provides a structured approach to implementing the backend APIs for the Ozgaar mobile web application based on comprehensive frontend analysis.

**📄 Main Documentation**: See `BACKEND_API_REQUIREMENTS.md` for complete API specifications

---

## 🏗 Implementation Priority Order

### Phase 1: Core Authentication & User Management (Week 1)
```typescript
// Priority 1: Essential for app functionality
POST /api/auth/send-otp
POST /api/auth/verify-otp
POST /api/auth/resend-otp
POST /api/auth/refresh-token
POST /api/auth/logout
GET  /api/auth/me

POST /api/users/worker-profile
POST /api/users/poster-profile
GET  /api/users/profile
PUT  /api/users/profile
POST /api/users/upload-avatar
```

### Phase 2: Job Management System (Week 2)
```typescript
// Priority 2: Core business logic
GET  /api/jobs/recommendations
GET  /api/jobs/search
GET  /api/jobs/{id}
POST /api/jobs
PUT  /api/jobs/{id}
GET  /api/jobs/categories
GET  /api/jobs/filters

POST /api/jobs/{id}/apply
GET  /api/applications/my-applications
GET  /api/jobs/{id}/applicants
PUT  /api/applications/{id}/withdraw
```

### Phase 3: Communication & Real-time Features (Week 3)
```typescript
// Priority 3: User engagement
GET  /api/messages/conversations
GET  /api/messages/conversations/{id}
POST /api/messages/conversations/{id}/messages
PUT  /api/messages/{id}/read

// WebSocket implementation
ws://api.ozgaar.com/ws?token={jwt_token}

GET  /api/notifications
PUT  /api/notifications/{id}/read
PUT  /api/notifications/mark-all-read
DELETE /api/notifications/{id}
```

### Phase 4: Payment & Review System (Week 4)
```typescript
// Priority 4: Monetization features
GET  /api/payments/history
GET  /api/payments/earnings
POST /api/payments/process
GET  /api/payments/export

GET  /api/reviews/received
POST /api/reviews
```

### Phase 5: Advanced Features & Polish (Week 5)
```typescript
// Priority 5: Enhancement features
GET  /api/users/skills/personas
POST /api/users/skills/personas
PUT  /api/users/skills/personas/{id}

POST /api/upload/images
POST /api/upload/documents

GET  /api/support/faq
POST /api/support/tickets
```

---

## 🗄 Database Schema Recommendations

### Core Tables Structure
```sql
-- Users table (main entity)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone VARCHAR(15) UNIQUE NOT NULL,
    phone_verified BOOLEAN DEFAULT FALSE,
    email VARCHAR(255),
    full_name VARCHAR(100) NOT NULL,
    avatar_url TEXT,
    location VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Worker profiles
CREATE TABLE worker_profiles (
    user_id UUID PRIMARY KEY REFERENCES users(id),
    primary_skill VARCHAR(100),
    skills TEXT[], -- Array of skills
    experience VARCHAR(20),
    hourly_rate INTEGER,
    daily_rate INTEGER,
    about TEXT,
    phone_visible BOOLEAN DEFAULT FALSE,
    completed_jobs INTEGER DEFAULT 0,
    total_earnings INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    portfolio_photos TEXT[], -- Array of URLs
    is_available BOOLEAN DEFAULT TRUE
);

-- Poster profiles
CREATE TABLE poster_profiles (
    user_id UUID PRIMARY KEY REFERENCES users(id),
    company_name VARCHAR(255),
    about TEXT,
    jobs_posted INTEGER DEFAULT 0,
    total_spent INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    business_verified BOOLEAN DEFAULT FALSE
);

-- Jobs table
CREATE TABLE jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    poster_id UUID REFERENCES users(id),
    title VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    subcategory VARCHAR(100),
    location VARCHAR(255) NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    rate INTEGER NOT NULL,
    rate_type VARCHAR(20) NOT NULL, -- 'hourly', 'daily', 'fixed'
    duration VARCHAR(100),
    requirements TEXT[],
    skills_required TEXT[],
    experience_level VARCHAR(20),
    status VARCHAR(20) DEFAULT 'active',
    urgency VARCHAR(20) DEFAULT 'normal',
    photos TEXT[],
    view_count INTEGER DEFAULT 0,
    application_count INTEGER DEFAULT 0,
    max_applications INTEGER,
    auto_accept BOOLEAN DEFAULT FALSE,
    assigned_worker_id UUID REFERENCES users(id),
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    work_photos TEXT[],
    payment_status VARCHAR(20) DEFAULT 'pending',
    payment_method VARCHAR(50),
    transaction_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP
);

-- Job applications
CREATE TABLE job_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID REFERENCES jobs(id),
    worker_id UUID REFERENCES users(id),
    persona_id UUID, -- Reference to worker persona
    cover_letter TEXT,
    proposed_rate INTEGER,
    proposed_rate_type VARCHAR(20),
    status VARCHAR(20) DEFAULT 'pending',
    applied_at TIMESTAMP DEFAULT NOW(),
    responded_at TIMESTAMP,
    has_messages BOOLEAN DEFAULT FALSE,
    last_message_at TIMESTAMP
);

-- Messages and conversations
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID REFERENCES jobs(id),
    worker_id UUID REFERENCES users(id),
    poster_id UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES conversations(id),
    sender_id UUID REFERENCES users(id),
    receiver_id UUID REFERENCES users(id),
    text TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text',
    status VARCHAR(20) DEFAULT 'sent',
    timestamp TIMESTAMP DEFAULT NOW()
);

-- Payments
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID REFERENCES jobs(id),
    payer_id UUID REFERENCES users(id),
    payee_id UUID REFERENCES users(id),
    amount INTEGER NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    method VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    transaction_id VARCHAR(255),
    gateway_response JSONB,
    has_dispute BOOLEAN DEFAULT FALSE,
    dispute_reason TEXT,
    dispute_status VARCHAR(20),
    description TEXT,
    receipt_url TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    processed_at TIMESTAMP,
    completed_at TIMESTAMP
);

-- Reviews
CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID REFERENCES jobs(id),
    reviewer_id UUID REFERENCES users(id),
    reviewee_id UUID REFERENCES users(id),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    review_type VARCHAR(30) NOT NULL,
    communication_rating INTEGER,
    work_quality_rating INTEGER,
    timeliness_rating INTEGER,
    professionalism_rating INTEGER,
    is_public BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    is_urgent BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- User settings
CREATE TABLE user_settings (
    user_id UUID PRIMARY KEY REFERENCES users(id),
    notifications JSONB DEFAULT '{}',
    privacy JSONB DEFAULT '{}',
    language VARCHAR(20) DEFAULT 'english',
    preferences JSONB DEFAULT '{}',
    updated_at TIMESTAMP DEFAULT NOW()
);
```

---

## 🔧 Technology Stack Recommendations

### Backend Framework
- **Node.js + Express.js** or **Node.js + Fastify** (TypeScript)
- **Alternative**: Python + FastAPI or Go + Gin

### Database
- **Primary**: PostgreSQL 14+ (for JSONB support)
- **Cache**: Redis (for sessions, rate limiting)
- **Search**: Elasticsearch (optional, for advanced job search)

### File Storage
- **Images**: AWS S3 + CloudFront CDN
- **Documents**: AWS S3 with encryption

### Real-time Communication
- **WebSocket**: Socket.io or native WebSocket
- **Push Notifications**: Firebase Cloud Messaging (FCM)

### Authentication
- **JWT**: jsonwebtoken library
- **OTP**: 2Factor.in API integration
- **Session Management**: Redis-based

### Payment Integration
- **Primary**: Razorpay (Indian market)
- **Alternative**: Stripe (international)

---

## 🔒 Security Implementation Checklist

### Authentication Security
- [ ] JWT tokens with short expiry (15 minutes)
- [ ] Refresh token rotation
- [ ] Rate limiting on auth endpoints
- [ ] OTP attempt limiting (3 attempts per phone)
- [ ] Phone number validation and sanitization

### API Security
- [ ] HTTPS enforcement
- [ ] CORS configuration
- [ ] Request size limiting
- [ ] SQL injection prevention (parameterized queries)
- [ ] XSS prevention (input sanitization)
- [ ] Rate limiting per user/IP

### File Upload Security
- [ ] File type validation
- [ ] File size limits (10MB images, 5MB documents)
- [ ] Virus scanning
- [ ] Secure file naming
- [ ] CDN with access controls

### Data Protection
- [ ] Password hashing (bcrypt)
- [ ] Sensitive data encryption
- [ ] PII data handling compliance
- [ ] Audit logging for sensitive operations
- [ ] Database connection encryption

---

## 📊 Performance Optimization

### Database Optimization
```sql
-- Essential indexes for performance
CREATE INDEX idx_jobs_location ON jobs USING GIN (location gin_trgm_ops);
CREATE INDEX idx_jobs_category ON jobs (category);
CREATE INDEX idx_jobs_status_created ON jobs (status, created_at DESC);
CREATE INDEX idx_applications_worker_status ON job_applications (worker_id, status);
CREATE INDEX idx_messages_conversation_timestamp ON messages (conversation_id, timestamp DESC);
CREATE INDEX idx_notifications_user_unread ON notifications (user_id, is_read, created_at DESC);
```

### Caching Strategy
```typescript
// Redis caching for frequently accessed data
const CACHE_KEYS = {
  user_profile: (userId: string) => `user:${userId}:profile`,
  job_details: (jobId: string) => `job:${jobId}:details`,
  job_search: (query: string) => `search:jobs:${hashQuery(query)}`,
  user_notifications: (userId: string) => `user:${userId}:notifications`
};

// Cache TTL (Time To Live)
const CACHE_TTL = {
  user_profile: 3600,      // 1 hour
  job_details: 1800,       // 30 minutes
  job_search: 300,         // 5 minutes
  user_notifications: 60   // 1 minute
};
```

---

## 🧪 Testing Strategy

### API Testing
- **Unit Tests**: Jest + Supertest
- **Integration Tests**: Database + API endpoints
- **Load Testing**: Artillery.js or k6
- **Security Testing**: OWASP ZAP

### Test Coverage Requirements
- [ ] Authentication flows (OTP, JWT)
- [ ] Job CRUD operations
- [ ] Application workflow
- [ ] Payment processing
- [ ] Real-time messaging
- [ ] File upload/download
- [ ] Error handling scenarios

---

## 📈 Monitoring & Analytics

### Essential Metrics
- API response times
- Error rates by endpoint
- User registration/retention
- Job posting/application rates
- Payment success rates
- Message delivery rates

### Logging Requirements
- Request/response logging
- Error logging with stack traces
- Security event logging
- Performance metrics
- User activity tracking

---

## 🚀 Deployment Checklist

### Environment Setup
- [ ] Production database setup
- [ ] Redis cache setup
- [ ] File storage configuration
- [ ] Environment variables
- [ ] SSL certificates
- [ ] Domain configuration

### Monitoring Setup
- [ ] Application monitoring (New Relic/DataDog)
- [ ] Database monitoring
- [ ] Error tracking (Sentry)
- [ ] Log aggregation (ELK stack)
- [ ] Uptime monitoring

---

**📞 Support**: Contact frontend team for clarification on any API requirements  
**📅 Timeline**: 5-week implementation plan provided above  
**🔄 Updates**: This document will be updated based on implementation feedback
