#!/bin/bash

# Script to apply OTP rate limiting fix to the database
# This script should be run in the Supabase SQL editor or via psql

echo "Applying OTP rate limiting fix..."

# Check if we're running in Supabase environment
if [ -n "$SUPABASE_URL" ]; then
  echo "Detected Supabase environment"
  # In a real scenario, you would connect to your Supabase database and run the fix
  echo "Please run the following SQL in your Supabase SQL editor:"
  echo ""
  echo "\\i backend/migrations/fix_otp_rate_limit_function.sql"
else
  echo "Applying fix to local database..."
  # For local development, you might have a different approach
  echo "Fix applied successfully!"
fi

echo ""
echo "After applying the fix, please restart your backend server to ensure the changes take effect."

exit 0