{"name": "ozgaar-monorepo", "private": true, "scripts": {"build:types": "tsc -p packages/types/tsconfig.json", "build:backend": "cd backend && pnpm build", "build": "pnpm build:types && pnpm build:backend", "android": "cd mobile && pnpm android", "dev:mobile": "cd mobile && pnpm start", "dev:backend": "cd backend && pnpm dev", "dev": "pnpm dev:mobile & pnpm dev:backend", "dev:android": "pnpm dev:backend & pnpm android", "ts-compile": "npx tsc && npx tsc -p packages/types/tsconfig.json && npx tsc -p mobile/tsconfig.json && npx tsc -p backend/tsconfig.json"}, "devDependencies": {"typescript": "^5.9.2"}}