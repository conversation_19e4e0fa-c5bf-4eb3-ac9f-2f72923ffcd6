# Integration Checklist

**Project Status: Ozgaar Integration project created with 28 tasks (INT-00 to INT-27) structured by integration phases**
**Current Status: Working on INT-04 - OTP Auth and Session (A2–A4)**
**Integration Goal: Replace all mock data with typed clients and Redux Toolkit slices across all 54 screens**

## Integration Progress Overview

**Phase 0: Repo and Plumbing (INT-00 to INT-03)**
- [x] INT-00: Create Integration Project + Checklists
- [x] INT-01: Type Sync + Path Aliases  
- [x] INT-02: Env + Scripts + Concurrency
- [x] INT-03: Typed API Base + Auth Interceptors

**Phase 1: Auth + Profiles (INT-04 to INT-07)**
- [x] INT-04: OTP Auth and Session (A2–A4)
- [ ] INT-05: Mode Selection + Settings Init (A5)
- [ ] INT-06: Worker Profile + Personas v1 (A6–A9)
- [ ] INT-07: Poster Profile v1 (A10–A11)

**Phase 2: Jobs Core (INT-08 to INT-13)**
- [ ] INT-08: Worker Home Recommendations (B1)
- [ ] INT-09: Job Search + Filters (Global + B1/B2)
- [ ] INT-10: Job Details + Bookmark (B2)
- [ ] INT-11: Apply Flow + Limits (B3–B4)
- [ ] INT-12: Poster Post Job Wizard (C2)
- [ ] INT-13: Poster My Jobs + Applicants (C3–C4)

**Phase 3: Messaging + Notifications (INT-14 to INT-15)**
- [ ] INT-14: Conversations + Messages (B6/C7)
- [ ] INT-15: Notifications Center + Push Registration

**Phase 4: Execution, Proof, Payments, Reviews (INT-16 to INT-19)**
- [ ] INT-16: Job Lifecycle + Status Timeline (B5/C8)
- [ ] INT-17: Photo Proof + Uploads (B7/C8)
- [ ] INT-18: Payments + Earnings (B8/C9/C11/B10)
- [ ] INT-19: Ratings & Reviews (B9/C10)

**Phase 5: Settings, Help, Advanced + Hardening (INT-20 to INT-27)**
- [ ] INT-20: Settings, Privacy, Language
- [ ] INT-21: Help & Support
- [ ] INT-22: Poster Worker Discovery + Invite
- [ ] INT-23: Assign/Hire Worker
- [ ] INT-24: Notifications: Push + WebSocket Events
- [ ] INT-25: Data Security, RLS, Rate Limits (Surface in UI)
- [ ] INT-26: Performance + Caching + Virtualization
- [ ] INT-27: Exports + CSVs

## Per-Screen Integration Status

### Onboarding Flow (11 screens)
| Screen | Backend Task | Frontend Task | Backend Complete | Frontend Complete | Integration Complete |
|--------|--------------|---------------|------------------|-------------------|---------------------|
| A1. Welcome Screen | N/A | TASK 2 | ✅ | ✅ | [ ] |
| A2. Phone Registration | BE-01 | TASK 2 | [ ] | ✅ | [ ] |
| A3. OTP Verification | BE-01 | TASK 2 | [ ] | ✅ | [ ] |
| A4. Verification Success | BE-01 | TASK 2 | [ ] | ✅ | [ ] |
| A5. Mode Selection | BE-02 | TASK 2 | [ ] | ✅ | [ ] |
| A6. Worker Basic Profile | BE-03 | TASK 3 | [ ] | ✅ | [ ] |
| A7. Persona Intro | BE-03 | TASK 3 | [ ] | ✅ | [ ] |
| A8. Persona Create Wizard | BE-03 | TASK 3 | [ ] | ✅ | [ ] |
| A9. Worker Home First-Run | BE-03 | TASK 3 | [ ] | ✅ | [ ] |
| A10. Poster Basic Profile | BE-04 | TASK 4 | [ ] | ✅ | [ ] |
| A11. Poster Home First-Run | BE-04 | TASK 4 | [ ] | ✅ | [ ] |

### Worker Flow (10 screens)
| Screen | Backend Task | Frontend Task | Backend Complete | Frontend Complete | Integration Complete |
|--------|--------------|---------------|------------------|-------------------|---------------------|
| B1. Worker Home (Jobs Feed) | BE-05 | TASK 5 | [ ] | ✅ | [ ] |
| B2. Job Details (Worker View) | BE-07 | TASK 5 | [ ] | ✅ | [ ] |
| B3. Apply Drawer | BE-08 | TASK 6 | [ ] | ✅ | [ ] |
| B4. My Jobs (Applications Tab) | BE-08 | TASK 6 | [ ] | ✅ | [ ] |
| B5. Job Status Detail | BE-13 | TASK 7 | [ ] | ✅ | [ ] |
| B6. Chat (Worker) | BE-11 | TASK 8 | [ ] | ✅ | [ ] |
| B7. Photo Proof Capture | BE-14 | TASK 9 | [ ] | ✅ | [ ] |
| B8. Worker Payment Confirmation | BE-15 | TASK 9 | [ ] | ✅ | [ ] |
| B9. Ratings To Give (Worker) | BE-16 | TASK 10 | [ ] | ✅ | [ ] |
| B10. Earnings Summary & History | BE-15 | TASK 10 | [ ] | ✅ | [ ] |

### Poster Flow (11 screens)
| Screen | Backend Task | Frontend Task | Backend Complete | Frontend Complete | Integration Complete |
|--------|--------------|---------------|------------------|-------------------|---------------------|
| C1. Poster Home | BE-19 | TASK 11 | [ ] | ✅ | [ ] |
| C2. Job Post Wizard | BE-09 | TASK 11 | [ ] | ✅ | [ ] |
| C3. My Jobs (Poster) | BE-10 | TASK 12 | [ ] | ✅ | [ ] |
| C4. Job Details & Applicants | BE-10 | TASK 12 | [ ] | ✅ | [ ] |
| C5. Worker Profile (Poster View) | BE-19 | TASK 13 | [ ] | ✅ | [ ] |
| C6. Invite Worker to Job | BE-19 | TASK 13 | [ ] | ✅ | [ ] |
| C7. Chat (Poster) | BE-11 | TASK 13 | [ ] | ✅ | [ ] |
| C8. Job Status (Poster) | BE-13 | TASK 14 | [ ] | ✅ | [ ] |
| C9. Poster Payment Confirmation | BE-15 | TASK 14 | [ ] | ✅ | [ ] |
| C10. Ratings To Give (Poster) | BE-16 | TASK 15 | [ ] | ✅ | [ ] |
| C11. Payments History (Poster) | BE-15 | TASK 15 | [ ] | ✅ | [ ] |

### Shared Components (8 screens)
| Screen | Backend Task | Frontend Task | Backend Complete | Frontend Complete | Integration Complete |
|--------|--------------|---------------|------------------|-------------------|---------------------|
| Bottom Navigation Bar | N/A | TASK 1 | ✅ | ✅ | [ ] |
| Top App Bar (Mode Switcher) | N/A | TASK 1 | ✅ | ✅ | [ ] |
| Persona Selector Overlay | BE-03 | TASK 16 | [ ] | ✅ | [ ] |
| Notifications Center | BE-12 | TASK 16 | [ ] | ✅ | [ ] |
| Global Search | BE-06 | TASK 16 | [ ] | ✅ | [ ] |
| Profile & Settings | BE-17 | TASK 17 | [ ] | ✅ | [ ] |
| Help & Support | BE-18 | TASK 17 | [ ] | ✅ | [ ] |
| Messages List | BE-11 | TASK 18 | [ ] | ✅ | [ ] |

## Integration Completion Criteria

### Per-Screen Integration Complete Checklist
For each screen to be marked as "Integration Complete":
- [ ] All mock data removed and replaced with live API calls
- [ ] Redux Toolkit slice implemented with proper typing from packages/types
- [ ] Loading states, error states, and empty states properly implemented
- [ ] Real-time updates working where applicable (WebSocket integration)
- [ ] Proper persistence configured for relevant data
- [ ] Error handling matches UX specifications
- [ ] Performance targets met (loading times, smooth scrolling)
- [ ] Accessibility requirements maintained
- [ ] Cross-browser compatibility verified on mobile devices

### Redux Store Architecture Complete
- [ ] All required slices implemented: auth, settings, userProfile, workerProfile, personas, posterProfile, jobsFeed, jobSearch, jobDetails, bookmarks, applications, jobStatus, chat, notifications, payments, earnings, reviews, uiPrefs
- [ ] Redux persist configured for: auth.tokens, mode, selectedPersona, language, lightweight filters
- [ ] Global error slice and standardized toasts implemented
- [ ] RTK Query baseApi with proper authentication and refresh flow
- [ ] WebSocket integration with typed events from packages/types

### Mock Data Purge Complete
- [ ] All mock data providers removed from UI TASK 5..15 implementations
- [ ] All JSON fixtures and hard-coded lists removed
- [ ] All mocked network shims replaced with RTK Query hooks
- [ ] Loading/error/empty states preserved and enhanced
- [ ] Zero residual mock usage across all 54 screens

### Type Safety Complete
- [ ] All API requests/responses use types from packages/types/src/server/api/
- [ ] All WebSocket events use types from packages/types/src/server/
- [ ] No 'any' types in API integration code
- [ ] TypeScript strict mode passes without errors
- [ ] Cross-package imports resolve correctly

### Performance Targets Met
- [ ] P95 response times within acceptable limits for all API calls
- [ ] Smooth scrolling maintained on long lists with virtualization
- [ ] Bundle size optimized with proper code splitting
- [ ] Client-side caching implemented for frequently accessed data
- [ ] Network calls minimized through proper prefetching and debouncing

## Integration Testing Strategy

### Smoke Tests (Per Module)
- [ ] Auth flow: OTP → Profile → Mode selection → Dashboard
- [ ] Profile management: Worker/Poster profile creation and updates
- [ ] Job discovery: Feed pagination, search, filters, details
- [ ] Application flow: Apply with limits, status tracking, withdrawal
- [ ] Messaging: Real-time chat send/read, conversation list
- [ ] Job lifecycle: Status transitions, photo proof, completion
- [ ] Payments: Dual confirmation, history, earnings, export
- [ ] Reviews: Rating submission, constraints, averages
- [ ] Notifications: Real-time updates, mark read/all, device registration
- [ ] Settings: Profile updates, privacy flags, language switching

### Integration Test Gates
- [ ] All smoke tests passing before marking phase complete
- [ ] Zero mock data usage verified per screen
- [ ] WebSocket connection stability under load
- [ ] Authentication flow including token refresh
- [ ] Real-time features working across multiple clients
- [ ] Error boundaries handling API failures gracefully
- [ ] Performance benchmarks met on mobile devices

## Next Steps

1. **Complete INT-04**: Finish OTP authentication and session implementation
2. **Move to Phase 1 tasks**: Continue with INT-05, INT-06, INT-07 for profile integration
3. **Follow sequential phases**: Complete each phase before moving to next
4. **Update this checklist**: Mark tasks and screens complete as integration progresses
5. **Maintain SCREEN_CHECKLIST.md**: Keep both checklists in sync
6. **Run integration tests**: Verify each module before proceeding
7. **Document issues**: Track any deviations or additional requirements discovered

## Success Metrics

- **54/54 screens** marked as Integration Complete
- **0 mock data** remaining in production code
- **100% type safety** maintained across server-client boundary
- **All user flows** working end-to-end with live data
- **Performance targets** met on mobile devices
- **Real-time features** functioning reliably
- **Error handling** providing user-friendly feedback
- **Accessibility** standards maintained throughout integration