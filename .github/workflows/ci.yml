name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # Code Quality & Linting
  lint:
    name: Code Quality Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run ESLint
        run: npm run lint
        
      - name: Run Prettier check
        run: npm run format:check
        
      - name: TypeScript type check
        run: npm run type-check

  # Unit & Integration Tests
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ozgaar_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Setup test database
        run: |
          npm run db:setup:test
          npm run db:seed:test
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ozgaar_test
          
      - name: Run unit tests
        run: npm run test:unit -- --coverage
        
      - name: Run integration tests
        run: npm run test:integration
        
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests

  # E2E Testing (on staging environment only)
  e2e:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/main'
    needs: [lint, test]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Setup Java (for Android emulator)
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
          
      - name: Setup Android SDK
        uses: android-actions/setup-android@v3
        
      - name: Create Android emulator
        run: |
          echo "y" | $ANDROID_HOME/tools/bin/sdkmanager --install "system-images;android-29;google_apis;x86_64"
          echo "no" | $ANDROID_HOME/tools/bin/avdmanager create avd --force --name test --abi google_apis/x86_64 --package "system-images;android-29;google_apis;x86_64"
          
      - name: Start Android emulator
        run: |
          $ANDROID_HOME/emulator/emulator @test -no-audio -no-window -gpu swiftshader_indirect &
          adb wait-for-device shell 'while [[ -z $(getprop sys.boot_completed | tr -d '\r') ]]; do sleep 1; done'
          
      - name: Build app for testing
        run: npm run build:android:test
        env:
          EXPO_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_TEST_URL }}
          EXPO_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_TEST_ANON_KEY }}
          
      - name: Run E2E tests
        run: npm run test:e2e:android
        timeout-minutes: 30

  # Build for different environments
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [lint, test]
    if: github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/main'
    
    strategy:
      matrix:
        environment: [staging, production]
        exclude:
          - environment: production
            
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Setup Expo CLI
        run: npm install -g @expo/cli
        
      - name: Install dependencies
        run: npm ci
        
      - name: Authenticate with Expo
        run: expo login --username ${{ secrets.EXPO_USERNAME }} --password ${{ secrets.EXPO_PASSWORD }}
        
      - name: Build Android (Staging)
        if: matrix.environment == 'staging'
        run: |
          expo build:android --release-channel staging --no-publish
        env:
          EXPO_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_STAGING_URL }}
          EXPO_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_STAGING_ANON_KEY }}
          EXPO_PUBLIC_GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY_STAGING }}
          
      - name: Build Android (Production)
        if: matrix.environment == 'production' && github.ref == 'refs/heads/main'
        run: |
          expo build:android --release-channel production --no-publish
        env:
          EXPO_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_PRODUCTION_URL }}
          EXPO_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_PRODUCTION_ANON_KEY }}
          EXPO_PUBLIC_GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY_PRODUCTION }}
          
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: android-${{ matrix.environment }}-build
          path: build/

  # Deployment to staging/production
  deploy:
    name: Deploy Application
    runs-on: ubuntu-latest
    needs: [build, e2e]
    if: github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/main'
    
    environment:
      name: ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Setup Expo CLI
        run: npm install -g @expo/cli eas-cli
        
      - name: Install dependencies
        run: npm ci
        
      - name: Authenticate with Expo
        run: expo login --username ${{ secrets.EXPO_USERNAME }} --password ${{ secrets.EXPO_PASSWORD }}
        
      - name: Deploy to Staging
        if: github.ref == 'refs/heads/develop'
        run: |
          expo publish --release-channel staging
          eas build --platform android --profile preview --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
          
      - name: Deploy to Production
        if: github.ref == 'refs/heads/main'
        run: |
          expo publish --release-channel production
          eas build --platform android --profile production --non-interactive
          eas submit --platform android --profile production --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
          GOOGLE_PLAY_SERVICE_ACCOUNT_KEY: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_KEY }}

  # Database migrations (production only)
  migrate:
    name: Database Migration
    runs-on: ubuntu-latest
    needs: [deploy]
    if: github.ref == 'refs/heads/main'
    
    environment:
      name: production
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run database migrations
        run: npm run db:migrate:production
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_PRODUCTION_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_PRODUCTION_SERVICE_KEY }}
          
      - name: Notify deployment success
        run: |
          curl -X POST -H 'Content-type: application/json' \
          --data '{"text":"🚀 Ozgaar Android deployed successfully to production!"}' \
          ${{ secrets.SLACK_WEBHOOK_URL }}