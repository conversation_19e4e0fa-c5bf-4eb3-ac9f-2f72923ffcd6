# Server Architecture Documentation

## Overview

The Ozgaar server is built using Node.js with Express.js and TypeScript, serving as an intermediary layer between the mobile application and Supabase database. It provides secure API endpoints with authentication, validation, and business logic.

## Technology Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js with TypeScript
- **Database**: Supabase (PostgreSQL)
- **Authentication**: JWT tokens with jsonwebtoken
- **Validation**: Jo<PERSON> for request validation
- **Security**: Helmet, CORS, Rate limiting
- **Logging**: Winston
- **File Processing**: Multer + Sharp
- **Realtime Communication**: Socket.IO
- **Type System**: TypeScript with shared types from @ozgaar/types

## Project Structure

```
server/
├── src/
│   ├── controllers/         # Request handlers
│   │   ├── auth.controller.ts
│   │   ├── jobs.controller.ts
│   │   ├── users.controller.ts
│   │   ├── skills.controller.ts
│   │   ├── poster.controller.ts
│   │   ├── messages.controller.ts
│   │   └── notifications.controller.ts
│   ├── middleware/          # Custom middleware
│   │   ├── auth.middleware.ts
│   │   ├── error.middleware.ts
│   │   ├── notFound.middleware.ts
│   │   └── validation.middleware.ts
│   ├── routes/              # Route definitions
│   │   ├── auth.routes.ts
│   │   ├── jobs.routes.ts
│   │   ├── users.routes.ts
│   │   ├── skills.routes.ts
│   │   ├── poster.routes.ts
│   │   ├── messages.routes.ts
│   │   └── notifications.routes.ts
│   ├── services/            # Business logic
│   │   ├── auth.service.ts
│   │   ├── database.service.ts
│   │   ├── jobs.service.ts
│   │   ├── users.service.ts
│   │   ├── skills.service.ts
│   │   ├── poster.service.ts
│   │   ├── twoFactor.service.ts
│   │   ├── messages.service.ts
│   │   └── notifications.service.ts
│   ├── utils/               # Utility functions
│   │   ├── logger.ts
│   │   └── response.ts
│   ├── config/              # Configuration files
│   │   ├── app.config.ts
│   │   └── database.config.ts
│   ├── realtime/            # WebSocket functionality
│   │   ├── socket.ts
│   │   ├── message.gateway.ts
│   │   └── notification.gateway.ts
│   └── index.ts             # Main entry point
├── docs/                    # Documentation
│   └── ARCHITECTURE.md
├── dist/                    # Compiled JavaScript (build output)
├── package.json
├── tsconfig.json
└── README.md
```

## Type System Structure

The shared types are organized in the `packages/types` directory:

```
packages/types/
├── src/
│   ├── server/              # Server-specific types
│   │   ├── api/             # API request/response types
│   │   ├── database/        # Database schema types
│   │   │   ├── index.ts     # Exports all database types
│   │   │   ├── jobs.types.ts # Job-related database types
│   │   │   ├── notifications.types.ts # Notification-related database types
│   │   │   ├── otp.types.ts # OTP-related types
│   │   │   ├── tables.types.ts # Core table types
│   │   │   ├── user.types.ts # User-related types
│   │   │   └── worker.types.ts # Worker-related types
│   │   ├── jobs/            # Job module types
│   │   │   ├── index.ts     # Exports job module types
│   │   │   └── recommendations.types.ts # Job recommendations types
│   │   ├── notifications/   # Notification module types
│   │   │   ├── index.ts     # Exports notification module types
│   │   │   ├── notifications.types.ts # Notification API types
│   │   │   └── websocket.types.ts # WebSocket notification types
│   │   ├── poster/          # Poster module types
│   │   ├── services/        # Service layer types
│   │   ├── user/            # User module types
│   │   ├── utils/           # Utility types
│   │   └── worker/          # Worker module types
│   └── index.ts             # Main export file
└── package.json
```

## Authentication Module

### Components

1. **AuthController** (`src/controllers/auth.controller.ts`)
   - Handles HTTP requests for authentication endpoints
   - Validates request data using Joi schemas
   - Delegates business logic to AuthService
   - Returns standardized API responses

2. **AuthService** (`src/services/auth.service.ts`)
   - Implements OTP sending and verification logic
   - Manages JWT token generation and validation
   - Handles user creation and authentication
   - Interfaces with Supabase database

3. **AuthMiddleware** (`src/middleware/auth.middleware.ts`)
   - Validates JWT tokens for protected routes
   - Extracts user information from tokens
   - Provides optional authentication for public routes

4. **AuthRoutes** (`src/routes/auth.routes.ts`)
   - Defines authentication API endpoints
   - Applies validation middleware to requests
   - Maps routes to controller methods

5. **TwoFactorService** (`src/services/twoFactor.service.ts`)
   - Integrates with 2Factor.in SMS API
   - Handles OTP sending and verification
   - Implements rate limiting and phone number validation
   - Provides fallback error handling

### API Endpoints

- `POST /api/auth/send-otp` - Send OTP to phone number
- `POST /api/auth/verify-otp` - Verify OTP and authenticate user
- `POST /api/auth/resend-otp` - Resend OTP to phone number
- `GET /api/auth/me` - Get current authenticated user info
- `POST /api/auth/refresh-token` - Refresh access token
- `POST /api/auth/logout` - Logout user

### Authentication Flow

1. **OTP Request**: User provides phone number
2. **Phone Validation**: Validate Indian mobile number format
3. **Rate Limiting**: Check if user hasn't exceeded OTP request limits (3 per 15 minutes)
4. **OTP Generation**: Send OTP via 2Factor.in SMS service
5. **OTP Verification**: Verify OTP using 2Factor.in session ID
6. **User Creation/Login**: Create new user or authenticate existing user
7. **Token Generation**: Generate JWT access and refresh tokens
8. **Protected Access**: Use access token for authenticated requests
9. **Token Refresh**: Use refresh token to get new access token
10. **Logout**: Invalidate tokens (client-side for now)

### Database Tables Used

- `user_profiles` - User account information
- `otp_requests` - OTP request tracking and rate limiting

## SMS OTP Integration (2Factor.in)

### Service Configuration
The TwoFactorService integrates with 2Factor.in API for SMS OTP delivery:

- **API Endpoint**: `https://2factor.in/API/V1`
- **Authentication**: API key from environment variable `TWOFACTOR_API_KEY`
- **Phone Format**: Indian mobile numbers (supports +91, 91, 0, or 10-digit formats)
- **OTP Format**: 6-digit auto-generated OTP

### Key Features

1. **Phone Number Validation**
   - Validates Indian mobile number formats
   - Automatically formats numbers for 2Factor.in API
   - Supports multiple input formats

2. **Rate Limiting**
   - Maximum 3 OTP requests per phone number in 15 minutes
   - Database-tracked rate limiting
   - Graceful error handling for exceeded limits

3. **OTP Lifecycle**
   - Send OTP: Returns session ID for verification
   - Verify OTP: Uses session ID + OTP code
   - Resend OTP: Creates new session (invalidates previous)

4. **Error Handling**
   - Comprehensive logging of all SMS attempts
   - Fallback error messages for API failures
   - Service availability checks

### Environment Variables Required

```env
TWOFACTOR_API_KEY=your_2factor_api_key_here
MOCK_OTP=false
MOCK_OTP_VALUE=123456
```

### Mock OTP for Development

To save costs during development, you can use mock OTP functionality by setting `MOCK_OTP=true` in your environment variables. When enabled:
- OTPs are not sent via SMS
- A fixed session ID (`MOCK_SESSION_ID`) is used
- Verification always succeeds when the OTP matches `MOCK_OTP_VALUE` (defaults to `123456`)
- All other functionality remains the same

### API Response Handling

**Success Response:**
```json
{
  "Status": "Success",
  "Details": "session_id_here"
}
```

**Error Response:**
```json
{
  "Status": "Error",
  "Details": "error_message_here"
}
```

## Configuration Management

### AppConfig (`src/config/app.config.ts`)
- Centralized environment variable management
- Validation of required configuration
- Environment-specific settings (development/production)
- Configuration for CORS, rate limiting, file uploads

### DatabaseConfig (`src/config/database.config.ts`)
- Supabase client initialization and management
- Connection testing and health checks
- Singleton pattern for client reuse

## Error Handling

### Global Error Handler (`src/middleware/error.middleware.ts`)
- Catches all unhandled errors
- Provides consistent error response format
- Logs errors with context information
- Environment-specific error details

### Response Utilities (`src/utils/response.ts`)
- Standardized API response formats
- Success and error response helpers
- HTTP status code management
- Pagination response formatting

## Security Features

1. **JWT Authentication**: Secure token-based authentication
2. **Rate Limiting**: Prevents abuse of OTP endpoints
3. **Request Validation**: Joi schema validation for all inputs
4. **CORS Protection**: Configurable cross-origin resource sharing
5. **Security Headers**: Helmet middleware for security headers
6. **Input Sanitization**: Automatic through Joi validation

## Poster Profile Module

### Components

1. **PosterController** (`src/controllers/poster.controller.ts`)
   - Handles HTTP requests for poster profile endpoints
   - Validates business profile data using Joi schemas
   - Delegates business logic to PosterService
   - Returns standardized API responses

2. **PosterService** (`src/services/poster.service.ts`)
   - Implements poster profile creation and management
   - Handles business document uploads and verification
   - Manages poster statistics and analytics
   - Interfaces with Supabase database

3. **PosterRoutes** (`src/routes/poster.routes.ts`)
   - Defines poster profile API endpoints
   - Applies validation middleware to requests
   - Maps routes to controller methods

### API Endpoints

- `POST /api/poster/profile` - Create poster profile
- `GET /api/poster/profile` - Get poster profile
- `PUT /api/poster/profile` - Update poster profile
- `GET /api/poster/stats` - Get poster statistics
- `POST /api/poster/documents/upload` - Upload business documents
- `GET /api/poster/documents` - Get business document status

### Business Profile Features

1. **Company Information**
   - Company name, industry, and size
   - Business address and contact details
   - Website and social media links

2. **Business Verification**
   - GST number validation
   - PAN card verification
   - Business license uploads
   - Document verification status

3. **Statistics & Analytics**
   - Total jobs posted and completed
   - Application response rates
   - Average response time
   - Total spending and ratings

### Database Tables Used

- `poster_profiles` - Business profile information
- `business_documents` - Document uploads and verification
- `jobs` - Posted jobs for statistics
- `job_applications` - Applications for analytics

## Type System

### Shared Types (`@ozgaar/types`)
- API request/response interfaces
- Database schema types
- Service layer types
- Utility types

### Type Organization
- Server-specific types in `packages/types/src/server/`
- API types in `packages/types/src/server/api/`
- Database types in `packages/types/src/server/database/`
- Service types in `packages/types/src/server/services/`
- Utility types in `packages/types/src/server/utils/`
- User types in `packages/types/src/server/user/`
- Worker types in `packages/types/src/server/worker/`
- Poster types in `packages/types/src/server/poster/`
- Jobs types in `packages/types/src/server/jobs/`
- Messages types in `packages/types/src/server/messages/`

### New Job Types
- Job database schema types in `packages/types/src/server/database/jobs.types.ts`
- Job recommendation request/response types in `packages/types/src/server/jobs/recommendations.types.ts`

### Message Types
- Message database schema types in `packages/types/src/server/database/messages.types.ts`
- Conversation API types in `packages/types/src/server/messages/conversations.types.ts`
- Message API types in `packages/types/src/server/messages/messages.types.ts`
- WebSocket types in `packages/types/src/server/messages/websocket.types.ts`

### Notification Types
- Notification database schema types in `packages/types/src/server/database/notifications.types.ts`
- Notification API request/response types in `packages/types/src/server/notifications/notifications.types.ts`
- WebSocket notification types in `packages/types/src/server/notifications/websocket.types.ts`

## Logging

### Winston Logger (`src/utils/logger.ts`)
- Structured logging with JSON format in production
- Console logging with colors in development
- File logging in production with rotation
- Request/response logging
- Error logging with stack traces

## Jobs Module

### Components

1. **JobsController** (`src/controllers/jobs.controller.ts`)
   - Handles HTTP requests for job-related endpoints
   - Validates request data using Joi schemas
   - Delegates business logic to JobsService
   - Returns standardized API responses

2. **JobsService** (`src/services/jobs.service.ts`)
   - Implements job creation, retrieval, and management
   - Handles job recommendations and worker dashboard statistics
   - Interfaces with Supabase database
   - Manages job applications and bookmarks

3. **JobsRoutes** (`src/routes/jobs.routes.ts`)
   - Defines job-related API endpoints
   - Applies validation middleware to requests
   - Maps routes to controller methods

### New API Endpoints

- `GET /api/jobs/recommendations` - Get personalized job recommendations for workers
- `GET /api/users/dashboard-stats` - Get worker dashboard statistics

### Job Recommendations Features

1. **Personalized Recommendations**
   - Featured jobs (high-paying or urgent)
   - Nearby jobs based on worker location
   - Skill-matched jobs based on worker skills

2. **Recommendation Algorithm**
   - Combines worker profile data, skills, and location
   - Prioritizes high-paying and urgent jobs
   - Matches jobs to worker's skill set
   - Finds jobs in worker's geographic area

3. **Pagination and Filtering**
   - Configurable page size and offset
   - Location-based radius filtering
   - Category and skill-based filtering

### Worker Dashboard Statistics

1. **Application Tracking**
   - Total applications and status breakdown
   - Pending, accepted, rejected, and withdrawn counts

2. **Profile Analytics**
   - Profile completion percentage
   - Missing fields identification
   - Persona count and status

3. **Activity Metrics**
   - Last application date
   - Jobs viewed (today and this week)
   - Login activity tracking

4. **Earnings Overview**
   - Total earnings
   - Completed jobs count
   - Average rating and review count

5. **Recommendation Counts**
   - New jobs matching skills
   - Urgent jobs in area
   - Nearby job opportunities

### Database Tables Used

- `jobs` - Job listings and details
- `job_applications` - Worker job applications
- `job_bookmarks` - Saved jobs
- `worker_profiles` - Worker information for matching
- `worker_personas` - Worker skills and expertise

## Realtime Communication Module

### Components

1. **SocketManager** (`src/realtime/socket.ts`)
   - Initializes and configures Socket.IO server
   - Handles WebSocket authentication using JWT tokens
   - Manages user and conversation rooms
   - Sets up connection and disconnection event handlers
   - Provides broadcasting methods to user and conversation rooms
   - Integrates Socket.IO Admin UI for monitoring and debugging

2. **MessageGateway** (`src/realtime/message.gateway.ts`)
   - Handles WebSocket message events
   - Validates incoming requests using Joi schemas
   - Delegates business logic to MessagesService
   - Broadcasts real-time events to appropriate rooms
   - Implements real-time features: messaging, typing indicators, read receipts

3. **MessagesController** (`src/controllers/messages.controller.ts`)
   - Handles HTTP requests for conversations and messages
   - Validates request data using Joi schemas
   - Delegates business logic to MessagesService
   - Returns standardized API responses

4. **MessagesService** (`src/services/messages.service.ts`)
   - Implements business logic for conversations and messages
   - Interfaces with Supabase database
   - Handles conversation creation, message sending, and read status updates
   - Fetches user profile information for message sender details

5. **MessagesRoutes** (`src/routes/messages.routes.ts`)
   - Defines REST API endpoints for conversations and messages
   - Applies authentication and validation middleware
   - Maps routes to controller methods

### WebSocket API Events

#### Client to Server Events

- `messages:conversations:list` - Get user's conversations list with pagination
- `messages:conversation:get` - Get messages in a conversation with pagination
- `messages:message:send` - Send a new message in a conversation
- `messages:message:read` - Mark a message as read by its ID
- `messages:typing` - Send typing indicator for a conversation

#### Server to Client Events

- `message:new` - Notify clients of a new message in a conversation
- `message:read` - Notify clients when a message is marked as read
- `typing:start` - Notify clients when a user starts typing in a conversation
- `typing:stop` - Notify clients when a user stops typing in a conversation
- `connected` - Connection established successfully
- `error` - Connection or authentication errors
- `disconnect` - Connection terminated

### Real-Time Features Implementation

#### 1. Instant Messaging (`messages:message:send`)

**Flow**:
1. Client sends `messages:message:send` event with conversation ID and message text
2. Server validates request and delegates to MessagesService
3. MessagesService saves message to database with proper sender/receiver information
4. Server sends response via callback to original sender
5. Server broadcasts `message:new` event to conversation room (except sender)
6. All other participants receive real-time notification

**Implementation Details**:
- Messages are validated for content (500 character limit) and type
- Sender profile information (name, avatar) is fetched from database
- Message attachments are supported with file upload integration
- All participants in conversation room receive instant notifications

#### 2. Typing Indicators (`messages:typing`)

**Flow**:
1. Client sends `messages:typing` event with conversation ID
2. Server broadcasts `typing:start` event to conversation room (except sender)
3. Server automatically broadcasts `typing:stop` event after 3 seconds

**Implementation Details**:
- Automatic start/stop mechanism prevents stale typing indicators
- Efficient broadcasting only to relevant conversation participants
- No callback response required for typing events
- Visual feedback for better user experience

#### 3. Read Receipts (`messages:message:read`)

**Flow**:
1. Client sends `messages:message:read` event with message ID
2. Server validates request and updates message status in database
3. Server sends response via callback to original requester
4. Server broadcasts `message:read` event to conversation room
5. All other participants receive real-time read status notification

**Implementation Details**:
- Message status updates from 'sent' to 'read' in database
- Real-time synchronization of read status across all participants
- Efficient broadcast mechanism using conversation rooms

#### 4. Conversation Management

**Flow**:
1. Client joins conversation room via `messages:conversation:get`
2. Server adds user to conversation-specific Socket.IO room
3. User receives all real-time events for that conversation
4. On disconnect, user is automatically removed from rooms

**Implementation Details**:
- Dynamic room management for conversations
- Efficient resource cleanup on user disconnect
- Secure room access with proper authentication validation

### REST API Endpoints

- `GET /api/messages/conversations` - Get user's conversations with pagination
- `GET /api/messages/conversations/:id` - Get conversation details
- `GET /api/messages/conversations/:id/messages` - Get messages in a conversation with pagination
- `POST /api/messages/conversations/:id/messages` - Send a message
- `PUT /api/messages/:messageId/read` - Mark message as read

### Authentication

WebSocket authentication is performed using JWT tokens. Clients can provide the token in two ways:
1. As a handshake parameter: `socket.handshake.auth.token`
2. In the authorization header: `socket.handshake.headers.authorization`

The server verifies the token using the same JWT secret used for HTTP authentication.

### Rooms and Broadcasting

The system uses Socket.IO rooms for efficient message broadcasting:

1. **User Rooms**: Each user joins a private room identified by `user:${userId}`
2. **Conversation Rooms**: Users join rooms for each conversation they participate in, identified by `conversation:${conversationId}`

Messages are broadcast to appropriate rooms based on the event type:
- **New messages** are sent to the conversation room (except sender)
- **Typing indicators** are sent to the conversation room (except sender)
- **Read status updates** are broadcast to conversation room
- **Auto-stop typing** is automatically sent after 3 seconds

### Event Broadcasting Implementation

#### Successful Message Sending
When a message is successfully sent:
1. Server saves message to database with proper sender/receiver mapping
2. Server sends success response to sender via callback
3. Server broadcasts `message:new` event to conversation room (except sender)
4. All other participants receive real-time message notification

#### Message Read Status
When a message is marked as read:
1. Server updates message status in database
2. Server sends success response to requester via callback
3. Server broadcasts `message:read` event to conversation room
4. All other participants receive real-time read status notification

#### Typing Indicators
When typing is initiated:
1. Server broadcasts `typing:start` event to conversation room (except sender)
2. Server schedules `typing:stop` event to be sent after 3 seconds
3. All other participants receive real-time typing notifications

### Database Tables Used

- `conversations` - Conversation information between users with job references
- `messages` - Individual messages within conversations with sender/receiver information
- `message_attachments` - File attachments for messages with file metadata
- `user_profiles` - User information for sender/receiver name and avatar
- `jobs` - Job information for conversation context

### Error Handling and Recovery

The system implements comprehensive error handling:
- **Graceful degradation**: Missing callbacks don't crash the server
- **Validation errors**: Detailed error messages for malformed requests
- **Database errors**: Proper logging and user-friendly error responses
- **Connection errors**: Automatic reconnection handling and state recovery

### Performance Optimizations

- **Batch profile fetching**: Efficiently fetches user profiles for multiple messages
- **Room-based broadcasting**: Only sends events to relevant participants
- **Caching strategies**: Minimizes database queries for frequently accessed data
- **Connection pooling**: Efficient resource utilization for database connections

### Security Features

- **JWT token authentication**: Secure user authentication for WebSocket connections
- **Room access control**: Users can only join conversations they're participants in
- **Input validation**: Comprehensive validation of all incoming events
- **Rate limiting**: Prevents abuse of messaging endpoints
- **Data sanitization**: Automatic through Joi validation

### Monitoring and Debugging

- **Socket.IO Admin UI**: Built-in monitoring dashboard at https://admin.socket.io
- **Comprehensive logging**: Structured logging for all real-time events
- **Error tracking**: Detailed error reporting for debugging purposes
- **Connection metrics**: Monitoring of active connections and room memberships

### Scalability Considerations

- **Horizontal scaling**: Socket.IO supports clustering for load distribution
- **Memory management**: Efficient cleanup of disconnected sockets and rooms
- **Database optimization**: Indexing for frequent message queries
- **Resource limits**: Configurable limits for message size and attachment counts

### Testing Framework

The real-time communication module can be tested using:
1. **Postman Socket.IO client**: For manual testing and debugging
2. **Automated integration tests**: For continuous integration validation
3. **Load testing tools**: For performance validation under stress
4. **Socket.IO Admin UI**: For real-time monitoring and event inspection

### Known Limitations

1. **Message history**: Limited to 50 most recent messages per conversation by default
2. **Attachment size**: Constrained by file upload limits in configuration
3. **Connection persistence**: Sessions are not persisted across server restarts
4. **Presence indicators**: Basic online/offline status only

### Future Enhancements

1. **Message reactions**: Emoji reactions to messages
2. **Message editing**: Ability to edit sent messages
3. **Message deletion**: Ability to delete messages with appropriate permissions
4. **Advanced presence**: Detailed online/offline status with last seen information
5. **Push notifications**: Fallback for offline users via mobile push notifications
6. **File sharing**: Enhanced file sharing capabilities with preview support
7. **Voice/video messaging**: Multimedia communication features
8. **Group conversations**: Multi-user conversation support beyond 1-on-1

## Notifications Module

### Components

1. **NotificationsController** (`src/controllers/notifications.controller.ts`)
   - Handles HTTP requests for notification endpoints
   - Validates request data using Joi schemas
   - Delegates business logic to NotificationsService
   - Returns standardized API responses

2. **NotificationsService** (`src/services/notifications.service.ts`)
   - Implements notification management business logic
   - Handles notification retrieval, marking as read, and deletion
   - Manages device token registration for push notifications
   - Interfaces with Supabase database

3. **NotificationGateway** (`src/realtime/notification.gateway.ts`)
   - Handles WebSocket notification events
   - Validates incoming requests using Joi schemas
   - Delegates business logic to NotificationsService
   - Broadcasts real-time events to appropriate user rooms
   - Implements real-time features: notification broadcasting, count updates

4. **NotificationsRoutes** (`src/routes/notifications.routes.ts`)
   - Defines REST API endpoints for notifications
   - Applies authentication and validation middleware
   - Maps routes to controller methods

### REST API Endpoints

- `GET /api/notifications` - Get user's notifications with filtering and pagination
- `PUT /api/notifications/{id}/read` - Mark a specific notification as read
- `PUT /api/notifications/mark-all-read` - Mark all notifications as read
- `DELETE /api/notifications/{id}` - Delete a specific notification
- `POST /api/notifications/register-device` - Register device token for push notifications

### WebSocket API Events

#### Client to Server Events

- `notifications:list` - Get user's notifications list with filtering and pagination
- `notifications:mark-read` - Mark a specific notification as read
- `notifications:mark-all-read` - Mark all notifications as read
- `notifications:delete` - Delete a specific notification
- `notifications:register-device` - Register device token for push notifications

#### Server to Client Events

- `notifications:new` - Notify clients of a new notification
- `notifications:updated` - Notify clients when a notification is updated
- `notifications:deleted` - Notify clients when a notification is deleted
- `notifications:count-updated` - Notify clients when notification counts change

### Notification Features

#### 1. Notification Management

**Features**:
- Retrieve notifications with filtering by type and read status
- Pagination support for efficient loading
- Mark individual notifications as read
- Mark all notifications as read
- Delete notifications
- Get notification summary counts (total, unread, urgent)

#### 2. Real-time Notifications

**Features**:
- Real-time notification delivery via WebSocket
- Real-time notification count updates
- Notification deletion events
- Notification update events

#### 3. Device Registration

**Features**:
- Register device tokens for push notifications
- Support for iOS, Android, and web platforms
- Duplicate device token prevention

### Database Tables Used

- `notifications` - Notification information with user references
- `notification_devices` - Device tokens for push notifications

### Type System

#### Notification Types (`@ozgaar/types`)

- API request/response interfaces in `packages/types/src/server/notifications/`
- Database schema types in `packages/types/src/server/database/notifications.types.ts`
- WebSocket types in `packages/types/src/server/notifications/websocket.types.ts`

## Development Workflow

1. **Type-First Development**: Define types before implementation
2. **Controller → Service → Database**: Layered architecture
3. **Validation First**: Validate all inputs with Joi
4. **Error Handling**: Comprehensive error handling at all layers
5. **Testing**: Unit and integration tests (to be implemented)

