/**
 * Discovery Controller
 * Handles HTTP requests for poster worker discovery and search
 */

import { Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response';
import { DiscoveryService } from '@/services/discovery.service';
import { asyncHandler } from '@/middleware/error.middleware';
import type {
  IPosterDashboardRequest,
  IWorkerSearchRequest
} from '@ozgaar/types';

export class DiscoveryController {
  private discoveryService: DiscoveryService;

  constructor() {
    this.discoveryService = new DiscoveryService();
  }

  /**
   * Get poster dashboard with nearby workers
   */
  getPosterDashboard = asyncHandler(async (req: Request, res: Response) => {
    const posterId = req.user!.id;
    const requestData: IPosterDashboardRequest = req.query;

    const result = await this.discoveryService.getPosterDashboard(posterId, requestData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, result.data, result.message);
  });

  /**
   * Search workers with filters
   */
  searchWorkers = asyncHandler(async (req: Request, res: Response) => {
    const requestData: IWorkerSearchRequest = req.query;

    const result = await this.discoveryService.searchWorkers(requestData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, result.data, result.message);
  });
}
