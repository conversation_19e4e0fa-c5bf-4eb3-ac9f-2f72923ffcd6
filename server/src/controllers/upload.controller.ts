/**
 * Upload Controller
 * Handles HTTP requests for file uploads
 */

import { Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response';
import { UploadService } from '@/services/upload.service';
import { asyncHandler } from '@/middleware/error.middleware';
import type { IUploadImagesRequest } from '@ozgaar/types';

export class UploadController {
  private uploadService: UploadService;

  constructor() {
    this.uploadService = new UploadService();
  }

  /**
   * Upload images
   */
  uploadImages = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const files = req.files as Express.Multer.File[];
    
    // Parse request body
    const requestData: IUploadImagesRequest = {
      category: req.body.category,
      jobId: req.body.jobId || undefined,
      captions: req.body.captions ? JSON.parse(req.body.captions) : undefined,
      metadata: req.body.metadata ? JSON.parse(req.body.metadata) : undefined
    };

    const result = await this.uploadService.uploadImages(userId, files, requestData);

    if (!result.success) {
      let statusCode = 400;
      if (result.error === 'JOB_ACCESS_DENIED') statusCode = 403;
      if (result.error === 'PROCESSING_FAILED') statusCode = 422;
      
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {
      images: result.images
    }, result.message);
  });
}
