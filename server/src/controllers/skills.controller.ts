/**
 * Skills Controller
 * Handles skills and market rates requests
 */

import { Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response';
import { SkillsService } from '@/services/skills.service';
import { asyncHandler } from '@/middleware/error.middleware';
import type { IGetMarketRatesRequest } from '@ozgaar/types';

export class SkillsController {
  private skillsService: SkillsService;

  constructor() {
    this.skillsService = new SkillsService();
  }

  /**
   * Get skill categories
   */
  getCategories = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.skillsService.getCategories();

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, { categories: result.categories }, result.message);
  });

  /**
   * Get market rates for skills
   */
  getMarketRates = asyncHandler(async (req: Request, res: Response) => {
    const query: IGetMarketRatesRequest = req.query;

    const result = await this.skillsService.getMarketRates(query);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, { rates: result.rates }, result.message);
  });
}
