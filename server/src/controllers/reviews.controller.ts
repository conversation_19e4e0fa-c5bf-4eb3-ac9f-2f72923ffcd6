/**
 * Reviews Controller
 * Handles HTTP requests for reviews and ratings
 */

import { Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response';
import { ReviewsService } from '@/services/reviews.service';
import { asyncHandler } from '@/middleware/error.middleware';
import type {
  IGetReviewsRequest,
  IPostReviewRequest
} from '@ozgaar/types';

export class ReviewsController {
  private reviewsService: ReviewsService;

  constructor() {
    this.reviewsService = new ReviewsService();
  }

  /**
   * Get reviews received by user
   */
  getReceivedReviews = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const requestData: IGetReviewsRequest = req.query;

    const result = await this.reviewsService.getReceivedReviews(userId, requestData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      reviews: result.reviews,
      summary: result.summary,
      pagination: result.pagination
    }, result.message);
  });

  /**
   * Post a new review
   */
  postReview = asyncHandler(async (req: Request, res: Response) => {
    const reviewerId = req.user!.id;
    const requestData: IPostReviewRequest = req.body;

    const result = await this.reviewsService.postReview(reviewerId, requestData);

    if (!result.success) {
      let statusCode = 400;
      if (result.error === 'JOB_NOT_FOUND') statusCode = 404;
      if (result.error === 'JOB_NOT_COMPLETED') statusCode = 422;
      if (result.error === 'NOT_JOB_PARTICIPANT') statusCode = 403;
      if (result.error === 'INVALID_REVIEWEE') statusCode = 422;
      if (result.error === 'REVIEW_ALREADY_EXISTS') statusCode = 409;
      
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {
      review: result.review
    }, result.message);
  });
}
