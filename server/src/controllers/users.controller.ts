/**
 * Users Controller
 * Handles user profile and settings requests
 */

import { Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response';
import { UsersService } from '@/services/users.service';
import { JobsService } from '@/services/jobs.service';
import { asyncHandler } from '@/middleware/error.middleware';
import type {
  CreateWorkerProfileRequest,
  CreatePosterProfileRequest,
  UpdateUserProfileRequest,
  UpdateUserSettingsRequest,
  IModeSelectionRequest,
  IInitializeSettingsRequest,
  IWorkerPersonaRequest,
  IUpdatePosterProfileRequest
} from '@ozgaar/types';

export class UsersController {
  private usersService: UsersService;
  private jobsService: JobsService;

  constructor() {
    this.usersService = new UsersService();
    this.jobsService = new JobsService();
  }

  /**
   * Create worker profile
   */
  createWorkerProfile = asyncHandler(async (req: Request, res: Response) => {
    const profileData: CreateWorkerProfileRequest = req.body;
    const userId = req.user!.id;

    const result = await this.usersService.createWorkerProfile(userId, profileData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.created(res, result.profile, 'Worker profile created successfully');
  });

  /**
   * Create poster profile
   */
  createPosterProfile = asyncHandler(async (req: Request, res: Response) => {
    const profileData: CreatePosterProfileRequest = req.body;
    const userId = req.user!.id;

    const result = await this.usersService.createPosterProfile(userId, profileData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.created(res, result.profile, 'Poster profile created successfully');
  });

  /**
   * Update user profile
   */
  updateProfile = asyncHandler(async (req: Request, res: Response) => {
    const profileData: UpdateUserProfileRequest = req.body;
    const userId = req.user!.id;

    const result = await this.usersService.updateProfile(userId, profileData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, result.user, 'Profile updated successfully');
  });

  /**
   * Get user settings
   */
  getSettings = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const result = await this.usersService.getSettings(userId);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, result.settings, 'Settings retrieved successfully');
  });

  /**
   * Update user settings
   */
  updateSettings = asyncHandler(async (req: Request, res: Response) => {
    const settingsData: UpdateUserSettingsRequest = req.body;
    const userId = req.user!.id;

    const result = await this.usersService.updateSettings(userId, settingsData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, result.settings, 'Settings updated successfully');
  });

  /**
   * Upload user avatar
   */
  uploadAvatar = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    // TODO: Implement file upload logic with multer
    // For now, return a placeholder response
    return ResponseUtil.success(res, {
      avatarUrl: 'https://placeholder.com/avatar.jpg'
    }, 'Avatar upload endpoint - implementation pending');
  });

  /**
   * Select user mode (Worker or Poster) and initialize settings
   */
  selectMode = asyncHandler(async (req: Request, res: Response) => {
    const modeData: IModeSelectionRequest = req.body;
    const userId = req.user!.id;

    const result = await this.usersService.selectMode(userId, modeData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, result.user, result.message);
  });

  /**
   * Get user mode status
   */
  getModeStatus = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const result = await this.usersService.getModeStatus(userId);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, result.status, 'Mode status retrieved successfully');
  });

  /**
   * Initialize user settings with defaults
   */
  initializeSettings = asyncHandler(async (req: Request, res: Response) => {
    const settingsData: IInitializeSettingsRequest = req.body;
    const userId = req.user!.id;

    const result = await this.usersService.initializeSettings(userId, settingsData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.created(res, result.settings, 'Settings initialized successfully');
  });

  /**
   * Create worker persona
   */
  createWorkerPersona = asyncHandler(async (req: Request, res: Response) => {
    const personaData: IWorkerPersonaRequest = req.body;
    const userId = req.user!.id;

    const result = await this.usersService.createWorkerPersona(userId, personaData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.created(res, result.persona, 'Worker persona created successfully');
  });

  /**
   * Get worker personas
   */
  getWorkerPersonas = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const result = await this.usersService.getWorkerPersonas(userId);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, { personas: result.personas }, 'Worker personas retrieved successfully');
  });

  /**
   * Update poster profile
   */
  updatePosterProfile = asyncHandler(async (req: Request, res: Response) => {
    const profileData: IUpdatePosterProfileRequest = req.body;
    const userId = req.user!.id;

    const result = await this.usersService.updatePosterProfile(userId, profileData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, result.profile, 'Poster profile updated successfully');
  });

  /**
   * Get poster profile
   */
  getPosterProfile = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const result = await this.usersService.getPosterProfile(userId);

    if (!result.success) {
      if (result.message.includes('not found')) {
        return ResponseUtil.notFound(res, result.message);
      }
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, { profile: result.profile }, 'Poster profile retrieved successfully');
  });

  /**
   * Get poster statistics
   */
  getPosterStats = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const result = await this.usersService.getPosterStats(userId);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, { stats: result.stats }, 'Poster statistics retrieved successfully');
  });

  /**
   * Get worker dashboard statistics
   */
  getDashboardStats = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const result = await this.jobsService.getDashboardStats(userId);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, { stats: result.stats }, 'Dashboard statistics retrieved successfully');
  });
}