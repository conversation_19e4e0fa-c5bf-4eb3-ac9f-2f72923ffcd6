/**
 * Authentication Controller
 * Handles authentication-related requests
 */

import { Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response';
import { AuthService } from '@/services/auth.service';
import { asyncHandler } from '@/middleware/error.middleware';
import type { 
  SendOtpRequest, 
  VerifyOtpRequest, 
  ResendOtpRequest, 
  RefreshTokenRequest, 
  LogoutRequest 
} from '@ozgaar/types';

export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  /**
   * Send OTP to phone number
   */
  sendOtp = asyncHandler(async (req: Request, res: Response) => {
    const { phone }: SendOtpRequest = req.body;

    const result = await this.authService.sendOtp(phone);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      otpId: result.otpId
    }, result.message);
  });

  /**
   * Verify OTP and authenticate user
   */
  verifyOtp = asyncHandler(async (req: Request, res: Response) => {
    const { phone, otp, otpId }: VerifyOtpRequest = req.body;

    const result = await this.authService.verifyOtp(phone, otp, otpId);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      user: result.user,
      tokens: result.tokens,
      isNewUser: result.isNewUser
    }, result.message);
  });

  /**
   * Resend OTP
   */
  resendOtp = asyncHandler(async (req: Request, res: Response) => {
    const { phone, otpId }: ResendOtpRequest = req.body;

    const result = await this.authService.resendOtp(phone, otpId);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      otpId: result.otpId
    }, result.message);
  });

  /**
   * Refresh access token
   */
  refreshToken = asyncHandler(async (req: Request, res: Response) => {
    const { refreshToken }: RefreshTokenRequest = req.body;

    const result = await this.authService.refreshToken(refreshToken);

    if (!result.success) {
      return ResponseUtil.unauthorized(res, result.message);
    }

    return ResponseUtil.success(res, {
      tokens: result.tokens
    }, result.message);
  });

  /**
   * Logout user
   */
  logout = asyncHandler(async (req: Request, res: Response) => {
    const { refreshToken }: LogoutRequest = req.body;

    const result = await this.authService.logout(refreshToken);

    return ResponseUtil.success(res, null, result.message);
  });

  /**
   * Get current user information
   */
  getMe = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const user = await this.authService.getCurrentUser(userId);

    if (!user) {
      return ResponseUtil.notFound(res, 'User not found');
    }

    return ResponseUtil.success(res, { user }, 'User information retrieved successfully');
  });
}
