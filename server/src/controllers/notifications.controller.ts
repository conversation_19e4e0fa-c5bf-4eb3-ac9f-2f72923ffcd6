/**
 * Notifications Controller
 * Handles HTTP requests for notifications
 */

import { Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response';
import { NotificationsService } from '@/services/notifications.service';
import { asyncHandler } from '@/middleware/error.middleware';
import type {
  IGetNotificationsRequest,
  IRegisterDeviceTokenRequest
} from '@ozgaar/types';

export class NotificationsController {
  private notificationsService: NotificationsService;

  constructor() {
    this.notificationsService = new NotificationsService();
  }

  /**
   * Get user's notifications
   */
  getNotifications = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const requestData: IGetNotificationsRequest = req.query;

    const result = await this.notificationsService.getNotifications(userId, requestData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      notifications: result.notifications,
      pagination: result.pagination,
      summary: result.summary
    }, result.message);
  });

  /**
   * Mark notification as read
   */
  markNotificationAsRead = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const notificationId = req.params.id;

    const result = await this.notificationsService.markNotificationAsRead(userId, notificationId);

    if (!result.success) {
      const statusCode = result.error === 'NOTIFICATION_NOT_FOUND' ? 404 : 400;
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {}, result.message);
  });

  /**
   * Mark all notifications as read
   */
  markAllNotificationsAsRead = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const result = await this.notificationsService.markAllNotificationsAsRead(userId);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      updatedCount: result.updatedCount
    }, result.message);
  });

  /**
   * Delete notification
   */
  deleteNotification = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const notificationId = req.params.id;

    const result = await this.notificationsService.deleteNotification(userId, notificationId);

    if (!result.success) {
      const statusCode = result.error === 'NOTIFICATION_NOT_FOUND' ? 404 : 400;
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {}, result.message);
  });

  /**
   * Register device token for push notifications
   */
  registerDeviceToken = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const requestData: IRegisterDeviceTokenRequest = req.body;

    const result = await this.notificationsService.registerDeviceToken(userId, requestData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {}, result.message);
  });
}
