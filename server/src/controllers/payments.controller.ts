/**
 * Payments Controller
 * Handles HTTP requests for payments, earnings, and exports
 */

import { Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response';
import { PaymentsService } from '@/services/payments.service';
import { asyncHandler } from '@/middleware/error.middleware';
import type {
  IPaymentHistoryRequest,
  IEarningsRequest,
  IPaymentExportRequest,
  IPaymentProcessRequest
} from '@ozgaar/types';

export class PaymentsController {
  private paymentsService: PaymentsService;

  constructor() {
    this.paymentsService = new PaymentsService();
  }

  /**
   * Get payment history
   */
  getPaymentHistory = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const requestData: IPaymentHistoryRequest = req.query;

    const result = await this.paymentsService.getPaymentHistory(userId, requestData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      payments: result.payments,
      pagination: result.pagination,
      summary: result.summary
    }, result.message);
  });

  /**
   * Get earnings data
   */
  getEarnings = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const requestData: IEarningsRequest = req.query;

    const result = await this.paymentsService.getEarnings(userId, requestData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      earnings: result.earnings
    }, result.message);
  });

  /**
   * Export payment data
   */
  exportPayments = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const requestData: IPaymentExportRequest = req.query;

    const result = await this.paymentsService.exportPayments(userId, requestData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      exportUrl: result.exportUrl,
      expiresAt: result.expiresAt
    }, result.message);
  });

  /**
   * Process payment (future implementation)
   */
  processPayment = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const requestData: IPaymentProcessRequest = req.body;

    const result = await this.paymentsService.processPayment(userId, requestData);

    if (!result.success) {
      let statusCode = 400;
      if (result.error === 'NOT_IMPLEMENTED') statusCode = 501;
      
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {
      paymentId: result.paymentId,
      transactionId: result.transactionId,
      status: result.status,
      gatewayResponse: result.gatewayResponse
    }, result.message);
  });
}
