/**
 * Jobs Controller
 * Handles job-related requests
 */

import { Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response';
import { JobsService } from '@/services/jobs.service';
import { asyncHandler } from '@/middleware/error.middleware';
import type {
  CreateJobRequest,
  BookmarkJobRequest,
  IJobRecommendationRequest,
  IJobSearchRequest,
  IJobTitleSuggestionsRequest,
  IJobApplicantsRequest,
  IMyJobsRequest,
  IJobStatusRequest,
  IStartWorkRequest,
  IUpdateProgressRequest,
  ICompleteJobRequest,
  IAssignWorkerRequest
} from '@ozgaar/types';

export class JobsController {
  private jobsService: JobsService;

  constructor() {
    this.jobsService = new JobsService();
  }

  /**
   * Create a new job
   */
  createJob = asyncHandler(async (req: Request, res: Response) => {
    const jobData: CreateJobRequest = req.body;
    const posterId = req.user!.id;

    const result = await this.jobsService.createJob(posterId, jobData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.created(res, result.job, 'Job created successfully');
  });

  /**
   * Get job by ID
   */
  getJobById = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const userId = req.user?.id;

    const result = await this.jobsService.getJobById(id, userId);

    if (!result.success) {
      if (result.message.includes('not found')) {
        return ResponseUtil.notFound(res, result.message);
      }
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, { job: result.job }, 'Job retrieved successfully');
  });

  /**
   * Bookmark/unbookmark a job
   */
  bookmarkJob = asyncHandler(async (req: Request, res: Response) => {
    const jobId = req.params.id;
    const userId = req.user!.id;

    const result = await this.jobsService.toggleBookmark(userId, jobId);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      isBookmarked: result.isBookmarked
    }, result.message);
  });

  /**
   * Get job recommendations for worker
   */
  getRecommendations = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const request: IJobRecommendationRequest = req.query;

    const result = await this.jobsService.getRecommendations(userId, request);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      recommendations: result.recommendations,
      pagination: result.pagination
    }, result.message);
  });

  /**
   * Search jobs with filters
   */
  searchJobs = asyncHandler(async (req: Request, res: Response) => {
    const searchParams: IJobSearchRequest = req.query;

    const result = await this.jobsService.searchJobs(searchParams);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      jobs: result.jobs,
      pagination: result.pagination,
      searchMeta: result.searchMeta
    }, result.message);
  });

  /**
   * Get job filters with counts
   */
  getJobFilters = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.jobsService.getJobFilters();

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      filters: result.filters,
      totalJobsCount: result.totalJobsCount
    }, result.message);
  });

  /**
   * Get job categories
   */
  getJobCategories = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.jobsService.getJobCategories();

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      categories: result.categories
    }, result.message);
  });

  /**
   * Get job title suggestions
   */
  getJobTitleSuggestions = asyncHandler(async (req: Request, res: Response) => {
    const requestData: IJobTitleSuggestionsRequest = req.query;

    const result = await this.jobsService.getJobTitleSuggestions(requestData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      suggestions: result.suggestions
    }, result.message);
  });

  /**
   * Get job applicants
   */
  getJobApplicants = asyncHandler(async (req: Request, res: Response) => {
    const jobId = req.params.id;
    const posterId = req.user!.id;
    const requestData: IJobApplicantsRequest = req.query;

    const result = await this.jobsService.getJobApplicants(jobId, posterId, requestData);

    if (!result.success) {
      const statusCode = result.error === 'JOB_NOT_FOUND' ? 404 : 400;
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {
      applicants: result.applicants,
      pagination: result.pagination,
      summary: result.summary
    }, result.message);
  });

  /**
   * Get my jobs (poster's own jobs)
   */
  getMyJobs = asyncHandler(async (req: Request, res: Response) => {
    const posterId = req.user!.id;
    const requestData: IMyJobsRequest = req.query;

    const result = await this.jobsService.getMyJobs(posterId, requestData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      jobs: result.jobs,
      pagination: result.pagination,
      summary: result.summary
    }, result.message);
  });

  /**
   * Get job status and timeline
   */
  getJobStatus = asyncHandler(async (req: Request, res: Response) => {
    const jobId = req.params.id;
    const userId = req.user!.id;

    const result = await this.jobsService.getJobStatus(jobId, userId);

    if (!result.success) {
      const statusCode = result.error === 'JOB_NOT_FOUND' ? 404 :
                        result.error === 'ACCESS_DENIED' ? 403 : 400;
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {
      jobStatus: result.jobStatus
    }, result.message);
  });

  /**
   * Start work on a job
   */
  startWork = asyncHandler(async (req: Request, res: Response) => {
    const jobId = req.params.id;
    const workerId = req.user!.id;

    const result = await this.jobsService.startWork(jobId, workerId);

    if (!result.success) {
      let statusCode = 400;
      if (result.error === 'JOB_NOT_FOUND') statusCode = 404;
      if (result.error === 'WORK_ALREADY_STARTED') statusCode = 409;
      if (result.error === 'INVALID_JOB_STATUS') statusCode = 422;

      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {
      startedAt: result.startedAt
    }, result.message);
  });

  /**
   * Update job progress
   */
  updateProgress = asyncHandler(async (req: Request, res: Response) => {
    const jobId = req.params.id;
    const workerId = req.user!.id;
    const requestData: IUpdateProgressRequest = req.body;

    const result = await this.jobsService.updateProgress(jobId, workerId, requestData);

    if (!result.success) {
      let statusCode = 400;
      if (result.error === 'JOB_NOT_FOUND') statusCode = 404;
      if (result.error === 'WORK_NOT_STARTED') statusCode = 422;
      if (result.error === 'INVALID_JOB_STATUS') statusCode = 422;

      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {
      updatedAt: result.updatedAt
    }, result.message);
  });

  /**
   * Complete job
   */
  completeJob = asyncHandler(async (req: Request, res: Response) => {
    const jobId = req.params.id;
    const workerId = req.user!.id;
    const requestData: ICompleteJobRequest = req.body;

    const result = await this.jobsService.completeJob(jobId, workerId, requestData);

    if (!result.success) {
      let statusCode = 400;
      if (result.error === 'JOB_NOT_FOUND') statusCode = 404;
      if (result.error === 'WORK_NOT_STARTED') statusCode = 422;
      if (result.error === 'JOB_ALREADY_COMPLETED') statusCode = 409;
      if (result.error === 'INVALID_JOB_STATUS') statusCode = 422;

      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {
      completedAt: result.completedAt
    }, result.message);
  });

  /**
   * Assign worker to job
   */
  assignWorker = asyncHandler(async (req: Request, res: Response) => {
    const posterId = req.user!.id;
    const jobId = req.params.id;
    const requestData: IAssignWorkerRequest = req.body;

    const result = await this.jobsService.assignWorker(jobId, posterId, requestData);

    if (!result.success) {
      let statusCode = 400;
      if (result.error === 'JOB_NOT_FOUND') statusCode = 404;
      if (result.error === 'WORKER_NOT_FOUND') statusCode = 404;
      if (result.error === 'APPLICATION_NOT_FOUND') statusCode = 404;
      if (result.error === 'INVITE_NOT_FOUND') statusCode = 404;
      if (result.error === 'INVALID_JOB_STATUS') statusCode = 422;
      if (result.error === 'WORKER_ALREADY_ASSIGNED') statusCode = 409;
      if (result.error === 'WORKER_NOT_AVAILABLE') statusCode = 422;
      if (result.error === 'INVALID_APPLICATION_STATUS') statusCode = 422;
      if (result.error === 'INVALID_INVITE_STATUS') statusCode = 422;

      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {
      job: result.job
    }, result.message);
  });
}
