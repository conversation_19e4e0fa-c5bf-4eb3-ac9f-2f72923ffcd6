/**
 * Support Controller
 * Handles HTTP requests for help and support operations
 */

import { Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response';
import { SupportService } from '@/services/support.service';
import { asyncHandler } from '@/middleware/error.middleware';
import type {
  ICreateSupportTicketRequest
} from '@ozgaar/types';

export class SupportController {
  private supportService: SupportService;

  constructor() {
    this.supportService = new SupportService();
  }

  /**
   * Get FAQs grouped by category
   */
  getFAQs = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.supportService.getFAQs();

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 500);
    }

    return ResponseUtil.success(res, {
      categories: result.categories
    }, result.message);
  });

  /**
   * Create support ticket
   */
  createSupportTicket = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.id; // Optional - can be used by anonymous users
    const ticketData: ICreateSupportTicketRequest = req.body;

    const result = await this.supportService.createSupportTicket(userId, ticketData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      ticket: result.ticket
    }, result.message);
  });

  /**
   * Get contact information
   */
  getContactInfo = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.supportService.getContactInfo();

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 500);
    }

    return ResponseUtil.success(res, {
      contactInfo: result.contactInfo
    }, result.message);
  });
}
