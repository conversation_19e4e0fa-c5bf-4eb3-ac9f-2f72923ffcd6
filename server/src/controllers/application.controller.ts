/**
 * Application Controller
 * Handles HTTP requests for job application operations
 */

import { Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response';
import { ApplicationService } from '@/services/application.service';
import { asyncHandler } from '@/middleware/error.middleware';
import type {
  IApplyToJobRequest,
  IMyApplicationsRequest
} from '@ozgaar/types';

export class ApplicationController {
  private applicationService = new ApplicationService();

  /**
   * Apply to a job
   */
  applyToJob = asyncHandler(async (req: Request, res: Response) => {
    const jobId = req.params.id;
    const userId = req.user!.id;
    const applicationData: IApplyToJobRequest = req.body;

    const result = await this.applicationService.applyToJob(userId, jobId, applicationData);

    if (!result.success) {
      const statusCode = result.error === 'JOB_NOT_FOUND' ? 404 : 
                        result.error === 'ALREADY_APPLIED' ? 409 :
                        result.error === 'DAILY_LIMIT_REACHED' ? 429 : 400;
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.created(res, result.application, result.message);
  });

  /**
   * Get application limits
   */
  getApplicationLimits = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const result = await this.applicationService.getApplicationLimits(userId);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, { limits: result.limits }, result.message);
  });

  /**
   * Get my applications
   */
  getMyApplications = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const requestData: IMyApplicationsRequest = req.query;

    const result = await this.applicationService.getMyApplications(userId, requestData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      applications: result.applications,
      pagination: result.pagination,
      summary: result.summary
    }, result.message);
  });

  /**
   * Withdraw application
   */
  withdrawApplication = asyncHandler(async (req: Request, res: Response) => {
    const applicationId = req.params.id;
    const userId = req.user!.id;

    const result = await this.applicationService.withdrawApplication(userId, applicationId);

    if (!result.success) {
      const statusCode = result.error === 'APPLICATION_NOT_FOUND' ? 404 : 400;
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, result.application, result.message);
  });
}
