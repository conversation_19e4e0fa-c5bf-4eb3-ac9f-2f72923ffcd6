/**
 * Messages Controller
 * Handles HTTP requests for conversations and messages
 */

import { Request, Response } from 'express';
import { ResponseUtil } from '@/utils/response';
import { MessagesService } from '@/services/messages.service';
import { asyncHandler } from '@/middleware/error.middleware';
import type {
  IGetConversationsRequest,
  IGetConversationRequest,
  ISendMessageRequest,
  IGetMessagesRequest,
  IMarkMessageReadRequest
} from '@ozgaar/types';

export class MessagesController {
  private messagesService: MessagesService;

  constructor() {
    this.messagesService = new MessagesService();
  }

  /**
   * Get user's conversations
   */
  getConversations = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const requestData: IGetConversationsRequest = req.query;

    const result = await this.messagesService.getConversations(userId, requestData);

    if (!result.success) {
      return ResponseUtil.error(res, result.message, result.error, 400);
    }

    return ResponseUtil.success(res, {
      conversations: result.conversations,
      pagination: result.pagination
    }, result.message);
  });

  /**
   * Get conversation details
   */
  getConversation = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const conversationId = req.params.id;

    const result = await this.messagesService.getConversation(userId, conversationId);

    if (!result.success) {
      const statusCode = result.error === 'CONVERSATION_NOT_FOUND' ? 404 : 400;
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {
      conversation: result.conversation
    }, result.message);
  });

  /**
   * Get messages in a conversation
   */
  getMessages = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const conversationId = req.params.id;
    const requestData: IGetMessagesRequest = req.query;

    const result = await this.messagesService.getMessages(userId, conversationId, requestData);

    if (!result.success) {
      const statusCode = result.error === 'CONVERSATION_NOT_FOUND' ? 404 : 400;
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {
      messages: result.messages,
      pagination: result.pagination
    }, result.message);
  });

  /**
   * Send a message
   */
  sendMessage = asyncHandler(async (req: Request, res: Response) => {
    const senderId = req.user!.id;
    const conversationId = req.params.id;
    const requestData: ISendMessageRequest = req.body;

    const result = await this.messagesService.sendMessage(senderId, conversationId, requestData);

    if (!result.success) {
      let statusCode = 400;
      if (result.error === 'CONVERSATION_NOT_FOUND') statusCode = 404;
      if (result.error === 'MESSAGE_TOO_LONG') statusCode = 422;
      
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {
      message: result.messageData
    }, result.message);
  });

  /**
   * Mark message as read
   */
  markMessageAsRead = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const messageId = req.params.messageId;

    const result = await this.messagesService.markMessageAsRead(userId, messageId);

    if (!result.success) {
      const statusCode = result.error === 'MESSAGE_NOT_FOUND' ? 404 : 400;
      return ResponseUtil.error(res, result.message, result.error, statusCode);
    }

    return ResponseUtil.success(res, {}, result.message);
  });
}
