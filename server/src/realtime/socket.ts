/**
 * Socket.IO Server Setup
 * Handles WebSocket connections and authentication
 */

import { Server as HttpServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import jwt from 'jsonwebtoken';
import { logger } from '@/utils/logger';
import { MessageGateway } from './message.gateway';
import { NotificationGateway } from './notification.gateway';
import { databaseConfig } from '@/config/database.config';
import type { ISocketAuthData, ISocketUser, ISocketEvents } from '@ozgaar/types';
import { instrument } from '@socket.io/admin-ui';

export class SocketManager {
  private io: SocketIOServer;
  private messageGateway: MessageGateway;
  private notificationGateway: NotificationGateway;
  private db = databaseConfig.getClient();

  constructor(server: HttpServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: [process.env.CORS_ORIGIN || '*', "https://admin.socket.io"],
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.messageGateway = new MessageGateway(this.io, this);
    this.messageGateway.setSocketManager(this);
    this.notificationGateway = new NotificationGateway(this.io);
    this.setupMiddleware();
    this.setupConnectionHandlers();
    
    // Add the Admin UI instrumentation
    instrument(this.io, {
      auth: {
        type: "basic",
        username: "admin",
        password: "$2a$10$lbYPObnK58kgslsKgQBbGueU3scTVnjr5cMpTzxW4WklxO5orMFLG" // "SECRET" encrypted with bcrypt
      },
      mode: "development",
    });
  }

  /**
   * Setup authentication middleware
   */
  private setupMiddleware() {
    this.io.use(async (socket, next) => {
      try {
        // Get token from handshake auth or headers
        const token = socket.handshake.auth?.token || 
                     socket.handshake.headers?.authorization?.replace('Bearer ', '');

        if (!token) {
          return next(new Error('Authentication token required'));
        }

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
        
        if (!decoded || !decoded.userId) {
          return next(new Error('Invalid authentication token'));
        }

        // Get user profile from database
        const { data: userProfile, error: profileError } = await this.db!
          .from('user_profiles')
          .select('full_name, avatar_url')
          .eq('user_id', decoded.userId)
          .single();

        if (profileError || !userProfile) {
          logger.error('Failed to get user profile for socket auth:', profileError);
          return next(new Error('Failed to get user profile'));
        }

        // Attach user info to socket
        const user: ISocketUser = {
          id: decoded.userId,
          name: (userProfile as any).full_name || 'Unknown User',
          avatar: (userProfile as any).avatar_url
        };

        socket.data.user = user;
        logger.info(`Socket authenticated for user: ${user.id}`);
        
        next();
      } catch (error) {
        logger.error('Socket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });
  }

  /**
   * Setup connection event handlers
   */
  private setupConnectionHandlers() {
    this.io.on('connection', async (socket) => {
      const user = socket.data.user as ISocketUser;
      logger.info(`User connected: ${user.id} (${socket.id})`);

      try {
        // Join user-specific room
        await socket.join(`user:${user.id}`);
        
        // Join conversation rooms for this user
        await this.joinUserConversations(socket, user.id);

        // Setup message event handlers
        this.messageGateway.setupMessageHandlers(socket);

        // Setup notification event handlers
        this.notificationGateway.setupNotificationHandlers(socket);

        // Handle disconnection
        socket.on('disconnect', (reason) => {
          logger.info(`User disconnected: ${user.id} (${socket.id}) - Reason: ${reason}`);
        });

        // Handle connection errors
        socket.on('error', (error) => {
          logger.error(`Socket error for user ${user.id}:`, error);
        });

        // Send connection success
        socket.emit('connected', {
          success: true,
          message: 'Connected successfully',
          userId: user.id
        });

      } catch (error) {
        logger.error(`Error setting up socket for user ${user.id}:`, error);
        socket.emit('error', {
          success: false,
          message: 'Failed to setup connection',
          error: 'CONNECTION_SETUP_ERROR'
        });
      }
    });
  }

  /**
   * Join user to their conversation rooms
   */
  private async joinUserConversations(socket: any, userId: string) {
    try {
      // This would typically query the database to get user's conversations
      // For now, we'll join rooms dynamically when conversations are accessed
      logger.info(`Setting up conversation rooms for user: ${userId}`);
    } catch (error) {
      logger.error(`Error joining conversation rooms for user ${userId}:`, error);
    }
  }

  /**
   * Get Socket.IO server instance
   */
  public getIO(): SocketIOServer {
    return this.io;
  }

  /**
   * Get notification gateway instance
   */
  public getNotificationGateway(): NotificationGateway {
    return this.notificationGateway;
  }

  /**
   * Emit to user room
   */
  public emitToUser(userId: string, event: string, data: any) {
    this.io.to(`user:${userId}`).emit(event, data);
  }

  /**
   * Emit to conversation room
   */
  public emitToConversation(conversationId: string, event: string, data: any) {
    this.io.to(`conversation:${conversationId}`).emit(event, data);
  }

  /**
   * Join socket to conversation room
   */
  public async joinConversation(socketId: string, conversationId: string) {
    const socket = this.io.sockets.sockets.get(socketId);
    if (socket) {
      await socket.join(`conversation:${conversationId}`);
      logger.info(`Socket ${socketId} joined conversation: ${conversationId}`);
    }
  }

  /**
   * Leave conversation room
   */
  public async leaveConversation(socketId: string, conversationId: string) {
    const socket = this.io.sockets.sockets.get(socketId);
    if (socket) {
      await socket.leave(`conversation:${conversationId}`);
      logger.info(`Socket ${socketId} left conversation: ${conversationId}`);
    }
  }
}

/**
 * Initialize Socket.IO server
 */
export function initSocket(server: HttpServer): SocketManager {
  const socketManager = new SocketManager(server);
  logger.info('🔌 Socket.IO server initialized');
  return socketManager;
}
