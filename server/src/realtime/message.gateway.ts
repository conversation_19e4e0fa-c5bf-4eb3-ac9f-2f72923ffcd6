/**
 * Message Gateway
 * Handles WebSocket message events
 */

import { Server as SocketIOServer, Socket } from 'socket.io';
import Joi from 'joi';
import { logger } from '@/utils/logger';
import { MessagesService } from '@/services/messages.service';
import { SocketManager } from './socket'; // Add this import
import type {
  ISocketEvents,
  ISocketUser,
  IGetConversationsRequest,
  ISocketConversationsListResponse,
  ISocketConversationGetRequest,
  ISocketConversationGetResponse,
  ISocketMessageSendRequest,
  ISocketMessageSendResponse,
  ISocketMessageReadRequest,
  ISocketMessageReadResponse,
  ISocketTypingRequest,
  ISocketNewMessageEvent,
  ISocketMessageReadEvent,
  ISocketTypingEvent
} from '@ozgaar/types';

export class MessageGateway {
  private io: SocketIOServer;
  private messagesService: MessagesService;
  private socketManager: SocketManager | null = null; // Add this property

  // Add a setter for the SocketManager
  public setSocketManager(socketManager: SocketManager) {
    this.socketManager = socketManager;
  }

  // Validation schemas
  private conversationsListSchema = Joi.object({
    page: Joi.number().positive().default(1),
    limit: Joi.number().positive().max(100).default(20)
  });

  private conversationGetSchema = Joi.object({
    conversationId: Joi.string().uuid().required(),
    page: Joi.number().positive().default(1),
    limit: Joi.number().positive().max(100).default(50)
  });

  private messageSendSchema = Joi.object({
    conversationId: Joi.string().uuid().required(),
    text: Joi.string().required().max(500),
    messageType: Joi.string().valid('text', 'image', 'document').default('text')
  });

  private messageReadSchema = Joi.object({
    messageId: Joi.string().uuid().required()
  });

  private typingSchema = Joi.object({
    conversationId: Joi.string().uuid().required()
  });

  constructor(io: SocketIOServer, socketManager?: SocketManager) {
    this.io = io;
    this.messagesService = new MessagesService();
    if (socketManager) {
      this.socketManager = socketManager;
    }
  }

  /**
   * Setup message event handlers for a socket
   */
  public setupMessageHandlers(socket: Socket) {
    const user = socket.data.user as ISocketUser;

    // Get conversations list
    socket.on('messages:conversations:list', async (data: IGetConversationsRequest, callback) => {
      try {
        // Check if callback is provided and is a function
        if (typeof callback !== 'function') {
          logger.warn('Socket conversations:list called without callback');
          return;
        }

        const { error, value } = this.conversationsListSchema.validate(data);
        if (error) {
          return callback({
            success: false,
            message: 'Validation error',
            error: error.details[0].message
          });
        }

        const result = await this.messagesService.getConversations(user.id, value);
        
        const response: ISocketConversationsListResponse = {
          success: result.success,
          message: result.message,
          error: result.error,
          conversations: result.conversations,
          pagination: result.pagination
        };

        callback(response);
      } catch (error) {
        logger.error('Socket conversations:list error:', error);
        if (typeof callback === 'function') {
          callback({
            success: false,
            message: 'Internal server error',
            error: 'INTERNAL_ERROR'
          });
        }
      }
    });

    // Get conversation messages
    socket.on('messages:conversation:get', async (data: ISocketConversationGetRequest, callback) => {
      try {
        // Check if callback is provided and is a function
        if (typeof callback !== 'function') {
          logger.warn('Socket conversation:get called without callback');
          return;
        }

        const { error, value } = this.conversationGetSchema.validate(data);
        if (error) {
          return callback({
            success: false,
            message: 'Validation error',
            error: error.details[0].message
          });
        }

        // Join conversation room
        await socket.join(`conversation:${value.conversationId}`);

        const result = await this.messagesService.getMessages(user.id, value.conversationId, {
          page: value.page,
          limit: value.limit
        });

        const response: ISocketConversationGetResponse = {
          success: result.success,
          message: result.message,
          error: result.error,
          messages: result.messages,
          pagination: result.pagination
        };

        callback(response);
      } catch (error) {
        logger.error('Socket conversation:get error:', error);
        if (typeof callback === 'function') {
          callback({
            success: false,
            message: 'Internal server error',
            error: 'INTERNAL_ERROR'
          });
        }
      }
    });

    // Send message
    socket.on('messages:message:send', async (data: ISocketMessageSendRequest, callback) => {
      try {
        // Check if callback is provided and is a function
        if (typeof callback !== 'function') {
          logger.warn('Socket message:send called without callback');
          return;
        }

        const { error, value } = this.messageSendSchema.validate(data);
        if (error) {
          return callback({
            success: false,
            message: 'Validation error',
            error: error.details[0].message
          });
        }

        const result = await this.messagesService.sendMessage(user.id, value.conversationId, {
          text: value.text,
          messageType: value.messageType
        });

        const response: ISocketMessageSendResponse = {
          success: result.success,
          message: result.message,
          error: result.error,
          messageData: result.messageData
        };

        callback(response);

        // Broadcast the new message to the conversation room if successful
        if (result.success && result.messageData && this.socketManager) {
          const newMessageEvent: ISocketNewMessageEvent = {
            conversationId: value.conversationId,
            message: result.messageData
          };

          // Emit to conversation room (this will notify all other participants)
          this.socketManager.emitToConversation(value.conversationId, 'message:new', newMessageEvent);
        }
      } catch (error) {
        logger.error('Socket message:send error:', error);
        if (typeof callback === 'function') {
          callback({
            success: false,
            message: 'Internal server error',
            error: 'INTERNAL_ERROR'
          });
        }
      }
    });

    // Mark message as read
    socket.on('messages:message:read', async (data: ISocketMessageReadRequest, callback) => {
      try {
        // Check if callback is provided and is a function
        if (typeof callback !== 'function') {
          logger.warn('Socket message:read called without callback');
          return;
        }

        const { error, value } = this.messageReadSchema.validate(data);
        if (error) {
          return callback({
            success: false,
            message: 'Validation error',
            error: error.details[0].message
          });
        }

        const result = await this.messagesService.markMessageAsRead(user.id, value.messageId);

        const response: ISocketMessageReadResponse = {
          success: result.success,
          message: result.message,
          error: result.error
        };

        callback(response);

        // Broadcast the read status to the conversation room if successful
        if (result.success && this.socketManager && result.messageData) {
          const readEvent: ISocketMessageReadEvent = {
            messageId: value.messageId,
            conversationId: result.messageData.conversationId,
            readBy: user.id,
            readAt: new Date().toISOString()
          };

          // Emit to conversation room (this will notify all other participants)
          this.socketManager.emitToConversation(result.messageData.conversationId, 'message:read', readEvent);
        }
      } catch (error) {
        logger.error('Socket message:read error:', error);
        if (typeof callback === 'function') {
          callback({
            success: false,
            message: 'Internal server error',
            error: 'INTERNAL_ERROR'
          });
        }
      }
    });

    // Typing indicator
    socket.on('messages:typing', async (data: ISocketTypingRequest) => {
      try {
        const { error, value } = this.typingSchema.validate(data);
        if (error) {
          logger.error('Typing validation error:', error);
          return;
        }

        const typingEvent: ISocketTypingEvent = {
          conversationId: value.conversationId,
          userId: user.id,
          userName: user.name
        };

        // Broadcast typing to conversation room (except sender)
        socket.to(`conversation:${value.conversationId}`).emit('typing:start', typingEvent);

        // Auto-stop typing after 3 seconds
        setTimeout(() => {
          socket.to(`conversation:${value.conversationId}`).emit('typing:stop', typingEvent);
        }, 3000);

      } catch (error) {
        logger.error('Socket typing error:', error);
      }
    });
  }
}
