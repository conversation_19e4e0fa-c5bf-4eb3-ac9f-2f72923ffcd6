/**
 * Notification Gateway
 * Handles WebSocket notification events
 */

import { Server as SocketIOServer, Socket } from 'socket.io';
import Joi from 'joi';
import { logger } from '@/utils/logger';
import { NotificationsService } from '@/services/notifications.service';
import type {
  ISocketUser,
  ISocketNotificationsListRequest,
  ISocketNotificationsListResponse,
  ISocketNotificationMarkReadRequest,
  ISocketNotificationMarkReadResponse,
  ISocketNotificationMarkAllReadRequest,
  ISocketNotificationMarkAllReadResponse,
  ISocketNotificationDeleteRequest,
  ISocketNotificationDeleteResponse,
  ISocketRegisterDeviceRequest,
  ISocketRegisterDeviceResponse,
  ISocketNewNotificationEvent,
  ISocketNotificationUpdatedEvent,
  ISocketNotificationDeletedEvent,
  ISocketNotificationCountUpdatedEvent
} from '@ozgaar/types';

export class NotificationGateway {
  private io: SocketIOServer;
  private notificationsService: NotificationsService;

  // Validation schemas
  private notificationsListSchema = Joi.object({
    type: Joi.string().max(50).optional(),
    isRead: Joi.boolean().optional(),
    page: Joi.number().positive().default(1),
    limit: Joi.number().positive().max(100).default(20)
  });

  private notificationMarkReadSchema = Joi.object({
    notificationId: Joi.string().uuid().required()
  });

  private notificationDeleteSchema = Joi.object({
    notificationId: Joi.string().uuid().required()
  });

  private registerDeviceSchema = Joi.object({
    deviceToken: Joi.string().required().min(10),
    platform: Joi.string().valid('ios', 'android', 'web').required()
  });

  constructor(io: SocketIOServer) {
    this.io = io;
    this.notificationsService = new NotificationsService();
  }

  /**
   * Setup notification event handlers for a socket
   */
  public setupNotificationHandlers(socket: Socket) {
    const user = socket.data.user as ISocketUser;

    // Get notifications list
    socket.on('notifications:list', async (data: ISocketNotificationsListRequest, callback) => {
      try {
        const { error, value } = this.notificationsListSchema.validate(data);
        if (error) {
          return callback({
            success: false,
            message: 'Validation error',
            error: error.details[0].message
          });
        }

        const result = await this.notificationsService.getNotifications(user.id, value);
        
        const response: ISocketNotificationsListResponse = {
          success: result.success,
          message: result.message,
          error: result.error,
          notifications: result.notifications,
          pagination: result.pagination,
          summary: result.summary
        };

        callback(response);
      } catch (error) {
        logger.error('Socket notifications:list error:', error);
        callback({
          success: false,
          message: 'Internal server error',
          error: 'INTERNAL_ERROR'
        });
      }
    });

    // Mark notification as read
    socket.on('notifications:mark-read', async (data: ISocketNotificationMarkReadRequest, callback) => {
      try {
        const { error, value } = this.notificationMarkReadSchema.validate(data);
        if (error) {
          return callback({
            success: false,
            message: 'Validation error',
            error: error.details[0].message
          });
        }

        const result = await this.notificationsService.markNotificationAsRead(user.id, value.notificationId);

        const response: ISocketNotificationMarkReadResponse = {
          success: result.success,
          message: result.message,
          error: result.error
        };

        callback(response);

        // Broadcast notification count update
        if (result.success) {
          await this.broadcastNotificationCountUpdate(user.id);
        }
      } catch (error) {
        logger.error('Socket notifications:mark-read error:', error);
        callback({
          success: false,
          message: 'Internal server error',
          error: 'INTERNAL_ERROR'
        });
      }
    });

    // Mark all notifications as read
    socket.on('notifications:mark-all-read', async (data: ISocketNotificationMarkAllReadRequest, callback) => {
      try {
        const result = await this.notificationsService.markAllNotificationsAsRead(user.id);

        const response: ISocketNotificationMarkAllReadResponse = {
          success: result.success,
          message: result.message,
          error: result.error,
          updatedCount: result.updatedCount
        };

        callback(response);

        // Broadcast notification count update
        if (result.success) {
          await this.broadcastNotificationCountUpdate(user.id);
        }
      } catch (error) {
        logger.error('Socket notifications:mark-all-read error:', error);
        callback({
          success: false,
          message: 'Internal server error',
          error: 'INTERNAL_ERROR'
        });
      }
    });

    // Delete notification
    socket.on('notifications:delete', async (data: ISocketNotificationDeleteRequest, callback) => {
      try {
        const { error, value } = this.notificationDeleteSchema.validate(data);
        if (error) {
          return callback({
            success: false,
            message: 'Validation error',
            error: error.details[0].message
          });
        }

        const result = await this.notificationsService.deleteNotification(user.id, value.notificationId);

        const response: ISocketNotificationDeleteResponse = {
          success: result.success,
          message: result.message,
          error: result.error
        };

        callback(response);

        // Broadcast notification deleted event
        if (result.success) {
          const deletedEvent: ISocketNotificationDeletedEvent = {
            notificationId: value.notificationId
          };

          socket.emit('notifications:deleted', deletedEvent);
          await this.broadcastNotificationCountUpdate(user.id);
        }
      } catch (error) {
        logger.error('Socket notifications:delete error:', error);
        callback({
          success: false,
          message: 'Internal server error',
          error: 'INTERNAL_ERROR'
        });
      }
    });

    // Register device token
    socket.on('notifications:register-device', async (data: ISocketRegisterDeviceRequest, callback) => {
      try {
        const { error, value } = this.registerDeviceSchema.validate(data);
        if (error) {
          return callback({
            success: false,
            message: 'Validation error',
            error: error.details[0].message
          });
        }

        const result = await this.notificationsService.registerDeviceToken(user.id, {
          deviceToken: value.deviceToken,
          platform: value.platform
        });

        const response: ISocketRegisterDeviceResponse = {
          success: result.success,
          message: result.message,
          error: result.error
        };

        callback(response);
      } catch (error) {
        logger.error('Socket notifications:register-device error:', error);
        callback({
          success: false,
          message: 'Internal server error',
          error: 'INTERNAL_ERROR'
        });
      }
    });
  }

  /**
   * Broadcast new notification to user
   */
  public async broadcastNewNotification(userId: string, notification: any) {
    try {
      const newNotificationEvent: ISocketNewNotificationEvent = {
        notification
      };

      this.io.to(`user:${userId}`).emit('notifications:new', newNotificationEvent);
      await this.broadcastNotificationCountUpdate(userId);
    } catch (error) {
      logger.error('Error broadcasting new notification:', error);
    }
  }

  /**
   * Broadcast notification count update
   */
  private async broadcastNotificationCountUpdate(userId: string) {
    try {
      // Get updated notification counts
      const result = await this.notificationsService.getNotifications(userId, { limit: 1 });
      
      if (result.success && result.summary) {
        const countUpdateEvent: ISocketNotificationCountUpdatedEvent = {
          unreadCount: result.summary.unread,
          totalCount: result.summary.total,
          urgentCount: result.summary.urgent
        };

        this.io.to(`user:${userId}`).emit('notifications:count-updated', countUpdateEvent);
      }
    } catch (error) {
      logger.error('Error broadcasting notification count update:', error);
    }
  }
}
