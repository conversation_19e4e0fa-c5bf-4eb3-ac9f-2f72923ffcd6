/**
 * Response Utility
 * Standardized API response helpers
 */

import { Response } from 'express';
import type { ApiResponse, ApiError, PaginatedResult } from '@ozgaar/types';

export class ResponseUtil {
  /**
   * Send success response
   */
  static success<T>(
    res: Response,
    data?: T,
    message: string = 'Success',
    statusCode: number = 200
  ): Response {
    const response: ApiResponse<T> = {
      success: true,
      message,
      data
    };

    return res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  static error(
    res: Response,
    message: string = 'Internal Server Error',
    error?: string,
    statusCode: number = 500
  ): Response {
    const response: ApiError = {
      success: false,
      message,
      error: error || message,
      statusCode
    };

    return res.status(statusCode).json(response);
  }

  /**
   * Send validation error response
   */
  static validationError(
    res: Response,
    message: string = 'Validation failed',
    errors?: any
  ): Response {
    return res.status(400).json({
      success: false,
      message,
      error: 'Validation Error',
      statusCode: 400,
      details: errors
    });
  }

  /**
   * Send unauthorized response
   */
  static unauthorized(
    res: Response,
    message: string = 'Unauthorized access'
  ): Response {
    return ResponseUtil.error(res, message, 'Unauthorized', 401);
  }

  /**
   * Send forbidden response
   */
  static forbidden(
    res: Response,
    message: string = 'Access forbidden'
  ): Response {
    return ResponseUtil.error(res, message, 'Forbidden', 403);
  }

  /**
   * Send not found response
   */
  static notFound(
    res: Response,
    message: string = 'Resource not found'
  ): Response {
    return ResponseUtil.error(res, message, 'Not Found', 404);
  }

  /**
   * Send conflict response
   */
  static conflict(
    res: Response,
    message: string = 'Resource conflict'
  ): Response {
    return ResponseUtil.error(res, message, 'Conflict', 409);
  }

  /**
   * Send paginated response
   */
  static paginated<T>(
    res: Response,
    result: PaginatedResult<T>,
    message: string = 'Success'
  ): Response {
    return res.status(200).json({
      success: true,
      message,
      data: result.data,
      pagination: result.pagination
    });
  }

  /**
   * Send created response
   */
  static created<T>(
    res: Response,
    data?: T,
    message: string = 'Resource created successfully'
  ): Response {
    return ResponseUtil.success(res, data, message, 201);
  }

  /**
   * Send no content response
   */
  static noContent(res: Response): Response {
    return res.status(204).send();
  }
}
