/**
 * Logger Utility
 * Winston-based logging configuration
 */

import winston from 'winston';
import { appConfig } from '@/config/app.config';

const loggerConfig = appConfig.getLoggerConfig();

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Create transports
const transports: winston.transport[] = [];

// Console transport
if (loggerConfig.transports.includes('console')) {
  transports.push(
    new winston.transports.Console({
      format: loggerConfig.format === 'json' ? logFormat : consoleFormat
    })
  );
}

// File transport
if (loggerConfig.transports.includes('file') && loggerConfig.filename) {
  transports.push(
    new winston.transports.File({
      filename: loggerConfig.filename,
      format: logFormat,
      maxsize: parseInt(loggerConfig.maxSize?.replace('m', '') || '20') * 1024 * 1024,
      maxFiles: loggerConfig.maxFiles || 5
    })
  );
}

// Create logger instance
export const logger = winston.createLogger({
  level: loggerConfig.level,
  format: logFormat,
  transports,
  exitOnError: false
});

// Add request logging helper
export const logRequest = (req: any, res: any, responseTime?: number) => {
  const logData = {
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    responseTime: responseTime ? `${responseTime}ms` : undefined
  };

  if (res.statusCode >= 400) {
    logger.warn('HTTP Request', logData);
  } else {
    logger.info('HTTP Request', logData);
  }
};

// Add error logging helper
export const logError = (error: Error, context?: any) => {
  logger.error('Application Error', {
    message: error.message,
    stack: error.stack,
    context
  });
};
