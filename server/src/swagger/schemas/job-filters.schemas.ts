/**
 * Job Filters Schema Definitions
 * Swagger schema definitions for job filters endpoints
 */

export const JOB_FILTERS_SCHEMAS = {
  // Job Filters Response Schema
  IJobFiltersResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Job filters retrieved successfully',
      },
      filters: {
        type: 'object',
        properties: {
          categories: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                example: 'categories',
              },
              label: {
                type: 'string',
                example: 'Categories',
              },
              type: {
                type: 'string',
                example: 'multiple',
              },
              options: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    value: {
                      type: 'string',
                      example: 'Home Services',
                    },
                    label: {
                      type: 'string',
                      example: 'Home Services',
                    },
                    count: {
                      type: 'integer',
                      example: 25,
                    },
                  },
                },
              },
            },
          },
          locations: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                example: 'locations',
              },
              label: {
                type: 'string',
                example: 'Locations',
              },
              type: {
                type: 'string',
                example: 'multiple',
              },
              options: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    value: {
                      type: 'string',
                      example: 'Mumbai',
                    },
                    label: {
                      type: 'string',
                      example: 'Mumbai',
                    },
                    count: {
                      type: 'integer',
                      example: 15,
                    },
                  },
                },
              },
            },
          },
          rateRanges: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                example: 'rateRanges',
              },
              label: {
                type: 'string',
                example: 'Rate Ranges',
              },
              type: {
                type: 'string',
                example: 'multiple',
              },
              options: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    value: {
                      type: 'string',
                      example: '500-1000',
                    },
                    label: {
                      type: 'string',
                      example: '₹500 - ₹1000',
                    },
                    count: {
                      type: 'integer',
                      example: 20,
                    },
                  },
                },
              },
            },
          },
          experienceLevels: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                example: 'experienceLevels',
              },
              label: {
                type: 'string',
                example: 'Experience Levels',
              },
              type: {
                type: 'string',
                example: 'multiple',
              },
              options: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    value: {
                      type: 'string',
                      example: '2-5 years',
                    },
                    label: {
                      type: 'string',
                      example: '2-5 years',
                    },
                    count: {
                      type: 'integer',
                      example: 30,
                    },
                  },
                },
              },
            },
          },
          urgency: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                example: 'urgency',
              },
              label: {
                type: 'string',
                example: 'Urgency',
              },
              type: {
                type: 'string',
                example: 'single',
              },
              options: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    value: {
                      type: 'string',
                      example: 'urgent',
                    },
                    label: {
                      type: 'string',
                      example: 'Urgent',
                    },
                    count: {
                      type: 'integer',
                      example: 10,
                    },
                  },
                },
              },
            },
          },
          skills: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                example: 'skills',
              },
              label: {
                type: 'string',
                example: 'Skills',
              },
              type: {
                type: 'string',
                example: 'multiple',
              },
              options: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    value: {
                      type: 'string',
                      example: 'Plumbing',
                    },
                    label: {
                      type: 'string',
                      example: 'Plumbing',
                    },
                    count: {
                      type: 'integer',
                      example: 12,
                    },
                  },
                },
              },
            },
          },
        },
      },
      totalJobsCount: {
        type: 'integer',
        example: 150,
      },
    },
  },
};