/**
 * User Schema Definitions
 * Swagger schema definitions for user endpoints
 */

export const USER_SCHEMAS = {
  // User schemas
  CreateWorkerProfileRequest: {
    type: 'object',
    required: ['primarySkill', 'skills', 'experience'],
    properties: {
      primarySkill: {
        type: 'string',
        example: 'Plumbing',
      },
      skills: {
        type: 'array',
        items: {
          type: 'string',
        },
        example: ['Plumbing', 'Pipe fitting', 'Water heater repair'],
      },
      experience: {
        type: 'string',
        example: '2-5',
        enum: ['0-1', '2-5', '5-10', '10+'],
      },
      hourlyRate: {
        type: 'integer',
        example: 500,
        minimum: 1,
      },
      dailyRate: {
        type: 'integer',
        example: 4000,
        minimum: 1,
      },
      about: {
        type: 'string',
        example: 'Experienced plumber with 5 years of experience',
        maxLength: 500,
      },
      phoneVisible: {
        type: 'boolean',
        example: false,
      },
      portfolioPhotos: {
        type: 'array',
        items: {
          type: 'string',
        },
        example: ['https://example.com/portfolio1.jpg'],
      },
    },
  },
  CreateWorkerProfileResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Worker profile created successfully',
      },
      data: {
        type: 'object',
        properties: {
          userId: {
            type: 'string',
            example: 'user_id_here',
          },
          primarySkill: {
            type: 'string',
            example: 'Plumbing',
          },
          skills: {
            type: 'array',
            items: {
              type: 'string',
            },
            example: ['Plumbing', 'Pipe fitting', 'Water heater repair'],
          },
          experience: {
            type: 'string',
            example: '2-5',
          },
          hourlyRate: {
            type: 'integer',
            example: 500,
          },
          dailyRate: {
            type: 'integer',
            example: 4000,
          },
          about: {
            type: 'string',
            example: 'Experienced plumber with 5 years of experience',
          },
          isAvailable: {
            type: 'boolean',
            example: true,
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            example: '2023-01-01T00:00:00.000Z',
          },
        },
      },
    },
  },
  CreatePosterProfileRequest: {
    type: 'object',
    properties: {
      companyName: {
        type: 'string',
        example: 'ABC Construction Company',
        maxLength: 100,
      },
      about: {
        type: 'string',
        example: 'We are a construction company specializing in residential projects',
        maxLength: 500,
      },
      businessVerified: {
        type: 'boolean',
        example: false,
      },
      businessDocuments: {
        type: 'array',
        items: {
          type: 'string',
        },
        example: ['https://example.com/document1.pdf'],
      },
    },
  },
  CreatePosterProfileResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Poster profile created successfully',
      },
      data: {
        type: 'object',
        properties: {
          userId: {
            type: 'string',
            example: 'user_id_here',
          },
          companyName: {
            type: 'string',
            example: 'ABC Construction Company',
          },
          about: {
            type: 'string',
            example: 'We are a construction company specializing in residential projects',
          },
          businessVerified: {
            type: 'boolean',
            example: false,
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            example: '2023-01-01T00:00:00.000Z',
          },
        },
      },
    },
  },
  UpdateUserProfileRequest: {
    type: 'object',
    properties: {
      fullName: {
        type: 'string',
        example: 'John Doe',
        minLength: 2,
        maxLength: 50,
      },
      email: {
        type: 'string',
        example: '<EMAIL>',
        format: 'email',
      },
      location: {
        type: 'string',
        example: 'Mumbai, Maharashtra',
        maxLength: 100,
      },
      avatarUrl: {
        type: 'string',
        example: 'https://example.com/avatar.jpg',
        format: 'uri',
      },
    },
  },
  UpdateUserProfileResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Profile updated successfully',
      },
      data: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            example: 'user_id_here',
          },
          phone: {
            type: 'string',
            example: '+919876543210',
          },
          fullName: {
            type: 'string',
            example: 'John Doe',
          },
          email: {
            type: 'string',
            example: '<EMAIL>',
          },
          location: {
            type: 'string',
            example: 'Mumbai, Maharashtra',
          },
          avatarUrl: {
            type: 'string',
            example: 'https://example.com/avatar.jpg',
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            example: '2023-01-01T00:00:00.000Z',
          },
        },
      },
    },
  },
  GetUserSettingsResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Settings retrieved successfully',
      },
      data: {
        type: 'object',
        properties: {
          userId: {
            type: 'string',
            example: 'user_id_here',
          },
          notifications: {
            type: 'object',
            example: {
              jobNotifications: true,
              messageNotifications: true,
            },
          },
          privacy: {
            type: 'object',
            example: {
              profileVisibility: 'public',
            },
          },
          language: {
            type: 'string',
            example: 'english',
            enum: ['english', 'hindi'],
          },
          preferences: {
            type: 'object',
            example: {
              theme: 'light',
            },
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            example: '2023-01-01T00:00:00.000Z',
          },
        },
      },
    },
  },
  UpdateUserSettingsRequest: {
    type: 'object',
    properties: {
      notifications: {
        type: 'object',
        example: {
          jobNotifications: true,
          messageNotifications: true,
        },
      },
      privacy: {
        type: 'object',
        example: {
          profileVisibility: 'public',
        },
      },
      language: {
        type: 'string',
        example: 'english',
        enum: ['english', 'hindi'],
      },
      preferences: {
        type: 'object',
        example: {
          theme: 'light',
        },
      },
    },
  },
  UpdateUserSettingsResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Settings updated successfully',
      },
      data: {
        type: 'object',
        properties: {
          userId: {
            type: 'string',
            example: 'user_id_here',
          },
          notifications: {
            type: 'object',
            example: {
              jobNotifications: true,
              messageNotifications: true,
            },
          },
          privacy: {
            type: 'object',
            example: {
              profileVisibility: 'public',
            },
          },
          language: {
            type: 'string',
            example: 'english',
          },
          preferences: {
            type: 'object',
            example: {
              theme: 'light',
            },
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            example: '2023-01-01T00:00:00.000Z',
          },
        },
      },
    },
  },
};