/**
 * Authentication Schema Definitions
 * Swagger schema definitions for authentication endpoints
 */

export const AUTH_SCHEMAS = {
  // Authentication schemas
  SendOtpRequest: {
    type: 'object',
    required: ['phone'],
    properties: {
      phone: {
        type: 'string',
        example: '+************',
        description: 'Valid Indian mobile number with country code',
      },
    },
  },
  SendOtpResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'OTP sent successfully',
      },
      data: {
        type: 'object',
        properties: {
          otpId: {
            type: 'string',
            example: 'session_id_here',
          },
        },
      },
    },
  },
  VerifyOtpRequest: {
    type: 'object',
    required: ['phone', 'otp', 'otpId'],
    properties: {
      phone: {
        type: 'string',
        example: '+************',
        description: 'Valid Indian mobile number with country code',
      },
      otp: {
        type: 'string',
        example: '123456',
        description: '6-digit OTP code',
      },
      otpId: {
        type: 'string',
        example: 'session_id_here',
        description: 'OTP session ID received from send-otp endpoint',
      },
    },
  },
  VerifyOtpResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'OTP verified successfully',
      },
      data: {
        type: 'object',
        properties: {
          user: {
            type: 'object',
            properties: {
              id: {
                type: 'string',
                example: 'user_id_here',
              },
              phone: {
                type: 'string',
                example: '+************',
              },
              phoneVerified: {
                type: 'boolean',
                example: true,
              },
              fullName: {
                type: 'string',
                example: 'User 3210',
              },
              email: {
                type: 'string',
                example: '<EMAIL>',
              },
              avatarUrl: {
                type: 'string',
                example: 'https://example.com/avatar.jpg',
              },
              location: {
                type: 'string',
                example: 'Mumbai, India',
              },
              isActive: {
                type: 'boolean',
                example: true,
              },
              isVerified: {
                type: 'boolean',
                example: false,
              },
              createdAt: {
                type: 'string',
                format: 'date-time',
                example: '2023-01-01T00:00:00.000Z',
              },
              updatedAt: {
                type: 'string',
                format: 'date-time',
                example: '2023-01-01T00:00:00.000Z',
              },
            },
          },
          tokens: {
            type: 'object',
            properties: {
              accessToken: {
                type: 'string',
                example: 'access_token_here',
              },
              refreshToken: {
                type: 'string',
                example: 'refresh_token_here',
              },
            },
          },
          isNewUser: {
            type: 'boolean',
            example: true,
          },
        },
      },
    },
  },
  ResendOtpRequest: {
    type: 'object',
    required: ['phone', 'otpId'],
    properties: {
      phone: {
        type: 'string',
        example: '+************',
        description: 'Valid Indian mobile number with country code',
      },
      otpId: {
        type: 'string',
        example: 'session_id_here',
        description: 'OTP session ID received from send-otp endpoint',
      },
    },
  },
  RefreshTokenRequest: {
    type: 'object',
    required: ['refreshToken'],
    properties: {
      refreshToken: {
        type: 'string',
        example: 'refresh_token_here',
        description: 'Refresh token received during login',
      },
    },
  },
  RefreshTokenResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Token refreshed successfully',
      },
      data: {
        type: 'object',
        properties: {
          tokens: {
            type: 'object',
            properties: {
              accessToken: {
                type: 'string',
                example: 'new_access_token_here',
              },
              refreshToken: {
                type: 'string',
                example: 'new_refresh_token_here',
              },
            },
          },
        },
      },
    },
  },
  LogoutRequest: {
    type: 'object',
    required: ['refreshToken'],
    properties: {
      refreshToken: {
        type: 'string',
        example: 'refresh_token_here',
        description: 'Refresh token to invalidate',
      },
    },
  },
  UserResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'User information retrieved successfully',
      },
      data: {
        type: 'object',
        properties: {
          user: {
            type: 'object',
            properties: {
              id: {
                type: 'string',
                example: 'user_id_here',
              },
              phone: {
                type: 'string',
                example: '+************',
              },
              phoneVerified: {
                type: 'boolean',
                example: true,
              },
              fullName: {
                type: 'string',
                example: 'User 3210',
              },
              email: {
                type: 'string',
                example: '<EMAIL>',
              },
              avatarUrl: {
                type: 'string',
                example: 'https://example.com/avatar.jpg',
              },
              location: {
                type: 'string',
                example: 'Mumbai, India',
              },
              isActive: {
                type: 'boolean',
                example: true,
              },
              isVerified: {
                type: 'boolean',
                example: false,
              },
              createdAt: {
                type: 'string',
                format: 'date-time',
                example: '2023-01-01T00:00:00.000Z',
              },
              updatedAt: {
                type: 'string',
                format: 'date-time',
                example: '2023-01-01T00:00:00.000Z',
              },
            },
          },
        },
      },
    },
  },
};