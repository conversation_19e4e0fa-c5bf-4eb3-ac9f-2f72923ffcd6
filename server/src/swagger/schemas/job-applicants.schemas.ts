/**
 * Job Applicants Schema Definitions
 * Swagger schema definitions for job applicants endpoints
 */

export const JOB_APPLICANTS_SCHEMAS = {
  // Job Applicants Response Schema
  JobApplicantsResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Job applicants retrieved successfully',
      },
      applicants: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              example: 'application_id_here',
            },
            workerId: {
              type: 'string',
              example: 'worker_id_here',
            },
            workerName: {
              type: 'string',
              example: '<PERSON>',
            },
            workerAvatar: {
              type: 'string',
              example: 'https://example.com/avatar.jpg',
            },
            workerRating: {
              type: 'number',
              example: 4.5,
            },
            workerReviewCount: {
              type: 'integer',
              example: 10,
            },
            workerCompletedJobs: {
              type: 'integer',
              example: 25,
            },
            personaId: {
              type: 'string',
              example: 'persona_id_here',
            },
            personaSkill: {
              type: 'string',
              example: 'Plumbing',
            },
            coverLetter: {
              type: 'string',
              example: 'I have 5 years of experience in plumbing and can fix your issue quickly.',
            },
            proposedRate: {
              type: 'integer',
              example: 500,
            },
            proposedRateType: {
              type: 'string',
              example: 'hourly',
            },
            status: {
              type: 'string',
              example: 'pending',
            },
            appliedAt: {
              type: 'string',
              format: 'date-time',
              example: '2023-01-01T00:00:00.000Z',
            },
            respondedAt: {
              type: 'string',
              format: 'date-time',
              example: '2023-01-01T00:00:00.000Z',
            },
            hasMessages: {
              type: 'boolean',
              example: false,
            },
            lastMessageAt: {
              type: 'string',
              format: 'date-time',
              example: '2023-01-01T00:00:00.000Z',
            },
            matchScore: {
              type: 'integer',
              example: 85,
            },
          },
        },
      },
      pagination: {
        type: 'object',
        properties: {
          page: {
            type: 'integer',
            example: 1,
          },
          limit: {
            type: 'integer',
            example: 20,
          },
          totalPages: {
            type: 'integer',
            example: 5,
          },
          totalCount: {
            type: 'integer',
            example: 100,
          },
          hasMore: {
            type: 'boolean',
            example: true,
          },
        },
      },
      summary: {
        type: 'object',
        properties: {
          total: {
            type: 'integer',
            example: 45,
          },
          pending: {
            type: 'integer',
            example: 12,
          },
          accepted: {
            type: 'integer',
            example: 25,
          },
          rejected: {
            type: 'integer',
            example: 8,
          },
          withdrawn: {
            type: 'integer',
            example: 0,
          },
        },
      },
    },
  },
  
  // My Jobs Response Schema
  MyJobsResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Jobs retrieved successfully',
      },
      jobs: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              example: 'job_id_here',
            },
            title: {
              type: 'string',
              example: 'Plumber needed for bathroom repair',
            },
            category: {
              type: 'string',
              example: 'Home Services',
            },
            location: {
              type: 'string',
              example: 'Mumbai, Maharashtra',
            },
            rate: {
              type: 'integer',
              example: 500,
            },
            rateType: {
              type: 'string',
              example: 'hourly',
            },
            status: {
              type: 'string',
              example: 'active',
            },
            urgency: {
              type: 'string',
              example: 'normal',
            },
            applicationCount: {
              type: 'integer',
              example: 5,
            },
            maxApplications: {
              type: 'integer',
              example: 10,
            },
            viewCount: {
              type: 'integer',
              example: 25,
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              example: '2023-01-01T00:00:00.000Z',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              example: '2023-01-01T00:00:00.000Z',
            },
            expiresAt: {
              type: 'string',
              format: 'date-time',
              example: '2023-01-15T00:00:00.000Z',
            },
          },
        },
      },
      pagination: {
        type: 'object',
        properties: {
          page: {
            type: 'integer',
            example: 1,
          },
          limit: {
            type: 'integer',
            example: 20,
          },
          totalPages: {
            type: 'integer',
            example: 5,
          },
          totalCount: {
            type: 'integer',
            example: 100,
          },
          hasMore: {
            type: 'boolean',
            example: true,
          },
        },
      },
      summary: {
        type: 'object',
        properties: {
          total: {
            type: 'integer',
            example: 25,
          },
          draft: {
            type: 'integer',
            example: 2,
          },
          active: {
            type: 'integer',
            example: 15,
          },
          paused: {
            type: 'integer',
            example: 3,
          },
          completed: {
            type: 'integer',
            example: 5,
          },
          cancelled: {
            type: 'integer',
            example: 0,
          },
        },
      },
    },
  },
  
  // Apply to Job Response Schema
  ApplyToJobResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Application submitted successfully',
      },
      application: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            example: 'application_id_here',
          },
          jobId: {
            type: 'string',
            example: 'job_id_here',
          },
          workerId: {
            type: 'string',
            example: 'worker_id_here',
          },
          personaId: {
            type: 'string',
            example: 'persona_id_here',
          },
          coverLetter: {
            type: 'string',
            example: 'I have 5 years of experience in plumbing and can fix your issue quickly.',
          },
          proposedRate: {
            type: 'integer',
            example: 500,
          },
          proposedRateType: {
            type: 'string',
            example: 'hourly',
          },
          status: {
            type: 'string',
            example: 'pending',
          },
          appliedAt: {
            type: 'string',
            format: 'date-time',
            example: '2023-01-01T00:00:00.000Z',
          },
        },
      },
    },
  },
};