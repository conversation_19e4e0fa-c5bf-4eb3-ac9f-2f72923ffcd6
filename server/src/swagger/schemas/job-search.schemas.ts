/**
 * Job Search Schema Definitions
 * Swagger schema definitions for job search endpoints
 */

export const J<PERSON><PERSON>_SEARCH_SCHEMAS = {
  // Job Search Response Schema
  IJobSearchResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Jobs search completed successfully',
      },
      jobs: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              example: 'job_id_here',
            },
            title: {
              type: 'string',
              example: 'Plumber needed for bathroom repair',
            },
            description: {
              type: 'string',
              example: 'Need a plumber to fix leaking taps in the bathroom',
            },
            category: {
              type: 'string',
              example: 'Home Services',
            },
            location: {
              type: 'string',
              example: 'Mumbai, Maharashtra',
            },
            rate: {
              type: 'integer',
              example: 500,
            },
            rateType: {
              type: 'string',
              example: 'hourly',
            },
            urgency: {
              type: 'string',
              example: 'normal',
            },
            photos: {
              type: 'array',
              items: {
                type: 'string',
              },
              example: ['https://example.com/photo1.jpg'],
            },
            viewCount: {
              type: 'integer',
              example: 10,
            },
            applicationCount: {
              type: 'integer',
              example: 2,
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              example: '2023-01-01T00:00:00.000Z',
            },
            poster: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                  example: 'user_id_here',
                },
                fullName: {
                  type: 'string',
                  example: 'John Doe',
                },
                avatarUrl: {
                  type: 'string',
                  example: 'https://example.com/avatar.jpg',
                },
                rating: {
                  type: 'number',
                  example: 4.5,
                },
                reviewCount: {
                  type: 'integer',
                  example: 10,
                },
              },
            },
          },
        },
      },
      pagination: {
        type: 'object',
        properties: {
          page: {
            type: 'integer',
            example: 1,
          },
          limit: {
            type: 'integer',
            example: 20,
          },
          totalPages: {
            type: 'integer',
            example: 5,
          },
          totalCount: {
            type: 'integer',
            example: 100,
          },
          hasMore: {
            type: 'boolean',
            example: true,
          },
        },
      },
    },
  },
};