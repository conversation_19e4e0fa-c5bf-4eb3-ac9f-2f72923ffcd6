/**
 * Job Recommendations Schema Definitions
 * Swagger schema definitions for job recommendations endpoints
 */

export const JOB_RECOMMENDATIONS_SCHEMAS = {
  // Job Recommendations Response Schema
  IJobRecommendationsResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Job recommendations retrieved successfully',
      },
      recommendations: {
        type: 'object',
        properties: {
          featured: {
            type: 'object',
            properties: {
              title: {
                type: 'string',
                example: 'Featured Jobs',
              },
              description: {
                type: 'string',
                example: 'High-paying and urgent opportunities',
              },
              jobs: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      example: 'job_id_here',
                    },
                    title: {
                      type: 'string',
                      example: 'Plumber needed for bathroom repair',
                    },
                    description: {
                      type: 'string',
                      example: 'Need a plumber to fix leaking taps in the bathroom',
                    },
                    category: {
                      type: 'string',
                      example: 'Home Services',
                    },
                    location: {
                      type: 'string',
                      example: 'Mumbai, Maharashtra',
                    },
                    rate: {
                      type: 'integer',
                      example: 500,
                    },
                    rateType: {
                      type: 'string',
                      example: 'hourly',
                    },
                    urgency: {
                      type: 'string',
                      example: 'normal',
                    },
                    photos: {
                      type: 'array',
                      items: {
                        type: 'string',
                      },
                      example: ['https://example.com/photo1.jpg'],
                    },
                    viewCount: {
                      type: 'integer',
                      example: 10,
                    },
                    applicationCount: {
                      type: 'integer',
                      example: 2,
                    },
                    createdAt: {
                      type: 'string',
                      format: 'date-time',
                      example: '2023-01-01T00:00:00.000Z',
                    },
                    poster: {
                      type: 'object',
                      properties: {
                        id: {
                          type: 'string',
                          example: 'user_id_here',
                        },
                        name: {
                          type: 'string',
                          example: 'John Doe',
                        },
                        companyName: {
                          type: 'string',
                          example: 'ABC Company',
                        },
                        rating: {
                          type: 'number',
                          example: 4.5,
                        },
                        reviewCount: {
                          type: 'integer',
                          example: 10,
                        },
                        businessVerified: {
                          type: 'boolean',
                          example: true,
                        },
                      },
                    },
                  },
                },
              },
              hasMore: {
                type: 'boolean',
                example: true,
              },
              totalCount: {
                type: 'integer',
                example: 50,
              },
            },
          },
          nearby: {
            type: 'object',
            properties: {
              title: {
                type: 'string',
                example: 'Nearby Jobs',
              },
              description: {
                type: 'string',
                example: 'Jobs in your area',
              },
              jobs: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      example: 'job_id_here',
                    },
                    title: {
                      type: 'string',
                      example: 'Electrician needed',
                    },
                    description: {
                      type: 'string',
                      example: 'Need an electrician to fix wiring',
                    },
                    category: {
                      type: 'string',
                      example: 'Home Services',
                    },
                    location: {
                      type: 'string',
                      example: 'Delhi, India',
                    },
                    rate: {
                      type: 'integer',
                      example: 800,
                    },
                    rateType: {
                      type: 'string',
                      example: 'daily',
                    },
                    urgency: {
                      type: 'string',
                      example: 'urgent',
                    },
                    photos: {
                      type: 'array',
                      items: {
                        type: 'string',
                      },
                      example: ['https://example.com/photo2.jpg'],
                    },
                    viewCount: {
                      type: 'integer',
                      example: 15,
                    },
                    applicationCount: {
                      type: 'integer',
                      example: 3,
                    },
                    createdAt: {
                      type: 'string',
                      format: 'date-time',
                      example: '2023-01-01T00:00:00.000Z',
                    },
                    poster: {
                      type: 'object',
                      properties: {
                        id: {
                          type: 'string',
                          example: 'user_id_here',
                        },
                        name: {
                          type: 'string',
                          example: 'Jane Smith',
                        },
                        companyName: {
                          type: 'string',
                          example: 'XYZ Services',
                        },
                        rating: {
                          type: 'number',
                          example: 4.2,
                        },
                        reviewCount: {
                          type: 'integer',
                          example: 8,
                        },
                        businessVerified: {
                          type: 'boolean',
                          example: false,
                        },
                      },
                    },
                  },
                },
              },
              hasMore: {
                type: 'boolean',
                example: true,
              },
              totalCount: {
                type: 'integer',
                example: 30,
              },
            },
          },
          skillMatch: {
            type: 'object',
            properties: {
              title: {
                type: 'string',
                example: 'Skill Match Jobs',
              },
              description: {
                type: 'string',
                example: 'Jobs matching your skills',
              },
              jobs: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      example: 'job_id_here',
                    },
                    title: {
                      type: 'string',
                      example: 'Web Developer needed',
                    },
                    description: {
                      type: 'string',
                      example: 'Build a responsive website',
                    },
                    category: {
                      type: 'string',
                      example: 'Technology & IT Services',
                    },
                    location: {
                      type: 'string',
                      example: 'Bangalore, India',
                    },
                    rate: {
                      type: 'integer',
                      example: 1500,
                    },
                    rateType: {
                      type: 'string',
                      example: 'fixed',
                    },
                    urgency: {
                      type: 'string',
                      example: 'normal',
                    },
                    photos: {
                      type: 'array',
                      items: {
                        type: 'string',
                      },
                      example: ['https://example.com/photo3.jpg'],
                    },
                    viewCount: {
                      type: 'integer',
                      example: 20,
                    },
                    applicationCount: {
                      type: 'integer',
                      example: 5,
                    },
                    createdAt: {
                      type: 'string',
                      format: 'date-time',
                      example: '2023-01-01T00:00:00.000Z',
                    },
                    poster: {
                      type: 'object',
                      properties: {
                        id: {
                          type: 'string',
                          example: 'user_id_here',
                        },
                        name: {
                          type: 'string',
                          example: 'Tech Corp',
                        },
                        companyName: {
                          type: 'string',
                          example: 'Tech Solutions Inc',
                        },
                        rating: {
                          type: 'number',
                          example: 4.8,
                        },
                        reviewCount: {
                          type: 'integer',
                          example: 25,
                        },
                        businessVerified: {
                          type: 'boolean',
                          example: true,
                        },
                      },
                    },
                  },
                },
              },
              hasMore: {
                type: 'boolean',
                example: true,
              },
              totalCount: {
                type: 'integer',
                example: 25,
              },
            },
          },
        },
      },
      pagination: {
        type: 'object',
        properties: {
          page: {
            type: 'integer',
            example: 1,
          },
          limit: {
            type: 'integer',
            example: 10,
          },
          totalPages: {
            type: 'integer',
            example: 5,
          },
          totalCount: {
            type: 'integer',
            example: 105,
          },
        },
      },
    },
  },
};