/**
 * Poster Profile Schema Definitions
 * Swagger schema definitions for poster profile endpoints
 */

export const POSTER_PROFILE_SCHEMAS = {
  // Poster Profile Response Schema
  GetPosterProfileResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Poster profile retrieved successfully',
      },
      data: {
        type: 'object',
        properties: {
          userId: {
            type: 'string',
            example: 'user_id_here',
          },
          companyName: {
            type: 'string',
            example: 'ABC Construction Company',
          },
          about: {
            type: 'string',
            example: 'We are a construction company specializing in residential projects',
          },
          businessVerified: {
            type: 'boolean',
            example: false,
          },
          businessDocuments: {
            type: 'array',
            items: {
              type: 'string',
            },
            example: ['https://example.com/document1.pdf'],
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            example: '2023-01-01T00:00:00.000Z',
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            example: '2023-01-01T00:00:00.000Z',
          },
        },
      },
    },
  },
  // Poster Stats Response Schema
  GetPosterStatsResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Poster statistics retrieved successfully',
      },
      data: {
        type: 'object',
        properties: {
          stats: {
            type: 'object',
            properties: {
              jobs: {
                type: 'object',
                properties: {
                  total: {
                    type: 'integer',
                    example: 25,
                  },
                  active: {
                    type: 'integer',
                    example: 15,
                  },
                  completed: {
                    type: 'integer',
                    example: 8,
                  },
                  cancelled: {
                    type: 'integer',
                    example: 2,
                  },
                },
              },
              applications: {
                type: 'object',
                properties: {
                  total: {
                    type: 'integer',
                    example: 45,
                  },
                  pending: {
                    type: 'integer',
                    example: 12,
                  },
                  accepted: {
                    type: 'integer',
                    example: 25,
                  },
                  rejected: {
                    type: 'integer',
                    example: 8,
                  },
                },
              },
              earnings: {
                type: 'object',
                properties: {
                  total: {
                    type: 'integer',
                    example: 15000,
                  },
                  pending: {
                    type: 'integer',
                    example: 3000,
                  },
                  completed: {
                    type: 'integer',
                    example: 12000,
                  },
                },
              },
              reviews: {
                type: 'object',
                properties: {
                  averageRating: {
                    type: 'number',
                    example: 4.5,
                  },
                  totalReviews: {
                    type: 'integer',
                    example: 20,
                  },
                  ratingDistribution: {
                    type: 'object',
                    properties: {
                      '5': {
                        type: 'integer',
                        example: 8,
                      },
                      '4': {
                        type: 'integer',
                        example: 7,
                      },
                      '3': {
                        type: 'integer',
                        example: 3,
                      },
                      '2': {
                        type: 'integer',
                        example: 1,
                      },
                      '1': {
                        type: 'integer',
                        example: 1,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};