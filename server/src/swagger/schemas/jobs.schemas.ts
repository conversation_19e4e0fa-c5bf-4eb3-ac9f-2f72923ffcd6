/**
 * Jobs Schema Definitions
 * Swagger schema definitions for jobs endpoints
 */

export const JOBS_SCHEMAS = {
  // Jobs schemas
  CreateJobRequest: {
    type: 'object',
    required: ['title', 'description', 'category', 'location', 'rate', 'rateType'],
    properties: {
      title: {
        type: 'string',
        example: 'Plumber needed for bathroom repair',
        minLength: 3,
        maxLength: 100,
      },
      description: {
        type: 'string',
        example: 'Need a plumber to fix leaking taps in the bathroom',
        minLength: 10,
        maxLength: 2000,
      },
      category: {
        type: 'string',
        example: 'Home Services',
      },
      subcategory: {
        type: 'string',
        example: 'Plumbing',
      },
      location: {
        type: 'string',
        example: 'Mumbai, Maharashtra',
      },
      latitude: {
        type: 'number',
        example: 19.076,
        minimum: -90,
        maximum: 90,
      },
      longitude: {
        type: 'number',
        example: 72.8777,
        minimum: -180,
        maximum: 180,
      },
      rate: {
        type: 'integer',
        example: 500,
        minimum: 1,
      },
      rateType: {
        type: 'string',
        example: 'hourly',
        enum: ['hourly', 'daily', 'fixed'],
      },
      duration: {
        type: 'string',
        example: '2 hours',
      },
      requirements: {
        type: 'array',
        items: {
          type: 'string',
        },
        example: ['Own tools', '5+ years experience'],
      },
      skillsRequired: {
        type: 'array',
        items: {
          type: 'string',
        },
        example: ['Plumbing', 'Pipe fitting'],
      },
      experienceLevel: {
        type: 'string',
        example: '2-5',
      },
      urgency: {
        type: 'string',
        example: 'normal',
        enum: ['normal', 'urgent'],
      },
      photos: {
        type: 'array',
        items: {
          type: 'string',
        },
        example: ['https://example.com/photo1.jpg'],
      },
      maxApplications: {
        type: 'integer',
        example: 10,
        minimum: 1,
      },
      autoAccept: {
        type: 'boolean',
        example: false,
      },
    },
  },
  CreateJobResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Job created successfully',
      },
      data: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            example: 'job_id_here',
          },
          title: {
            type: 'string',
            example: 'Plumber needed for bathroom repair',
          },
          status: {
            type: 'string',
            example: 'active',
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            example: '2023-01-01T00:00:00.000Z',
          },
        },
      },
    },
  },
  GetJobsQuery: {
    type: 'object',
    properties: {
      page: {
        type: 'integer',
        example: 1,
        minimum: 1,
        default: 1,
      },
      limit: {
        type: 'integer',
        example: 20,
        minimum: 1,
        maximum: 100,
        default: 20,
      },
      category: {
        type: 'string',
        example: 'Home Services',
      },
      location: {
        type: 'string',
        example: 'Mumbai',
      },
      minRate: {
        type: 'integer',
        example: 100,
        minimum: 1,
      },
      maxRate: {
        type: 'integer',
        example: 1000,
        minimum: 1,
      },
      rateType: {
        type: 'string',
        example: 'hourly',
        enum: ['hourly', 'daily', 'fixed'],
      },
      urgency: {
        type: 'string',
        example: 'normal',
        enum: ['normal', 'urgent'],
      },
      sortBy: {
        type: 'string',
        example: 'created_at',
        enum: ['created_at', 'rate', 'distance'],
      },
      sortOrder: {
        type: 'string',
        example: 'desc',
        enum: ['asc', 'desc'],
      },
    },
  },
  GetJobsResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Jobs retrieved successfully',
      },
      data: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              example: 'job_id_here',
            },
            title: {
              type: 'string',
              example: 'Plumber needed for bathroom repair',
            },
            description: {
              type: 'string',
              example: 'Need a plumber to fix leaking taps in the bathroom',
            },
            category: {
              type: 'string',
              example: 'Home Services',
            },
            location: {
              type: 'string',
              example: 'Mumbai, Maharashtra',
            },
            rate: {
              type: 'integer',
              example: 500,
            },
            rateType: {
              type: 'string',
              example: 'hourly',
            },
            urgency: {
              type: 'string',
              example: 'normal',
            },
            photos: {
              type: 'array',
              items: {
                type: 'string',
              },
              example: ['https://example.com/photo1.jpg'],
            },
            viewCount: {
              type: 'integer',
              example: 10,
            },
            applicationCount: {
              type: 'integer',
              example: 2,
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              example: '2023-01-01T00:00:00.000Z',
            },
            poster: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                  example: 'user_id_here',
                },
                fullName: {
                  type: 'string',
                  example: 'John Doe',
                },
                avatarUrl: {
                  type: 'string',
                  example: 'https://example.com/avatar.jpg',
                },
                rating: {
                  type: 'number',
                  example: 4.5,
                },
                reviewCount: {
                  type: 'integer',
                  example: 10,
                },
              },
            },
          },
        },
      },
      pagination: {
        type: 'object',
        properties: {
          page: {
            type: 'integer',
            example: 1,
          },
          limit: {
            type: 'integer',
            example: 20,
          },
          total: {
            type: 'integer',
            example: 100,
          },
          totalPages: {
            type: 'integer',
            example: 5,
          },
          hasMore: {
            type: 'boolean',
            example: true,
          },
        },
      },
    },
  },
  GetJobByIdResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Job retrieved successfully',
      },
      data: {
        type: 'object',
        properties: {
          job: {
            type: 'object',
            properties: {
              id: {
                type: 'string',
                example: 'job_id_here',
              },
              title: {
                type: 'string',
                example: 'Plumber needed for bathroom repair',
              },
              description: {
                type: 'string',
                example: 'Need a plumber to fix leaking taps in the bathroom',
              },
              category: {
                type: 'string',
                example: 'Home Services',
              },
              location: {
                type: 'string',
                example: 'Mumbai, Maharashtra',
              },
              rate: {
                type: 'integer',
                example: 500,
              },
              rateType: {
                type: 'string',
                example: 'hourly',
              },
              urgency: {
                type: 'string',
                example: 'normal',
              },
              photos: {
                type: 'array',
                items: {
                  type: 'string',
                },
                example: ['https://example.com/photo1.jpg'],
              },
              viewCount: {
                type: 'integer',
                example: 10,
              },
              applicationCount: {
                type: 'integer',
                example: 2,
              },
              createdAt: {
                type: 'string',
                format: 'date-time',
                example: '2023-01-01T00:00:00.000Z',
              },
              poster: {
                type: 'object',
                properties: {
                  id: {
                    type: 'string',
                    example: 'user_id_here',
                  },
                  fullName: {
                    type: 'string',
                    example: 'John Doe',
                  },
                  avatarUrl: {
                    type: 'string',
                    example: 'https://example.com/avatar.jpg',
                  },
                  rating: {
                    type: 'number',
                    example: 4.5,
                  },
                  reviewCount: {
                    type: 'integer',
                    example: 10,
                  },
                  jobsPosted: {
                    type: 'integer',
                    example: 25,
                  },
                },
              },
              canApply: {
                type: 'boolean',
                example: true,
              },
              hasApplied: {
                type: 'boolean',
                example: false,
              },
              isBookmarked: {
                type: 'boolean',
                example: false,
              },
            },
          },
        },
      },
    },
  },
  BookmarkJobRequest: {
    type: 'object',
    required: ['jobId'],
    properties: {
      jobId: {
        type: 'string',
        example: 'job_id_here',
      },
    },
  },
  BookmarkJobResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
      },
      message: {
        type: 'string',
        example: 'Job added to bookmarks',
      },
      data: {
        type: 'object',
        properties: {
          isBookmarked: {
            type: 'boolean',
            example: true,
          },
        },
      },
    },
  },
};