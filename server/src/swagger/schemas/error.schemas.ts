/**
 * Error Schema Definitions
 * Swagger schema definitions for error responses
 */

export const ERROR_SCHEMAS = {
  // Error schemas
  ErrorResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: false,
      },
      message: {
        type: 'string',
        example: 'Error message',
      },
      error: {
        type: 'string',
        example: 'Error code',
      },
      statusCode: {
        type: 'integer',
        example: 400,
      },
    },
  },
  ValidationErrorResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: false,
      },
      message: {
        type: 'string',
        example: 'Validation failed',
      },
      error: {
        type: 'string',
        example: 'Validation Error',
      },
      statusCode: {
        type: 'integer',
        example: 400,
      },
      details: {
        type: 'object',
        example: {
          body: [
            {
              field: 'phone',
              message: '"phone" is required',
              value: null,
            },
          ],
        },
      },
    },
  },
};