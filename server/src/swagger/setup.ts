/**
 * Swagger Setup
 * Configure and initialize Swagger UI for the Ozgaar API
 */

import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';
import options from './config';

// Initialize Swagger
const specs = swaggerJsdoc(options);

/**
 * Setup Swagger UI documentation
 * @param app Express application instance
 */
export const setupSwagger = (app: Express): void => {
  // Serve Swagger UI
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true, // Enable the explorer bar
    customSiteTitle: 'Ozgaar API Documentation',
    customCss: '.swagger-ui .topbar { display: none }', // Hide the top bar
    swaggerOptions: {
      deepLinking: false,
      docExpansion: 'list', // Expand all endpoints by default
      filter: true, // Enable filtering
      showRequestDuration: true,
    },
  }));

  // Serve Swagger JSON
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });

  console.log(`Swagger UI available at http://localhost:${process.env.PORT || 3000}/api-docs`);
};