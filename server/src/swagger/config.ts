/**
 * Swagger Configuration
 * OpenAPI specification for the Ozgaar API
 */

import swaggerJsdoc from 'swagger-jsdoc';
import { SWAGGER_CONSTANTS } from './constants';
import { SECURITY_SCHEMES } from './security';
import { AUTH_SCHEMAS } from './schemas/auth.schemas';
import { JOBS_SCHEMAS } from './schemas/jobs.schemas';
import { USER_SCHEMAS } from './schemas/user.schemas';
import { ERROR_SCHEMAS } from './schemas/error.schemas';
import { JOB_SEARCH_SCHEMAS } from './schemas/job-search.schemas';
import { JOB_RECOMMENDATIONS_SCHEMAS } from './schemas/job-recommendations.schemas';
import { JOB_FILTERS_SCHEMAS } from './schemas/job-filters.schemas';
import { POSTER_PROFILE_SCHEMAS } from './schemas/poster-profile.schemas';
import { JOB_APPLICANTS_SCHEMAS } from './schemas/job-applicants.schemas';

const options: swaggerJsdoc.Options = {
  definition: {
    openapi: SWAGGER_CONSTANTS.OPENAPI_VERSION,
    info: SWAGGER_CONSTANTS.INFO,
    servers: SWAGGER_CONSTANTS.SERVERS,
    components: {
      securitySchemes: SECURITY_SCHEMES,
      schemas: {
        ...AUTH_SCHEMAS,
        ...JOBS_SCHEMAS,
        ...USER_SCHEMAS,
        ...ERROR_SCHEMAS,
        ...JOB_SEARCH_SCHEMAS,
        ...JOB_RECOMMENDATIONS_SCHEMAS,
        ...JOB_FILTERS_SCHEMAS,
        ...POSTER_PROFILE_SCHEMAS,
        ...JOB_APPLICANTS_SCHEMAS,
      },
    },
    security: SWAGGER_CONSTANTS.SECURITY,
  },
  apis: ['./src/routes/*.ts', './src/controllers/*.ts'], // Paths to files containing OpenAPI definitions
};

export default options;