/**
 * Authentication Service
 * Business logic for authentication operations
 */

import * as jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { databaseConfig } from '@/config/database.config';
import { appConfig } from '@/config/app.config';
import { logger } from '@/utils/logger';
import { twoFactorService } from '@/services/twoFactor.service';
import type { 
  OtpServiceResult, 
  TokenPair, 
  JwtPayload, 
  RefreshTokenPayload, 
  AuthenticatedUser, 
  OtpVerificationResult 
} from '@ozgaar/types';

export class AuthService {
  private db = databaseConfig.getClient();

  /**
   * Send OTP to phone number
   */
  async sendOtp(phone: string): Promise<OtpServiceResult> {
    try {
      // Validate phone number format
      if (!twoFactorService.validatePhoneNumber(phone)) {
        return {
          success: false,
          message: 'Invalid phone number format',
          error: 'INVALID_PHONE_FORMAT'
        };
      }

      // Send OTP using 2Factor.in service
      const result = await twoFactorService.sendOtp({ phone });

      if (!result.success) {
        return {
          success: false,
          message: result.error || 'Failed to send OTP',
          error: 'SMS_SEND_FAILED'
        };
      }

      return {
        success: true,
        otpId: result.sessionId,
        message: 'OTP sent successfully'
      };
    } catch (error) {
      logger.error('Send OTP error:', error);
      return {
        success: false,
        message: 'Failed to send OTP',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Verify OTP and authenticate user
   */
  async verifyOtp(phone: string, otp: string, otpId: string): Promise<OtpVerificationResult> {
    try {
      // Validate OTP format
      if (!/^\d{6}$/.test(otp)) {
        return {
          success: false,
          message: 'Invalid OTP format',
          error: 'INVALID_OTP',
          isNewUser: false
        };
      }

      // Verify OTP using 2Factor.in service
      const verificationResult = await twoFactorService.verifyOtp(otpId, otp);

      if (!verificationResult.success) {
        return {
          success: false,
          message: verificationResult.error || 'Invalid OTP',
          error: 'OTP_VERIFICATION_FAILED',
          isNewUser: false
        };
      }

      // Check if user exists
      const { data: existingUser } = await this.db!
        .from('user_profiles')
        .select('*')
        .eq('phone', phone)
        .single();

      let user: AuthenticatedUser;
      let isNewUser = false;

      if (!existingUser) {
        // Create user in Supabase Auth first
        const { data: authUser, error: authError } = await this.db!.auth.admin.createUser({
          phone: phone,
          email: `${phone}@ozgaar.local`, // Temporary email
          password: uuidv4(), // Random password
          email_confirm: false,
          phone_confirm: true,
          user_metadata: {
            full_name: `User ${phone.slice(-4)}`,
            phone: phone
          }
        });

        if (authError) {
          logger.error('Failed to create auth user:', authError);
          return {
            success: false,
            message: 'Failed to create user account',
            error: 'AUTH_USER_CREATION_FAILED',
            isNewUser: false
          };
        }

        // Create user profile with the auth user ID
        const { data: newUser, error: profileError } = await this.db!
          .from('user_profiles')
          .insert({
            user_id: authUser.user.id, // Use the Supabase Auth user ID
            phone,
            phone_verified: true,
            full_name: `User ${phone.slice(-4)}`, // Temporary name
            is_active: true
          })
          .select()
          .single();

        if (profileError || !newUser) {
          logger.error('Failed to create user profile:', profileError);
          return {
            success: false,
            message: 'Failed to create user account',
            error: 'USER_PROFILE_CREATION_FAILED',
            isNewUser: false
          };
        }

        user = this.mapToAuthenticatedUser(newUser);
        isNewUser = true;
      } else {
        // Update phone verification status
        const { data: updatedUser, error } = await this.db!
          .from('user_profiles')
          .update({ phone_verified: true })
          .eq('user_id', (existingUser as any).user_id)
          .select()
          .single();

        if (error || !updatedUser) {
          logger.error('Failed to update user:', error);
          return {
            success: false,
            message: 'Failed to verify user',
            error: 'USER_UPDATE_FAILED',
            isNewUser: false
          };
        }

        user = this.mapToAuthenticatedUser(updatedUser);
      }

      // Generate tokens
      const tokens = this.generateTokens(user.id, user.phone);

      return {
        success: true,
        user,
        tokens,
        isNewUser,
        message: 'OTP verified successfully'
      };
    } catch (error) {
      logger.error('Verify OTP error:', error);
      return {
        success: false,
        message: 'Failed to verify OTP',
        error: 'INTERNAL_ERROR',
        isNewUser: false
      };
    }
  }

  /**
   * Resend OTP
   */
  async resendOtp(phone: string, _otpId: string): Promise<OtpServiceResult> {
    // For 2Factor.in, we need to send a new OTP request
    // The previous session ID becomes invalid
    return this.sendOtp(phone);
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<{ success: boolean; tokens?: TokenPair; message: string }> {
    try {
      const decoded = (jwt as any).verify(refreshToken, appConfig.env.JWT_SECRET) as RefreshTokenPayload;
      
      // Generate new tokens
      const tokens = this.generateTokens(decoded.userId, decoded.phone);

      return {
        success: true,
        tokens,
        message: 'Token refreshed successfully'
      };
    } catch (error) {
      return {
        success: false,
        message: 'Invalid refresh token'
      };
    }
  }

  /**
   * Logout user
   */
  async logout(_refreshToken: string): Promise<{ success: boolean; message: string }> {
    // TODO: Implement token blacklisting if needed
    // For now, logout is handled client-side by removing tokens
    return {
      success: true,
      message: 'Logged out successfully'
    };
  }

  /**
   * Get current user
   */
  async getCurrentUser(userId: string): Promise<AuthenticatedUser | null> {
    try {
      const { data: user } = await this.db!
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      return user ? this.mapToAuthenticatedUser(user) : null;
    } catch (error) {
      logger.error('Get current user error:', error);
      return null;
    }
  }

  /**
   * Generate JWT tokens
   */
  private generateTokens(userId: string, phone: string): TokenPair {
    const accessTokenPayload = { userId, phone };
    const accessToken = (jwt as any).sign(
      accessTokenPayload,
      appConfig.env.JWT_SECRET,
      { expiresIn: appConfig.env.JWT_EXPIRES_IN }
    );

    const refreshTokenPayload = { userId, phone, tokenId: uuidv4() };
    const refreshToken = (jwt as any).sign(
      refreshTokenPayload,
      appConfig.env.JWT_SECRET,
      { expiresIn: appConfig.env.REFRESH_TOKEN_EXPIRES_IN }
    );

    return { accessToken, refreshToken };
  }

  /**
   * Map database user to authenticated user
   */
  private mapToAuthenticatedUser(dbUser: any): AuthenticatedUser {
    return {
      id: dbUser.user_id,
      phone: dbUser.phone,
      phoneVerified: dbUser.phone_verified,
      fullName: dbUser.full_name,
      email: dbUser.email,
      avatarUrl: dbUser.avatar_url,
      location: dbUser.location,
      isActive: dbUser.is_active,
      isVerified: dbUser.is_verified,
      createdAt: dbUser.created_at,
      updatedAt: dbUser.updated_at
    };
  }
}
