/**
 * Messages Service
 * Business logic for conversations and messages
 */

import { databaseConfig } from '@/config/database.config';
import { logger } from '@/utils/logger';
import type {
  IGetConversationsRequest,
  IGetConversationsResponse,
  IConversationSummary,
  IGetConversationRequest,
  IGetConversationResponse,
  IConversationDetails,
  ISendMessageRequest,
  ISendMessageResponse,
  IMessage,
  IGetMessagesRequest,
  IGetMessagesResponse,
  IMarkMessageReadResponse,
  IConversationDB,
  IMessageDB,
  IMessageAttachmentDB
} from '@ozgaar/types';

export class MessagesService {
  private db = databaseConfig.getClient();

  /**
   * Get user's conversations
   */
  async getConversations(userId: string, request: IGetConversationsRequest): Promise<IGetConversationsResponse> {
    try {
      const page = request.page || 1;
      const limit = Math.min(request.limit || 20, 100);
      const offset = (page - 1) * limit;

      // Get conversations with job details
      const { data: conversations, error, count } = await this.db!
        .from('conversations')
        .select(`
          *,
          jobs (
            title
          )
        `, { count: 'exact' })
        .or(`worker_id.eq.${userId},poster_id.eq.${userId}`)
        .eq('is_active', true)
        .order('updated_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error('Failed to get conversations:', error);
        return {
          success: false,
          message: 'Failed to retrieve conversations',
          error: 'DATABASE_ERROR'
        };
      }

      // Get user profiles for workers and posters
      const userIds = new Set<string>();
      conversations?.forEach(conv => {
        userIds.add(conv.worker_id as string);
        userIds.add(conv.poster_id as string);
      });

      let userProfiles: any[] = [];
      if (userIds.size > 0) {
        const { data: profiles } = await this.db!
          .from('user_profiles')
          .select('user_id, full_name, avatar_url')
          .in('user_id', Array.from(userIds));

        userProfiles = profiles || [];
      }

      // Get last messages and unread counts for each conversation
      const conversationIds = conversations?.map(c => c.id) || [];
      
      let lastMessages: any[] = [];
      let unreadCounts: any[] = [];

      if (conversationIds.length > 0) {
        const lastMessagesResult = await this.db!
          .from('messages')
          .select('*')
          .in('conversation_id', conversationIds)
          .order('timestamp', { ascending: false });

        const unreadCountsResult = await this.db!
          .from('messages')
          .select('conversation_id')
          .in('conversation_id', conversationIds)
          .neq('sender_id', userId)
          .eq('status', 'sent');

        lastMessages = lastMessagesResult.data || [];
        unreadCounts = unreadCountsResult.data || [];
      }

      // Create a map for quick profile lookup
      const profileMap = new Map<string, any>();
      userProfiles.forEach(profile => {
        profileMap.set(profile.user_id, profile);
      });

      // Format conversations
      const formattedConversations: IConversationSummary[] = conversations?.map((conv: any) => {
        const lastMessage = lastMessages?.find(m => m.conversation_id === conv.id);
        const unreadCount = unreadCounts?.filter(m => m.conversation_id === conv.id).length || 0;

        const workerProfile = profileMap.get(conv.worker_id);
        const posterProfile = profileMap.get(conv.poster_id);

        return {
          id: conv.id as string,
          jobId: conv.job_id as string,
          jobTitle: conv.jobs?.title as string || 'Unknown Job',
          workerId: conv.worker_id as string,
          workerName: workerProfile?.full_name as string || 'Unknown Worker',
          workerAvatar: workerProfile?.avatar_url as string,
          posterId: conv.poster_id as string,
          posterName: posterProfile?.full_name as string || 'Unknown Poster',
          posterAvatar: posterProfile?.avatar_url as string,
          isActive: conv.is_active as boolean,
          lastMessage: lastMessage ? {
            id: lastMessage.id as string,
            text: lastMessage.text as string,
            senderId: lastMessage.sender_id as string,
            timestamp: lastMessage.timestamp as string,
            status: lastMessage.status as 'sent' | 'delivered' | 'read'
          } : undefined,
          unreadCount,
          createdAt: conv.created_at as string,
          updatedAt: conv.updated_at as string
        };
      }) || [];

      return {
        success: true,
        message: 'Conversations retrieved successfully',
        conversations: formattedConversations,
        pagination: {
          page,
          limit,
          totalPages: Math.ceil((count || 0) / limit),
          totalCount: count || 0,
          hasMore: (count || 0) > offset + limit
        }
      };
    } catch (error) {
      logger.error('Get conversations error:', error);
      return {
        success: false,
        message: 'Failed to retrieve conversations',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get conversation details
   */
  async getConversation(userId: string, conversationId: string): Promise<IGetConversationResponse> {
    try {
      // Verify user is participant in conversation
      const { data: conversation, error } = await this.db!
        .from('conversations')
        .select(`
          *,
          jobs (
            title,
            location,
            rate,
            rate_type
          )
        `)
        .eq('id', conversationId)
        .or(`worker_id.eq.${userId},poster_id.eq.${userId}`)
        .single();

      if (error || !conversation) {
        return {
          success: false,
          message: 'Conversation not found or access denied',
          error: 'CONVERSATION_NOT_FOUND'
        };
      }

      // Get user profiles for worker and poster
      const workerId = String(conversation.worker_id);
      const posterId = String(conversation.poster_id);
      
      const { data: userProfiles } = await this.db!
        .from('user_profiles')
        .select('user_id, full_name, avatar_url')
        .in('user_id', [workerId, posterId]);

      const profileMap = new Map<string, any>();
      userProfiles?.forEach(profile => {
        profileMap.set(profile.user_id as string, profile);
      });

      const workerProfile = profileMap.get(workerId);
      const posterProfile = profileMap.get(posterId);

      const conversationDetails: IConversationDetails = {
        id: conversation.id as string,
        jobId: conversation.job_id as string,
        jobTitle: (conversation.jobs as any)?.title as string || 'Unknown Job',
        jobLocation: (conversation.jobs as any)?.location as string || 'Unknown Location',
        jobRate: (conversation.jobs as any)?.rate as number || 0,
        jobRateType: (conversation.jobs as any)?.rate_type as 'hourly' | 'daily' | 'fixed' || 'fixed',
        workerId: conversation.worker_id as string,
        workerName: workerProfile?.full_name as string || 'Unknown Worker',
        workerAvatar: workerProfile?.avatar_url as string,
        posterId: conversation.poster_id as string,
        posterName: posterProfile?.full_name as string || 'Unknown Poster',
        posterAvatar: posterProfile?.avatar_url as string,
        isActive: conversation.is_active as boolean,
        createdAt: conversation.created_at as string,
        updatedAt: conversation.updated_at as string
      };

      return {
        success: true,
        message: 'Conversation retrieved successfully',
        conversation: conversationDetails
      };
    } catch (error) {
      logger.error('Get conversation error:', error);
      return {
        success: false,
        message: 'Failed to retrieve conversation',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get messages in a conversation
   */
  async getMessages(userId: string, conversationId: string, request: IGetMessagesRequest): Promise<IGetMessagesResponse> {
    try {
      // Verify user is participant in conversation
      const { data: conversation } = await this.db!
        .from('conversations')
        .select('id')
        .eq('id', conversationId)
        .or(`worker_id.eq.${userId},poster_id.eq.${userId}`)
        .single();

      if (!conversation) {
        return {
          success: false,
          message: 'Conversation not found or access denied',
          error: 'CONVERSATION_NOT_FOUND'
        };
      }

      const page = request.page || 1;
      const limit = Math.min(request.limit || 50, 100);
      const offset = (page - 1) * limit;

      // Build query
      let query = this.db!
        .from('messages')
        .select(`
          *,
          message_attachments (*)
        `, { count: 'exact' })
        .eq('conversation_id', conversationId);

      // Apply before filter for pagination
      if (request.before) {
        query = query.lt('timestamp', request.before);
      }

      query = query
        .order('timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: messages, error, count } = await query;

      if (error) {
        logger.error('Failed to get messages:', error);
        return {
          success: false,
          message: 'Failed to retrieve messages',
          error: 'DATABASE_ERROR'
        };
      }

      // Get unique sender IDs to fetch user profiles
      const senderIds = [...new Set(messages?.map(msg => msg.sender_id) || [])];
      
      let userProfiles: any[] = [];
      if (senderIds.length > 0) {
        const { data: profiles } = await this.db!
          .from('user_profiles')
          .select('user_id, full_name, avatar_url')
          .in('user_id', senderIds);

        userProfiles = profiles || [];
      }

      // Create a map for quick profile lookup
      const profileMap = new Map<string, any>();
      userProfiles.forEach(profile => {
        profileMap.set(profile.user_id, profile);
      });

      // Format messages
      const formattedMessages: IMessage[] = messages?.map((msg: any) => {
        const senderProfile = profileMap.get(msg.sender_id);
        
        return {
          id: msg.id as string,
          conversationId: msg.conversation_id as string,
          senderId: msg.sender_id as string,
          senderName: senderProfile?.full_name as string || 'Unknown User',
          senderAvatar: senderProfile?.avatar_url as string,
          receiverId: msg.receiver_id as string,
          text: msg.text as string,
          messageType: msg.message_type as 'text' | 'image' | 'document',
          status: msg.status as 'sent' | 'delivered' | 'read',
          timestamp: msg.timestamp as string,
          attachments: msg.message_attachments?.map((att: any) => ({
            id: att.id as string,
            url: att.url as string,
            fileName: att.file_name as string,
            fileSize: att.file_size as number,
            mimeType: att.mime_type as string
          })) || []
        };
      }) || [];

      return {
        success: true,
        message: 'Messages retrieved successfully',
        messages: formattedMessages.reverse(), // Return in chronological order
        pagination: {
          page,
          limit,
          totalPages: Math.ceil((count || 0) / limit),
          totalCount: count || 0,
          hasMore: (count || 0) > offset + limit,
          before: messages && messages.length > 0 ? (messages[messages.length - 1].timestamp as string) : undefined
        }
      };
    } catch (error) {
      logger.error('Get messages error:', error);
      return {
        success: false,
        message: 'Failed to retrieve messages',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Send a message
   */
  async sendMessage(senderId: string, conversationId: string, request: ISendMessageRequest): Promise<ISendMessageResponse> {
    try {
      // Verify user is participant in conversation and get receiver
      const { data: conversation } = await this.db!
        .from('conversations')
        .select('*')
        .eq('id', conversationId)
        .or(`worker_id.eq.${senderId},poster_id.eq.${senderId}`)
        .single();

      if (!conversation) {
        return {
          success: false,
          message: 'Conversation not found or access denied',
          error: 'CONVERSATION_NOT_FOUND'
        };
      }

      // Validate message length (500 char limit as per acceptance criteria)
      if (request.text.length > 500) {
        return {
          success: false,
          message: 'Message text exceeds 500 character limit',
          error: 'MESSAGE_TOO_LONG'
        };
      }

      const receiverId = conversation.worker_id === senderId ? conversation.poster_id : conversation.worker_id;

      // Insert message
      const { data: message, error } = await this.db!
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_id: senderId,
          receiver_id: receiverId,
          text: request.text,
          message_type: request.messageType || 'text',
          status: 'sent'
        })
        .select(`
          *,
          message_attachments (*)
        `)
        .single();

      if (error) {
        logger.error('Failed to send message:', error);
        return {
          success: false,
          message: 'Failed to send message',
          error: 'DATABASE_ERROR'
        };
      }

      // Handle attachments if provided
      let attachments: any[] = [];
      if (request.attachments && request.attachments.length > 0) {
        const { data: insertedAttachments, error: attachError } = await this.db!
          .from('message_attachments')
          .insert(
            request.attachments.map(att => ({
              message_id: message.id,
              url: att.url,
              file_name: att.fileName,
              file_size: att.fileSize,
              mime_type: att.mimeType
            }))
          )
          .select('*');

        if (attachError) {
          logger.error('Failed to insert attachments:', attachError);
        } else {
          attachments = insertedAttachments || [];
        }
      }

      // Update conversation timestamp
      await this.db!
        .from('conversations')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', conversationId);

      const formattedMessage: IMessage = {
        id: message.id as string,
        conversationId: message.conversation_id as string,
        senderId: message.sender_id as string,
        senderName: 'Unknown User', // Will be populated below
        senderAvatar: undefined, // Will be populated below
        receiverId: message.receiver_id as string,
        text: message.text as string,
        messageType: message.message_type as 'text' | 'image' | 'document',
        status: message.status as 'sent' | 'delivered' | 'read',
        timestamp: message.timestamp as string,
        attachments: attachments.map(att => ({
          id: att.id as string,
          url: att.url as string,
          fileName: att.file_name as string,
          fileSize: att.file_size as number,
          mimeType: att.mime_type as string
        }))
      };

      // Fetch sender profile information
      const senderProfileResult: any = await this.db!
        .from('user_profiles')
        .select('full_name, avatar_url')
        .eq('user_id', message.sender_id as string)
        .single();

      if (senderProfileResult.data) {
        formattedMessage.senderName = senderProfileResult.data.full_name as string || 'Unknown User';
        formattedMessage.senderAvatar = senderProfileResult.data.avatar_url as string;
      }

      return {
        success: true,
        message: 'Message sent successfully',
        messageData: formattedMessage
      };
    } catch (error) {
      logger.error('Send message error:', error);
      return {
        success: false,
        message: 'Failed to send message',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Mark message as read
   */
  async markMessageAsRead(userId: string, messageId: string): Promise<IMarkMessageReadResponse> {
    try {
      // Verify user is receiver of the message
      const { data: message, error } = await this.db!
        .from('messages')
        .update({ status: 'read' })
        .eq('id', messageId)
        .eq('receiver_id', userId)
        .select('*')
        .single();

      if (error || !message) {
        return {
          success: false,
          message: 'Message not found or access denied',
          error: 'MESSAGE_NOT_FOUND'
        };
      }

      return {
        success: true,
        message: 'Message marked as read',
        messageData: {
          id: message.id as string,
          conversationId: message.conversation_id as string,
          senderId: message.sender_id as string,
          receiverId: message.receiver_id as string,
          text: message.text as string,
          messageType: message.message_type as 'text' | 'image' | 'document',
          status: message.status as 'sent' | 'delivered' | 'read',
          timestamp: message.timestamp as string
        }
      };
    } catch (error) {
      logger.error('Mark message as read error:', error);
      return {
        success: false,
        message: 'Failed to mark message as read',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Create a conversation between a worker and poster for a job
   */
  async createConversation(jobId: string, workerId: string, posterId: string): Promise<{
    success: boolean;
    conversationId?: string;
    message: string;
    error?: string;
  }> {
    try {
      // Check if conversation already exists
      const { data: existingConversation } = await this.db!
        .from('conversations')
        .select('id')
        .eq('job_id', jobId)
        .eq('worker_id', workerId)
        .eq('poster_id', posterId)
        .single();

      if (existingConversation) {
        return {
          success: true,
          conversationId: existingConversation.id as string,
          message: 'Conversation already exists'
        };
      }

      // Create new conversation
      const { data: conversation, error } = await this.db!
        .from('conversations')
        .insert({
          job_id: jobId,
          worker_id: workerId,
          poster_id: posterId,
          is_active: true
        })
        .select('id')
        .single();

      if (error) {
        logger.error('Failed to create conversation:', error);
        return {
          success: false,
          message: 'Failed to create conversation',
          error: 'DATABASE_ERROR'
        };
      }

      return {
          success: true,
          conversationId: conversation.id as string,
          message: 'Conversation created successfully'
        };
    } catch (error) {
      logger.error('Create conversation error:', error);
      return {
        success: false,
        message: 'Failed to create conversation',
        error: 'INTERNAL_ERROR'
      };
    }
  }
}
