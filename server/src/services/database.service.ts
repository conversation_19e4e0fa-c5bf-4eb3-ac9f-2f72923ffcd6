/**
 * Database Service
 * Centralized database operations and utilities
 */

import { databaseConfig } from '@/config/database.config';
import { logger } from '@/utils/logger';
import type { 
  DatabaseService as IDatabaseService, 
  QueryResult, 
  TransactionOptions 
} from '@ozgaar/types';

export class DatabaseService implements IDatabaseService {
  public client = databaseConfig.getClient();

  /**
   * Check if database is connected
   */
  async isConnected(): Promise<boolean> {
    return await databaseConfig.testConnection();
  }

  /**
   * Disconnect from database
   */
  async disconnect(): Promise<void> {
    // Supabase client doesn't need explicit disconnection
    logger.info('Database service disconnected');
  }

  /**
   * Execute a raw query
   */
  async query<T = any>(sql: string, params?: any[]): Promise<QueryResult<T>> {
    try {
      // Note: Supabase doesn't support raw SQL queries directly
      // This is a placeholder for potential future implementation
      logger.warn('Raw SQL queries not supported with Supabase client');
      return {
        data: null,
        error: new Error('Raw SQL queries not supported')
      };
    } catch (error) {
      logger.error('Query execution error:', error);
      return {
        data: null,
        error: error as Error
      };
    }
  }

  /**
   * Begin a transaction
   */
  async beginTransaction(options?: TransactionOptions): Promise<any> {
    // Supabase handles transactions automatically for single operations
    // For complex transactions, we would need to use stored procedures
    logger.info('Transaction started (Supabase auto-managed)');
    return null;
  }

  /**
   * Commit a transaction
   */
  async commitTransaction(transaction: any): Promise<void> {
    logger.info('Transaction committed (Supabase auto-managed)');
  }

  /**
   * Rollback a transaction
   */
  async rollbackTransaction(transaction: any): Promise<void> {
    logger.info('Transaction rolled back (Supabase auto-managed)');
  }

  /**
   * Get table row count
   */
  async getTableCount(tableName: string, conditions?: Record<string, any>): Promise<number> {
    try {
      let query = this.client!.from(tableName).select('*', { count: 'exact', head: true });

      if (conditions) {
        Object.entries(conditions).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { count, error } = await query;

      if (error) {
        logger.error(`Failed to get count for table ${tableName}:`, error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      logger.error(`Get table count error for ${tableName}:`, error);
      return 0;
    }
  }

  /**
   * Check if a record exists
   */
  async recordExists(tableName: string, conditions: Record<string, any>): Promise<boolean> {
    try {
      let query = this.client!.from(tableName).select('*', { count: 'exact', head: true });

      Object.entries(conditions).forEach(([key, value]) => {
        query = query.eq(key, value);
      });

      const { count, error } = await query;

      if (error) {
        logger.error(`Failed to check existence in table ${tableName}:`, error);
        return false;
      }

      return (count || 0) > 0;
    } catch (error) {
      logger.error(`Record exists check error for ${tableName}:`, error);
      return false;
    }
  }

  /**
   * Bulk insert records
   */
  async bulkInsert<T = any>(tableName: string, records: any[]): Promise<QueryResult<T[]>> {
    try {
      const { data, error } = await this.client!
        .from(tableName)
        .insert(records)
        .select();

      if (error) {
        logger.error(`Bulk insert failed for table ${tableName}:`, error);
        return {
          data: null,
          error: new Error(error.message)
        };
      }

      return {
        data: data as T[],
        error: null
      };
    } catch (error) {
      logger.error(`Bulk insert error for ${tableName}:`, error);
      return {
        data: null,
        error: error as Error
      };
    }
  }

  /**
   * Bulk update records
   */
  async bulkUpdate<T = any>(
    tableName: string, 
    updates: Array<{ conditions: Record<string, any>; data: Record<string, any> }>
  ): Promise<QueryResult<T[]>> {
    try {
      // Supabase doesn't support bulk updates directly
      // We need to perform individual updates
      const results: T[] = [];
      
      for (const update of updates) {
        let query = this.client!.from(tableName).update(update.data);
        
        Object.entries(update.conditions).forEach(([key, value]) => {
          query = query.eq(key, value);
        });

        const { data, error } = await query.select();

        if (error) {
          logger.error(`Bulk update failed for table ${tableName}:`, error);
          return {
            data: null,
            error: new Error(error.message)
          };
        }

        if (data) {
          results.push(...(data as T[]));
        }
      }

      return {
        data: results,
        error: null
      };
    } catch (error) {
      logger.error(`Bulk update error for ${tableName}:`, error);
      return {
        data: null,
        error: error as Error
      };
    }
  }

  /**
   * Delete records with conditions
   */
  async deleteWhere(tableName: string, conditions: Record<string, any>): Promise<QueryResult<any>> {
    try {
      let query = this.client!.from(tableName).delete();

      Object.entries(conditions).forEach(([key, value]) => {
        query = query.eq(key, value);
      });

      const { data, error } = await query;

      if (error) {
        logger.error(`Delete failed for table ${tableName}:`, error);
        return {
          data: null,
          error: new Error(error.message)
        };
      }

      return {
        data,
        error: null
      };
    } catch (error) {
      logger.error(`Delete error for ${tableName}:`, error);
      return {
        data: null,
        error: error as Error
      };
    }
  }
}

export const databaseService = new DatabaseService();
