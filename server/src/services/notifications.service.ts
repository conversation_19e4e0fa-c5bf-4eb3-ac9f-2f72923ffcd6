/**
 * Notifications Service
 * Business logic for notifications and device registration
 * 
 * This service handles all notification-related operations including:
 * - Creating notifications (used internally by other services)
 * - Retrieving user notifications with filtering and pagination
 * - Marking notifications as read
 * - Deleting notifications
 * - Managing device tokens for push notifications
 * 
 * Other services should use the createNotification method to notify users about events.
 * For example, when a job application is received, the applications service would call
 * createNotification to notify the job poster.
 */

import { databaseConfig } from '@/config/database.config';
import { logger } from '@/utils/logger';
import { socketManager } from '@/index'; // Import the global socket manager
import type {
  IGetNotificationsRequest,
  IGetNotificationsResponse,
  INotification,
  IMarkNotificationReadResponse,
  IMarkAllNotificationsReadResponse,
  IDeleteNotificationResponse,
  IRegisterDeviceTokenRequest,
  IRegisterDeviceTokenResponse,
  ICreateNotificationRequest,
  ICreateNotificationResponse,
  INotificationDB,
  INotificationDeviceDB
} from '@ozgaar/types';

export class NotificationsService {
  private db = databaseConfig.getClient();

  /**
   * Get user's notifications
   */
  async getNotifications(userId: string, request: IGetNotificationsRequest): Promise<IGetNotificationsResponse> {
    try {
      const page = request.page || 1;
      const limit = Math.min(request.limit || 20, 100);
      const offset = (page - 1) * limit;

      // Build query
      let query = this.db!
        .from('notifications')
        .select('*', { count: 'exact' })
        .eq('user_id', userId);

      // Apply type filter if provided
      if (request.type) {
        query = query.eq('type', request.type);
      }

      // Apply read status filter if provided
      if (request.isRead !== undefined) {
        query = query.eq('is_read', request.isRead);
      }

      // Apply pagination and ordering
      query = query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: notifications, error, count } = await query;

      if (error) {
        logger.error('Failed to get notifications:', error);
        return {
          success: false,
          message: 'Failed to retrieve notifications',
          error: 'DATABASE_ERROR'
        };
      }

      // Get summary counts
      const { data: summaryData } = await this.db!
        .from('notifications')
        .select('is_read, is_urgent')
        .eq('user_id', userId);

      const summary = {
        total: summaryData?.length || 0,
        unread: summaryData?.filter(n => !n.is_read).length || 0,
        urgent: summaryData?.filter(n => n.is_urgent && !n.is_read).length || 0
      };

      // Format notifications
      const formattedNotifications: INotification[] = notifications?.map((notif: any) => ({
        id: notif.id as string,
        userId: notif.user_id as string,
        type: notif.type as string,
        title: notif.title as string,
        message: notif.message as string,
        isRead: notif.is_read as boolean,
        isUrgent: notif.is_urgent as boolean,
        actionUrl: notif.action_url as string,
        metadata: notif.metadata as Record<string, any>,
        createdAt: notif.created_at as string
      })) || [];

      return {
        success: true,
        message: 'Notifications retrieved successfully',
        notifications: formattedNotifications,
        pagination: {
          page,
          limit,
          totalPages: Math.ceil((count || 0) / limit),
          totalCount: count || 0,
          hasMore: (count || 0) > offset + limit
        },
        summary
      };
    } catch (error) {
      logger.error('Get notifications error:', error);
      return {
        success: false,
        message: 'Failed to retrieve notifications',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(userId: string, notificationId: string): Promise<IMarkNotificationReadResponse> {
    try {
      const { data: notification, error } = await this.db!
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId)
        .eq('user_id', userId)
        .select('*')
        .single();

      if (error || !notification) {
        return {
          success: false,
          message: 'Notification not found or access denied',
          error: 'NOTIFICATION_NOT_FOUND'
        };
      }

      return {
        success: true,
        message: 'Notification marked as read'
      };
    } catch (error) {
      logger.error('Mark notification as read error:', error);
      return {
        success: false,
        message: 'Failed to mark notification as read',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllNotificationsAsRead(userId: string): Promise<IMarkAllNotificationsReadResponse> {
    try {
      const { data: notifications, error } = await this.db!
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', userId)
        .eq('is_read', false)
        .select('id');

      if (error) {
        logger.error('Failed to mark all notifications as read:', error);
        return {
          success: false,
          message: 'Failed to mark all notifications as read',
          error: 'DATABASE_ERROR'
        };
      }

      return {
        success: true,
        message: 'All notifications marked as read',
        updatedCount: notifications?.length || 0
      };
    } catch (error) {
      logger.error('Mark all notifications as read error:', error);
      return {
        success: false,
        message: 'Failed to mark all notifications as read',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Delete notification
   */
  async deleteNotification(userId: string, notificationId: string): Promise<IDeleteNotificationResponse> {
    try {
      const { data: notification, error } = await this.db!
        .from('notifications')
        .delete()
        .eq('id', notificationId)
        .eq('user_id', userId)
        .select('*')
        .single();

      if (error || !notification) {
        return {
          success: false,
          message: 'Notification not found or access denied',
          error: 'NOTIFICATION_NOT_FOUND'
        };
      }

      return {
        success: true,
        message: 'Notification deleted successfully'
      };
    } catch (error) {
      logger.error('Delete notification error:', error);
      return {
        success: false,
        message: 'Failed to delete notification',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Register device token for push notifications
   */
  async registerDeviceToken(userId: string, request: IRegisterDeviceTokenRequest): Promise<IRegisterDeviceTokenResponse> {
    try {
      // Check if device token already exists
      const { data: existingDevice } = await this.db!
        .from('notification_devices')
        .select('*')
        .eq('user_id', userId)
        .eq('device_token', request.deviceToken)
        .single();

      if (existingDevice) {
        return {
          success: true,
          message: 'Device token already registered'
        };
      }

      // Insert new device token
      const { data: device, error } = await this.db!
        .from('notification_devices')
        .insert({
          user_id: userId,
          device_token: request.deviceToken,
          platform: request.platform
        })
        .select('*')
        .single();

      if (error) {
        logger.error('Failed to register device token:', error);
        return {
          success: false,
          message: 'Failed to register device token',
          error: 'DATABASE_ERROR'
        };
      }

      return {
        success: true,
        message: 'Device token registered successfully'
      };
    } catch (error) {
      logger.error('Register device token error:', error);
      return {
        success: false,
        message: 'Failed to register device token',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Create notification (internal use)
   * 
   * This method is used internally by other services to create notifications for users.
   * It should be called whenever an event occurs that requires notifying a user.
   * 
   * Example use cases:
   * - Job application received (notify poster)
   * - Job application status changed (notify worker)
   * - New message received (notify recipient)
   * - Profile viewed (notify user)
   * - Job posted (notify workers in area)
   * 
   * Usage example:
   * ```typescript
   * const notificationService = new NotificationsService();
   * await notificationService.createNotification({
   *   userId: 'user-uuid',
   *   type: 'job_application',
   *   title: 'New Job Application',
   *   message: 'You have received a new application for your job posting',
   *   isUrgent: false,
   *   actionUrl: '/jobs/job-uuid/applications/app-uuid',
   *   metadata: {
   *     jobId: 'job-uuid',
   *     applicantId: 'applicant-uuid'
   *   }
   * });
   * ```
   * 
   * When called, this method will:
   * 1. Insert the notification into the database
   * 2. Format the notification data
   * 3. Broadcast the new notification to the user via WebSocket (if they're online)
   * 4. Broadcast updated notification counts to the user
   */
  async createNotification(request: ICreateNotificationRequest): Promise<ICreateNotificationResponse> {
    try {
      const { data: notification, error } = await this.db!
        .from('notifications')
        .insert({
          user_id: request.userId,
          type: request.type,
          title: request.title,
          message: request.message,
          is_urgent: request.isUrgent || false,
          action_url: request.actionUrl,
          metadata: request.metadata
        })
        .select('*')
        .single();

      if (error) {
        logger.error('Failed to create notification:', error);
        return {
          success: false,
          message: 'Failed to create notification',
          error: 'DATABASE_ERROR'
        };
      }

      const formattedNotification: INotification = {
        id: notification.id as string,
        userId: notification.user_id as string,
        type: notification.type as string,
        title: notification.title as string,
        message: notification.message as string,
        isRead: notification.is_read as boolean,
        isUrgent: notification.is_urgent as boolean,
        actionUrl: notification.action_url as string,
        metadata: notification.metadata as Record<string, any>,
        createdAt: notification.created_at as string
      };

      // Broadcast new notification to user if socketManager is available
      if (socketManager) {
        const notificationGateway = socketManager.getNotificationGateway();
        await notificationGateway.broadcastNewNotification(request.userId, formattedNotification);
      }

      return {
        success: true,
        message: 'Notification created successfully',
        notification: formattedNotification
      };
    } catch (error) {
      logger.error('Create notification error:', error);
      return {
        success: false,
        message: 'Failed to create notification',
        error: 'INTERNAL_ERROR'
      };
    }
  }
}
