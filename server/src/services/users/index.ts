/**
 * Users Service
 * Business logic for user profile and settings operations
 */

import { databaseConfig } from '@/config/database.config';
import { logger } from '@/utils/logger';
import type {
  CreateWorkerProfileRequest,
  CreatePosterProfileRequest,
  UpdateUserProfileRequest,
  UpdateUserSettingsRequest,
  IModeSelectionRequest,
  IModeSelectionResponse,
  IGetModeStatusResponse,
  IUserModeStatus,
  IInitializeSettingsRequest,
  IInitializeSettingsResponse,
  IWorkerPersonaRequest,
  IWorkerPersonaResponse,
  IGetWorkerPersonasResponse,
  ICreatePosterProfileRequest,
  IUpdatePosterProfileRequest,
  IGetPosterProfileResponse,
  IGetPosterStatsResponse,
  IPosterStats
} from '@ozgaar/types';
import {
  DEFAULT_NOTIFICATIONS,
  DEFAULT_PRIVACY,
  DEFAULT_PREFERENCES
} from '@ozgaar/types';
import { 
  createWorkerProfile,
  createPosterProfile,
  updateProfile,
  getSettings,
  updateSettings,
  selectMode,
  getModeStatus,
  initializeSettings,
  createWorkerPersona,
  getWorkerPersonas,
  updatePosterProfile,
  getPosterProfile,
  getPosterStats
} from './user-operations';
import * as constants from './constants';

export class UsersService {
  private db = databaseConfig.getClient();

  /**
   * Create worker profile
   */
  async createWorkerProfile(userId: string, profileData: CreateWorkerProfileRequest) {
    return createWorkerProfile(this.db!, userId, profileData);
  }

  /**
   * Create poster profile
   */
  async createPosterProfile(userId: string, profileData: ICreatePosterProfileRequest) {
    return createPosterProfile(this.db!, userId, profileData);
  }

  /**
   * Update user profile
   */
  async updateProfile(userId: string, profileData: UpdateUserProfileRequest) {
    return updateProfile(this.db!, userId, profileData);
  }

  /**
   * Get user settings
   */
  async getSettings(userId: string) {
    return getSettings(this.db!, userId);
  }

  /**
   * Update user settings
   */
  async updateSettings(userId: string, settingsData: UpdateUserSettingsRequest) {
    return updateSettings(this.db!, userId, settingsData);
  }

  /**
   * Select user mode and initialize settings
   */
  async selectMode(userId: string, modeData: IModeSelectionRequest): Promise<IModeSelectionResponse> {
    return selectMode(this.db!, userId, modeData, DEFAULT_NOTIFICATIONS, DEFAULT_PRIVACY, DEFAULT_PREFERENCES);
  }

  /**
   * Get user mode status
   */
  async getModeStatus(userId: string): Promise<IGetModeStatusResponse> {
    return getModeStatus(this.db!, userId);
  }

  /**
   * Initialize user settings with defaults
   */
  async initializeSettings(userId: string, settingsData: IInitializeSettingsRequest): Promise<IInitializeSettingsResponse> {
    return initializeSettings(this.db!, userId, settingsData, DEFAULT_NOTIFICATIONS, DEFAULT_PRIVACY, DEFAULT_PREFERENCES);
  }

  /**
   * Create worker persona
   */
  async createWorkerPersona(userId: string, personaData: IWorkerPersonaRequest): Promise<IWorkerPersonaResponse> {
    return createWorkerPersona(this.db!, userId, personaData);
  }

  /**
   * Get worker personas
   */
  async getWorkerPersonas(userId: string): Promise<IGetWorkerPersonasResponse> {
    return getWorkerPersonas(this.db!, userId);
  }

  /**
   * Update poster profile
   */
  async updatePosterProfile(userId: string, profileData: IUpdatePosterProfileRequest) {
    return updatePosterProfile(this.db!, userId, profileData);
  }

  /**
   * Get poster profile
   */
  async getPosterProfile(userId: string): Promise<IGetPosterProfileResponse> {
    return getPosterProfile(this.db!, userId);
  }

  /**
   * Get poster statistics
   */
  async getPosterStats(userId: string): Promise<IGetPosterStatsResponse> {
    return getPosterStats(this.db!, userId);
  }
}