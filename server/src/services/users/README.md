# Users Service Module

This directory contains the refactored users service module, organized into smaller, focused files following the single-responsibility principle.

## Structure

- `index.ts` - Main entry point exporting the UsersService class
- `constants.ts` - Shared constants used across user-related functionality
- `types.ts` - Internal types used within the users service module
- `user-operations.ts` - Business logic for user profile and settings operations

## Design Principles

1. **Single Responsibility**: Each module has a clear, focused purpose
2. **Logical Separation**: Related functionality is grouped together
3. **Backward Compatibility**: The main UsersService class is still exported from the original location
4. **Type Safety**: Full TypeScript support with proper typing
5. **Maintainability**: Smaller, focused files are easier to understand and modify

## Usage

The service can still be imported exactly as before:

```typescript
import { UsersService } from '@/services/users.service';
```

This maintains full backward compatibility with existing code while providing a cleaner internal structure.