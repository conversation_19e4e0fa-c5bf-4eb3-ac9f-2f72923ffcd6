/**
 * Users Service Operations
 * Business logic for user profile and settings operations
 */

import type { SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@/utils/logger';
import type {
  CreateWorkerProfileRequest,
  CreatePosterProfileRequest,
  UpdateUserProfileRequest,
  UpdateUserSettingsRequest,
  IModeSelectionRequest,
  IModeSelectionResponse,
  IGetModeStatusResponse,
  IUserModeStatus,
  IInitializeSettingsRequest,
  IInitializeSettingsResponse,
  IWorkerPersonaRequest,
  IWorkerPersonaResponse,
  IGetWorkerPersonasResponse,
  ICreatePosterProfileRequest,
  IUpdatePosterProfileRequest,
  IGetPosterProfileResponse,
  IGetPosterStatsResponse,
  IPosterStats
} from '@ozgaar/types';
import { 
  FormattedWorkerProfile, 
  FormattedPosterProfile, 
  FormattedUserSettings, 
  FormattedWorkerPersona 
} from './types';
import * as constants from './constants';

/**
 * Create worker profile
 */
export async function createWorkerProfile(
  db: SupabaseClient,
  userId: string,
  profileData: CreateWorkerProfileRequest
): Promise<{
  success: boolean;
  profile?: FormattedWorkerProfile;
  message: string;
  error?: string;
}> {
  try {
    const { data: profile, error } = await db
      .from('worker_profiles')
      .insert({
        user_id: userId,
        primary_skill: profileData.primarySkill,
        skills: profileData.skills,
        experience: profileData.experience,
        hourly_rate: profileData.hourlyRate,
        daily_rate: profileData.dailyRate,
        about: profileData.about,
        phone_visible: profileData.phoneVisible || false,
        portfolio_photos: profileData.portfolioPhotos,
        is_available: true
      })
      .select()
      .single();

    if (error) {
      logger.error('Failed to create worker profile:', error);
      return {
        success: false,
        message: 'Failed to create worker profile',
        error: 'DATABASE_ERROR'
      };
    }

    return {
      success: true,
      profile: {
        userId: profile.user_id,
        primarySkill: profile.primary_skill,
        skills: profile.skills,
        experience: profile.experience,
        hourlyRate: profile.hourly_rate,
        dailyRate: profile.daily_rate,
        about: profile.about,
        isAvailable: profile.is_available,
        createdAt: profile.updated_at
      },
      message: 'Worker profile created successfully'
    };
  } catch (error) {
    logger.error('Create worker profile error:', error);
    return {
      success: false,
      message: 'Failed to create worker profile',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Create poster profile
 */
export async function createPosterProfile(
  db: SupabaseClient,
  userId: string,
  profileData: ICreatePosterProfileRequest
): Promise<{
  success: boolean;
  profile?: FormattedPosterProfile;
  message: string;
  error?: string;
}> {
  try {
    // Check if poster profile already exists
    const { data: existingProfile } = await db
      .from('poster_profiles')
      .select('user_id')
      .eq('user_id', userId)
      .single();

    if (existingProfile) {
      return {
        success: false,
        message: 'Poster profile already exists',
        error: 'PROFILE_EXISTS'
      };
    }

    // Create poster profile
    const profileToCreate = {
      user_id: userId,
      company_name: profileData.companyName,
      about: profileData.about,
      industry: profileData.industry,
      website: profileData.website,
      address: profileData.address,
      city: profileData.city,
      state: profileData.state,
      pincode: profileData.pincode,
      gst_number: profileData.gstNumber,
      pan_number: profileData.panNumber,
      business_verified: profileData.businessVerified || false,
      business_documents: profileData.businessDocuments,
      jobs_posted: 0,
      total_spent: 0,
      rating: 0,
      review_count: 0
    };

    const { data: profile, error } = await db
      .from('poster_profiles')
      .insert(profileToCreate)
      .select()
      .single();

    if (error) {
      logger.error('Failed to create poster profile:', error);
      return {
        success: false,
        message: 'Failed to create poster profile',
        error: 'DATABASE_ERROR'
      };
    }

    return {
      success: true,
      profile: {
        userId: profile.user_id,
        companyName: profile.company_name,
        about: profile.about,
        industry: profile.industry,
        companySize: profile.company_size,
        website: profile.website,
        address: profile.address,
        city: profile.city,
        state: profile.state,
        pincode: profile.pincode,
        businessVerified: profile.business_verified,
        jobsPosted: profile.jobs_posted,
        totalSpent: profile.total_spent,
        rating: profile.rating,
        reviewCount: profile.review_count,
        createdAt: profile.updated_at
      },
      message: 'Poster profile created successfully'
    };
  } catch (error) {
    logger.error('Create poster profile error:', error);
    return {
      success: false,
      message: 'Failed to create poster profile',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Update user profile
 */
export async function updateProfile(
  db: SupabaseClient,
  userId: string,
  profileData: UpdateUserProfileRequest
): Promise<{
  success: boolean;
  user?: any;
  message: string;
  error?: string;
}> {
  try {
    const updateData: any = {};
    
    if (profileData.fullName) updateData.full_name = profileData.fullName;
    if (profileData.email) updateData.email = profileData.email;
    if (profileData.location) updateData.location = profileData.location;
    if (profileData.avatarUrl) updateData.avatar_url = profileData.avatarUrl;

    const { data: user, error } = await db
      .from('user_profiles')
      .update(updateData)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      logger.error('Failed to update profile:', error);
      return {
        success: false,
        message: 'Failed to update profile',
        error: 'DATABASE_ERROR'
      };
    }

    return {
      success: true,
      user: {
        id: user.user_id,
        phone: user.phone,
        fullName: user.full_name,
        email: user.email,
        location: user.location,
        avatarUrl: user.avatar_url,
        updatedAt: user.updated_at
      },
      message: 'Profile updated successfully'
    };
  } catch (error) {
    logger.error('Update profile error:', error);
    return {
      success: false,
      message: 'Failed to update profile',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Get user settings
 */
export async function getSettings(
  db: SupabaseClient,
  userId: string
): Promise<{
  success: boolean;
  settings?: FormattedUserSettings;
  message: string;
  error?: string;
}> {
  try {
    const { data: settings, error } = await db
      .from('user_settings')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      logger.error('Failed to get settings:', error);
      return {
        success: false,
        message: 'Failed to retrieve settings',
        error: 'DATABASE_ERROR'
      };
    }

    // If no settings exist, create default settings
    if (!settings) {
      const defaultSettings = {
        user_id: userId,
        notifications: {},
        privacy: {},
        language: 'english',
        preferences: {}
      };

      const { data: newSettings, error: createError } = await db
        .from('user_settings')
        .insert(defaultSettings)
        .select()
        .single();

      if (createError) {
        logger.error('Failed to create default settings:', createError);
        return {
          success: false,
          message: 'Failed to create settings',
          error: 'DATABASE_ERROR'
        };
      }

      return {
        success: true,
        settings: {
          userId: newSettings.user_id,
          notifications: newSettings.notifications,
          privacy: newSettings.privacy,
          language: newSettings.language,
          preferences: newSettings.preferences,
          updatedAt: newSettings.updated_at
        },
        message: 'Settings retrieved successfully'
      };
    }

    return {
      success: true,
      settings: {
        userId: settings.user_id,
        notifications: settings.notifications,
        privacy: settings.privacy,
        language: settings.language,
        preferences: settings.preferences,
        updatedAt: settings.updated_at
      },
      message: 'Settings retrieved successfully'
    };
  } catch (error) {
    logger.error('Get settings error:', error);
    return {
      success: false,
      message: 'Failed to retrieve settings',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Update user settings
 */
export async function updateSettings(
  db: SupabaseClient,
  userId: string,
  settingsData: UpdateUserSettingsRequest
): Promise<{
  success: boolean;
  settings?: FormattedUserSettings;
  message: string;
  error?: string;
}> {
  try {
    const updateData: any = {};
    
    if (settingsData.notifications) updateData.notifications = settingsData.notifications;
    if (settingsData.privacy) updateData.privacy = settingsData.privacy;
    if (settingsData.language) updateData.language = settingsData.language;
    if (settingsData.preferences) updateData.preferences = settingsData.preferences;

    const { data: settings, error } = await db
      .from('user_settings')
      .update(updateData)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      logger.error('Failed to update settings:', error);
      return {
        success: false,
        message: 'Failed to update settings',
        error: 'DATABASE_ERROR'
      };
    }

    return {
      success: true,
      settings: {
        userId: settings.user_id,
        notifications: settings.notifications,
        privacy: settings.privacy,
        language: settings.language,
        preferences: settings.preferences,
        updatedAt: settings.updated_at
      },
      message: 'Settings updated successfully'
    };
  } catch (error) {
    logger.error('Update settings error:', error);
    return {
      success: false,
      message: 'Failed to update settings',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Select user mode and initialize settings
 */
export async function selectMode(
  db: SupabaseClient,
  userId: string,
  modeData: IModeSelectionRequest,
  DEFAULT_NOTIFICATIONS: any,
  DEFAULT_PRIVACY: any,
  DEFAULT_PREFERENCES: any
): Promise<IModeSelectionResponse> {
  try {
    // Update user profile with selected mode
    const { data: updatedUser, error: userError } = await db
      .from('user_profiles')
      .update({
        selected_mode: modeData.mode,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select()
      .single();

    if (userError) {
      logger.error('Failed to update user mode:', userError);
      return {
        success: false,
        message: 'Failed to select mode',
        error: 'DATABASE_ERROR'
      };
    }

    // Initialize settings with defaults and user preferences
    const defaultSettings = {
      user_id: userId,
      language: modeData.language || 'english',
      notifications: {
        ...DEFAULT_NOTIFICATIONS,
        ...modeData.preferences?.notifications
      },
      privacy: {
        ...DEFAULT_PRIVACY,
        ...modeData.preferences?.privacy
      },
      preferences: DEFAULT_PREFERENCES
    };

    // Check if settings already exist
    const { data: existingSettings } = await db
      .from('user_settings')
      .select('user_id')
      .eq('user_id', userId)
      .single();

    let settings;
    if (existingSettings) {
      // Update existing settings
      const { data: updatedSettings, error: settingsError } = await db
        .from('user_settings')
        .update(defaultSettings)
        .eq('user_id', userId)
        .select()
        .single();

      if (settingsError) {
        logger.error('Failed to update settings:', settingsError);
        return {
          success: false,
          message: 'Failed to initialize settings',
          error: 'DATABASE_ERROR'
        };
      }
      settings = updatedSettings;
    } else {
      // Create new settings
      const { data: newSettings, error: settingsError } = await db
        .from('user_settings')
        .insert(defaultSettings)
        .select()
        .single();

      if (settingsError) {
        logger.error('Failed to create settings:', settingsError);
        return {
          success: false,
          message: 'Failed to initialize settings',
          error: 'DATABASE_ERROR'
        };
      }
      settings = newSettings;
    }

    // Check if user has profile for selected mode
    const hasProfile = await checkUserProfile(db, userId, modeData.mode);

    return {
      success: true,
      message: `Mode selected as ${modeData.mode} successfully`,
      user: {
        id: userId,
        mode: modeData.mode,
        hasProfile,
        settings: {
          language: settings.language,
          notifications: settings.notifications,
          privacy: settings.privacy,
          preferences: settings.preferences
        }
      },
      nextStep: hasProfile ? 'complete' : 'create_profile'
    };
  } catch (error) {
    logger.error('Select mode error:', error);
    return {
      success: false,
      message: 'Failed to select mode',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Get user mode status
 */
export async function getModeStatus(
  db: SupabaseClient,
  userId: string
): Promise<IGetModeStatusResponse> {
  try {
    const { data: user, error: userError } = await db
      .from('user_profiles')
      .select('selected_mode, profile_completed')
      .eq('user_id', userId)
      .single();

    if (userError) {
      logger.error('Failed to get user:', userError);
      return {
        success: false,
        message: 'Failed to get mode status',
        error: 'DATABASE_ERROR'
      };
    }

    // Check for worker profile
    const { data: workerProfile } = await db
      .from('worker_profiles')
      .select('user_id')
      .eq('user_id', userId)
      .single();

    // Check for poster profile
    const { data: posterProfile } = await db
      .from('poster_profiles')
      .select('user_id')
      .eq('user_id', userId)
      .single();

    // Check for settings
    const { data: settings } = await db
      .from('user_settings')
      .select('user_id')
      .eq('user_id', userId)
      .single();

    const status: IUserModeStatus = {
      userId,
      selectedMode: user?.selected_mode,
      hasWorkerProfile: !!workerProfile,
      hasPosterProfile: !!posterProfile,
      hasSettings: !!settings,
      isProfileComplete: user?.profile_completed || false
    };

    return {
      success: true,
      status,
      message: 'Mode status retrieved successfully'
    };
  } catch (error) {
    logger.error('Get mode status error:', error);
    return {
      success: false,
      message: 'Failed to get mode status',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Initialize user settings with defaults
 */
export async function initializeSettings(
  db: SupabaseClient,
  userId: string,
  settingsData: IInitializeSettingsRequest,
  DEFAULT_NOTIFICATIONS: any,
  DEFAULT_PRIVACY: any,
  DEFAULT_PREFERENCES: any
): Promise<IInitializeSettingsResponse> {
  try {
    const defaultSettings = {
      user_id: userId,
      language: settingsData.language,
      notifications: {
        ...DEFAULT_NOTIFICATIONS,
        ...settingsData.notifications
      },
      privacy: {
        ...DEFAULT_PRIVACY,
        ...settingsData.privacy
      },
      preferences: {
        ...DEFAULT_PREFERENCES,
        ...settingsData.preferences
      }
    };

    const { data: settings, error } = await db
      .from('user_settings')
      .upsert(defaultSettings)
      .select()
      .single();

    if (error) {
      logger.error('Failed to initialize settings:', error);
      return {
        success: false,
        message: 'Failed to initialize settings',
        error: 'DATABASE_ERROR'
      };
    }

    return {
      success: true,
      message: 'Settings initialized successfully',
      settings: {
        userId: settings.user_id,
        language: settings.language,
        notifications: settings.notifications,
        privacy: settings.privacy,
        preferences: settings.preferences,
        createdAt: settings.updated_at
      }
    };
  } catch (error) {
    logger.error('Initialize settings error:', error);
    return {
      success: false,
      message: 'Failed to initialize settings',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Check if user has profile for the selected mode
 */
async function checkUserProfile(
  db: SupabaseClient,
  userId: string,
  mode: 'worker' | 'poster'
): Promise<boolean> {
  try {
    const tableName = mode === 'worker' ? 'worker_profiles' : 'poster_profiles';
    const { data } = await db
      .from(tableName)
      .select('user_id')
      .eq('user_id', userId)
      .single();

    return !!data;
  } catch (error) {
    return false;
  }
}

/**
 * Create worker persona
 */
export async function createWorkerPersona(
  db: SupabaseClient,
  userId: string,
  personaData: IWorkerPersonaRequest
): Promise<IWorkerPersonaResponse> {
  try {
    // Check if user has worker profile
    const { data: workerProfile } = await db
      .from('worker_profiles')
      .select('user_id')
      .eq('user_id', userId)
      .single();

    if (!workerProfile) {
      return {
        success: false,
        message: 'Worker profile required to create personas',
        error: 'WORKER_PROFILE_REQUIRED'
      };
    }

    // Create persona
    const { data: persona, error } = await db
      .from('worker_personas')
      .insert({
        user_id: userId,
        skill: personaData.skill,
        experience: personaData.experience,
        hourly_rate: personaData.hourlyRate,
        daily_rate: personaData.dailyRate,
        is_active: personaData.isActive !== false // default to true
      })
      .select()
      .single();

    if (error) {
      logger.error('Failed to create worker persona:', error);
      return {
        success: false,
        message: 'Failed to create worker persona',
        error: 'DATABASE_ERROR'
      };
    }

    return {
      success: true,
      message: 'Worker persona created successfully',
      persona: {
        id: persona.id,
        userId: persona.user_id,
        skill: persona.skill,
        experience: persona.experience,
        hourlyRate: persona.hourly_rate,
        dailyRate: persona.daily_rate,
        isActive: persona.is_active,
        completedJobs: persona.completed_jobs || 0,
        rating: persona.rating || 0,
        reviewCount: persona.review_count || 0,
        createdAt: persona.created_at
      }
    };
  } catch (error) {
    logger.error('Create worker persona error:', error);
    return {
      success: false,
      message: 'Failed to create worker persona',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Get worker personas
 */
export async function getWorkerPersonas(
  db: SupabaseClient,
  userId: string
): Promise<IGetWorkerPersonasResponse> {
  try {
    const { data: personas, error } = await db
      .from('worker_personas')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Failed to get worker personas:', error);
      return {
        success: false,
        message: 'Failed to retrieve worker personas',
        error: 'DATABASE_ERROR'
      };
    }

    const formattedPersonas = personas?.map((persona: any) => ({
      id: persona.id,
      userId: persona.user_id,
      skill: persona.skill,
      experience: persona.experience,
      hourlyRate: persona.hourly_rate,
      dailyRate: persona.daily_rate,
      isActive: persona.is_active,
      completedJobs: persona.completed_jobs || 0,
      rating: persona.rating || 0,
      reviewCount: persona.review_count || 0,
      createdAt: persona.created_at,
      updatedAt: persona.updated_at
    })) || [];

    return {
      success: true,
      message: 'Worker personas retrieved successfully',
      personas: formattedPersonas
    };
  } catch (error) {
    logger.error('Get worker personas error:', error);
    return {
      success: false,
      message: 'Failed to retrieve worker personas',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Update poster profile
 */
export async function updatePosterProfile(
  db: SupabaseClient,
  userId: string,
  profileData: IUpdatePosterProfileRequest
): Promise<{
  success: boolean;
  profile?: any;
  message: string;
  error?: string;
}> {
  try {
    const updateData: any = {};
    
    if (profileData.companyName !== undefined) updateData.company_name = profileData.companyName;
    if (profileData.about !== undefined) updateData.about = profileData.about;
    if (profileData.industry !== undefined) updateData.industry = profileData.industry;
    if (profileData.companySize !== undefined) updateData.company_size = profileData.companySize;
    if (profileData.website !== undefined) updateData.website = profileData.website;
    if (profileData.address !== undefined) updateData.address = profileData.address;
    if (profileData.city !== undefined) updateData.city = profileData.city;
    if (profileData.state !== undefined) updateData.state = profileData.state;
    if (profileData.pincode !== undefined) updateData.pincode = profileData.pincode;
    if (profileData.gstNumber !== undefined) updateData.gst_number = profileData.gstNumber;
    if (profileData.panNumber !== undefined) updateData.pan_number = profileData.panNumber;

    updateData.updated_at = new Date().toISOString();

    const { data: profile, error } = await db
      .from('poster_profiles')
      .update(updateData)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      logger.error('Failed to update poster profile:', error);
      return {
        success: false,
        message: 'Failed to update poster profile',
        error: 'DATABASE_ERROR'
      };
    }

    return {
      success: true,
      profile: {
        userId: profile.user_id,
        companyName: profile.company_name,
        about: profile.about,
        industry: profile.industry,
        companySize: profile.company_size,
        website: profile.website,
        address: profile.address,
        city: profile.city,
        state: profile.state,
        pincode: profile.pincode,
        businessVerified: profile.business_verified,
        updatedAt: profile.updated_at
      },
      message: 'Poster profile updated successfully'
    };
  } catch (error) {
    logger.error('Update poster profile error:', error);
    return {
      success: false,
      message: 'Failed to update poster profile',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Get poster profile
 */
export async function getPosterProfile(
  db: SupabaseClient,
  userId: string
): Promise<IGetPosterProfileResponse> {
  try {
    const { data: profile, error } = await db
      .from('poster_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !profile) {
      return {
        success: false,
        message: 'Poster profile not found',
        error: 'PROFILE_NOT_FOUND'
      };
    }

    return {
      success: true,
      message: 'Poster profile retrieved successfully',
      profile: {
        userId: profile.user_id,
        companyName: profile.company_name,
        about: profile.about,
        industry: profile.industry,
        companySize: profile.company_size,
        website: profile.website,
        address: profile.address,
        city: profile.city,
        state: profile.state,
        pincode: profile.pincode,
        gstNumber: profile.gst_number,
        panNumber: profile.pan_number,
        businessVerified: profile.business_verified,
        businessDocuments: profile.business_documents || [],
        jobsPosted: profile.jobs_posted,
        totalSpent: profile.total_spent,
        rating: profile.rating,
        reviewCount: profile.review_count,
        createdAt: profile.updated_at,
        updatedAt: profile.updated_at
      }
    };
  } catch (error) {
    logger.error('Get poster profile error:', error);
    return {
      success: false,
      message: 'Failed to retrieve poster profile',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Get poster statistics
 */
export async function getPosterStats(
  db: SupabaseClient,
  userId: string
): Promise<IGetPosterStatsResponse> {
  try {
    // Get basic stats from poster profile
    const { data: profile } = await db
      .from('poster_profiles')
      .select('jobs_posted, total_spent, rating, review_count')
      .eq('user_id', userId)
      .single();

    if (!profile) {
      return {
        success: false,
        message: 'Poster profile not found',
        error: 'PROFILE_NOT_FOUND'
      };
    }

    // Get additional stats from jobs table
    const { data: jobStats } = await db
      .from('jobs')
      .select('status')
      .eq('poster_id', userId);

    const activeJobs = jobStats?.filter(job => job.status === 'active').length || 0;
    const completedJobs = jobStats?.filter(job => job.status === 'completed').length || 0;

    // Get application stats
    const jobIdResult = await db
      .from('jobs')
      .select('id')
      .eq('poster_id', userId);
    
    const jobIds = jobIdResult.data?.map(job => job.id) || [];
    
    let applicationStats: any[] = [];
    if (jobIds.length > 0) {
      const applicationResult = await db
        .from('job_applications')
        .select('id')
        .in('job_id', jobIds);
      applicationStats = applicationResult.data || [];
    }

    const stats: IPosterStats = {
      totalJobsPosted: profile.jobs_posted || 0,
      activeJobs,
      completedJobs,
      totalApplicationsReceived: applicationStats.length || 0,
      totalSpent: profile.total_spent || 0,
      averageRating: profile.rating || 0,
      totalReviews: profile.review_count || 0,
      responseRate: 85, // TODO: Calculate actual response rate
      averageResponseTime: '2.5 hours' // TODO: Calculate actual response time
    };

    return {
      success: true,
      message: 'Poster statistics retrieved successfully',
      stats
    };
  } catch (error) {
    logger.error('Get poster stats error:', error);
    return {
      success: false,
      message: 'Failed to retrieve poster statistics',
      error: 'INTERNAL_ERROR'
    };
  }
}