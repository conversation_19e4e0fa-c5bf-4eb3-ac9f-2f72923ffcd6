/**
 * Users Service Types
 * Internal types used within the users service module
 */

// Internal worker profile structure
export interface WorkerProfileDB {
  id?: string;
  user_id: string;
  primary_skill: string;
  skills: string[];
  experience: string;
  hourly_rate?: number;
  daily_rate?: number;
  about?: string;
  phone_visible: boolean;
  portfolio_photos?: string[];
  is_available: boolean;
  rating?: number;
  review_count?: number;
  completed_jobs?: number;
  total_earnings?: number;
  created_at?: string;
  updated_at?: string;
}

// Internal poster profile structure
export interface PosterProfileDB {
  id?: string;
  user_id: string;
  company_name?: string;
  about?: string;
  industry?: string;
  company_size?: string;
  website?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  gst_number?: string;
  pan_number?: string;
  business_verified: boolean;
  business_documents?: string[];
  jobs_posted: number;
  total_spent: number;
  rating: number;
  review_count: number;
  created_at?: string;
  updated_at?: string;
}

// Internal user settings structure
export interface UserSettingsDB {
  id?: string;
  user_id: string;
  notifications: Record<string, any>;
  privacy: Record<string, any>;
  language: string;
  preferences: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

// Internal worker persona structure
export interface WorkerPersonaDB {
  id?: string;
  user_id: string;
  skill: string;
  experience?: string;
  hourly_rate?: number;
  daily_rate?: number;
  is_active: boolean;
  completed_jobs: number;
  rating: number;
  review_count: number;
  created_at?: string;
  updated_at?: string;
}

// Formatted worker profile response
export interface FormattedWorkerProfile {
  userId: string;
  primarySkill: string;
  skills: string[];
  experience: string;
  hourlyRate?: number;
  dailyRate?: number;
  about?: string;
  isAvailable: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Formatted poster profile response
export interface FormattedPosterProfile {
  userId: string;
  companyName?: string;
  about?: string;
  industry?: string;
  companySize?: string;
  website?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  businessVerified: boolean;
  jobsPosted: number;
  totalSpent: number;
  rating: number;
  reviewCount: number;
  createdAt?: string;
  updatedAt?: string;
}

// Formatted user settings response
export interface FormattedUserSettings {
  userId: string;
  notifications: Record<string, any>;
  privacy: Record<string, any>;
  language: string;
  preferences: Record<string, any>;
  updatedAt?: string;
}

// Formatted worker persona response
export interface FormattedWorkerPersona {
  id: string;
  userId: string;
  skill: string;
  experience?: string;
  hourlyRate?: number;
  dailyRate?: number;
  isActive: boolean;
  completedJobs: number;
  rating: number;
  reviewCount: number;
  createdAt?: string;
  updatedAt?: string;
}