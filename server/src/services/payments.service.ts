/**
 * Payments Service
 * Business logic for payments, earnings, and export functionality
 */

import { databaseConfig } from '@/config/database.config';
import { logger } from '@/utils/logger';
import { v4 as uuidv4 } from 'uuid';
import type {
  IPaymentHistoryRequest,
  IPaymentHistoryResponse,
  IPaymentHistoryItem,
  IEarningsRequest,
  IEarningsResponse,
  IEarningsStats,
  IEarningsBreakdown,
  IPaymentExportRequest,
  IPaymentExportResponse,
  IPaymentProcessRequest,
  IPaymentProcessResponse
} from '@ozgaar/types';

export class PaymentsService {
  private db = databaseConfig.getClient();

  /**
   * Get payment history for user
   */
  async getPaymentHistory(userId: string, request: IPaymentHistoryRequest): Promise<IPaymentHistoryResponse> {
    try {
      const page = request.page || 1;
      const limit = Math.min(request.limit || 20, 100);
      const offset = (page - 1) * limit;

      // Build query using the view
      let query = this.db!
        .from('payment_history_view')
        .select('*', { count: 'exact' })
        .or(`payer_id.eq.${userId},payee_id.eq.${userId}`);

      // Apply filters
      if (request.status) {
        query = query.eq('status', request.status);
      }

      if (request.method) {
        query = query.eq('method', request.method);
      }

      if (request.startDate) {
        query = query.gte('created_at', request.startDate);
      }

      if (request.endDate) {
        query = query.lte('created_at', request.endDate);
      }

      // Apply pagination and ordering
      query = query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: payments, error, count } = await query;

      if (error) {
        logger.error('Failed to get payment history:', error);
        return {
          success: false,
          message: 'Failed to retrieve payment history',
          error: 'DATABASE_ERROR'
        };
      }

      // Get summary counts using the view
      const { data: summaryData } = await this.db!
        .from('payment_history_view')
        .select('status')
        .or(`payer_id.eq.${userId},payee_id.eq.${userId}`);

      const summary = {
        total: summaryData?.length || 0,
        pending: summaryData?.filter(p => p.status === 'pending').length || 0,
        completed: summaryData?.filter(p => p.status === 'completed').length || 0,
        failed: summaryData?.filter(p => p.status === 'failed').length || 0,
        disputed: summaryData?.filter(p => p.status === 'disputed').length || 0
      };

      // Format payments using view column names
      const formattedPayments: IPaymentHistoryItem[] = payments?.map((payment: any) => ({
        id: payment.id,
        jobId: payment.job_id,
        jobTitle: payment.job_title || 'Unknown Job',
        payerId: payment.payer_id,
        payerName: payment.payer_name || 'Unknown Payer',
        payeeId: payment.payee_id,
        payeeName: payment.payee_name || 'Unknown Payee',
        amount: payment.amount,
        currency: payment.currency,
        method: payment.method,
        status: payment.status,
        transactionId: payment.transaction_id,
        description: payment.description,
        receiptUrl: payment.receipt_url,
        hasDispute: payment.has_dispute,
        disputeReason: payment.dispute_reason,
        disputeStatus: payment.dispute_status,
        createdAt: payment.created_at,
        processedAt: payment.processed_at,
        completedAt: payment.completed_at
      })) || [];

      return {
        success: true,
        message: 'Payment history retrieved successfully',
        payments: formattedPayments,
        pagination: {
          page,
          limit,
          totalPages: Math.ceil((count || 0) / limit),
          totalCount: count || 0,
          hasMore: (count || 0) > offset + limit
        },
        summary
      };
    } catch (error) {
      logger.error('Get payment history error:', error);
      return {
        success: false,
        message: 'Failed to retrieve payment history',
        error: 'INTERNAL_ERROR'
      };
    }
  }


  /**
   * Get earnings data for worker
   */
  async getEarnings(userId: string, request: IEarningsRequest): Promise<IEarningsResponse> {
    try {
      // Build date filter based on period
      let startDate: string;
      let endDate = new Date().toISOString();

      if (request.startDate && request.endDate) {
        startDate = request.startDate;
        endDate = request.endDate;
      } else {
        const now = new Date();
        switch (request.period) {
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
            break;
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
            break;
          case 'quarter':
            const quarterStart = Math.floor(now.getMonth() / 3) * 3;
            startDate = new Date(now.getFullYear(), quarterStart, 1).toISOString();
            break;
          case 'year':
            startDate = new Date(now.getFullYear(), 0, 1).toISOString();
            break;
          default:
            startDate = new Date(0).toISOString(); // All time
        }
      }

      // Get completed payments for the user as payee (worker earnings)
      const { data: payments, error } = await this.db!
        .from('payments')
        .select(`
          *,
          jobs!payments_job_id_fkey (
            title,
            category
          )
        `)
        .eq('payee_id', userId)
        .eq('status', 'completed')
        .gte('completed_at', startDate)
        .lte('completed_at', endDate)
        .order('completed_at', { ascending: false });

      if (error) {
        logger.error('Failed to get earnings:', error);
        return {
          success: false,
          message: 'Failed to retrieve earnings',
          error: 'DATABASE_ERROR'
        };
      }

      // Calculate earnings stats
      const totalEarnings = payments?.reduce((sum: number, p: any) => sum + (p.amount || 0), 0) || 0;
      const totalJobs = payments?.length || 0;
      const averageJobValue = totalJobs > 0 ? totalEarnings / totalJobs : 0;

      // Group by category for top categories
      const categoryMap = new Map<string, { earnings: number; jobCount: number }>();
      payments?.forEach((payment: any) => {
        const category = payment.jobs?.category || 'Other';
        const existing = categoryMap.get(category) || { earnings: 0, jobCount: 0 };
        categoryMap.set(category, {
          earnings: existing.earnings + (payment.amount || 0),
          jobCount: existing.jobCount + 1
        });
      });

      const topCategories = Array.from(categoryMap.entries())
        .map(([category, data]) => ({
          category,
          earnings: data.earnings,
          jobCount: data.jobCount
        }))
        .sort((a, b) => b.earnings - a.earnings)
        .slice(0, 5);

      // Create breakdown by period (simplified - monthly breakdown)
      const breakdown: IEarningsBreakdown[] = [];
      if (request.period !== 'all') {
        // Group payments by month for breakdown
        const monthlyMap = new Map<string, { earnings: number; jobs: number }>();
        payments?.forEach((payment: any) => {
          const month = new Date(payment.completed_at).toISOString().substring(0, 7); // YYYY-MM
          const existing = monthlyMap.get(month) || { earnings: 0, jobs: 0 };
          monthlyMap.set(month, {
            earnings: existing.earnings + (payment.amount || 0),
            jobs: existing.jobs + 1
          });
        });

        Array.from(monthlyMap.entries()).forEach(([month, data]) => {
          breakdown.push({
            period: month,
            totalEarnings: data.earnings,
            completedJobs: data.jobs,
            averageJobValue: data.jobs > 0 ? data.earnings / data.jobs : 0,
            currency: 'INR'
          });
        });
      }

      const earningsStats: IEarningsStats = {
        totalEarnings,
        totalJobs,
        averageJobValue,
        currency: 'INR',
        breakdown: breakdown.sort((a, b) => b.period.localeCompare(a.period)),
        topCategories
      };

      return {
        success: true,
        message: 'Earnings retrieved successfully',
        earnings: earningsStats
      };
    } catch (error) {
      logger.error('Get earnings error:', error);
      return {
        success: false,
        message: 'Failed to retrieve earnings',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Export payment data
   */
  async exportPayments(userId: string, request: IPaymentExportRequest): Promise<IPaymentExportResponse> {
    try {
      // Generate export file (in a real implementation, this would create actual files)
      const exportId = uuidv4();
      const format = request.format || 'csv';
      const timestamp = Date.now();
      
      // Create signed URL (in production, this would be a real cloud storage URL)
      const exportUrl = `/api/payments/download/${exportId}.${format}`;
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(); // 24 hours

      // In a real implementation, you would:
      // 1. Query the payments data based on filters
      // 2. Generate the export file (CSV/XLSX/PDF)
      // 3. Upload to cloud storage
      // 4. Return signed URL with expiration

      return {
        success: true,
        message: 'Export generated successfully',
        exportUrl,
        expiresAt
      };
    } catch (error) {
      logger.error('Export payments error:', error);
      return {
        success: false,
        message: 'Failed to generate export',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Process payment (future implementation)
   */
  async processPayment(userId: string, request: IPaymentProcessRequest): Promise<IPaymentProcessResponse> {
    try {
      // This is a placeholder for future payment gateway integration
      // In a real implementation, this would:
      // 1. Validate the job and user permissions
      // 2. Integrate with payment gateway (Razorpay, Stripe, etc.)
      // 3. Create payment record
      // 4. Handle success/failure responses

      return {
        success: false,
        message: 'Payment processing not yet implemented',
        error: 'NOT_IMPLEMENTED'
      };
    } catch (error) {
      logger.error('Process payment error:', error);
      return {
        success: false,
        message: 'Failed to process payment',
        error: 'INTERNAL_ERROR'
      };
    }
  }
}
