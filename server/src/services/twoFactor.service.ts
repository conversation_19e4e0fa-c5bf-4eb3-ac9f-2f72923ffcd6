/**
 * TwoFactor Service
 * 2Factor.in SMS OTP service integration
 */

import axios from 'axios';
import { appConfig } from '@/config/app.config';
import { databaseConfig } from '@/config/database.config';
import { logger } from '@/utils/logger';

interface ITwoFactorApiResponse {
  Status: 'Success' | 'Error';
  Details: string;
}

interface ISendOtpOptions {
  phone: string;
  message?: string;
}

interface ISendOtpResult {
  success: boolean;
  sessionId?: string;
  error?: string;
  remainingAttempts?: number;
}

interface IVerifyOtpResult {
  success: boolean;
  error?: string;
}

interface IRateLimitResult {
  allowed: boolean;
  remainingAttempts: number;
}

export class TwoFactorService {
  private baseUrl: string = 'https://2factor.in/API/V1';
  private isConfigured: boolean = false;
  private db = databaseConfig.getClient();

  constructor() {
    this.initializeService();
  }

  private initializeService(): void {
    try {
      if (!appConfig.env.TWOFACTOR_API_KEY && !appConfig.env.MOCK_OTP) {
        logger.warn('2Factor.in API key not configured. SMS service will not be available.');
        return;
      }

      this.isConfigured = true;
      logger.info('2Factor.in service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize 2Factor.in service:', error);
      this.isConfigured = false;
    }
  }

  /**
   * Check if 2Factor.in is properly configured
   */
  public isAvailable(): boolean {
    return this.isConfigured && (!!appConfig.env.TWOFACTOR_API_KEY || appConfig.env.MOCK_OTP === 'true');
  }

  /**
   * Format phone number for 2Factor.in (remove + symbol, ensure country code)
   */
  private formatPhoneNumber(phone: string): string {
    // Remove + symbol and any spaces/dashes
    let formatted = phone.replace(/[\+\s\-]/g, '');
    
    // If phone starts with 91 (India), use as is
    // If phone starts with 0, replace with 91
    // If phone is 10 digits, prepend 91
    if (formatted.startsWith('91') && formatted.length === 12) {
      return formatted;
    } else if (formatted.startsWith('0') && formatted.length === 11) {
      return '91' + formatted.substring(1);
    } else if (formatted.length === 10) {
      return '91' + formatted;
    }
    
    return formatted;
  }

  /**
   * Check OTP rate limiting
   */
  private async checkRateLimit(phone: string): Promise<IRateLimitResult> {
    try {
      const windowStart = new Date(Date.now() - 15 * 60 * 1000); // 15 minutes ago
      
      const { data: recentRequests, error } = await this.db!
        .from('otp_requests')
        .select('*')
        .eq('phone', phone)
        .gte('requested_at', windowStart.toISOString())
        .order('requested_at', { ascending: false });

      if (error) {
        logger.error('Rate limit check failed:', error);
        return { allowed: false, remainingAttempts: 0 };
      }

      const requestCount = recentRequests?.length || 0;
      const maxRequests = 3; // Max 3 requests per 15 minutes
      const remainingAttempts = Math.max(0, maxRequests - requestCount);

      return {
        allowed: requestCount < maxRequests,
        remainingAttempts
      };
    } catch (error) {
      logger.error('Rate limit check error:', error);
      return { allowed: false, remainingAttempts: 0 };
    }
  }

  /**
   * Log SMS attempt
   */
  private async logSmsAttempt(phone: string, sessionId?: string, status: string = 'sent', errorMessage?: string): Promise<void> {
    try {
      await this.db!
        .from('otp_requests')
        .insert({
          phone,
          otp_id: sessionId,
          status,
          ip: null, // Could be passed from request
          requested_at: new Date().toISOString()
        });
    } catch (error) {
      logger.error('Failed to log SMS attempt:', error);
    }
  }

  /**
   * Send OTP with rate limiting check
   */
  public async sendOtp({ phone }: ISendOtpOptions): Promise<ISendOtpResult> {
    if (!this.isAvailable()) {
      logger.error('2Factor.in service not available');
      return {
        success: false,
        error: 'SMS service not configured'
      };
    }

    // Check if we're using mock OTP
    if (appConfig.env.MOCK_OTP === 'true') {
      logger.info(`MOCK OTP: Sending OTP to ${phone}`);
      
      // Log mock SMS attempt
      await this.logSmsAttempt(phone, 'MOCK_SESSION_ID', 'sent');
      
      return {
        success: true,
        sessionId: 'MOCK_SESSION_ID',
        remainingAttempts: 3
      };
    }

    try {
      // Check rate limiting
      const rateLimitResult = await this.checkRateLimit(phone);

      if (!rateLimitResult.allowed) {
        logger.warn(`SMS rate limit exceeded for phone: ${phone}`);
        return {
          success: false,
          error: 'Rate limit exceeded. Please try again later.',
          remainingAttempts: rateLimitResult.remainingAttempts
        };
      }

      // Format phone number for 2Factor.in
      const formattedPhone = this.formatPhoneNumber(phone);

      // Send OTP via 2Factor.in API
      const url = `${this.baseUrl}/${appConfig.env.TWOFACTOR_API_KEY}/SMS/${formattedPhone}/AUTOGEN`;
      
      const response = await axios.get<ITwoFactorApiResponse>(url, {
        timeout: 10000, // 10 second timeout
      });

      if (response.data.Status === 'Success') {
        const sessionId = response.data.Details;

        // Log successful SMS
        await this.logSmsAttempt(phone, sessionId, 'sent');

        logger.info(`OTP sent successfully to ${phone}, Session ID: ${sessionId}`);

        return {
          success: true,
          sessionId: sessionId,
          remainingAttempts: rateLimitResult.remainingAttempts - 1
        };
      } else {
        // Handle API error response
        logger.error(`2Factor.in API error: ${response.data.Details}`);
        
        await this.logSmsAttempt(phone, undefined, 'failed', response.data.Details);

        return {
          success: false,
          error: response.data.Details
        };
      }

    } catch (error: any) {
      logger.error('2Factor.in SMS send error:', error);

      // Log failed SMS attempt
      await this.logSmsAttempt(phone, undefined, 'failed', error.message || 'Unknown error');

      return {
        success: false,
        error: error.message || 'Failed to send SMS'
      };
    }
  }

  /**
   * Verify OTP using session ID
   */
  public async verifyOtp(sessionId: string, otpCode: string): Promise<IVerifyOtpResult> {
    if (!this.isAvailable()) {
      logger.error('2Factor.in service not available');
      return {
        success: false,
        error: 'SMS service not configured'
      };
    }

    // Check if we're using mock OTP
    if (appConfig.env.MOCK_OTP === 'true') {
      const mockOtp = appConfig.env.MOCK_OTP_VALUE || '123456';
      
      if (otpCode === mockOtp && sessionId === 'MOCK_SESSION_ID') {
        logger.info(`MOCK OTP: Verified successfully`);
        return {
          success: true
        };
      } else {
        logger.warn(`MOCK OTP: Verification failed. Expected ${mockOtp}, got ${otpCode}`);
        return {
          success: false,
          error: 'Invalid OTP'
        };
      }
    }

    try {
      // Verify OTP via 2Factor.in API
      const url = `${this.baseUrl}/${appConfig.env.TWOFACTOR_API_KEY}/SMS/VERIFY/${sessionId}/${otpCode}`;
      
      const response = await axios.get<ITwoFactorApiResponse>(url, {
        timeout: 10000, // 10 second timeout
      });

      if (response.data.Status === 'Success') {
        logger.info(`OTP verified successfully for session: ${sessionId}`);
        return {
          success: true
        };
      } else {
        logger.warn(`OTP verification failed for session ${sessionId}: ${response.data.Details}`);
        return {
          success: false,
          error: response.data.Details
        };
      }

    } catch (error: any) {
      logger.error('2Factor.in OTP verification error:', error);
      return {
        success: false,
        error: error.message || 'Failed to verify OTP'
      };
    }
  }

  /**
   * Validate phone number for 2Factor.in (Indian mobile numbers)
   */
  public validatePhoneNumber(phone: string): boolean {
    // Basic validation for Indian mobile numbers
    // Accepts formats: +************, ************, 09876543210, 9876543210
    const phoneRegex = /^(\+91|91|0)?[6-9][0-9]{9}$/;
    return phoneRegex.test(phone.replace(/[\s\-]/g, ''));
  }
}

export const twoFactorService = new TwoFactorService();
