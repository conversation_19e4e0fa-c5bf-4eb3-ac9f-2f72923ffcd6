/**
 * Reviews Service
 * Business logic for reviews and ratings
 */

import { databaseConfig } from '@/config/database.config';
import { logger } from '@/utils/logger';
import type {
  IGetReviewsRequest,
  IGetReviewsResponse,
  IReviewItem,
  IReviewsSummary,
  IPostReviewRequest,
  IPostReviewResponse
} from '@ozgaar/types';

export class ReviewsService {
  private db = databaseConfig.getClient();

  /**
   * Get reviews received by user
   */
  async getReceivedReviews(userId: string, request: IGetReviewsRequest): Promise<IGetReviewsResponse> {
    try {
      const page = request.page || 1;
      const limit = Math.min(request.limit || 20, 100);
      const offset = (page - 1) * limit;

      // Build query for reviews where user is the reviewee
      let query = this.db!
        .from('reviews')
        .select('*', { count: 'exact' })
        .eq('reviewee_id', userId);

      // Apply filters
      if (request.reviewType) {
        query = query.eq('review_type', request.reviewType);
      }

      if (request.isPublic !== undefined) {
        query = query.eq('is_public', request.isPublic);
      }

      // Apply pagination and ordering
      query = query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: reviews, error, count } = await query;

      if (error) {
        logger.error('Failed to get reviews:', error);
        return {
          success: false,
          message: 'Failed to retrieve reviews',
          error: 'DATABASE_ERROR'
        };
      }

      // Calculate summary statistics
      const summary = await this.calculateReviewsSummary(userId);

      // Format reviews with additional data fetching
      const formattedReviews: IReviewItem[] = [];

      if (reviews && reviews.length > 0) {
        for (const review of reviews) {
          // Get job title
          const { data: job } = await this.db!
            .from('jobs')
            .select('title')
            .eq('id', review.job_id as string)
            .single();

          // Get reviewer profile
          const { data: reviewerProfile } = await this.db!
            .from('user_profiles')
            .select('full_name, avatar_url')
            .eq('user_id', review.reviewer_id as string)
            .single();

          // Get reviewee profile
          const { data: revieweeProfile } = await this.db!
            .from('user_profiles')
            .select('full_name')
            .eq('user_id', review.reviewee_id as string)
            .single();

          formattedReviews.push({
            id: review.id as string,
            jobId: review.job_id as string,
            jobTitle: job?.title as string || 'Unknown Job',
            reviewerId: review.reviewer_id as string,
            reviewerName: reviewerProfile?.full_name as string || 'Anonymous',
            reviewerAvatar: reviewerProfile?.avatar_url as string,
            revieweeId: review.reviewee_id as string,
            revieweeName: revieweeProfile?.full_name as string || 'Unknown User',
            rating: review.rating as number,
            comment: review.comment as string,
            reviewType: review.review_type as 'worker_to_poster' | 'poster_to_worker',
            communicationRating: review.communication_rating as number,
            workQualityRating: review.work_quality_rating as number,
            timelinessRating: review.timeliness_rating as number,
            professionalismRating: review.professionalism_rating as number,
            isPublic: review.is_public as boolean,
            isVerified: review.is_verified as boolean,
            createdAt: review.created_at as string,
            updatedAt: review.updated_at as string
          });
        }
      }

      return {
        success: true,
        message: 'Reviews retrieved successfully',
        reviews: formattedReviews,
        summary,
        pagination: {
          page,
          limit,
          totalPages: Math.ceil((count || 0) / limit),
          totalCount: count || 0,
          hasMore: (count || 0) > offset + limit
        }
      };
    } catch (error) {
      logger.error('Get reviews error:', error);
      return {
        success: false,
        message: 'Failed to retrieve reviews',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Post a new review
   */
  async postReview(reviewerId: string, request: IPostReviewRequest): Promise<IPostReviewResponse> {
    try {
      // Verify job exists and user participated in it
      const { data: job, error: jobError } = await this.db!
        .from('jobs')
        .select('id, poster_id, assigned_worker_id, status')
        .eq('id', request.jobId)
        .single();

      if (jobError || !job) {
        return {
          success: false,
          message: 'Job not found',
          error: 'JOB_NOT_FOUND'
        };
      }

      // Check if job is completed
      if (job.status !== 'completed') {
        return {
          success: false,
          message: 'Can only review completed jobs',
          error: 'JOB_NOT_COMPLETED'
        };
      }

      // Verify reviewer participated in the job
      const isParticipant = job.poster_id === reviewerId || job.assigned_worker_id === reviewerId;
      if (!isParticipant) {
        return {
          success: false,
          message: 'You can only review jobs you participated in',
          error: 'NOT_JOB_PARTICIPANT'
        };
      }

      // Verify reviewee is the other participant
      const expectedRevieweeId = job.poster_id === reviewerId ? job.assigned_worker_id : job.poster_id;
      if (request.revieweeId !== expectedRevieweeId) {
        return {
          success: false,
          message: 'Invalid reviewee for this job',
          error: 'INVALID_REVIEWEE'
        };
      }

      // Check if review already exists (one rating per pair per job)
      const { data: existingReview } = await this.db!
        .from('reviews')
        .select('id')
        .eq('job_id', request.jobId)
        .eq('reviewer_id', reviewerId)
        .eq('reviewee_id', request.revieweeId)
        .single();

      if (existingReview) {
        return {
          success: false,
          message: 'You have already reviewed this job',
          error: 'REVIEW_ALREADY_EXISTS'
        };
      }

      // Determine review type
      const reviewType = job.poster_id === reviewerId ? 'poster_to_worker' : 'worker_to_poster';

      // Create review
      const { data: review, error: insertError } = await this.db!
        .from('reviews')
        .insert({
          job_id: request.jobId,
          reviewer_id: reviewerId,
          reviewee_id: request.revieweeId,
          rating: request.rating,
          comment: request.comment,
          review_type: reviewType,
          communication_rating: request.communicationRating,
          work_quality_rating: request.workQualityRating,
          timeliness_rating: request.timelinessRating,
          professionalism_rating: request.professionalismRating,
          is_public: request.isPublic !== false // Default to true
        })
        .select('*')
        .single();

      if (insertError) {
        logger.error('Failed to create review:', insertError);
        return {
          success: false,
          message: 'Failed to create review',
          error: 'DATABASE_ERROR'
        };
      }

      // Get additional data for response
      const { data: jobData } = await this.db!
        .from('jobs')
        .select('title')
        .eq('id', review.job_id as string)
        .single();

      const { data: reviewerProfile } = await this.db!
        .from('user_profiles')
        .select('full_name, avatar_url')
        .eq('user_id', review.reviewer_id as string)
        .single();

      const { data: revieweeProfile } = await this.db!
        .from('user_profiles')
        .select('full_name')
        .eq('user_id', review.reviewee_id as string)
        .single();

      // Format review response
      const formattedReview: IReviewItem = {
        id: review.id as string,
        jobId: review.job_id as string,
        jobTitle: jobData?.title as string || 'Unknown Job',
        reviewerId: review.reviewer_id as string,
        reviewerName: reviewerProfile?.full_name as string || 'Anonymous',
        reviewerAvatar: reviewerProfile?.avatar_url as string,
        revieweeId: review.reviewee_id as string,
        revieweeName: revieweeProfile?.full_name as string || 'Unknown User',
        rating: review.rating as number,
        comment: review.comment as string,
        reviewType: review.review_type as 'worker_to_poster' | 'poster_to_worker',
        communicationRating: review.communication_rating as number,
        workQualityRating: review.work_quality_rating as number,
        timelinessRating: review.timeliness_rating as number,
        professionalismRating: review.professionalism_rating as number,
        isPublic: review.is_public as boolean,
        isVerified: review.is_verified as boolean,
        createdAt: review.created_at as string,
        updatedAt: review.updated_at as string
      };

      return {
        success: true,
        message: 'Review posted successfully',
        review: formattedReview
      };
    } catch (error) {
      logger.error('Post review error:', error);
      return {
        success: false,
        message: 'Failed to post review',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Calculate reviews summary statistics
   */
  private async calculateReviewsSummary(userId: string): Promise<IReviewsSummary> {
    try {
      // Get all reviews for the user
      const { data: reviews } = await this.db!
        .from('reviews')
        .select('rating, communication_rating, work_quality_rating, timeliness_rating, professionalism_rating')
        .eq('reviewee_id', userId);

      if (!reviews || reviews.length === 0) {
        return {
          totalReviews: 0,
          averageRating: 0,
          ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
          categoryAverages: {}
        };
      }

      // Calculate average rating
      const totalRating = reviews.reduce((sum: number, r: any) => sum + (r.rating || 0), 0);
      const averageRating = totalRating / reviews.length;

      // Calculate rating distribution
      const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
      reviews.forEach((r: any) => {
        if (r.rating >= 1 && r.rating <= 5) {
          ratingDistribution[r.rating as keyof typeof ratingDistribution]++;
        }
      });

      // Calculate category averages
      const categoryAverages: any = {};
      const categories = ['communication_rating', 'work_quality_rating', 'timeliness_rating', 'professionalism_rating'];
      
      categories.forEach(category => {
        const validRatings = reviews.filter((r: any) => r[category] != null);
        if (validRatings.length > 0) {
          const sum = validRatings.reduce((sum: number, r: any) => sum + r[category], 0);
          const key = category.replace('_rating', '');
          categoryAverages[key] = sum / validRatings.length;
        }
      });

      return {
        totalReviews: reviews.length,
        averageRating: Math.round(averageRating * 100) / 100, // Round to 2 decimal places
        ratingDistribution,
        categoryAverages
      };
    } catch (error) {
      logger.error('Calculate reviews summary error:', error);
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        categoryAverages: {}
      };
    }
  }
}
