/**
 * Upload Service
 * Business logic for file uploads and image processing
 */

import { databaseConfig } from '@/config/database.config';
import { logger } from '@/utils/logger';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs/promises';
import type { SupabaseClient } from '@supabase/supabase-js';
import type {
  IUploadImagesRequest,
  IUploadImagesResponse,
  IUploadedImage,
  IUploadConfig,
  IUploadValidation,
  IImageProcessingOptions
} from '@ozgaar/types';

export class UploadService {
  private db: ReturnType<typeof databaseConfig.getClient> = databaseConfig.getClient();

  // Upload configuration
  private config: IUploadConfig = {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
    maxFiles: 10,
    imageProcessing: {
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 85,
      format: 'jpeg',
      generateThumbnail: true,
      thumbnailSize: 300
    }
  };

  /**
   * Upload and process images
   */
  async uploadImages(
    userId: string,
    files: Express.Multer.File[],
    request: IUploadImagesRequest
  ): Promise<IUploadImagesResponse> {
    try {
      // Validate request
      const validation = this.validateUpload(files, request);
      if (!validation.isValid) {
        return {
          success: false,
          message: 'Upload validation failed',
          error: validation.errors.join(', ')
        };
      }

      // Verify job ownership if jobId is provided
      if (request.jobId) {
        const hasAccess = await this.verifyJobAccess(userId, request.jobId);
        if (!hasAccess) {
          return {
            success: false,
            message: 'Access denied to job',
            error: 'JOB_ACCESS_DENIED'
          };
        }
      }

      const uploadedImages: IUploadedImage[] = [];

      // Process each file
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const caption = request.captions?.[i];

        try {
          // Process and save image
          const processedImage = await this.processImage(file, userId, request.category);
          
          // Save to uploads table
          const uploadRecord = await this.saveUploadRecord(
            userId,
            file,
            processedImage,
            request
          );

          // Save to job_photos table if it's a job-related upload
          if (request.jobId && (request.category === 'job' || request.category === 'work_progress')) {
            await this.saveJobPhoto(request.jobId, processedImage.url, caption, userId);
          }

          uploadedImages.push({
            id: uploadRecord.id,
            originalName: file.originalname,
            url: processedImage.url,
            thumbnailUrl: processedImage.thumbnailUrl,
            size: processedImage.size,
            mimeType: processedImage.mimeType,
            category: request.category,
            jobId: request.jobId,
            uploadedAt: uploadRecord.created_at
          });

        } catch (error) {
          logger.error(`Failed to process file ${file.originalname}:`, error);
          // Continue with other files, don't fail the entire upload
        }
      }

      if (uploadedImages.length === 0) {
        return {
          success: false,
          message: 'No images were successfully uploaded',
          error: 'PROCESSING_FAILED'
        };
      }

      return {
        success: true,
        message: `Successfully uploaded ${uploadedImages.length} image(s)`,
        images: uploadedImages
      };

    } catch (error) {
      logger.error('Upload images error:', error);
      return {
        success: false,
        message: 'Failed to upload images',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Validate upload request and files
   */
  private validateUpload(files: Express.Multer.File[], request: IUploadImagesRequest): IUploadValidation {
    const errors: string[] = [];

    // Check file count
    if (!files || files.length === 0) {
      errors.push('No files provided');
    }

    if (files && files.length > this.config.maxFiles) {
      errors.push(`Maximum ${this.config.maxFiles} files allowed`);
    }

    // Validate each file
    files?.forEach((file, index) => {
      // Check file size
      if (file.size > this.config.maxFileSize) {
        errors.push(`File ${index + 1}: Size exceeds ${this.config.maxFileSize / (1024 * 1024)}MB limit`);
      }

      // Check mime type
      if (!this.config.allowedMimeTypes.includes(file.mimetype)) {
        errors.push(`File ${index + 1}: Invalid file type. Allowed: ${this.config.allowedMimeTypes.join(', ')}`);
      }
    });

    // Validate category
    const validCategories = ['profile', 'job', 'work_progress', 'portfolio', 'verification'];
    if (!validCategories.includes(request.category)) {
      errors.push(`Invalid category. Allowed: ${validCategories.join(', ')}`);
    }

    // Validate jobId if provided
    if (request.jobId && !this.isValidUUID(request.jobId)) {
      errors.push('Invalid job ID format');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Verify user has access to the job
   */
  private async verifyJobAccess(userId: string, jobId: string): Promise<boolean> {
    try {
      const { data: job, error } = await this.db!
        .from('jobs')
        .select('poster_id, assigned_worker_id')
        .eq('id', jobId)
        .single();

      if (error || !job) {
        return false;
      }

      return job.poster_id === userId || job.assigned_worker_id === userId;
    } catch (error) {
      logger.error('Error verifying job access:', error);
      return false;
    }
  }

  /**
   * Process image with sharp and upload to Supabase storage
   */
  private async processImage(
    file: Express.Multer.File,
    userId: string,
    category: string
  ): Promise<{
    url: string;
    thumbnailUrl?: string;
    size: number;
    mimeType: string;
  }> {
    const fileId = uuidv4();
    const timestamp = Date.now();
    
    // Create file paths for Supabase storage
    const fileName = `${userId}/${category}/${timestamp}_${fileId}.${this.config.imageProcessing.format}`;
    const thumbnailName = `${userId}/${category}/thumbnails/${timestamp}_${fileId}_thumb.${this.config.imageProcessing.format}`;

    // Process main image
    const processedBuffer = await sharp(file.buffer)
      .resize(this.config.imageProcessing.maxWidth, this.config.imageProcessing.maxHeight, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .jpeg({ quality: this.config.imageProcessing.quality })
      .toBuffer();

    // Process thumbnail if needed
    let thumbnailBuffer: Buffer | null = null;
    if (this.config.imageProcessing.generateThumbnail) {
      thumbnailBuffer = await sharp(file.buffer)
        .resize(this.config.imageProcessing.thumbnailSize, this.config.imageProcessing.thumbnailSize, {
          fit: 'cover'
        })
        .jpeg({ quality: 80 })
        .toBuffer();
    }

    // Upload to Supabase storage
    const { url, thumbnailUrl } = await this.uploadToSupabaseStorage(
      fileName,
      thumbnailName,
      processedBuffer,
      thumbnailBuffer
    );

    return {
      url,
      thumbnailUrl,
      size: processedBuffer.length,
      mimeType: `image/${this.config.imageProcessing.format}`
    };
  }

  /**
   * Upload files to Supabase storage
   */
  private async uploadToSupabaseStorage(
    fileName: string,
    thumbnailName: string,
    fileBuffer: Buffer,
    thumbnailBuffer: Buffer | null
  ): Promise<{ url: string; thumbnailUrl?: string }> {
    try {
      // Ensure the uploads bucket exists
      await this.createBucketIfNotExists('uploads');

      // Upload main image
      const { data: uploadData, error: uploadError } = await this.db!.storage
        .from('uploads')
        .upload(fileName, fileBuffer, {
          contentType: `image/${this.config.imageProcessing.format}`,
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        logger.error('Failed to upload file to Supabase storage:', uploadError);
        throw new Error(`Failed to upload file: ${uploadError.message}`);
      }

      // Get public URL for main image
      const { data: urlData } = this.db!.storage
        .from('uploads')
        .getPublicUrl(fileName);
      
      let thumbnailUrl: string | undefined;

      // Upload thumbnail if it exists
      if (thumbnailBuffer && this.config.imageProcessing.generateThumbnail) {
        const { error: thumbError } = await this.db!.storage
          .from('uploads')
          .upload(thumbnailName, thumbnailBuffer, {
            contentType: `image/${this.config.imageProcessing.format}`,
            cacheControl: '3600',
            upsert: false
          });

        if (thumbError) {
          logger.error('Failed to upload thumbnail to Supabase storage:', thumbError);
          // Don't throw here, as the main upload was successful
        } else {
          // Get public URL for thumbnail
          const { data: thumbUrlData } = this.db!.storage
            .from('uploads')
            .getPublicUrl(thumbnailName);
          thumbnailUrl = thumbUrlData.publicUrl;
        }
      }

      return {
        url: urlData.publicUrl,
        thumbnailUrl
      };
    } catch (error) {
      logger.error('Error uploading to Supabase storage:', error);
      throw error;
    }
  }

  /**
   * Create bucket if it doesn't exist
   */
  private async createBucketIfNotExists(bucketName: string): Promise<void> {
    try {
      // Check if bucket exists
      const { data: buckets, error: listError } = await this.db!.storage.listBuckets();
      
      if (listError) {
        logger.error('Failed to list buckets:', listError);
        return;
      }
      
      const bucketExists = buckets?.some(bucket => bucket.name === bucketName);
      
      if (!bucketExists) {
        // Create bucket
        const { error: createError } = await this.db!.storage.createBucket(bucketName, {
          public: true,
          fileSizeLimit: this.config.maxFileSize,
          allowedMimeTypes: this.config.allowedMimeTypes
        });
        
        if (createError) {
          logger.error(`Failed to create bucket ${bucketName}:`, createError);
        } else {
          logger.info(`Successfully created bucket ${bucketName}`);
        }
      }
    } catch (error) {
      logger.error(`Error checking/creating bucket ${bucketName}:`, error);
    }
  }

  /**
   * Save upload record to database
   */
  private async saveUploadRecord(
    userId: string,
    file: Express.Multer.File,
    processedImage: any,
    request: IUploadImagesRequest
  ): Promise<any> {
    const { data: upload, error } = await this.db!
      .from('uploads')
      .insert({
        user_id: userId,
        category: request.category,
        job_id: request.jobId || null,
        original_name: file.originalname,
        url: processedImage.url,
        thumbnail_url: processedImage.thumbnailUrl,
        size: processedImage.size,
        mime_type: processedImage.mimeType
      })
      .select('*')
      .single();

    if (error) {
      throw new Error(`Failed to save upload record: ${error.message}`);
    }

    return upload;
  }

  /**
   * Save job photo record
   */
  private async saveJobPhoto(
    jobId: string,
    url: string,
    caption: string | undefined,
    uploadedBy: string
  ): Promise<void> {
    const { error } = await this.db!
      .from('job_photos')
      .insert({
        job_id: jobId,
        url,
        caption: caption || null,
        uploaded_by: uploadedBy
      });

    if (error) {
      logger.error('Failed to save job photo:', error);
      // Don't throw here, as the main upload was successful
    }
  }

  /**
   * Validate UUID format
   */
  private isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }
}
