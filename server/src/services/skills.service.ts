/**
 * Skills Service
 * Business logic for skills and market rates operations
 */

import { databaseConfig } from '@/config/database.config';
import { logger } from '@/utils/logger';
import type { 
  IGetSkillCategoriesResponse,
  IGetMarketRatesRequest,
  IGetMarketRatesResponse,
  ISkillCategory,
  IMarketRate
} from '@ozgaar/types';
import { 
  DEFAULT_SKILL_CATEGORIES,
  DEFAULT_MARKET_RATES
} from '@ozgaar/types';

export class SkillsService {
  private db = databaseConfig.getClient();

  /**
   * Get skill categories
   */
  async getCategories(): Promise<IGetSkillCategoriesResponse> {
    try {
      // First try to get categories from database
      const { data: categories, error } = await this.db!
        .from('job_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        logger.error('Failed to get categories from database:', error);
        // Fall back to default categories
        return {
          success: true,
          categories: DEFAULT_SKILL_CATEGORIES,
          message: 'Categories retrieved successfully (using defaults)'
        };
      }

      // If no categories in database, return defaults
      if (!categories || categories.length === 0) {
        return {
          success: true,
          categories: DEFAULT_SKILL_CATEGORIES,
          message: 'Categories retrieved successfully (using defaults)'
        };
      }

      // Transform database categories to API format
      const formattedCategories: ISkillCategory[] = categories.map((cat: any) => ({
        id: cat.id,
        name: cat.name,
        description: cat.description,
        icon: cat.icon,
        skills: [], // TODO: Get skills for each category
        isActive: cat.is_active
      }));

      return {
        success: true,
        categories: formattedCategories,
        message: 'Categories retrieved successfully'
      };
    } catch (error) {
      logger.error('Get categories error:', error);
      // Fall back to default categories
      return {
        success: true,
        categories: DEFAULT_SKILL_CATEGORIES,
        message: 'Categories retrieved successfully (using defaults)'
      };
    }
  }

  /**
   * Get market rates for skills
   */
  async getMarketRates(query: IGetMarketRatesRequest): Promise<IGetMarketRatesResponse> {
    try {
      let dbQuery = this.db!
        .from('market_rates')
        .select('*');

      // Apply filters
      if (query.skills && query.skills.length > 0) {
        dbQuery = dbQuery.in('skill', query.skills);
      }

      if (query.category) {
        dbQuery = dbQuery.eq('category', query.category);
      }

      if (query.location) {
        dbQuery = dbQuery.eq('location', query.location);
      }

      if (query.experienceLevel) {
        dbQuery = dbQuery.eq('experience_level', query.experienceLevel);
      }

      const { data: rates, error } = await dbQuery;

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        logger.error('Failed to get market rates from database:', error);
        // Fall back to default rates
        return this.getDefaultMarketRates(query);
      }

      // If no rates in database, return defaults
      if (!rates || rates.length === 0) {
        return this.getDefaultMarketRates(query);
      }

      // Transform database rates to API format
      const formattedRates: IMarketRate[] = rates.map((rate: any) => ({
        skill: rate.skill,
        category: rate.category,
        averageHourlyRate: rate.average_hourly_rate,
        averageDailyRate: rate.average_daily_rate,
        minRate: rate.min_rate,
        maxRate: rate.max_rate,
        currency: rate.currency,
        location: rate.location,
        experienceLevel: rate.experience_level,
        sampleSize: rate.sample_size,
        lastUpdated: rate.last_updated
      }));

      return {
        success: true,
        rates: formattedRates,
        message: 'Market rates retrieved successfully'
      };
    } catch (error) {
      logger.error('Get market rates error:', error);
      // Fall back to default rates
      return this.getDefaultMarketRates(query);
    }
  }

  /**
   * Get default market rates with filtering
   */
  private getDefaultMarketRates(query: IGetMarketRatesRequest): IGetMarketRatesResponse {
    let filteredRates = DEFAULT_MARKET_RATES;

    // Apply filters to default rates
    if (query.skills && query.skills.length > 0) {
      filteredRates = filteredRates.filter(rate => 
        query.skills!.includes(rate.skill)
      );
    }

    if (query.category) {
      filteredRates = filteredRates.filter(rate => 
        rate.category === query.category
      );
    }

    // Transform to API format
    const formattedRates: IMarketRate[] = filteredRates.map(rate => ({
      skill: rate.skill,
      category: rate.category,
      averageHourlyRate: rate.hourlyRate,
      averageDailyRate: rate.dailyRate,
      minRate: Math.round(rate.hourlyRate * 0.7), // 30% below average
      maxRate: Math.round(rate.hourlyRate * 1.5), // 50% above average
      currency: 'INR',
      location: query.location || 'India',
      experienceLevel: query.experienceLevel || 'all',
      sampleSize: 100, // Mock sample size
      lastUpdated: new Date().toISOString()
    }));

    return {
      success: true,
      rates: formattedRates,
      message: 'Market rates retrieved successfully (using defaults)'
    };
  }
}
