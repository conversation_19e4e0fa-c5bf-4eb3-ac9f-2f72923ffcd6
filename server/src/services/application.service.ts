/**
 * Application Service
 * Handles job application business logic, limits, and workflow
 */

import { databaseConfig } from '@/config/database.config';
import { logger } from '@/utils/logger';
import { MessagesService } from '@/services/messages.service';
import type {
  IApplyToJobRequest,
  IApplyToJobResponse,
  IApplicationLimitsResponse,
  IMyApplicationsRequest,
  IMyApplicationsResponse,
  IWithdrawApplicationResponse,
  IApplicationSummary,
  ApplicationStatus
} from '@ozgaar/types';

export class ApplicationService {
  private db = databaseConfig.getClient();
  private messagesService = new MessagesService();

  /**
   * Apply to a job
   */
  async applyToJob(userId: string, jobId: string, applicationData: IApplyToJobRequest): Promise<IApplyToJobResponse> {
    try {
      // Check if job exists and is active
      const { data: job, error: jobError } = await this.db!
        .from('jobs')
        .select('id, poster_id, status, max_applications, application_count')
        .eq('id', jobId)
        .single();

      if (jobError || !job) {
        return {
          success: false,
          message: 'Job not found',
          error: 'JOB_NOT_FOUND'
        };
      }

      if (job.status !== 'active') {
        return {
          success: false,
          message: 'Job is not available for applications',
          error: 'JOB_NOT_ACTIVE'
        };
      }

      // Check if user is trying to apply to their own job
      if (job.poster_id === userId) {
        return {
          success: false,
          message: 'Cannot apply to your own job',
          error: 'CANNOT_APPLY_OWN_JOB'
        };
      }

      // Check if max applications reached
      if (job.max_applications && (job.application_count as number) >= (job.max_applications as number)) {
        return {
          success: false,
          message: 'Maximum applications reached for this job',
          error: 'MAX_APPLICATIONS_REACHED'
        };
      }

      // Check if user has already applied
      const { data: existingApplication } = await this.db!
        .from('job_applications')
        .select('id')
        .eq('job_id', jobId)
        .eq('worker_id', userId)
        .single();

      if (existingApplication) {
        return {
          success: false,
          message: 'You have already applied to this job',
          error: 'ALREADY_APPLIED'
        };
      }

      // Check daily application limits
      const limitsCheck = await this.getApplicationLimits(userId);
      if (limitsCheck.success && limitsCheck.limits && !limitsCheck.limits.canApply) {
        return {
          success: false,
          message: 'Daily application limit reached',
          error: 'DAILY_LIMIT_REACHED'
        };
      }

      // Validate persona if provided
      if (applicationData.personaId) {
        const { data: persona } = await this.db!
          .from('worker_personas')
          .select('id')
          .eq('id', applicationData.personaId)
          .eq('user_id', userId)
          .single();

        if (!persona) {
          return {
            success: false,
            message: 'Invalid persona selected',
            error: 'INVALID_PERSONA'
          };
        }
      }

      // Create application
      const { data: application, error: applicationError } = await this.db!
        .from('job_applications')
        .insert({
          job_id: jobId,
          worker_id: userId,
          persona_id: applicationData.personaId,
          cover_letter: applicationData.coverLetter,
          proposed_rate: applicationData.proposedRate,
          proposed_rate_type: applicationData.proposedRateType,
          status: 'pending'
        })
        .select()
        .single();

      if (applicationError) {
        logger.error('Failed to create application:', applicationError);
        return {
          success: false,
          message: 'Failed to submit application',
          error: 'DATABASE_ERROR'
        };
      }

      // Create conversation between worker and poster for this job
      const conversationResult = await this.messagesService.createConversation(
        jobId,
        userId, // workerId
        job.poster_id as string // posterId
      );

      if (!conversationResult.success) {
        logger.error('Failed to create conversation:', conversationResult.error);
        // Note: We don't fail the application if conversation creation fails
        // The conversation can be created later when users try to message each other
      }

      return {
        success: true,
        message: 'Application submitted successfully',
        application: {
          id: application.id as string,
          jobId: application.job_id as string,
          workerId: application.worker_id as string,
          personaId: application.persona_id as string | undefined,
          coverLetter: application.cover_letter as string | undefined,
          proposedRate: application.proposed_rate as number | undefined,
          proposedRateType: application.proposed_rate_type as any,
          status: application.status as ApplicationStatus,
          appliedAt: application.applied_at as string
        }
      };
    } catch (error) {
      logger.error('Apply to job error:', error);
      return {
        success: false,
        message: 'Failed to submit application',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get application limits for user
   */
  async getApplicationLimits(userId: string): Promise<IApplicationLimitsResponse> {
    try {
      const dailyLimit = 10; // TODO: Make this configurable
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Count applications today
      const { count: applicationsToday, error } = await this.db!
        .from('job_applications')
        .select('*', { count: 'exact', head: true })
        .eq('worker_id', userId)
        .gte('applied_at', today.toISOString())
        .lt('applied_at', tomorrow.toISOString());

      if (error) {
        logger.error('Failed to get application count:', error);
        return {
          success: false,
          message: 'Failed to retrieve application limits',
          error: 'DATABASE_ERROR'
        };
      }

      const applicationsCount = applicationsToday || 0;
      const remainingToday = Math.max(0, dailyLimit - applicationsCount);
      const canApply = remainingToday > 0;

      return {
        success: true,
        message: 'Application limits retrieved successfully',
        limits: {
          dailyLimit,
          applicationsToday: applicationsCount,
          remainingToday,
          nextResetAt: tomorrow.toISOString(),
          canApply
        }
      };
    } catch (error) {
      logger.error('Get application limits error:', error);
      return {
        success: false,
        message: 'Failed to retrieve application limits',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get user's applications with pagination and filtering
   */
  async getMyApplications(userId: string, request: IMyApplicationsRequest): Promise<IMyApplicationsResponse> {
    try {
      const page = request.page || 1;
      const limit = Math.min(request.limit || 20, 100);
      const offset = (page - 1) * limit;

      // Build query using the view
      let query = this.db!
        .from('job_applications_view')
        .select('*', { count: 'exact' })
        .eq('worker_id', userId);

      // Apply status filter if provided
      if (request.status) {
        query = query.eq('status', request.status);
      }

      // Apply pagination and ordering
      query = query
        .order('applied_at', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: applications, error, count } = await query;

      if (error) {
        logger.error('Failed to get applications:', error);
        return {
          success: false,
          message: 'Failed to retrieve applications',
          error: 'DATABASE_ERROR'
        };
      }

      // Format applications using view column names
      const formattedApplications: IApplicationSummary[] = applications?.map((app: any) => ({
        id: app.id,
        jobId: app.job_id,
        jobTitle: app.job_title,
        jobCategory: app.job_category,
        jobLocation: app.job_location,
        jobRate: app.job_rate,
        jobRateType: app.job_rate_type,
        jobUrgency: app.job_urgency,
        posterName: app.poster_name || 'Unknown',
        posterCompany: app.poster_company,
        posterVerified: app.poster_verified || false,
        personaId: app.persona_id,
        personaSkill: app.persona_skill,
        coverLetter: app.cover_letter,
        proposedRate: app.proposed_rate,
        proposedRateType: app.proposed_rate_type,
        status: app.status,
        appliedAt: app.applied_at,
        respondedAt: app.responded_at,
        hasMessages: app.has_messages,
        lastMessageAt: app.last_message_at
      })) || [];

      // Get summary counts using the view
      const { data: summaryData } = await this.db!
        .from('job_applications_view')
        .select('status')
        .eq('worker_id', userId);

      const summary = {
        total: summaryData?.length || 0,
        pending: summaryData?.filter(app => app.status === 'pending').length || 0,
        accepted: summaryData?.filter(app => app.status === 'accepted').length || 0,
        rejected: summaryData?.filter(app => app.status === 'rejected').length || 0,
        withdrawn: summaryData?.filter(app => app.status === 'withdrawn').length || 0
      };

      return {
        success: true,
        message: 'Applications retrieved successfully',
        applications: formattedApplications,
        pagination: {
          page,
          limit,
          totalPages: Math.ceil((count || 0) / limit),
          totalCount: count || 0,
          hasMore: (count || 0) > offset + limit
        },
        summary
      };
    } catch (error) {
      logger.error('Get my applications error:', error);
      return {
        success: false,
        message: 'Failed to retrieve applications',
        error: 'INTERNAL_ERROR'
      };
    }
  }


  /**
   * Withdraw an application
   */
  async withdrawApplication(userId: string, applicationId: string): Promise<IWithdrawApplicationResponse> {
    try {
      // Check if application exists and belongs to user
      const { data: application, error: fetchError } = await this.db!
        .from('job_applications')
        .select('id, status, worker_id')
        .eq('id', applicationId)
        .eq('worker_id', userId)
        .single();

      if (fetchError || !application) {
        return {
          success: false,
          message: 'Application not found',
          error: 'APPLICATION_NOT_FOUND'
        };
      }

      // Check if application can be withdrawn
      if (application.status === 'withdrawn') {
        return {
          success: false,
          message: 'Application is already withdrawn',
          error: 'ALREADY_WITHDRAWN'
        };
      }

      if (application.status === 'accepted') {
        return {
          success: false,
          message: 'Cannot withdraw accepted application',
          error: 'CANNOT_WITHDRAW_ACCEPTED'
        };
      }

      // Update application status
      const { data: updatedApplication, error: updateError } = await this.db!
        .from('job_applications')
        .update({
          status: 'withdrawn',
          responded_at: new Date().toISOString()
        })
        .eq('id', applicationId)
        .select()
        .single();

      if (updateError) {
        logger.error('Failed to withdraw application:', updateError);
        return {
          success: false,
          message: 'Failed to withdraw application',
          error: 'DATABASE_ERROR'
        };
      }

      return {
        success: true,
        message: 'Application withdrawn successfully',
        application: {
          id: updatedApplication.id as string,
          status: updatedApplication.status as ApplicationStatus,
          withdrawnAt: updatedApplication.responded_at as string
        }
      };
    } catch (error) {
      logger.error('Withdraw application error:', error);
      return {
        success: false,
        message: 'Failed to withdraw application',
        error: 'INTERNAL_ERROR'
      };
    }
  }
}
