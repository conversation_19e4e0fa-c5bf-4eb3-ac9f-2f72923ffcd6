/**
 * Support Service
 * Business logic for help and support operations
 */

import { logger } from '@/utils/logger';
import type {
  IGetFAQResponse,
  IFAQCategory,
  ICreateSupportTicketRequest,
  ICreateSupportTicketResponse,
  ISupportTicket,
  IGetContactInfoResponse,
  IContactInfo
} from '@ozgaar/types';

export class SupportService {
  /**
   * Get FAQs grouped by category
   */
  async getFAQs(): Promise<IGetFAQResponse> {
    try {
      // Static FAQ data grouped by categories
      const categories: IFAQCategory[] = [
        {
          category: 'getting_started',
          displayName: 'Getting Started',
          order: 1,
          faqs: [
            {
              id: 'faq-001',
              question: 'How do I create an account on Ozgaar?',
              answer: 'To create an account, download the Ozgaar app and sign up using your phone number. You will receive an OTP to verify your account.',
              category: 'getting_started',
              order: 1,
              isActive: true,
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z'
            },
            {
              id: 'faq-002',
              question: 'What is the difference between Worker and Poster modes?',
              answer: 'Worker mode is for people looking for work opportunities. Poster mode is for people who want to hire workers for their jobs.',
              category: 'getting_started',
              order: 2,
              isActive: true,
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z'
            }
          ]
        },
        {
          category: 'jobs',
          displayName: 'Jobs & Applications',
          order: 2,
          faqs: [
            {
              id: 'faq-003',
              question: 'How do I apply for a job?',
              answer: 'Browse available jobs, tap on a job that interests you, and click "Apply". You can add a cover letter and propose your rate.',
              category: 'jobs',
              order: 1,
              isActive: true,
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z'
            },
            {
              id: 'faq-004',
              question: 'How many jobs can I apply to per day?',
              answer: 'You can apply to up to 10 jobs per day. This limit helps maintain quality applications and prevents spam.',
              category: 'jobs',
              order: 2,
              isActive: true,
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z'
            }
          ]
        },
        {
          category: 'payments',
          displayName: 'Payments & Earnings',
          order: 3,
          faqs: [
            {
              id: 'faq-005',
              question: 'How do I get paid for completed work?',
              answer: 'Currently, payments are handled directly between workers and job posters. Digital payment integration is coming soon.',
              category: 'payments',
              order: 1,
              isActive: true,
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z'
            },
            {
              id: 'faq-006',
              question: 'Can I export my earnings data?',
              answer: 'Yes, you can export your earnings data from the Earnings section in your profile. Data is available in CSV format.',
              category: 'payments',
              order: 2,
              isActive: true,
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z'
            }
          ]
        },
        {
          category: 'account',
          displayName: 'Account & Profile',
          order: 4,
          faqs: [
            {
              id: 'faq-007',
              question: 'How do I update my profile information?',
              answer: 'Go to Profile & Settings, then tap "Edit Profile" to update your information, skills, and rates.',
              category: 'account',
              order: 1,
              isActive: true,
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z'
            },
            {
              id: 'faq-008',
              question: 'Can I switch between Worker and Poster modes?',
              answer: 'Yes, you can switch modes anytime from the Profile & Settings section. Your data for both modes will be preserved.',
              category: 'account',
              order: 2,
              isActive: true,
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z'
            }
          ]
        }
      ];

      return {
        success: true,
        message: 'FAQs retrieved successfully',
        categories
      };
    } catch (error) {
      logger.error('Get FAQs error:', error);
      return {
        success: false,
        message: 'Failed to retrieve FAQs',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Create support ticket
   */
  async createSupportTicket(userId: string | undefined, ticketData: ICreateSupportTicketRequest): Promise<ICreateSupportTicketResponse> {
    try {
      // Generate ticket number
      const ticketNumber = this.generateTicketNumber();
      
      // Calculate SLA deadline based on priority
      const slaDeadline = this.calculateSLADeadline(ticketData.priority);

      // Create ticket object
      const ticket: ISupportTicket = {
        id: `ticket-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        ticketNumber,
        userId,
        subject: ticketData.subject,
        description: ticketData.description,
        category: ticketData.category,
        priority: ticketData.priority,
        status: 'open',
        userEmail: ticketData.userEmail,
        userPhone: ticketData.userPhone,
        attachments: ticketData.attachments || [],
        slaDeadline,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // In a real implementation, this would be saved to database or sent to external ticketing system
      // For now, we'll log the ticket creation
      logger.info('Support ticket created:', {
        ticketNumber: ticket.ticketNumber,
        category: ticket.category,
        priority: ticket.priority,
        userId: ticket.userId
      });

      return {
        success: true,
        message: 'Support ticket created successfully',
        ticket
      };
    } catch (error) {
      logger.error('Create support ticket error:', error);
      return {
        success: false,
        message: 'Failed to create support ticket',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get contact information
   */
  async getContactInfo(): Promise<IGetContactInfoResponse> {
    try {
      const contactInfo: IContactInfo = {
        email: '<EMAIL>',
        phone: '+91-9876543210',
        whatsapp: '+91-9876543210',
        address: {
          street: '123 Tech Park',
          city: 'Bangalore',
          state: 'Karnataka',
          country: 'India',
          postalCode: '560001'
        },
        businessHours: {
          monday: '9:00 AM - 6:00 PM',
          tuesday: '9:00 AM - 6:00 PM',
          wednesday: '9:00 AM - 6:00 PM',
          thursday: '9:00 AM - 6:00 PM',
          friday: '9:00 AM - 6:00 PM',
          saturday: '10:00 AM - 4:00 PM',
          sunday: 'Closed'
        },
        socialMedia: {
          facebook: 'https://facebook.com/ozgaar',
          twitter: 'https://twitter.com/ozgaar',
          instagram: 'https://instagram.com/ozgaar',
          linkedin: 'https://linkedin.com/company/ozgaar'
        },
        emergencyContact: {
          phone: '+91-9876543211',
          hours: '24/7 for urgent issues'
        }
      };

      return {
        success: true,
        message: 'Contact information retrieved successfully',
        contactInfo
      };
    } catch (error) {
      logger.error('Get contact info error:', error);
      return {
        success: false,
        message: 'Failed to retrieve contact information',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Generate unique ticket number
   */
  private generateTicketNumber(): string {
    const prefix = 'OZG';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `${prefix}-${timestamp}-${random}`;
  }

  /**
   * Calculate SLA deadline based on priority
   */
  private calculateSLADeadline(priority: string): string {
    const now = new Date();
    let hoursToAdd = 24; // Default 24 hours

    switch (priority) {
      case 'urgent':
        hoursToAdd = 2; // 2 hours for urgent
        break;
      case 'high':
        hoursToAdd = 8; // 8 hours for high
        break;
      case 'medium':
        hoursToAdd = 24; // 24 hours for medium
        break;
      case 'low':
        hoursToAdd = 72; // 72 hours for low
        break;
    }

    now.setHours(now.getHours() + hoursToAdd);
    return now.toISOString();
  }
}
