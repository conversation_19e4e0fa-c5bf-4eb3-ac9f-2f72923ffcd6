/**
 * Discovery Service
 * Business logic for poster worker discovery and search
 */

import { databaseConfig } from '@/config/database.config';
import { logger } from '@/utils/logger';
import type {
  IPosterDashboardRequest,
  IPosterDashboardResponse,
  IPosterDashboardStats,
  INearbyWorker,
  IWorkerPersonaSummary,
  IWorkerSearchRequest,
  IWorkerSearchResponse,
  IWorkerSearchResult,
  IWorkerSearchFilters
} from '@ozgaar/types';

export class DiscoveryService {
  private db = databaseConfig.getClient();

  /**
   * Get poster dashboard with nearby workers
   */
  async getPosterDashboard(posterId: string, request: IPosterDashboardRequest): Promise<IPosterDashboardResponse> {
    try {
      const page = 1;
      const limit = Math.min(request.limit || 10, 20);
      const radius = request.radius || 50;

      // Get poster stats
      const stats = await this.getPosterStats(posterId);

      // Get nearby workers using the view
      const { data: workers, error: workersError, count } = await this.db!
        .from('worker_discovery_view')
        .select('*', { count: 'exact' })
        .order('rating', { ascending: false })
        .order('completed_jobs', { ascending: false })
        .range(0, limit - 1);

      if (workersError) {
        logger.error('Failed to get nearby workers:', workersError);
        return {
          success: false,
          message: 'Failed to retrieve nearby workers',
          error: 'DATABASE_ERROR'
        };
      }

      // Format workers with personas
      const nearbyWorkers: INearbyWorker[] = [];
      
      if (workers && workers.length > 0) {
        for (const worker of workers) {
          // Get worker personas
          const { data: personas } = await this.db!
            .from('worker_personas')
            .select('*')
            .eq('user_id', worker.user_id as string)
            .eq('is_active', true);

          const formattedPersonas: IWorkerPersonaSummary[] = personas?.map((persona: any) => ({
            id: persona.id as string,
            skill: persona.skill as string,
            experience: persona.experience as string,
            hourlyRate: persona.hourly_rate as number,
            dailyRate: persona.daily_rate as number,
            rating: persona.rating as number,
            reviewCount: persona.review_count as number,
            completedJobs: persona.completed_jobs as number,
            isActive: persona.is_active as boolean
          })) || [];

          nearbyWorkers.push({
            userId: worker.user_id as string,
            fullName: worker.full_name as string || 'Unknown User',
            avatarUrl: worker.avatar_url as string,
            location: worker.location as string,
            distance: Math.floor(Math.random() * radius), // TODO: Calculate actual distance
            primarySkill: worker.primary_skill as string,
            skills: worker.skills as string[] || [],
            experience: worker.experience as string,
            hourlyRate: worker.hourly_rate as number,
            dailyRate: worker.daily_rate as number,
            rating: worker.rating as number,
            reviewCount: worker.review_count as number,
            completedJobs: worker.completed_jobs as number,
            isAvailable: worker.is_available as boolean,
            lastActive: worker.updated_at as string,
            personas: formattedPersonas
          });
        }
      }

      return {
        success: true,
        message: 'Poster dashboard retrieved successfully',
        data: {
          stats,
          nearbyWorkers,
          pagination: {
            page,
            limit,
            totalCount: count || 0,
            hasMore: (count || 0) > limit
          }
        }
      };
    } catch (error) {
      logger.error('Get poster dashboard error:', error);
      return {
        success: false,
        message: 'Failed to retrieve poster dashboard',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Search workers with filters
   */
  async searchWorkers(request: IWorkerSearchRequest): Promise<IWorkerSearchResponse> {
    try {
      const page = request.page || 1;
      const limit = Math.min(request.limit || 20, 100);
      const offset = (page - 1) * limit;

      // Build base query using the view
      let query = this.db!
        .from('worker_discovery_view')
        .select('*', { count: 'exact' });

      // Apply filters
      if (request.availability !== undefined) {
        query = query.eq('is_available', request.availability);
      }

      if (request.experience) {
        query = query.eq('experience', request.experience);
      }

      if (request.minHourlyRate) {
        query = query.gte('hourly_rate', request.minHourlyRate);
      }

      if (request.maxHourlyRate) {
        query = query.lte('hourly_rate', request.maxHourlyRate);
      }

      if (request.minRating) {
        query = query.gte('rating', request.minRating);
      }

      // Apply sorting
      const sortBy = request.sortBy || 'rating';
      const sortOrder = request.sortOrder === 'asc' ? { ascending: true } : { ascending: false };

      switch (sortBy) {
        case 'rating':
          query = query.order('rating', sortOrder);
          break;
        case 'experience':
          query = query.order('completed_jobs', sortOrder);
          break;
        case 'completedJobs':
          query = query.order('completed_jobs', sortOrder);
          break;
        case 'rate':
          query = query.order('hourly_rate', sortOrder);
          break;
        default:
          query = query.order('rating', { ascending: false });
      }

      // Apply pagination
      query = query.range(offset, offset + limit - 1);

      const { data: workers, error, count } = await query;

      if (error) {
        logger.error('Failed to search workers:', error);
        return {
          success: false,
          message: 'Failed to search workers',
          error: 'DATABASE_ERROR'
        };
      }

      // Format workers with personas
      const searchResults: IWorkerSearchResult[] = [];
      
      if (workers && workers.length > 0) {
        for (const worker of workers) {
          // Get worker personas
          const { data: personas } = await this.db!
            .from('worker_personas')
            .select('*')
            .eq('user_id', worker.user_id as string)
            .eq('is_active', true);

          const formattedPersonas: IWorkerPersonaSummary[] = personas?.map((persona: any) => ({
            id: persona.id as string,
            skill: persona.skill as string,
            experience: persona.experience as string,
            hourlyRate: persona.hourly_rate as number,
            dailyRate: persona.daily_rate as number,
            rating: persona.rating as number,
            reviewCount: persona.review_count as number,
            completedJobs: persona.completed_jobs as number,
            isActive: persona.is_active as boolean
          })) || [];

          // Calculate match score if query provided
          let matchScore = 0;
          if (request.query) {
            const query = request.query.toLowerCase();
            const skills = worker.skills as string[] || [];
            const fullName = worker.full_name as string || '';
            
            if (skills.some(skill => skill.toLowerCase().includes(query))) {
              matchScore += 50;
            }
            if (fullName.toLowerCase().includes(query)) {
              matchScore += 30;
            }
            if ((worker.primary_skill as string || '').toLowerCase().includes(query)) {
              matchScore += 40;
            }
          }

          searchResults.push({
            userId: worker.user_id as string,
            fullName: worker.full_name as string || 'Unknown User',
            avatarUrl: worker.avatar_url as string,
            location: worker.location as string,
            distance: Math.floor(Math.random() * 50), // TODO: Calculate actual distance
            primarySkill: worker.primary_skill as string,
            skills: worker.skills as string[] || [],
            experience: worker.experience as string,
            hourlyRate: worker.hourly_rate as number,
            dailyRate: worker.daily_rate as number,
            about: worker.about as string,
            rating: worker.rating as number,
            reviewCount: worker.review_count as number,
            completedJobs: worker.completed_jobs as number,
            totalEarnings: worker.total_earnings as number,
            isAvailable: worker.is_available as boolean,
            phoneVisible: worker.phone_visible as boolean,
            portfolioPhotos: worker.portfolio_photos as string[] || [],
            lastActive: worker.updated_at as string,
            personas: formattedPersonas,
            matchScore: request.query ? matchScore : undefined
          });
        }
      }

      // Sort by match score if query provided
      if (request.query) {
        searchResults.sort((a, b) => (b.matchScore || 0) - (a.matchScore || 0));
      }

      // Get search filters
      const filters = await this.getSearchFilters();

      return {
        success: true,
        message: 'Workers search completed successfully',
        data: {
          workers: searchResults,
          filters,
          pagination: {
            page,
            limit,
            totalPages: Math.ceil((count || 0) / limit),
            totalCount: count || 0,
            hasMore: (count || 0) > offset + limit
          }
        }
      };
    } catch (error) {
      logger.error('Search workers error:', error);
      return {
        success: false,
        message: 'Failed to search workers',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get poster statistics
   */
  private async getPosterStats(posterId: string): Promise<IPosterDashboardStats> {
    try {
      // Get job stats
      const { data: jobStats } = await this.db!
        .from('jobs')
        .select('status, application_count')
        .eq('poster_id', posterId);

      // Get poster profile stats
      const { data: posterProfile } = await this.db!
        .from('poster_profiles')
        .select('jobs_posted, total_spent')
        .eq('user_id', posterId)
        .single();

      // Calculate stats
      const totalActiveJobs = jobStats?.filter(job => job.status === 'active').length || 0;
      const totalApplications = jobStats?.reduce((sum: number, job: any) => sum + (job.application_count || 0), 0) || 0;
      const totalCompletedJobs = jobStats?.filter(job => job.status === 'completed').length || 0;
      const totalSpent = (posterProfile?.total_spent as number) || 0;

      // Get nearby workers count (simplified)
      const { count: nearbyWorkersCount } = await this.db!
        .from('worker_profiles')
        .select('user_id', { count: 'exact' })
        .eq('is_available', true);

      return {
        totalActiveJobs,
        totalApplications,
        totalCompletedJobs,
        totalSpent,
        nearbyWorkersCount: nearbyWorkersCount || 0
      };
    } catch (error) {
      logger.error('Get poster stats error:', error);
      return {
        totalActiveJobs: 0,
        totalApplications: 0,
        totalCompletedJobs: 0,
        totalSpent: 0,
        nearbyWorkersCount: 0
      };
    }
  }

  /**
   * Get search filters data
   */
  private async getSearchFilters(): Promise<IWorkerSearchFilters> {
    try {
      // Get skills aggregation
      const { data: skillsData } = await this.db!
        .from('worker_profiles')
        .select('skills');

      const skillsMap = new Map<string, number>();
      skillsData?.forEach((worker: any) => {
        const skills = worker.skills as string[] || [];
        skills.forEach(skill => {
          skillsMap.set(skill, (skillsMap.get(skill) || 0) + 1);
        });
      });

      const skills = Array.from(skillsMap.entries())
        .map(([skill, count]) => ({ skill, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 20);

      // Get experience levels
      const { data: experienceData } = await this.db!
        .from('worker_profiles')
        .select('experience');

      const experienceMap = new Map<string, number>();
      experienceData?.forEach((worker: any) => {
        if (worker.experience) {
          experienceMap.set(worker.experience, (experienceMap.get(worker.experience) || 0) + 1);
        }
      });

      const experienceLevels = Array.from(experienceMap.entries())
        .map(([level, count]) => ({ level, count }));

      // Get rate ranges
      const { data: ratesData } = await this.db!
        .from('worker_profiles')
        .select('hourly_rate, daily_rate');

      const hourlyRates = ratesData?.map((w: any) => w.hourly_rate).filter(Boolean) || [];
      const dailyRates = ratesData?.map((w: any) => w.daily_rate).filter(Boolean) || [];

      const rateRanges = {
        hourly: {
          min: hourlyRates.length > 0 ? Math.min(...hourlyRates) : 0,
          max: hourlyRates.length > 0 ? Math.max(...hourlyRates) : 0,
          average: hourlyRates.length > 0 ? Math.round(hourlyRates.reduce((a: number, b: number) => a + b, 0) / hourlyRates.length) : 0
        },
        daily: {
          min: dailyRates.length > 0 ? Math.min(...dailyRates) : 0,
          max: dailyRates.length > 0 ? Math.max(...dailyRates) : 0,
          average: dailyRates.length > 0 ? Math.round(dailyRates.reduce((a: number, b: number) => a + b, 0) / dailyRates.length) : 0
        }
      };

      // Get locations
      const { data: locationsData } = await this.db!
        .from('user_profiles')
        .select('location');

      const locationsMap = new Map<string, number>();
      locationsData?.forEach((user: any) => {
        if (user.location) {
          locationsMap.set(user.location, (locationsMap.get(user.location) || 0) + 1);
        }
      });

      const locations = Array.from(locationsMap.entries())
        .map(([location, count]) => ({ location, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return {
        skills,
        experienceLevels,
        rateRanges,
        locations
      };
    } catch (error) {
      logger.error('Get search filters error:', error);
      return {
        skills: [],
        experienceLevels: [],
        rateRanges: {
          hourly: { min: 0, max: 0, average: 0 },
          daily: { min: 0, max: 0, average: 0 }
        },
        locations: []
      };
    }
  }
}
