/**
 * Jobs Service Recommendations
 * Functions for generating job recommendations
 */

import type { SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@/utils/logger';
import type { 
  IJobRecommendationBucket,
  IJobSummary
} from '@ozgaar/types';
import { formatJobSummaries } from './formatters';
import * as constants from './constants';

/**
 * Get featured jobs (high-paying or urgent)
 */
export async function getFeaturedJobs(
  db: SupabaseClient,
  limit: number,
  offset: number
): Promise<IJobRecommendationBucket> {
  try {
    const { data: jobs, error, count } = await db
      .from('job_search_view')
      .select(`
        id,
        poster_id,
        title,
        description,
        category,
        subcategory,
        location,
        latitude,
        longitude,
        rate,
        rate_type,
        duration,
        requirements,
        skills_required,
        experience_level,
        status,
        urgency,
        photos,
        view_count,
        application_count,
        max_applications,
        auto_accept,
        assigned_worker_id,
        started_at,
        completed_at,
        work_photos,
        payment_status,
        payment_method,
        transaction_id,
        created_at,
        updated_at,
        expires_at,
        company_name,
        poster_rating,
        poster_review_count,
        business_verified,
        poster_full_name,
        poster_avatar_url
      `, { count: 'exact' })
      .eq('status', constants.JOB_STATUS.ACTIVE)
      .or('urgency.eq.urgent,rate.gte.500') // Urgent jobs or high-paying (≥500 INR)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Failed to get featured jobs:', error);
      return createEmptyBucket('Featured Jobs', 'High-paying and urgent opportunities');
    }

    // Transform the data to match the expected format
    const jobSummaries: IJobSummary[] = formatJobSummaries(jobs || []);

    return {
      title: 'Featured Jobs',
      description: 'High-paying and urgent opportunities',
      jobs: jobSummaries,
      hasMore: (count || 0) > offset + limit,
      totalCount: count || 0
    };
  } catch (error) {
    logger.error('Get featured jobs error:', error);
    return createEmptyBucket('Featured Jobs', 'High-paying and urgent opportunities');
  }
}

/**
 * Get nearby jobs based on location
 */
export async function getNearbyJobs(
  db: SupabaseClient,
  location?: string,
  radius: number = 25,
  limit: number = 10,
  offset: number = 0
): Promise<IJobRecommendationBucket> {
  try {
    if (!location) {
      return createEmptyBucket('Nearby Jobs', 'Jobs in your area');
    }

    // For now, use simple text matching on location
    // TODO: Implement proper geospatial queries with latitude/longitude
    const { data: jobs, error, count } = await db
      .from('job_search_view')
      .select(`
        id,
        poster_id,
        title,
        description,
        category,
        subcategory,
        location,
        latitude,
        longitude,
        rate,
        rate_type,
        duration,
        requirements,
        skills_required,
        experience_level,
        status,
        urgency,
        photos,
        view_count,
        application_count,
        max_applications,
        auto_accept,
        assigned_worker_id,
        started_at,
        completed_at,
        work_photos,
        payment_status,
        payment_method,
        transaction_id,
        created_at,
        updated_at,
        expires_at,
        company_name,
        poster_rating,
        poster_review_count,
        business_verified,
        poster_full_name,
        poster_avatar_url
      `, { count: 'exact' })
      .eq('status', constants.JOB_STATUS.ACTIVE)
      .ilike('location', `%${location}%`)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Failed to get nearby jobs:', error);
      return createEmptyBucket('Nearby Jobs', 'Jobs in your area');
    }

    // Transform the data to match the expected format
    const jobSummaries: IJobSummary[] = formatJobSummaries(jobs || []);

    return {
      title: 'Nearby Jobs',
      description: 'Jobs in your area',
      jobs: jobSummaries,
      hasMore: (count || 0) > offset + limit,
      totalCount: count || 0
    };
  } catch (error) {
    logger.error('Get nearby jobs error:', error);
    return createEmptyBucket('Nearby Jobs', 'Jobs in your area');
  }
}

/**
 * Get jobs matching user's skills
 */
export async function getSkillMatchJobs(
  db: SupabaseClient,
  skills: string[],
  limit: number,
  offset: number
): Promise<IJobRecommendationBucket> {
  try {
    if (skills.length === 0) {
      return createEmptyBucket('Skill Match', 'Jobs matching your skills');
    }

    const { data: jobs, error, count } = await db
      .from('job_search_view')
      .select(`
        id,
        poster_id,
        title,
        description,
        category,
        subcategory,
        location,
        latitude,
        longitude,
        rate,
        rate_type,
        duration,
        requirements,
        skills_required,
        experience_level,
        status,
        urgency,
        photos,
        view_count,
        application_count,
        max_applications,
        auto_accept,
        assigned_worker_id,
        started_at,
        completed_at,
        work_photos,
        payment_status,
        payment_method,
        transaction_id,
        created_at,
        updated_at,
        expires_at,
        company_name,
        poster_rating,
        poster_review_count,
        business_verified,
        poster_full_name,
        poster_avatar_url
      `, { count: 'exact' })
      .eq('status', constants.JOB_STATUS.ACTIVE)
      .overlaps('skills_required', skills)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Failed to get skill match jobs:', error);
      return createEmptyBucket('Skill Match', 'Jobs matching your skills');
    }

    // Transform the data to match the expected format
    const jobSummaries: IJobSummary[] = formatJobSummaries(jobs || []);

    return {
      title: 'Skill Match',
      description: 'Jobs matching your skills',
      jobs: jobSummaries,
      hasMore: (count || 0) > offset + limit,
      totalCount: count || 0
    };
  } catch (error) {
    logger.error('Get skill match jobs error:', error);
    return createEmptyBucket('Skill Match', 'Jobs matching your skills');
  }
}

/**
 * Create empty recommendation bucket
 */
export function createEmptyBucket(title: string, description: string): IJobRecommendationBucket {
  return {
    title,
    description,
    jobs: [],
    hasMore: false,
    totalCount: 0
  };
}