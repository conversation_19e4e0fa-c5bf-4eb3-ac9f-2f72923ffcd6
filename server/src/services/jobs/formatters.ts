/**
 * Jobs Service Formatters
 * Utility functions for formatting job data
 */

import type { SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@/utils/logger';
import type { 
  IJobSummary,
  PaginationResponse
} from '@ozgaar/types';
import type { JobWithPhotos, FormattedJob } from './types';
import * as constants from './constants';

/**
 * Format job response with all related data
 */
export async function formatJobResponse(
  db: SupabaseClient,
  job: JobWithPhotos | any,
  userId?: string
): Promise<{
  success: boolean;
  job?: FormattedJob;
  message: string;
  error?: string;
}> {
  try {
    let hasApplied = false;
    let isBookmarked = false;
    let canApply = true;
    let applicationStatus = null;

    if (userId) {
      // Check if user has applied
      const { data: application } = await db
        .from('job_applications')
        .select('status')
        .eq('job_id', job.id)
        .eq('worker_id', userId)
        .single();

      if (application) {
        hasApplied = true;
        applicationStatus = application.status;
        canApply = false; // Can't apply if already applied
      }

      // Check if user has bookmarked
      const { data: bookmark } = await db
        .from('job_bookmarks')
        .select('id')
        .eq('job_id', job.id)
        .eq('user_id', userId)
        .single();

      isBookmarked = !!bookmark;

      // Additional canApply logic
      if (job.poster_id === userId) {
        canApply = false; // Can't apply to own job
      }

      // Handle different property names based on source
      const maxApplications = job.max_applications || job.maxApplications;
      const applicationCount = job.application_count || job.applicationCount;

      if (maxApplications && applicationCount >= maxApplications) {
        canApply = false; // Max applications reached
      }
    }

    // Get related jobs (same category, different job)
    const { data: relatedJobs } = await db
      .from('jobs')
      .select(`
        id,
        title,
        category,
        location,
        rate,
        rate_type,
        urgency,
        created_at,
        user_profiles!jobs_poster_id_fkey (
          full_name
        ),
        poster_profiles!jobs_poster_id_fkey (
          company_name,
          business_verified
        )
      `)
      .eq('status', constants.JOB_STATUS.ACTIVE)
      .eq('category', job.category as string)
      .neq('id', job.id)
      .order('created_at', { ascending: false })
      .limit(5);

    // Handle different property names based on source
    const viewCount = job.view_count || job.viewCount || 0;
    const applicationCount = job.application_count || job.applicationCount || 0;
    const maxApplications = job.max_applications || job.maxApplications;
    const autoAccept = job.auto_accept !== undefined ? job.auto_accept : job.autoAccept;
    const posterFullName = job.poster_full_name || job.poster?.fullName;
    const companyName = job.company_name || job.poster?.companyName;
    const posterAvatarUrl = job.poster_avatar_url || job.poster?.avatarUrl;
    const posterRating = job.poster_rating || job.poster?.rating || 0;
    const posterReviewCount = job.poster_review_count || job.poster?.reviewCount || 0;
    const businessVerified = job.business_verified !== undefined ? job.business_verified : job.poster?.businessVerified;

    const formattedJob: FormattedJob = {
      id: job.id,
      title: job.title,
      description: job.description,
      category: job.category,
      subcategory: job.subcategory,
      location: job.location,
      latitude: job.latitude,
      longitude: job.longitude,
      rate: job.rate,
      rateType: job.rate_type || job.rateType,
      duration: job.duration,
      requirements: job.requirements || [],
      skillsRequired: job.skills_required || job.skillsRequired || [],
      experienceLevel: job.experience_level || job.experienceLevel,
      urgency: job.urgency,
      photos: Array.isArray(job.job_photos) ? job.job_photos.map((photo: any) => ({
        id: photo.id,
        url: photo.url,
        caption: photo.caption
      })) : [],
      viewCount: viewCount,
      applicationCount: applicationCount,
      maxApplications: maxApplications,
      autoAccept: autoAccept,
      createdAt: job.created_at || job.createdAt,
      updatedAt: job.updated_at || job.updatedAt,
      expiresAt: job.expires_at || job.expiresAt,
      poster: {
        id: job.poster_id || job.poster?.id,
        fullName: posterFullName,
        companyName: companyName,
        avatarUrl: posterAvatarUrl,
        rating: posterRating,
        reviewCount: posterReviewCount,
        jobsPosted: 0, // This isn't consistently available
        businessVerified: businessVerified
      },
      canApply,
      hasApplied,
      applicationStatus,
      isBookmarked,
      relatedJobs: relatedJobs?.map(relatedJob => ({
        id: relatedJob.id,
        title: relatedJob.title,
        category: relatedJob.category,
        location: relatedJob.location,
        rate: relatedJob.rate,
        rateType: relatedJob.rate_type,
        urgency: relatedJob.urgency,
        createdAt: relatedJob.created_at,
        poster: {
          fullName: (relatedJob.user_profiles as any)?.full_name,
          companyName: (relatedJob.poster_profiles as any)?.company_name,
          businessVerified: (relatedJob.poster_profiles as any)?.business_verified || false
        }
      })) || []
    };

    // Increment view count (fire and forget)
    try {
      await db
        .from('jobs')
        .update({ view_count: (viewCount as number) + 1 })
        .eq('id', job.id);
    } catch (error) {
      // Ignore errors for view count
      logger.debug('View count update failed:', error);
    }

    return {
      success: true,
      job: formattedJob,
      message: 'Job retrieved successfully'
    };
  } catch (error) {
    logger.error('Format job response error:', error);
    return {
      success: false,
      message: 'Failed to format job response',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Format job summaries from database records
 */
export function formatJobSummaries(jobs: any[]): IJobSummary[] {
  return jobs?.map(job => ({
    id: job.id as string,
    title: job.title as string,
    description: job.description as string,
    category: job.category as string,
    subcategory: job.subcategory as string,
    location: job.location as string,
    latitude: job.latitude as number,
    longitude: job.longitude as number,
    rate: job.rate as number,
    rateType: job.rate_type as 'hourly' | 'daily' | 'fixed',
    duration: job.duration as string,
    skillsRequired: (job.skills_required as string[]) || [],
    experienceLevel: job.experience_level as string,
    urgency: job.urgency as 'normal' | 'urgent',
    photos: (job.photos as string[]) || [],
    viewCount: (job.view_count as number) || 0,
    applicationCount: (job.application_count as number) || 0,
    maxApplications: job.max_applications as number,
    createdAt: job.created_at as string,
    expiresAt: job.expires_at as string,
    poster: {
      id: job.poster_id as string,
      name: job.poster_full_name as string,
      companyName: job.company_name as string,
      rating: (job.poster_rating as number) || 0,
      reviewCount: (job.poster_review_count as number) || 0,
      businessVerified: (job.business_verified as boolean) || false
    }
  })) || [];
}

/**
 * Format pagination response
 */
export function formatPaginationResponse(
  page: number,
  limit: number,
  total: number
): PaginationResponse {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page,
    limit,
    total,
    totalPages,
    hasMore: page < totalPages
  };
}