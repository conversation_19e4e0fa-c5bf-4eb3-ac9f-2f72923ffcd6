/**
 * Jobs Service Dashboard
 * Functions for worker dashboard statistics
 */

import type { SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@/utils/logger';
import type { IWorkerDashboardStatsResponse } from '@ozgaar/types';

/**
 * Get worker dashboard statistics
 */
export async function getDashboardStats(
  db: SupabaseClient,
  userId: string
): Promise<IWorkerDashboardStatsResponse> {
  try {
    // Get application statistics
    const { data: applications } = await db
      .from('job_applications')
      .select('status')
      .eq('worker_id', userId);

    const applicationStats = {
      total: applications?.length || 0,
      pending: applications?.filter(app => app.status === 'pending').length || 0,
      accepted: applications?.filter(app => app.status === 'accepted').length || 0,
      rejected: applications?.filter(app => app.status === 'rejected').length || 0,
      withdrawn: applications?.filter(app => app.status === 'withdrawn').length || 0
    };

    // Get bookmark statistics
    const { data: bookmarks } = await db
      .from('job_bookmarks')
      .select('created_at')
      .eq('user_id', userId);

    const recentBookmarks = bookmarks?.filter(bookmark => {
      const bookmarkDate = new Date(bookmark.created_at as string);
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      return bookmarkDate > weekAgo;
    }).length || 0;

    // Get profile information
    const { data: workerProfile } = await db
      .from('worker_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    const { data: personas } = await db
      .from('worker_personas')
      .select('id')
      .eq('user_id', userId)
      .eq('is_active', true);

    // Calculate profile completion
    const profileFields = ['primary_skill', 'skills', 'experience', 'about'];
    const completedFields = profileFields.filter(field =>
      workerProfile && workerProfile[field] &&
      (Array.isArray(workerProfile[field]) ? workerProfile[field].length > 0 : true)
    );
    const completionPercentage = Math.round((completedFields.length / profileFields.length) * 100);

    // Get recent activity (simplified)
    const { data: recentApplications } = await db
      .from('job_applications')
      .select('applied_at')
      .eq('worker_id', userId)
      .order('applied_at', { ascending: false })
      .limit(1);

    const stats = {
      applications: applicationStats,
      bookmarks: {
        total: bookmarks?.length || 0,
        recentCount: recentBookmarks
      },
      profile: {
        completionPercentage,
        missingFields: profileFields.filter(field =>
          !workerProfile || !workerProfile[field] ||
          (Array.isArray(workerProfile[field]) && workerProfile[field].length === 0)
        ),
        hasWorkerProfile: !!workerProfile,
        hasPersonas: (personas?.length || 0) > 0,
        personaCount: personas?.length || 0
      },
      activity: {
        lastApplicationDate: recentApplications?.[0]?.applied_at as string | undefined,
        lastLoginDate: new Date().toISOString(), // TODO: Track actual login dates
        jobsViewedToday: 0, // TODO: Implement view tracking
        jobsViewedThisWeek: 0
      },
      earnings: {
        totalEarnings: (workerProfile?.total_earnings as number) || 0,
        completedJobs: (workerProfile?.completed_jobs as number) || 0,
        averageRating: (workerProfile?.rating as number) || 0,
        reviewCount: (workerProfile?.review_count as number) || 0
      },
      recommendations: {
        newJobsCount: 0, // TODO: Count jobs posted in last 24 hours matching skills
        urgentJobsCount: 0, // TODO: Count urgent jobs
        nearbyJobsCount: 0 // TODO: Count nearby jobs
      }
    };

    return {
      success: true,
      message: 'Dashboard statistics retrieved successfully',
      stats
    };
  } catch (error) {
    logger.error('Get dashboard stats error:', error);
    return {
      success: false,
      message: 'Failed to retrieve dashboard statistics',
      error: 'INTERNAL_ERROR'
    };
  }
}