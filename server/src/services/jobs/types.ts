/**
 * Jobs Service Types
 * Internal types used within the jobs service module
 */

import type { 
  IJobSummary, 
  IJobRecommendationBucket 
} from '@ozgaar/types';

// Internal job data structure with photos
export interface JobWithPhotos {
  id: string;
  poster_id: string;
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  location: string;
  latitude?: number;
  longitude?: number;
  rate: number;
  rate_type: string;
  duration?: string;
  requirements?: string[];
  skills_required?: string[];
  experience_level?: string;
  status: string;
  urgency: string;
  photos?: string[];
  view_count: number;
  application_count: number;
  max_applications?: number;
  auto_accept: boolean;
  assigned_worker_id?: string;
  started_at?: string;
  completed_at?: string;
  work_photos?: string[];
  payment_status: string;
  payment_method?: string;
  transaction_id?: string;
  created_at: string;
  updated_at: string;
  expires_at?: string;
  company_name?: string;
  poster_rating?: number;
  poster_review_count?: number;
  business_verified?: boolean;
  poster_full_name?: string;
  poster_avatar_url?: string;
  job_photos?: Array<{
    id: string;
    url: string;
    caption?: string;
  }>;
}

// Formatted job response structure
export interface FormattedJob {
  id: string;
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  location: string;
  latitude?: number;
  longitude?: number;
  rate: number;
  rateType: string;
  duration?: string;
  requirements: string[];
  skillsRequired: string[];
  experienceLevel?: string;
  urgency: string;
  photos: Array<{
    id: string;
    url: string;
    caption?: string;
  }>;
  viewCount: number;
  applicationCount: number;
  maxApplications?: number;
  autoAccept: boolean;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
  poster: {
    id: string;
    fullName?: string;
    companyName?: string;
    avatarUrl?: string;
    rating: number;
    reviewCount: number;
    jobsPosted: number;
    businessVerified: boolean;
  };
  canApply: boolean;
  hasApplied: boolean;
  applicationStatus: string | null;
  isBookmarked: boolean;
  relatedJobs: Array<{
    id: string;
    title: string;
    category: string;
    location: string;
    rate: number;
    rateType: string;
    urgency: string;
    createdAt: string;
    poster: {
      fullName?: string;
      companyName?: string;
      businessVerified: boolean;
    };
  }>;
}