/**
 * Jobs Service
 * Business logic for job operations
 */

import { databaseConfig } from '@/config/database.config';
import { logger } from '@/utils/logger';
import type {
  CreateJobRequest,
  GetJobsQuery,
  GetJobsResponse,
  PaginationResponse,
  IJobRecommendationRequest,
  IJobRecommendationsResponse,
  IWorkerDashboardStatsResponse,
  IJobSearchRequest,
  IJobSearchResponse,
  IJobFiltersResponse,
  IJobCategoriesResponse,
  IJobTitleSuggestionsRequest,
  IJobTitleSuggestionsResponse,
  IJobApplicantsRequest,
  IJobApplicantsResponse,
  IJobApplicant,
  IMyJobsRequest,
  IMyJobsResponse,
  IMyJobSummary,
  IJobStatusRequest,
  IJobStatusResponse,
  IJobStatusDetails,
  IJobStatusTimeline,
  IStartWorkRequest,
  IStartWorkResponse,
  IUpdateProgressRequest,
  IUpdateProgressResponse,
  ICompleteJobRequest,
  ICompleteJobResponse,
  IAssignWorkerRequest,
  IAssignWorkerResponse
} from '@ozgaar/types';
import { JobWithPhotos, FormattedJob } from './types';
import { 
  formatJobResponse,
  formatJobSummaries,
  formatPaginationResponse
} from './formatters';
import { 
  getFeaturedJobs,
  getNearbyJobs,
  getSkillMatchJobs,
  createEmptyBucket
} from './recommendations';
import { getDashboardStats } from './dashboard';
import { searchJobs } from './search';
import { getJobFilters } from './filters';
import { getJobCategories, getJobTitleSuggestions } from './categories';
import * as constants from './constants';

export class JobsService {
  private db = databaseConfig.getClient();

  /**
   * Create a new job
   */
  async createJob(posterId: string, jobData: CreateJobRequest) {
    try {
      const { data: job, error } = await this.db!
        .from('jobs')
        .insert({
          poster_id: posterId,
          title: jobData.title,
          description: jobData.description,
          category: jobData.category,
          subcategory: jobData.subcategory,
          location: jobData.location,
          latitude: jobData.latitude,
          longitude: jobData.longitude,
          rate: jobData.rate,
          rate_type: jobData.rateType,
          duration: jobData.duration,
          requirements: jobData.requirements,
          skills_required: jobData.skillsRequired,
          experience_level: jobData.experienceLevel,
          urgency: jobData.urgency || constants.URGENCY.NORMAL,
          photos: jobData.photos,
          max_applications: jobData.maxApplications,
          auto_accept: jobData.autoAccept || false,
          status: constants.JOB_STATUS.ACTIVE
        })
        .select('id, title, status, created_at')
        .single();

      if (error) {
        logger.error('Failed to create job:', error);
        return {
          success: false,
          message: 'Failed to create job',
          error: 'DATABASE_ERROR'
        };
      }

      return {
        success: true,
        job,
        message: 'Job created successfully'
      };
    } catch (error) {
      logger.error('Create job error:', error);
      return {
        success: false,
        message: 'Failed to create job',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get job by ID with poster summary, related jobs, and user-specific data
   */
  async getJobById(jobId: string, userId?: string) {
    try {
      // Get job with poster details from the job_search_view
      let query = this.db!
        .from('job_search_view')
        .select(`
          id,
          poster_id,
          title,
          description,
          category,
          subcategory,
          location,
          latitude,
          longitude,
          rate,
          rate_type,
          duration,
          requirements,
          skills_required,
          experience_level,
          status,
          urgency,
          photos,
          view_count,
          application_count,
          max_applications,
          auto_accept,
          assigned_worker_id,
          started_at,
          completed_at,
          work_photos,
          payment_status,
          payment_method,
          transaction_id,
          created_at,
          updated_at,
          expires_at,
          company_name,
          poster_rating,
          poster_review_count,
          business_verified,
          poster_full_name,
          poster_avatar_url
        `)
        .eq('id', jobId);
      
      // Get the job data
      const { data: jobData, error: jobError } = await query.single();
      
      if (jobError || !jobData) {
        // If not found in job_search_view, try direct query (job might not be active)
        const { data: job, error } = await this.db!
          .from('jobs')
          .select(`
            *,
            user_profiles!jobs_poster_id_fkey (
              user_id,
              full_name,
              avatar_url
            ),
            poster_profiles!jobs_poster_id_fkey (
              company_name,
              rating,
              review_count,
              business_verified,
              jobs_posted
            )
          `)
          .eq('id', jobId)
          .single();
          
        if (error || !job) {
          return {
            success: false,
            message: 'Job not found',
            error: 'NOT_FOUND'
          };
        }
        
        // Get job photos separately
        const { data: jobPhotos } = await this.db!
          .from('job_photos')
          .select('id, url, caption')
          .eq('job_id', jobId);
          
        // Combine job data with photos
        const jobWithPhotos: JobWithPhotos | any = {
          ...job,
          job_photos: jobPhotos || []
        };
        
        return await formatJobResponse(this.db!, jobWithPhotos, userId);
      }
      
      // Get job photos for the job from job_search_view
      const { data: jobPhotos } = await this.db!
        .from('job_photos')
        .select('id, url, caption')
        .eq('job_id', jobId);
      
      // Combine job data with photos
      const jobWithPhotos: JobWithPhotos | any = {
        ...jobData,
        job_photos: jobPhotos || []
      };
      
      return await formatJobResponse(this.db!, jobWithPhotos, userId);

    } catch (error) {
      logger.error('Get job by ID error:', error);
      return {
        success: false,
        message: 'Failed to retrieve job',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Toggle job bookmark
   */
  async toggleBookmark(userId: string, jobId: string) {
    try {
      // Check if already bookmarked
      const { data: existingBookmark } = await this.db!
        .from('job_bookmarks')
        .select('id')
        .eq('user_id', userId)
        .eq('job_id', jobId)
        .single();

      if (existingBookmark) {
        // Remove bookmark
        const { error } = await this.db!
          .from('job_bookmarks')
          .delete()
          .eq('id', (existingBookmark as any).id);

        if (error) {
          logger.error('Failed to remove bookmark:', error);
          return {
            success: false,
            message: 'Failed to remove bookmark',
            error: 'DATABASE_ERROR'
          };
        }

        return {
          success: true,
          isBookmarked: false,
          message: 'Job removed from bookmarks'
        };
      } else {
        // Add bookmark
        const { error } = await this.db!
          .from('job_bookmarks')
          .insert({
            user_id: userId,
            job_id: jobId
          });

        if (error) {
          logger.error('Failed to add bookmark:', error);
          return {
            success: false,
            message: 'Failed to add bookmark',
            error: 'DATABASE_ERROR'
          };
        }

        return {
          success: true,
          isBookmarked: true,
          message: 'Job added to bookmarks'
        };
      }
    } catch (error) {
      logger.error('Toggle bookmark error:', error);
      return {
        success: false,
        message: 'Failed to toggle bookmark',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get job recommendations for worker
   */
  async getRecommendations(userId: string, request: IJobRecommendationRequest): Promise<IJobRecommendationsResponse> {
    try {
      const page = request.page || constants.DEFAULT_PAGE;
      const limit = Math.min(request.limit || 10, 50); // Max 50 per page
      const offset = (page - 1) * limit;

      // Get user's profile and skills for matching
      const { data: workerProfile } = await this.db!
        .from('worker_profiles')
        .select('skills, primary_skill, location')
        .eq('user_id', userId)
        .single();

      const { data: workerPersonas } = await this.db!
        .from('worker_personas')
        .select('skill')
        .eq('user_id', userId)
        .eq('is_active', true);

      // Combine skills from profile and personas
      const userSkills = new Set<string>();
      if (workerProfile?.skills && Array.isArray(workerProfile.skills)) {
        workerProfile.skills.forEach((skill: string) => userSkills.add(skill));
      }
      if (workerProfile?.primary_skill && typeof workerProfile.primary_skill === 'string') {
        userSkills.add(workerProfile.primary_skill);
      }
      if (workerPersonas) {
        workerPersonas.forEach((persona: any) => userSkills.add(persona.skill));
      }

      // Get recommendations in parallel
      const [featuredJobs, nearbyJobs, skillMatchJobs] = await Promise.all([
        getFeaturedJobs(this.db!, limit, offset),
        getNearbyJobs(this.db!, request.location || (typeof workerProfile?.location === 'string' ? workerProfile.location : undefined), request.radius || 25, limit, offset),
        getSkillMatchJobs(this.db!, Array.from(userSkills), limit, offset)
      ]);

      return {
        success: true,
        message: 'Job recommendations retrieved successfully',
        recommendations: {
          featured: featuredJobs,
          nearby: nearbyJobs,
          skillMatch: skillMatchJobs
        },
        pagination: {
          page,
          limit,
          totalPages: Math.max(
            Math.ceil(featuredJobs.totalCount / limit),
            Math.ceil(nearbyJobs.totalCount / limit),
            Math.ceil(skillMatchJobs.totalCount / limit)
          ),
          totalCount: featuredJobs.totalCount + nearbyJobs.totalCount + skillMatchJobs.totalCount
        }
      };
    } catch (error) {
      logger.error('Get job recommendations error:', error);
      return {
        success: false,
        message: 'Failed to retrieve job recommendations',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get worker dashboard statistics
   */
  async getDashboardStats(userId: string): Promise<IWorkerDashboardStatsResponse> {
    return getDashboardStats(this.db!, userId);
  }

  /**
   * Search jobs with filters and sorting
   */
  async searchJobs(searchParams: IJobSearchRequest): Promise<IJobSearchResponse> {
    return searchJobs(this.db!, searchParams);
  }

  /**
   * Get job filters with counts
   */
  async getJobFilters(): Promise<IJobFiltersResponse> {
    return getJobFilters(this.db!);
  }

  /**
   * Get job categories
   */
  async getJobCategories(): Promise<IJobCategoriesResponse> {
    return getJobCategories(this.db!);
  }

  /**
   * Get job title suggestions
   */
  async getJobTitleSuggestions(request: IJobTitleSuggestionsRequest): Promise<IJobTitleSuggestionsResponse> {
    return getJobTitleSuggestions(this.db!, request);
  }

  /**
   * Get job applicants for a specific job
   */
  async getJobApplicants(jobId: string, posterId: string, request: IJobApplicantsRequest): Promise<IJobApplicantsResponse> {
    try {
      // Verify job ownership
      const { data: job, error: jobError } = await this.db!
        .from('jobs')
        .select('id, poster_id')
        .eq('id', jobId)
        .eq('poster_id', posterId)
        .single();

      if (jobError || !job) {
        return {
          success: false,
          message: 'Job not found or access denied',
          error: 'JOB_NOT_FOUND'
        };
      }

      const page = request.page || 1;
      const limit = Math.min(request.limit || 20, 100);
      const offset = (page - 1) * limit;

      // First get the job applications
      let query = this.db!
        .from('job_applications')
        .select('*', { count: 'exact' })
        .eq('job_id', jobId);

      // Apply status filter if provided
      if (request.status) {
        query = query.eq('status', request.status);
      }

      // Apply sorting
      const sortBy = request.sortBy || 'applied_at';
      const sortOrder = request.sortOrder || 'desc';

      switch (sortBy) {
        case 'applied_at':
          query = query.order('applied_at', { ascending: sortOrder === 'asc' });
          break;
        case 'rating':
          // We'll sort after getting worker profiles
          query = query.order('applied_at', { ascending: false });
          break;
        case 'match_score':
          // TODO: Implement match score calculation
          query = query.order('applied_at', { ascending: false });
          break;
        default:
          query = query.order('applied_at', { ascending: false });
      }

      // Apply pagination
      query = query.range(offset, offset + limit - 1);

      const { data: applications, error, count } = await query;

      if (error) {
        logger.error('Failed to get job applicants:', error);
        return {
          success: false,
          message: 'Failed to retrieve job applicants',
          error: 'DATABASE_ERROR'
        };
      }

      // If we have applications, get the related user profiles and worker profiles
      let enrichedApplications = applications;
      if (applications && applications.length > 0) {
        // Get unique worker IDs
        const workerIds = [...new Set(applications.map(app => app.worker_id))];

        // Get user profiles
        const { data: userProfiles } = await this.db!
          .from('user_profiles')
          .select('user_id, full_name, avatar_url')
          .in('user_id', workerIds);

        // Get worker profiles
        const { data: workerProfiles } = await this.db!
          .from('worker_profiles')
          .select('user_id, rating, review_count, completed_jobs')
          .in('user_id', workerIds);

        // Get personas (already joined in the original query)
        const { data: personas } = await this.db!
          .from('worker_personas')
          .select('id, skill')
          .in('id', applications.filter(app => app.persona_id).map(app => app.persona_id));

        // Create lookup maps
        const userProfileMap = new Map();
        const workerProfileMap = new Map();
        const personaMap = new Map();

        userProfiles?.forEach(profile => {
          userProfileMap.set(profile.user_id, profile);
        });

        workerProfiles?.forEach(profile => {
          workerProfileMap.set(profile.user_id, profile);
        });

        personas?.forEach(persona => {
          personaMap.set(persona.id, persona);
        });

        // Enrich applications with profile data
        enrichedApplications = applications.map(app => ({
          ...app,
          user_profiles: userProfileMap.get(app.worker_id) || null,
          worker_profiles: workerProfileMap.get(app.worker_id) || null,
          worker_personas: app.persona_id ? personaMap.get(app.persona_id) : null
        }));
      }

      const applicationsToUse = enrichedApplications;

      if (error) {
        logger.error('Failed to get job applicants:', error);
        return {
          success: false,
          message: 'Failed to retrieve job applicants',
          error: 'DATABASE_ERROR'
        };
      }

      // Format applicants
      const formattedApplicants: IJobApplicant[] = applicationsToUse?.map((app: any) => ({
        id: app.id,
        workerId: app.worker_id,
        workerName: app.user_profiles?.full_name || 'Unknown',
        workerAvatar: app.user_profiles?.avatar_url,
        workerRating: app.worker_profiles?.rating || 0,
        workerReviewCount: app.worker_profiles?.review_count || 0,
        workerCompletedJobs: app.worker_profiles?.completed_jobs || 0,
        personaId: app.persona_id,
        personaSkill: app.worker_personas?.skill,
        coverLetter: app.cover_letter,
        proposedRate: app.proposed_rate,
        proposedRateType: app.proposed_rate_type,
        status: app.status,
        appliedAt: app.applied_at,
        respondedAt: app.responded_at,
        hasMessages: app.has_messages,
        lastMessageAt: app.last_message_at,
        matchScore: 0 // TODO: Calculate match score
      })) || [];

      // Get summary counts
      const { data: summaryData } = await this.db!
        .from('job_applications')
        .select('status')
        .eq('job_id', jobId);

      const summary = {
        total: summaryData?.length || 0,
        pending: summaryData?.filter(app => app.status === 'pending').length || 0,
        accepted: summaryData?.filter(app => app.status === 'accepted').length || 0,
        rejected: summaryData?.filter(app => app.status === 'rejected').length || 0,
        withdrawn: summaryData?.filter(app => app.status === 'withdrawn').length || 0
      };

      return {
        success: true,
        message: 'Job applicants retrieved successfully',
        applicants: formattedApplicants,
        pagination: {
          page,
          limit,
          totalPages: Math.ceil((count || 0) / limit),
          totalCount: count || 0,
          hasMore: (count || 0) > offset + limit
        },
        summary
      };
    } catch (error) {
      logger.error('Get job applicants error:', error);
      return {
        success: false,
        message: 'Failed to retrieve job applicants',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get poster's own jobs
   */
  async getMyJobs(posterId: string, request: IMyJobsRequest): Promise<IMyJobsResponse> {
    try {
      const page = request.page || 1;
      const limit = Math.min(request.limit || 20, 100);
      const offset = (page - 1) * limit;

      // Build query
      let query = this.db!
        .from('jobs')
        .select('*', { count: 'exact' })
        .eq('poster_id', posterId);

      // Apply status filter if provided
      if (request.status) {
        query = query.eq('status', request.status);
      }

      // Apply pagination and ordering
      query = query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: jobs, error, count } = await query;

      if (error) {
        logger.error('Failed to get my jobs:', error);
        return {
          success: false,
          message: 'Failed to retrieve jobs',
          error: 'DATABASE_ERROR'
        };
      }

      // Format jobs
      const formattedJobs: IMyJobSummary[] = jobs?.map((job: any) => ({
        id: job.id,
        title: job.title,
        category: job.category,
        location: job.location,
        rate: job.rate,
        rateType: job.rate_type,
        status: job.status,
        urgency: job.urgency,
        applicationCount: job.application_count || 0,
        maxApplications: job.max_applications,
        viewCount: job.view_count || 0,
        createdAt: job.created_at,
        updatedAt: job.updated_at,
        expiresAt: job.expires_at
      })) || [];

      // Get summary counts
      const { data: summaryData } = await this.db!
        .from('jobs')
        .select('status')
        .eq('poster_id', posterId);

      const summary = {
        total: summaryData?.length || 0,
        draft: summaryData?.filter(job => job.status === 'draft').length || 0,
        active: summaryData?.filter(job => job.status === 'active').length || 0,
        paused: summaryData?.filter(job => job.status === 'paused').length || 0,
        completed: summaryData?.filter(job => job.status === 'completed').length || 0,
        cancelled: summaryData?.filter(job => job.status === 'cancelled').length || 0
      };

      return {
        success: true,
        message: 'Jobs retrieved successfully',
        jobs: formattedJobs,
        pagination: {
          page,
          limit,
          totalPages: Math.ceil((count || 0) / limit),
          totalCount: count || 0,
          hasMore: (count || 0) > offset + limit
        },
        summary
      };
    } catch (error) {
      logger.error('Get my jobs error:', error);
      return {
        success: false,
        message: 'Failed to retrieve jobs',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Get job status and timeline
   */
  async getJobStatus(jobId: string, userId: string): Promise<IJobStatusResponse> {
    try {
      // First get the job to check access
      const { data: jobCheck, error: checkError } = await this.db!
        .from('jobs')
        .select('id, poster_id, assigned_worker_id')
        .eq('id', jobId)
        .single();

      if (checkError || !jobCheck) {
        return {
          success: false,
          message: 'Job not found',
          error: 'JOB_NOT_FOUND'
        };
      }

      // Check if user has access (is poster or assigned worker)
      const hasAccess = jobCheck.poster_id === userId || jobCheck.assigned_worker_id === userId;
      if (!hasAccess) {
        return {
          success: false,
          message: 'Access denied',
          error: 'ACCESS_DENIED'
        };
      }

      // Get job details with poster information from the job_search_view
      const { data: job, error } = await this.db!
        .from('job_search_view')
        .select('*')
        .eq('id', jobId)
        .single();

      if (error || !job) {
        logger.error('Failed to get job details:', error);
        return {
          success: false,
          message: 'Failed to retrieve job details',
          error: 'DATABASE_ERROR'
        };
      }

      // Get assigned worker details if there is one
      let workerDetails = null;
      if (job.assigned_worker_id) {
        const { data: worker } = await this.db!
          .from('user_profiles')
          .select('full_name, avatar_url')
          .eq('user_id', job.assigned_worker_id)
          .single();
        workerDetails = worker;
      }

      // Build timeline events
      const timeline: IJobStatusTimeline[] = [];

      // Job created event
      timeline.push({
        id: `${job.id}-created`,
        event: 'created',
        description: 'Job posted',
        timestamp: job.created_at as string,
        userId: job.poster_id as string,
        userName: job.poster_full_name as string || 'Unknown Poster'
      });

      // Job assigned event (if assigned)
      if (job.assigned_worker_id && workerDetails) {
        timeline.push({
          id: `${job.id}-assigned`,
          event: 'assigned',
          description: 'Worker assigned to job',
          timestamp: job.updated_at as string, // Approximation
          userId: job.assigned_worker_id as string,
          userName: workerDetails.full_name as string || 'Unknown Worker'
        });
      }

      // Job started event (if started)
      if (job.started_at) {
        timeline.push({
          id: `${job.id}-started`,
          event: 'started',
          description: 'Work started',
          timestamp: job.started_at as string,
          userId: job.assigned_worker_id as string,
          userName: workerDetails?.full_name as string || 'Unknown Worker'
        });
      }

      // Job completed event (if completed)
      if (job.completed_at) {
        timeline.push({
          id: `${job.id}-completed`,
          event: 'completed',
          description: 'Work completed',
          timestamp: job.completed_at as string,
          userId: job.assigned_worker_id as string,
          userName: workerDetails?.full_name as string || 'Unknown Worker'
        });
      }

      // Sort timeline by timestamp
      timeline.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

      const jobStatusDetails: IJobStatusDetails = {
        id: job.id as string,
        title: job.title as string,
        description: job.description as string,
        category: job.category as string,
        location: job.location as string,
        rate: job.rate as number,
        rateType: job.rate_type as 'hourly' | 'daily' | 'fixed',
        status: job.status as 'draft' | 'active' | 'paused' | 'completed' | 'cancelled',
        urgency: job.urgency as 'normal' | 'urgent',
        posterId: job.poster_id as string,
        posterName: job.poster_full_name as string || 'Unknown Poster',
        posterAvatar: job.poster_avatar_url as string,
        assignedWorkerId: job.assigned_worker_id as string,
        assignedWorkerName: workerDetails?.full_name as string,
        assignedWorkerAvatar: workerDetails?.avatar_url as string,
        startedAt: job.started_at as string,
        completedAt: job.completed_at as string,
        workPhotos: job.work_photos as string[],
        paymentStatus: job.payment_status as 'pending' | 'processing' | 'completed' | 'failed' | 'disputed' | 'refunded',
        timeline,
        createdAt: job.created_at as string,
        updatedAt: job.updated_at as string
      };

      return {
        success: true,
        message: 'Job status retrieved successfully',
        jobStatus: jobStatusDetails
      };
    } catch (error) {
      logger.error('Get job status error:', error);
      return {
        success: false,
        message: 'Failed to retrieve job status',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Start work on a job
   */
  async startWork(jobId: string, workerId: string): Promise<IStartWorkResponse> {
    try {
      // Verify worker is assigned to this job
      const { data: job, error: jobError } = await this.db!
        .from('job_search_view')
        .select('id, assigned_worker_id, status, started_at')
        .eq('id', jobId)
        .single();

      if (jobError || !job) {
        // If not found in job_search_view, try direct query (job might not be active)
        const { data: directJob, error: directError } = await this.db!
          .from('jobs')
          .select('id, assigned_worker_id, status, started_at')
          .eq('id', jobId)
          .eq('assigned_worker_id', workerId)
          .single();

        if (directError || !directJob) {
          return {
            success: false,
            message: 'Job not found or worker not assigned',
            error: 'JOB_NOT_FOUND'
          };
        }

        // Continue with the direct job data
        const jobToUse = directJob;
        
        // Check if work already started
        if (jobToUse.started_at) {
          return {
            success: false,
            message: 'Work has already been started',
            error: 'WORK_ALREADY_STARTED'
          };
        }

        // Check job status
        if (jobToUse.status !== 'active') {
          return {
            success: false,
            message: 'Job is not in active status',
            error: 'INVALID_JOB_STATUS'
          };
        }

        const startedAt = new Date().toISOString();

        // Update job with started timestamp
        const { error: updateError } = await this.db!
          .from('jobs')
          .update({
            started_at: startedAt,
            updated_at: startedAt
          })
          .eq('id', jobId);

        if (updateError) {
          logger.error('Failed to start work:', updateError);
          return {
            success: false,
            message: 'Failed to start work',
            error: 'DATABASE_ERROR'
          };
        }

        return {
          success: true,
          message: 'Work started successfully',
          startedAt
        };
      }

      // Check if worker is assigned
      if (job.assigned_worker_id !== workerId) {
        return {
          success: false,
          message: 'Worker not assigned to this job',
          error: 'ACCESS_DENIED'
        };
      }

      // Check if work already started
      if (job.started_at) {
        return {
          success: false,
          message: 'Work has already been started',
          error: 'WORK_ALREADY_STARTED'
        };
      }

      // Check job status
      if (job.status !== 'active') {
        return {
          success: false,
          message: 'Job is not in active status',
          error: 'INVALID_JOB_STATUS'
        };
      }

      const startedAt = new Date().toISOString();

      // Update job with started timestamp
      const { error: updateError } = await this.db!
        .from('jobs')
        .update({
          started_at: startedAt,
          updated_at: startedAt
        })
        .eq('id', jobId);

      if (updateError) {
        logger.error('Failed to start work:', updateError);
        return {
          success: false,
          message: 'Failed to start work',
          error: 'DATABASE_ERROR'
        };
      }

      return {
        success: true,
        message: 'Work started successfully',
        startedAt
      };
    } catch (error) {
      logger.error('Start work error:', error);
      return {
        success: false,
        message: 'Failed to start work',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Update job progress
   */
  async updateProgress(jobId: string, workerId: string, request: IUpdateProgressRequest): Promise<IUpdateProgressResponse> {
    try {
      // Verify worker is assigned to this job and work has started
      const { data: job, error: jobError } = await this.db!
        .from('job_search_view')
        .select('id, assigned_worker_id, status, started_at, work_photos')
        .eq('id', jobId)
        .single();

      if (jobError || !job) {
        // If not found in job_search_view, try direct query (job might not be active)
        const { data: directJob, error: directError } = await this.db!
          .from('jobs')
          .select('id, assigned_worker_id, status, started_at, work_photos')
          .eq('id', jobId)
          .eq('assigned_worker_id', workerId)
          .single();

        if (directError || !directJob) {
          return {
            success: false,
            message: 'Job not found or worker not assigned',
            error: 'JOB_NOT_FOUND'
          };
        }

        // Continue with the direct job data
        const jobToUse = directJob;
        
        // Check if work has started
        if (!jobToUse.started_at) {
          return {
            success: false,
            message: 'Work has not been started yet',
            error: 'WORK_NOT_STARTED'
          };
        }

        // Check job status
        if (jobToUse.status !== 'active') {
          return {
            success: false,
            message: 'Job is not in active status',
            error: 'INVALID_JOB_STATUS'
          };
        }

        const updatedAt = new Date().toISOString();

        // Merge new progress photos with existing work photos
        let updatedWorkPhotos = jobToUse.work_photos as string[] || [];
        if (request.progressPhotos && request.progressPhotos.length > 0) {
          updatedWorkPhotos = [...updatedWorkPhotos, ...request.progressPhotos];
        }

        // Update job with progress information
        const { error: updateError } = await this.db!
          .from('jobs')
          .update({
            work_photos: updatedWorkPhotos,
            updated_at: updatedAt
          })
          .eq('id', jobId);

        if (updateError) {
          logger.error('Failed to update progress:', updateError);
          return {
            success: false,
            message: 'Failed to update progress',
            error: 'DATABASE_ERROR'
          };
        }

        return {
          success: true,
          message: 'Progress updated successfully',
          updatedAt
        };
      }

      // Check if worker is assigned
      if (job.assigned_worker_id !== workerId) {
        return {
          success: false,
          message: 'Worker not assigned to this job',
          error: 'ACCESS_DENIED'
        };
      }

      // Check if work has started
      if (!job.started_at) {
        return {
          success: false,
          message: 'Work has not been started yet',
          error: 'WORK_NOT_STARTED'
        };
      }

      // Check job status
      if (job.status !== 'active') {
        return {
          success: false,
          message: 'Job is not in active status',
          error: 'INVALID_JOB_STATUS'
        };
      }

      const updatedAt = new Date().toISOString();

      // Merge new progress photos with existing work photos
      let updatedWorkPhotos = job.work_photos as string[] || [];
      if (request.progressPhotos && request.progressPhotos.length > 0) {
        updatedWorkPhotos = [...updatedWorkPhotos, ...request.progressPhotos];
      }

      // Update job with progress information
      const { error: updateError } = await this.db!
        .from('jobs')
        .update({
          work_photos: updatedWorkPhotos,
          updated_at: updatedAt
        })
        .eq('id', jobId);

      if (updateError) {
        logger.error('Failed to update progress:', updateError);
        return {
          success: false,
          message: 'Failed to update progress',
          error: 'DATABASE_ERROR'
        };
      }

      return {
        success: true,
        message: 'Progress updated successfully',
        updatedAt
      };
    } catch (error) {
      logger.error('Update progress error:', error);
      return {
        success: false,
        message: 'Failed to update progress',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Complete job
   */
  async completeJob(jobId: string, workerId: string, request: ICompleteJobRequest): Promise<ICompleteJobResponse> {
    try {
      // Verify worker is assigned to this job and work has started
      const { data: job, error: jobError } = await this.db!
        .from('job_search_view')
        .select('id, assigned_worker_id, status, started_at, completed_at, work_photos')
        .eq('id', jobId)
        .single();

      if (jobError || !job) {
        // If not found in job_search_view, try direct query (job might not be active)
        const { data: directJob, error: directError } = await this.db!
          .from('jobs')
          .select('id, assigned_worker_id, status, started_at, completed_at, work_photos')
          .eq('id', jobId)
          .eq('assigned_worker_id', workerId)
          .single();

        if (directError || !directJob) {
          return {
            success: false,
            message: 'Job not found or worker not assigned',
            error: 'JOB_NOT_FOUND'
          };
        }

        // Continue with the direct job data
        const jobToUse = directJob;
        
        // Check if work has started
        if (!jobToUse.started_at) {
          return {
            success: false,
            message: 'Work has not been started yet',
            error: 'WORK_NOT_STARTED'
          };
        }

        // Check if job is already completed
        if (jobToUse.completed_at) {
          return {
            success: false,
            message: 'Job has already been completed',
            error: 'JOB_ALREADY_COMPLETED'
          };
        }

        // Check job status
        if (jobToUse.status !== 'active') {
          return {
            success: false,
            message: 'Job is not in active status',
            error: 'INVALID_JOB_STATUS'
          };
        }

        const completedAt = new Date().toISOString();

        // Merge final photos with existing work photos
        let updatedWorkPhotos = jobToUse.work_photos as string[] || [];
        if (request.finalPhotos && request.finalPhotos.length > 0) {
          updatedWorkPhotos = [...updatedWorkPhotos, ...request.finalPhotos];
        }

        // Update job as completed
        const { error: updateError } = await this.db!
          .from('jobs')
          .update({
            status: 'completed',
            completed_at: completedAt,
            work_photos: updatedWorkPhotos,
            updated_at: completedAt
          })
          .eq('id', jobId);

        if (updateError) {
          logger.error('Failed to complete job:', updateError);
          return {
            success: false,
            message: 'Failed to complete job',
            error: 'DATABASE_ERROR'
          };
        }

        return {
          success: true,
          message: 'Job completed successfully',
          completedAt
        };
      }

      // Check if worker is assigned
      if (job.assigned_worker_id !== workerId) {
        return {
          success: false,
          message: 'Worker not assigned to this job',
          error: 'ACCESS_DENIED'
        };
      }

      // Check if work has started
      if (!job.started_at) {
        return {
          success: false,
          message: 'Work has not been started yet',
          error: 'WORK_NOT_STARTED'
        };
      }

      // Check if job is already completed
      if (job.completed_at) {
        return {
          success: false,
          message: 'Job has already been completed',
          error: 'JOB_ALREADY_COMPLETED'
        };
      }

      // Check job status
      if (job.status !== 'active') {
        return {
          success: false,
          message: 'Job is not in active status',
          error: 'INVALID_JOB_STATUS'
        };
      }

      const completedAt = new Date().toISOString();

      // Merge final photos with existing work photos
      let updatedWorkPhotos = job.work_photos as string[] || [];
      if (request.finalPhotos && request.finalPhotos.length > 0) {
        updatedWorkPhotos = [...updatedWorkPhotos, ...request.finalPhotos];
      }

      // Update job as completed
      const { error: updateError } = await this.db!
        .from('jobs')
        .update({
          status: 'completed',
          completed_at: completedAt,
          work_photos: updatedWorkPhotos,
          updated_at: completedAt
        })
        .eq('id', jobId);

      if (updateError) {
        logger.error('Failed to complete job:', updateError);
        return {
          success: false,
          message: 'Failed to complete job',
          error: 'DATABASE_ERROR'
        };
      }

      return {
        success: true,
        message: 'Job completed successfully',
        completedAt
      };
    } catch (error) {
      logger.error('Complete job error:', error);
      return {
        success: false,
        message: 'Failed to complete job',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Assign worker to job
   */
  async assignWorker(jobId: string, posterId: string, request: IAssignWorkerRequest): Promise<IAssignWorkerResponse> {
    try {
      // Verify job ownership and get job details
      const { data: job, error: jobError } = await this.db!
        .from('jobs')
        .select('id, poster_id, status, assigned_worker_id, max_applications')
        .eq('id', jobId)
        .eq('poster_id', posterId)
        .single();

      if (jobError || !job) {
        return {
          success: false,
          message: 'Job not found or access denied',
          error: 'JOB_NOT_FOUND'
        };
      }

      // Check if job is in valid status for assignment
      if (job.status !== 'active') {
        return {
          success: false,
          message: 'Job must be in active status to assign worker',
          error: 'INVALID_JOB_STATUS'
        };
      }

      // Check if worker is already assigned (idempotent assignment)
      if (job.assigned_worker_id) {
        if (job.assigned_worker_id === request.workerId) {
          // Same worker already assigned - idempotent response
          const jobStatus = await this.getJobStatus(jobId, posterId);
          return {
            success: true,
            message: 'Worker already assigned to this job',
            job: jobStatus.success ? jobStatus.jobStatus : undefined
          };
        } else {
          // Different worker already assigned - prevent over-assign
          return {
            success: false,
            message: 'Another worker is already assigned to this job',
            error: 'WORKER_ALREADY_ASSIGNED'
          };
        }
      }

      // Verify worker exists and is available
      const { data: workerProfile, error: workerError } = await this.db!
        .from('worker_profiles')
        .select('user_id, is_available')
        .eq('user_id', request.workerId)
        .single();

      if (workerError || !workerProfile) {
        return {
          success: false,
          message: 'Worker not found',
          error: 'WORKER_NOT_FOUND'
        };
      }

      if (!workerProfile.is_available) {
        return {
          success: false,
          message: 'Worker is not currently available',
          error: 'WORKER_NOT_AVAILABLE'
        };
      }

      // If assigning from application, verify application exists and update status
      if (request.source === 'application' && request.applicationId) {
        const { data: application, error: appError } = await this.db!
          .from('job_applications')
          .select('id, job_id, worker_id, status')
          .eq('id', request.applicationId)
          .eq('job_id', jobId)
          .eq('worker_id', request.workerId)
          .single();

        if (appError || !application) {
          return {
            success: false,
            message: 'Application not found',
            error: 'APPLICATION_NOT_FOUND'
          };
        }

        if (application.status !== 'pending') {
          return {
            success: false,
            message: 'Application is not in pending status',
            error: 'INVALID_APPLICATION_STATUS'
          };
        }

        // Update application status to accepted
        const { error: updateAppError } = await this.db!
          .from('job_applications')
          .update({
            status: 'accepted',
            responded_at: new Date().toISOString()
          })
          .eq('id', request.applicationId);

        if (updateAppError) {
          logger.error('Failed to update application status:', updateAppError);
          return {
            success: false,
            message: 'Failed to update application status',
            error: 'DATABASE_ERROR'
          };
        }

        // Reject other pending applications for this job
        await this.db!
          .from('job_applications')
          .update({
            status: 'rejected',
            responded_at: new Date().toISOString()
          })
          .eq('job_id', jobId)
          .eq('status', 'pending')
          .neq('id', request.applicationId);
      }

      // If assigning from invite, verify invite exists and update status
      if (request.source === 'invite' && request.inviteId) {
        const { data: invite, error: inviteError } = await this.db!
          .from('job_invites')
          .select('id, job_id, worker_id, status')
          .eq('id', request.inviteId)
          .eq('job_id', jobId)
          .eq('worker_id', request.workerId)
          .single();

        if (inviteError || !invite) {
          return {
            success: false,
            message: 'Invite not found',
            error: 'INVITE_NOT_FOUND'
          };
        }

        if (invite.status !== 'pending') {
          return {
            success: false,
            message: 'Invite is not in pending status',
            error: 'INVALID_INVITE_STATUS'
          };
        }

        // Update invite status to accepted
        const { error: updateInviteError } = await this.db!
          .from('job_invites')
          .update({
            status: 'accepted',
            responded_at: new Date().toISOString()
          })
          .eq('id', request.inviteId);

        if (updateInviteError) {
          logger.error('Failed to update invite status:', updateInviteError);
          return {
            success: false,
            message: 'Failed to update invite status',
            error: 'DATABASE_ERROR'
          };
        }
      }

      const assignedAt = new Date().toISOString();

      // Assign worker to job
      const { error: assignError } = await this.db!
        .from('jobs')
        .update({
          assigned_worker_id: request.workerId,
          updated_at: assignedAt
        })
        .eq('id', jobId);

      if (assignError) {
        logger.error('Failed to assign worker:', assignError);
        return {
          success: false,
          message: 'Failed to assign worker to job',
          error: 'DATABASE_ERROR'
        };
      }

      // Get updated job status
      const jobStatus = await this.getJobStatus(jobId, posterId);

      // TODO: Send notification to worker about assignment
      logger.info('Worker assigned to job:', {
        jobId,
        workerId: request.workerId,
        posterId,
        source: request.source,
        assignedAt
      });

      return {
        success: true,
        message: 'Worker assigned successfully',
        job: jobStatus.success ? jobStatus.jobStatus : undefined
      };
    } catch (error) {
      logger.error('Assign worker error:', error);
      return {
        success: false,
        message: 'Failed to assign worker',
        error: 'INTERNAL_ERROR'
      };
    }
  }
}