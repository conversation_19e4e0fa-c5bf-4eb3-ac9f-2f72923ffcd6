/**
 * Jobs Service Search
 * Functions for searching jobs with filters and sorting
 */

import type { SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@/utils/logger';
import type { 
  IJobSearchRequest,
  IJobSearchResponse
} from '@ozgaar/types';
import { formatJobSummaries } from './formatters';
import * as constants from './constants';

/**
 * Search jobs with filters and sorting
 */
export async function searchJobs(
  db: SupabaseClient,
  searchParams: IJobSearchRequest
): Promise<IJobSearchResponse> {
  try {
    const startTime = Date.now();
    const page = searchParams.page || 1;
    const limit = Math.min(searchParams.limit || 20, 100); // Max 100 per page
    const offset = (page - 1) * limit;

    // Build base query using the job_search_view
    let query = db
      .from('job_search_view')
      .select(`
        id,
        poster_id,
        title,
        description,
        category,
        subcategory,
        location,
        latitude,
        longitude,
        rate,
        rate_type,
        duration,
        requirements,
        skills_required,
        experience_level,
        status,
        urgency,
        photos,
        view_count,
        application_count,
        max_applications,
        auto_accept,
        assigned_worker_id,
        started_at,
        completed_at,
        work_photos,
        payment_status,
        payment_method,
        transaction_id,
        created_at,
        updated_at,
        expires_at,
        company_name,
        poster_rating,
        poster_review_count,
        business_verified,
        poster_full_name,
        poster_avatar_url
      `, { count: 'exact' });

    // Apply keyword search
    if (searchParams.keyword) {
      query = query.or(`title.ilike.%${searchParams.keyword}%,description.ilike.%${searchParams.keyword}%`);
    }

    // Apply category filter
    if (searchParams.category) {
      query = query.eq('category', searchParams.category);
    }

    // Apply subcategory filter
    if (searchParams.subcategory) {
      query = query.eq('subcategory', searchParams.subcategory);
    }

    // Apply location filter
    if (searchParams.location) {
      query = query.ilike('location', `%${searchParams.location}%`);
    }

    // Apply rate filters
    if (searchParams.minRate) {
      query = query.gte('rate', searchParams.minRate);
    }
    if (searchParams.maxRate) {
      query = query.lte('rate', searchParams.maxRate);
    }

    // Apply rate type filter
    if (searchParams.rateType) {
      query = query.eq('rate_type', searchParams.rateType);
    }

    // Apply experience level filter
    if (searchParams.experienceLevel) {
      query = query.eq('experience_level', searchParams.experienceLevel);
    }

    // Apply urgency filter
    if (searchParams.urgency) {
      query = query.eq('urgency', searchParams.urgency);
    }

    // Apply skills filter
    if (searchParams.skills && searchParams.skills.length > 0) {
      query = query.overlaps('skills_required', searchParams.skills);
    }

    // Apply sorting
    const sortBy = searchParams.sortBy || 'date';
    const sortOrder = searchParams.sortOrder || 'desc';

    switch (sortBy) {
      case 'date':
        query = query.order('created_at', { ascending: sortOrder === 'asc' });
        break;
      case 'rate':
        query = query.order('rate', { ascending: sortOrder === 'asc' });
        break;
      case 'relevance':
        // For relevance, we'll use a combination of factors
        query = query.order('urgency', { ascending: false })
                    .order('created_at', { ascending: false });
        break;
      case 'distance':
        // TODO: Implement proper distance sorting with geospatial queries
        query = query.order('created_at', { ascending: false });
        break;
      default:
        query = query.order('created_at', { ascending: false });
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: jobs, error, count } = await query;

    if (error) {
      logger.error('Failed to search jobs:', error);
      return {
        success: false,
        message: 'Failed to search jobs',
        error: 'DATABASE_ERROR'
      };
    }

    // Transform the data to match the expected format
    const jobSummaries: any[] = formatJobSummaries(jobs || []);

    const searchTime = Date.now() - startTime;

    return {
      success: true,
      message: 'Jobs search completed successfully',
      jobs: jobSummaries,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil((count || 0) / limit),
        totalCount: count || 0,
        hasMore: (count || 0) > offset + limit
      },
      searchMeta: {
        keyword: searchParams.keyword,
        appliedFilters: {
          category: searchParams.category,
          location: searchParams.location,
          rateRange: searchParams.minRate || searchParams.maxRate ? {
            min: searchParams.minRate || 0,
            max: searchParams.maxRate || 999999
          } : undefined,
          skills: searchParams.skills
        },
        searchTime
      }
    };
  } catch (error) {
    logger.error('Search jobs error:', error);
    return {
      success: false,
      message: 'Failed to search jobs',
      error: 'INTERNAL_ERROR'
    };
  }
}