/**
 * Jobs Service Categories
 * Functions for retrieving job categories and title suggestions
 */

import type { SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@/utils/logger';
import type { 
  IJobCategoriesResponse,
  IJobTitleSuggestionsRequest,
  IJobTitleSuggestionsResponse
} from '@ozgaar/types';

/**
 * Get job categories
 */
export async function getJobCategories(db: SupabaseClient): Promise<IJobCategoriesResponse> {
  try {
    // Get all categories with job counts
    const { data: categories, error } = await db
      .from('job_categories')
      .select('*')
      .order('name');

    if (error) {
      logger.error('Failed to get job categories:', error);
      return {
        success: false,
        message: 'Failed to retrieve job categories',
        error: 'DATABASE_ERROR'
      };
    }

    // Get job counts for each category
    const { data: jobs } = await db
      .from('jobs')
      .select('category')
      .eq('status', 'active');

    const jobCountMap = new Map<string, number>();
    jobs?.forEach(job => {
      if (job.category && typeof job.category === 'string') {
        jobCountMap.set(job.category, (jobCountMap.get(job.category) || 0) + 1);
      }
    });

    const formattedCategories = categories?.map(category => ({
      id: category.id as string,
      name: category.name as string,
      jobCount: jobCountMap.get(category.name as string) || 0
    })) || [];

    return {
      success: true,
      message: 'Job categories retrieved successfully',
      categories: formattedCategories
    };
  } catch (error) {
    logger.error('Get job categories error:', error);
    return {
      success: false,
      message: 'Failed to retrieve job categories',
      error: 'INTERNAL_ERROR'
    };
  }
}

/**
 * Get job title suggestions
 */
export async function getJobTitleSuggestions(
  db: SupabaseClient,
  request: IJobTitleSuggestionsRequest
): Promise<IJobTitleSuggestionsResponse> {
  try {
    const limit = Math.min(request.limit || 10, 50);

    let query = db
      .from('jobs')
      .select('title')
      .eq('status', 'active');

    // Filter by category if provided
    if (request.category) {
      query = query.eq('category', request.category);
    }

    // Filter by keyword if provided
    if (request.keyword) {
      query = query.ilike('title', `%${request.keyword}%`);
    }

    const { data: jobs, error } = await query
      .order('created_at', { ascending: false })
      .limit(limit * 2); // Get more to have variety after deduplication

    if (error) {
      logger.error('Failed to get job title suggestions:', error);
      return {
        success: false,
        message: 'Failed to retrieve job title suggestions',
        error: 'DATABASE_ERROR'
      };
    }

    // Extract unique titles and limit results
    const uniqueTitles = new Set<string>();
    jobs?.forEach(job => {
      if (job.title && typeof job.title === 'string') {
        uniqueTitles.add(job.title);
      }
    });

    const suggestions = Array.from(uniqueTitles).slice(0, limit);

    return {
      success: true,
      message: 'Job title suggestions retrieved successfully',
      suggestions
    };
  } catch (error) {
    logger.error('Get job title suggestions error:', error);
    return {
      success: false,
      message: 'Failed to retrieve job title suggestions',
      error: 'INTERNAL_ERROR'
    };
  }
}