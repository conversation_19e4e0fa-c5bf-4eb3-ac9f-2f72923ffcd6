/**
 * Jobs Service Constants
 * Shared constants used across job-related functionality
 */

// Default values for pagination
export const DEFAULT_PAGE = 1;
export const DEFAULT_LIMIT = 20;
export const MAX_LIMIT = 100;

// Job status values
export const JOB_STATUS = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
} as const;

// Application status values
export const APPLICATION_STATUS = {
  PENDING: 'pending',
  ACCEPTED: 'accepted',
  REJECTED: 'rejected',
  WITHDRAWN: 'withdrawn'
} as const;

// Rate type values
export const RATE_TYPE = {
  HOURLY: 'hourly',
  DAILY: 'daily',
  FIXED: 'fixed'
} as const;

// Urgency values
export const URGENCY = {
  NORMAL: 'normal',
  URGENT: 'urgent'
} as const;