# Jobs Service Module

This directory contains the refactored jobs service module, organized into smaller, focused files following the single-responsibility principle.

## Structure

- `index.ts` - Main entry point exporting the JobsService class
- `constants.ts` - Shared constants used across job-related functionality
- `types.ts` - Internal types used within the jobs service module
- `formatters.ts` - Utility functions for formatting job data
- `recommendations.ts` - Functions for generating job recommendations
- `dashboard.ts` - Functions for worker dashboard statistics
- `search.ts` - Functions for searching jobs with filters and sorting
- `filters.ts` - Functions for retrieving job filters
- `categories.ts` - Functions for retrieving job categories and title suggestions

## Design Principles

1. **Single Responsibility**: Each module has a clear, focused purpose
2. **Logical Separation**: Related functionality is grouped together
3. **Backward Compatibility**: The main JobsService class is still exported from the original location
4. **Type Safety**: Full TypeScript support with proper typing
5. **Maintainability**: Smaller, focused files are easier to understand and modify

## Usage

The service can still be imported exactly as before:

```typescript
import { JobsService } from '@/services/jobs.service';
```

This maintains full backward compatibility with existing code while providing a cleaner internal structure.