/**
 * Jobs Service Filters
 * Functions for retrieving job filters
 */

import type { SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@/utils/logger';
import type { IJobFiltersResponse } from '@ozgaar/types';

/**
 * Get job filters with counts
 */
export async function getJobFilters(db: SupabaseClient): Promise<IJobFiltersResponse> {
  try {
    // Get all active jobs for filter counts
    const { data: jobs, error } = await db
      .from('jobs')
      .select('category, subcategory, location, rate, rate_type, experience_level, urgency, skills_required')
      .eq('status', 'active');

    if (error) {
      logger.error('Failed to get jobs for filters:', error);
      return {
        success: false,
        message: 'Failed to retrieve job filters',
        error: 'DATABASE_ERROR'
      };
    }

    const jobsData = jobs || [];
    const totalJobsCount = jobsData.length;

    // Build category filters
    const categoryMap = new Map<string, number>();
    jobsData.forEach(job => {
      if (job.category && typeof job.category === 'string') {
        categoryMap.set(job.category, (categoryMap.get(job.category) || 0) + 1);
      }
    });

    const categories = {
      name: 'categories',
      label: 'Categories',
      type: 'multiple' as const,
      options: Array.from(categoryMap.entries()).map(([value, count]) => ({
        value,
        label: value,
        count
      })).sort((a, b) => b.count - a.count)
    };

    // Build location filters
    const locationMap = new Map<string, number>();
    jobsData.forEach(job => {
      if (job.location && typeof job.location === 'string') {
        // Extract city from location string
        const city = job.location.split(',')[0].trim();
        locationMap.set(city, (locationMap.get(city) || 0) + 1);
      }
    });

    const locations = {
      name: 'locations',
      label: 'Locations',
      type: 'multiple' as const,
      options: Array.from(locationMap.entries()).map(([value, count]) => ({
        value,
        label: value,
        count
      })).sort((a, b) => b.count - a.count).slice(0, 20) // Top 20 locations
    };

    // Build rate range filters
    const rateRanges = {
      name: 'rateRanges',
      label: 'Rate Ranges',
      type: 'multiple' as const,
      options: [
        { value: '0-200', label: '₹0 - ₹200', count: 0 },
        { value: '200-500', label: '₹200 - ₹500', count: 0 },
        { value: '500-1000', label: '₹500 - ₹1000', count: 0 },
        { value: '1000-2000', label: '₹1000 - ₹2000', count: 0 },
        { value: '2000+', label: '₹2000+', count: 0 }
      ]
    };

    // Count jobs in each rate range
    jobsData.forEach(job => {
      const rate = (job.rate as number) || 0;
      if (rate < 200) {
        rateRanges.options[0].count++;
      } else if (rate < 500) {
        rateRanges.options[1].count++;
      } else if (rate < 1000) {
        rateRanges.options[2].count++;
      } else if (rate < 2000) {
        rateRanges.options[3].count++;
      } else {
        rateRanges.options[4].count++;
      }
    });

    // Build experience level filters
    const experienceMap = new Map<string, number>();
    jobsData.forEach(job => {
      if (job.experience_level && typeof job.experience_level === 'string') {
        experienceMap.set(job.experience_level, (experienceMap.get(job.experience_level) || 0) + 1);
      }
    });

    const experienceLevels = {
      name: 'experienceLevels',
      label: 'Experience Levels',
      type: 'multiple' as const,
      options: Array.from(experienceMap.entries()).map(([value, count]) => ({
        value,
        label: value,
        count
      }))
    };

    // Build urgency filters
    const urgencyMap = new Map<string, number>();
    jobsData.forEach(job => {
      const urgency = (job.urgency as string) || 'normal';
      urgencyMap.set(urgency, (urgencyMap.get(urgency) || 0) + 1);
    });

    const urgency = {
      name: 'urgency',
      label: 'Urgency',
      type: 'single' as const,
      options: Array.from(urgencyMap.entries()).map(([value, count]) => ({
        value,
        label: value === 'urgent' ? 'Urgent' : 'Normal',
        count
      }))
    };

    // Build skills filters
    const skillsMap = new Map<string, number>();
    jobsData.forEach(job => {
      if (job.skills_required && Array.isArray(job.skills_required)) {
        job.skills_required.forEach((skill: string) => {
          skillsMap.set(skill, (skillsMap.get(skill) || 0) + 1);
        });
      }
    });

    const skills = {
      name: 'skills',
      label: 'Skills',
      type: 'multiple' as const,
      options: Array.from(skillsMap.entries()).map(([value, count]) => ({
        value,
        label: value,
        count
      })).sort((a, b) => b.count - a.count).slice(0, 30) // Top 30 skills
    };

    return {
      success: true,
      message: 'Job filters retrieved successfully',
      filters: {
        categories,
        locations,
        rateRanges,
        experienceLevels,
        urgency,
        skills
      },
      totalJobsCount
    };
  } catch (error) {
    logger.error('Get job filters error:', error);
    return {
      success: false,
      message: 'Failed to retrieve job filters',
      error: 'INTERNAL_ERROR'
    };
  }
}