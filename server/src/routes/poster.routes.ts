/**
 * Poster Routes
 * Defines routes for poster-specific functionality
 */

import { Router } from 'express';
import { DiscoveryController } from '@/controllers/discovery.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { validate, commonSchemas } from '@/middleware/validation.middleware';
import Jo<PERSON> from 'joi';

const router: Router = Router();
const discoveryController = new DiscoveryController();

// Validation schemas
const getPosterDashboardSchema = {
  query: Joi.object({
    radius: Joi.number().min(1).max(200).optional(),
    limit: Joi.number().min(1).max(20).optional()
  })
};

/**
 * @openapi
 * /api/poster/dashboard:
 *   get:
 *     summary: Get poster dashboard
 *     description: Retrieves poster dashboard with statistics and nearby workers. Shows active jobs, applications, and available workers in the area.
 *     tags: [Poster]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: radius
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 200
 *           default: 50
 *         description: Search radius in kilometers for nearby workers
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 20
 *           default: 10
 *         description: Maximum number of nearby workers to return
 *     responses:
 *       200:
 *         description: Poster dashboard retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Poster dashboard retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     stats:
 *                       type: object
 *                       properties:
 *                         totalActiveJobs:
 *                           type: integer
 *                           example: 5
 *                         totalApplications:
 *                           type: integer
 *                           example: 23
 *                         totalCompletedJobs:
 *                           type: integer
 *                           example: 12
 *                         totalSpent:
 *                           type: integer
 *                           example: 45000
 *                         nearbyWorkersCount:
 *                           type: integer
 *                           example: 156
 *                     nearbyWorkers:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           userId:
 *                             type: string
 *                             example: "user-123e4567-e89b-12d3-a456-426614174000"
 *                           fullName:
 *                             type: string
 *                             example: "Rajesh Kumar"
 *                           avatarUrl:
 *                             type: string
 *                             format: uri
 *                             example: "https://example.com/avatar.jpg"
 *                           location:
 *                             type: string
 *                             example: "Bangalore, Karnataka"
 *                           distance:
 *                             type: number
 *                             example: 12.5
 *                           primarySkill:
 *                             type: string
 *                             example: "Plumbing"
 *                           skills:
 *                             type: array
 *                             items:
 *                               type: string
 *                             example: ["Plumbing", "Electrical", "Carpentry"]
 *                           experience:
 *                             type: string
 *                             example: "2-5"
 *                           hourlyRate:
 *                             type: integer
 *                             example: 500
 *                           dailyRate:
 *                             type: integer
 *                             example: 3000
 *                           rating:
 *                             type: number
 *                             format: float
 *                             example: 4.5
 *                           reviewCount:
 *                             type: integer
 *                             example: 23
 *                           completedJobs:
 *                             type: integer
 *                             example: 45
 *                           isAvailable:
 *                             type: boolean
 *                             example: true
 *                           lastActive:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-01-15T10:30:00Z"
 *                           personas:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                   example: "persona-123"
 *                                 skill:
 *                                   type: string
 *                                   example: "Plumbing"
 *                                 experience:
 *                                   type: string
 *                                   example: "2-5"
 *                                 hourlyRate:
 *                                   type: integer
 *                                   example: 500
 *                                 dailyRate:
 *                                   type: integer
 *                                   example: 3000
 *                                 rating:
 *                                   type: number
 *                                   example: 4.5
 *                                 reviewCount:
 *                                   type: integer
 *                                   example: 15
 *                                 completedJobs:
 *                                   type: integer
 *                                   example: 25
 *                                 isActive:
 *                                   type: boolean
 *                                   example: true
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                           example: 1
 *                         limit:
 *                           type: integer
 *                           example: 10
 *                         totalCount:
 *                           type: integer
 *                           example: 156
 *                         hasMore:
 *                           type: boolean
 *                           example: true
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/dashboard', authenticateToken, validate(getPosterDashboardSchema), discoveryController.getPosterDashboard);

export { router as posterRoutes };
