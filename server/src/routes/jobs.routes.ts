/**
 * Jobs Routes
 * Routes for job-related operations
 * 
 * @openapi
 * tags:
 *   name: Jobs
 *   description: Job management endpoints
 */

import { Router } from 'express';
import { JobsController } from '@/controllers/jobs.controller';
import { ApplicationController } from '@/controllers/application.controller';
import { validate, commonSchemas } from '@/middleware/validation.middleware';
import { authenticateToken, optionalAuth } from '@/middleware/auth.middleware';
import Joi from 'joi';

const router: Router = Router();
const jobsController = new JobsController();
const applicationController = new ApplicationController();

// Validation schemas
const createJobSchema = {
  body: Joi.object({
    title: Joi.string().min(3).max(100).required(),
    description: Joi.string().min(10).max(2000).required(),
    category: Joi.string().required(),
    subcategory: Joi.string().optional(),
    location: Joi.string().required(),
    latitude: Joi.number().min(-90).max(90).optional(),
    longitude: Joi.number().min(-180).max(180).optional(),
    rate: Joi.number().positive().required(),
    rateType: Joi.string().valid('hourly', 'daily', 'fixed').required(),
    duration: Joi.string().optional(),
    requirements: Joi.array().items(Joi.string()).optional(),
    skillsRequired: Joi.array().items(Joi.string()).optional(),
    experienceLevel: Joi.string().optional(),
    urgency: Joi.string().valid('normal', 'urgent').default('normal'),
    photos: Joi.array().items(Joi.string().uri()).optional(),
    maxApplications: Joi.number().positive().optional(),
    autoAccept: Joi.boolean().default(false)
  })
};

const getJobByIdSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  })
};

const bookmarkJobSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  })
};

const getRecommendationsSchema = {
  query: Joi.object({
    page: commonSchemas.pagination.page,
    limit: commonSchemas.pagination.limit,
    location: Joi.string().optional(),
    radius: Joi.number().positive().max(100).default(25),
    skills: Joi.array().items(Joi.string()).optional(),
    categories: Joi.array().items(Joi.string()).optional()
  })
};

const searchJobsSchema = {
  query: Joi.object({
    keyword: Joi.string().max(100).optional(),
    category: Joi.string().max(50).optional(),
    subcategory: Joi.string().max(50).optional(),
    location: Joi.string().max(100).optional(),
    radius: Joi.number().positive().max(100).default(25),
    minRate: Joi.number().positive().optional(),
    maxRate: Joi.number().positive().optional(),
    rateType: Joi.string().valid('hourly', 'daily', 'fixed').optional(),
    experienceLevel: Joi.string().max(20).optional(),
    urgency: Joi.string().valid('normal', 'urgent').optional(),
    skills: Joi.array().items(Joi.string()).optional(),
    sortBy: Joi.string().valid('relevance', 'date', 'rate', 'distance').default('date'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
    page: commonSchemas.pagination.page,
    limit: commonSchemas.pagination.limit
  })
};

const applyToJobSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  }),
  body: Joi.object({
    personaId: commonSchemas.uuid.optional(),
    coverLetter: Joi.string().max(1000).optional(),
    proposedRate: Joi.number().positive().optional(),
    proposedRateType: Joi.string().valid('hourly', 'daily', 'fixed').optional()
  })
};

const getJobTitleSuggestionsSchema = {
  query: Joi.object({
    category: Joi.string().max(50).optional(),
    keyword: Joi.string().max(100).optional(),
    limit: Joi.number().positive().max(50).default(10)
  })
};

const getJobApplicantsSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  }),
  query: Joi.object({
    sortBy: Joi.string().valid('match_score', 'applied_at', 'rating').default('applied_at'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
    status: Joi.string().valid('pending', 'accepted', 'rejected', 'withdrawn').optional(),
    page: commonSchemas.pagination.page,
    limit: commonSchemas.pagination.limit
  })
};

const getMyJobsSchema = {
  query: Joi.object({
    status: Joi.string().valid('draft', 'active', 'paused', 'completed', 'cancelled').optional(),
    page: commonSchemas.pagination.page,
    limit: commonSchemas.pagination.limit
  })
};

const getJobStatusSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  })
};

const startWorkSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  })
};

const updateProgressSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  }),
  body: Joi.object({
    progressDescription: Joi.string().required().max(1000),
    progressPhotos: Joi.array().items(Joi.string().uri()).optional(),
    estimatedCompletion: Joi.string().isoDate().optional(),
    metadata: Joi.object().optional()
  })
};

const completeJobSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  }),
  body: Joi.object({
    completionNotes: Joi.string().max(1000).optional(),
    finalPhotos: Joi.array().items(Joi.string().uri()).optional(),
    hoursWorked: Joi.number().positive().optional(),
    metadata: Joi.object().optional()
  })
};

const assignWorkerSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  }),
  body: Joi.object({
    workerId: commonSchemas.uuid.required(),
    source: Joi.string().valid('application', 'invite', 'direct').optional(),
    applicationId: commonSchemas.uuid.optional(),
    inviteId: commonSchemas.uuid.optional(),
    message: Joi.string().max(500).optional()
  })
};

/**
 * @openapi
 * /api/jobs:
 *   post:
 *     summary: Create a new job
 *     description: Creates a new job posting. Requires authentication.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateJobRequest'
 *     responses:
 *       201:
 *         description: Job created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CreateJobResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/', authenticateToken, validate(createJobSchema), jobsController.createJob);

/**
 * @openapi
 * /api/jobs/search:
 *   get:
 *     summary: Search jobs with filters
 *     description: Search for jobs using keyword, category, location, rate, and other filters. Supports sorting and pagination.
 *     tags: [Jobs]
 *     parameters:
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Search keyword for job title or description
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           maxLength: 50
 *         description: Job category filter
 *       - in: query
 *         name: subcategory
 *         schema:
 *           type: string
 *           maxLength: 50
 *         description: Job subcategory filter
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Location filter
 *       - in: query
 *         name: radius
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 25
 *         description: Search radius in kilometers
 *       - in: query
 *         name: minRate
 *         schema:
 *           type: integer
 *           minimum: 0
 *         description: Minimum rate filter
 *       - in: query
 *         name: maxRate
 *         schema:
 *           type: integer
 *           minimum: 0
 *         description: Maximum rate filter
 *       - in: query
 *         name: rateType
 *         schema:
 *           type: string
 *           enum: [hourly, daily, fixed]
 *         description: Rate type filter
 *       - in: query
 *         name: experienceLevel
 *         schema:
 *           type: string
 *           maxLength: 20
 *         description: Experience level filter
 *       - in: query
 *         name: urgency
 *         schema:
 *           type: string
 *           enum: [normal, urgent]
 *         description: Job urgency filter
 *       - in: query
 *         name: skills
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Skills filter array
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [relevance, date, rate, distance]
 *           default: date
 *         description: Sort field
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Jobs search completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/IJobSearchResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/search', validate(searchJobsSchema), jobsController.searchJobs);

/**
 * @openapi
 * /api/jobs:
 *   get:
 *     summary: Search jobs with filters (alias for /search)
 *     description: Search for jobs using keyword, category, location, rate, and other filters. Supports sorting and pagination.
 *     tags: [Jobs]
 *     parameters:
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Search keyword for job title or description
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           maxLength: 50
 *         description: Job category filter
 *       - in: query
 *         name: subcategory
 *         schema:
 *           type: string
 *           maxLength: 50
 *         description: Job subcategory filter
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Location filter
 *       - in: query
 *         name: radius
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 25
 *         description: Search radius in kilometers
 *       - in: query
 *         name: minRate
 *         schema:
 *           type: integer
 *           minimum: 0
 *         description: Minimum rate filter
 *       - in: query
 *         name: maxRate
 *         schema:
 *           type: integer
 *           minimum: 0
 *         description: Maximum rate filter
 *       - in: query
 *         name: rateType
 *         schema:
 *           type: string
 *           enum: [hourly, daily, fixed]
 *         description: Rate type filter
 *       - in: query
 *         name: experienceLevel
 *         schema:
 *           type: string
 *           maxLength: 20
 *         description: Experience level filter
 *       - in: query
 *         name: urgency
 *         schema:
 *           type: string
 *           enum: [normal, urgent]
 *         description: Job urgency filter
 *       - in: query
 *         name: skills
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Skills filter array
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [relevance, date, rate, distance]
 *           default: date
 *         description: Sort field
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Jobs search completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/IJobSearchResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/', validate(searchJobsSchema), jobsController.searchJobs);

/**
 * @openapi
 * /api/jobs/recommendations:
 *   get:
 *     summary: Get job recommendations for worker
 *     description: Retrieves personalized job recommendations based on worker's skills, location, and preferences. Requires authentication.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *         description: Override location for nearby jobs
 *       - in: query
 *         name: radius
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 25
 *         description: Search radius in kilometers for nearby jobs
 *       - in: query
 *         name: skills
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Override skills for skill matching
 *       - in: query
 *         name: categories
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Filter by job categories
 *     responses:
 *       200:
 *         description: Job recommendations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/IJobRecommendationsResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/recommendations', authenticateToken, validate(getRecommendationsSchema), jobsController.getRecommendations);

/**
 * @openapi
 * /api/jobs/filters:
 *   get:
 *     summary: Get job filters with counts
 *     description: Retrieves available filter options with job counts for each filter value.
 *     tags: [Jobs]
 *     responses:
 *       200:
 *         description: Job filters retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/IJobFiltersResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/filters', jobsController.getJobFilters);

/**
 * @openapi
 * /api/jobs/categories:
 *   get:
 *     summary: Get job categories
 *     description: Retrieves all available job categories with job counts.
 *     tags: [Jobs]
 *     responses:
 *       200:
 *         description: Job categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Job categories retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     categories:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                             example: "550e8400-e29b-41d4-a716-************"
 *                           name:
 *                             type: string
 *                             example: "Technology"
 *                           jobCount:
 *                             type: integer
 *                             example: 25
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/categories', jobsController.getJobCategories);

/**
 * @openapi
 * /api/jobs/title-suggestions:
 *   get:
 *     summary: Get job title suggestions
 *     description: Retrieves job title suggestions based on category and keyword filters.
 *     tags: [Jobs]
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           maxLength: 50
 *         description: Filter suggestions by job category
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Filter suggestions by keyword
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Maximum number of suggestions to return
 *     responses:
 *       200:
 *         description: Job title suggestions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Job title suggestions retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     suggestions:
 *                       type: array
 *                       items:
 *                         type: string
 *                       example: ["Software Developer", "Frontend Developer", "Backend Developer"]
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/title-suggestions', validate(getJobTitleSuggestionsSchema), jobsController.getJobTitleSuggestions);

/**
 * @openapi
 * /api/jobs/{id}/applicants:
 *   get:
 *     summary: Get job applicants
 *     description: Retrieves all applicants for a specific job. Only accessible by the job poster.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Job ID to get applicants for
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [match_score, applied_at, rating]
 *           default: applied_at
 *         description: Sort applicants by field
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, accepted, rejected, withdrawn]
 *         description: Filter applicants by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of applicants per page
 *     responses:
 *       200:
 *         description: Job applicants retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/JobApplicantsResponse'
 *       404:
 *         description: Job not found or access denied
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/:id/applicants', authenticateToken, validate(getJobApplicantsSchema), jobsController.getJobApplicants);

/**
 * @openapi
 * /api/jobs/my-jobs:
 *   get:
 *     summary: Get my jobs
 *     description: Retrieves all jobs posted by the authenticated user with pagination and filtering options.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, active, paused, completed, cancelled]
 *         description: Filter jobs by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of jobs per page
 *     responses:
 *       200:
 *         description: Jobs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MyJobsResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/my-jobs', authenticateToken, validate(getMyJobsSchema), jobsController.getMyJobs);

/**
 * @openapi
 * /api/jobs/{id}:
 *   get:
 *     summary: Get job by ID
 *     description: Retrieves a specific job by its ID. Authentication is optional.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Job ID
 *     responses:
 *       200:
 *         description: Job retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/GetJobByIdResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/:id', optionalAuth, validate(getJobByIdSchema), jobsController.getJobById);

/**
 * @openapi
 * /api/jobs/{id}/bookmark:
 *   post:
 *     summary: Bookmark/unbookmark a job
 *     description: Adds or removes a job from the user's bookmarks. Requires authentication.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Job ID to bookmark/unbookmark
 *     responses:
 *       200:
 *         description: Bookmark status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BookmarkJobResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/:id/bookmark', authenticateToken, validate(bookmarkJobSchema), jobsController.bookmarkJob);

/**
 * @openapi
 * /api/jobs/{id}/apply:
 *   post:
 *     summary: Apply to a job
 *     description: Submit an application for a job. Requires authentication and enforces daily application limits.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Job ID to apply to
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               personaId:
 *                 type: string
 *                 format: uuid
 *                 description: Worker persona ID to use for application
 *               coverLetter:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Cover letter for the application
 *               proposedRate:
 *                 type: number
 *                 minimum: 0
 *                 description: Proposed rate (counter-offer)
 *               proposedRateType:
 *                 type: string
 *                 enum: [hourly, daily, fixed]
 *                 description: Type of proposed rate
 *     responses:
 *       201:
 *         description: Application submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApplyToJobResponse'
 *       400:
 *         description: Validation error or business rule violation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       409:
 *         description: Already applied to this job
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       429:
 *         description: Daily application limit reached
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/:id/apply', authenticateToken, validate(applyToJobSchema), applicationController.applyToJob);

/**
 * @openapi
 * /api/jobs/{id}/status:
 *   get:
 *     summary: Get job status and timeline
 *     description: Retrieves detailed job status information including timeline events. Accessible by job poster or assigned worker.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Job ID to get status for
 *     responses:
 *       200:
 *         description: Job status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/JobStatusResponse'
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Access denied
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/:id/status', authenticateToken, validate(getJobStatusSchema), jobsController.getJobStatus);

/**
 * @openapi
 * /api/jobs/{id}/start-work:
 *   post:
 *     summary: Start work on a job
 *     description: Marks the job as started by the assigned worker. Only accessible by the assigned worker.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Job ID to start work on
 *     responses:
 *       200:
 *         description: Work started successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/StartWorkResponse'
 *       404:
 *         description: Job not found or worker not assigned
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       409:
 *         description: Work has already been started
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       422:
 *         description: Job is not in active status
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/:id/start-work', authenticateToken, validate(startWorkSchema), jobsController.startWork);

/**
 * @openapi
 * /api/jobs/{id}/update-progress:
 *   post:
 *     summary: Update job progress
 *     description: Updates the progress of an ongoing job with description and photos. Only accessible by the assigned worker.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Job ID to update progress for
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - progressDescription
 *             properties:
 *               progressDescription:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Description of the progress made
 *                 example: "Completed foundation work, starting on walls"
 *               progressPhotos:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uri
 *                 description: URLs of progress photos
 *               estimatedCompletion:
 *                 type: string
 *                 format: date-time
 *                 description: Estimated completion time
 *               metadata:
 *                 type: object
 *                 description: Additional metadata
 *     responses:
 *       200:
 *         description: Progress updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UpdateProgressResponse'
 *       404:
 *         description: Job not found or worker not assigned
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       422:
 *         description: Work has not been started or job not in active status
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/:id/update-progress', authenticateToken, validate(updateProgressSchema), jobsController.updateProgress);

/**
 * @openapi
 * /api/jobs/{id}/complete:
 *   post:
 *     summary: Complete job
 *     description: Marks the job as completed by the assigned worker. Only accessible by the assigned worker.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Job ID to complete
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               completionNotes:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Notes about the job completion
 *                 example: "Job completed successfully, all requirements met"
 *               finalPhotos:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uri
 *                 description: URLs of final completion photos
 *               hoursWorked:
 *                 type: number
 *                 minimum: 0.1
 *                 description: Total hours worked on the job
 *               metadata:
 *                 type: object
 *                 description: Additional metadata
 *     responses:
 *       200:
 *         description: Job completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CompleteJobResponse'
 *       404:
 *         description: Job not found or worker not assigned
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       409:
 *         description: Job has already been completed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       422:
 *         description: Work has not been started or job not in active status
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/:id/complete', authenticateToken, validate(completeJobSchema), jobsController.completeJob);

/**
 * @openapi
 * /api/jobs/{id}/assign-worker:
 *   post:
 *     summary: Assign worker to job
 *     description: Assigns a worker to a job and updates the job status. Only accessible by the job poster. Supports assignment from applications, invites, or direct assignment. Prevents over-assignment and provides idempotent behavior.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Job ID
 *         example: "550e8400-e29b-41d4-a716-************"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - workerId
 *             properties:
 *               workerId:
 *                 type: string
 *                 format: uuid
 *                 description: Worker ID to assign to the job
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               source:
 *                 type: string
 *                 enum: [application, invite, direct]
 *                 description: Source of the assignment
 *                 example: "application"
 *               applicationId:
 *                 type: string
 *                 format: uuid
 *                 description: Application ID if assigning from application
 *                 example: "app-123e4567-e89b-12d3-a456-************"
 *               inviteId:
 *                 type: string
 *                 format: uuid
 *                 description: Invite ID if assigning from invite
 *                 example: "inv-123e4567-e89b-12d3-a456-************"
 *               message:
 *                 type: string
 *                 maxLength: 500
 *                 description: Optional message to the worker
 *                 example: "Looking forward to working with you on this project!"
 *     responses:
 *       200:
 *         description: Worker assigned successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Worker assigned successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     job:
 *                       $ref: '#/components/schemas/JobStatusDetails'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       404:
 *         description: Job, worker, application, or invite not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       409:
 *         description: Worker already assigned to job
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       422:
 *         description: Invalid job status, worker not available, or invalid application/invite status
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/:id/assign-worker', authenticateToken, validate(assignWorkerSchema), jobsController.assignWorker);

export { router as jobRoutes };
