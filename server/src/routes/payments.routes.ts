/**
 * Payments Routes
 * Defines routes for payments, earnings, and exports
 */

import { Router } from 'express';
import rateLimit from 'express-rate-limit';
import { PaymentsController } from '@/controllers/payments.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { validate, commonSchemas } from '@/middleware/validation.middleware';
import Joi from 'joi';

const router: Router = Router();
const paymentsController = new PaymentsController();

// Rate limiting for payment processing
const paymentProcessingLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each user to 5 payment processing requests per windowMs
  message: {
    success: false,
    message: 'Too many payment processing attempts, please try again later',
    error: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for export requests
const exportLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // Limit each user to 10 export requests per hour
  message: {
    success: false,
    message: 'Too many export requests, please try again later',
    error: 'EXPORT_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation schemas
const getPaymentHistorySchema = {
  query: Joi.object({
    status: Joi.string().valid('pending', 'processing', 'completed', 'failed', 'disputed', 'refunded').optional(),
    method: Joi.string().max(50).optional(),
    startDate: Joi.string().isoDate().optional(),
    endDate: Joi.string().isoDate().optional(),
    page: commonSchemas.pagination.page,
    limit: commonSchemas.pagination.limit
  })
};

const getEarningsSchema = {
  query: Joi.object({
    period: Joi.string().valid('week', 'month', 'quarter', 'year', 'all').optional(),
    startDate: Joi.string().isoDate().optional(),
    endDate: Joi.string().isoDate().optional()
  })
};

const exportPaymentsSchema = {
  query: Joi.object({
    format: Joi.string().valid('csv', 'xlsx', 'pdf').optional(),
    status: Joi.string().valid('pending', 'processing', 'completed', 'failed', 'disputed', 'refunded').optional(),
    startDate: Joi.string().isoDate().optional(),
    endDate: Joi.string().isoDate().optional()
  })
};

const processPaymentSchema = {
  body: Joi.object({
    jobId: commonSchemas.uuid.required(),
    amount: Joi.number().positive().required(),
    method: Joi.string().required().max(50),
    description: Joi.string().max(500).optional(),
    metadata: Joi.object().optional()
  })
};

/**
 * @openapi
 * /api/payments/history:
 *   get:
 *     summary: Get payment history
 *     description: Retrieves payment history for the authenticated user with filtering and pagination. Shows both payments made and received.
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, processing, completed, failed, disputed, refunded]
 *         description: Filter payments by status
 *       - in: query
 *         name: method
 *         schema:
 *           type: string
 *           maxLength: 50
 *         description: Filter payments by method (e.g., 'cash', 'upi')
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter payments from this date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter payments until this date
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of payments per page
 *     responses:
 *       200:
 *         description: Payment history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaymentHistoryResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/history', authenticateToken, validate(getPaymentHistorySchema), paymentsController.getPaymentHistory);

/**
 * @openapi
 * /api/payments/earnings:
 *   get:
 *     summary: Get earnings data
 *     description: Retrieves earnings statistics and breakdown for workers. Shows total earnings, job counts, and category breakdowns.
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year, all]
 *           default: month
 *         description: Time period for earnings calculation
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Custom start date (overrides period)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Custom end date (overrides period)
 *     responses:
 *       200:
 *         description: Earnings data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/EarningsResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/earnings', authenticateToken, validate(getEarningsSchema), paymentsController.getEarnings);

/**
 * @openapi
 * /api/payments/export:
 *   get:
 *     summary: Export payment data
 *     description: Generates and returns a signed URL for downloading payment data in various formats. Rate limited to 10 requests per hour.
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [csv, xlsx, pdf]
 *           default: csv
 *         description: Export format
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, processing, completed, failed, disputed, refunded]
 *         description: Filter payments by status
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter payments from this date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter payments until this date
 *     responses:
 *       200:
 *         description: Export URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Export generated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     exportUrl:
 *                       type: string
 *                       format: uri
 *                       example: "/api/payments/download/123e4567-e89b-12d3-a456-426614174000.csv"
 *                     expiresAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-16T10:30:00Z"
 *       429:
 *         description: Rate limit exceeded
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/export', authenticateToken, exportLimit, validate(exportPaymentsSchema), paymentsController.exportPayments);

/**
 * @openapi
 * /api/payments/process:
 *   post:
 *     summary: Process payment (Future Implementation)
 *     description: Processes a payment for a job. Currently returns not implemented. Rate limited to 5 requests per 15 minutes.
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - jobId
 *               - amount
 *               - method
 *             properties:
 *               jobId:
 *                 type: string
 *                 format: uuid
 *                 description: Job ID for the payment
 *                 example: "550e8400-e29b-41d4-a716-************"
 *               amount:
 *                 type: number
 *                 minimum: 0.01
 *                 description: Payment amount
 *                 example: 1500.00
 *               method:
 *                 type: string
 *                 maxLength: 50
 *                 description: Payment method
 *                 example: "upi"
 *               description:
 *                 type: string
 *                 maxLength: 500
 *                 description: Payment description
 *                 example: "Payment for plumbing work"
 *               metadata:
 *                 type: object
 *                 description: Additional payment metadata
 *     responses:
 *       501:
 *         description: Not implemented yet
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       429:
 *         description: Rate limit exceeded
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/process', authenticateToken, paymentProcessingLimit, validate(processPaymentSchema), paymentsController.processPayment);

export { router as paymentRoutes };
