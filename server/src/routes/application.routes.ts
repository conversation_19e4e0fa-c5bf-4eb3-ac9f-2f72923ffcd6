/**
 * Application Routes
 * Routes for job application operations
 */

import { Router } from 'express';
import { ApplicationController } from '@/controllers/application.controller';
import { validate, commonSchemas } from '@/middleware/validation.middleware';
import { authenticateToken } from '@/middleware/auth.middleware';
import Joi from 'joi';

const router: Router = Router();
const applicationController = new ApplicationController();

// Validation schemas
const applyToJobSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  }),
  body: Joi.object({
    personaId: commonSchemas.uuid.optional(),
    coverLetter: Joi.string().max(1000).optional(),
    proposedRate: Joi.number().positive().optional(),
    proposedRateType: Joi.string().valid('hourly', 'daily', 'fixed').optional()
  })
};

const getMyApplicationsSchema = {
  query: Joi.object({
    status: Joi.string().valid('pending', 'accepted', 'rejected', 'withdrawn').optional(),
    page: commonSchemas.pagination.page,
    limit: commonSchemas.pagination.limit
  })
};

const withdrawApplicationSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  })
};

/**
 * @openapi
 * /api/applications/my-applications:
 *   get:
 *     summary: Get my job applications
 *     description: Retrieves all job applications for the authenticated user with pagination and filtering options.
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, accepted, rejected, withdrawn]
 *         description: Filter applications by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of applications per page
 *     responses:
 *       200:
 *         description: Applications retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MyApplicationsResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/my-applications', authenticateToken, validate(getMyApplicationsSchema), applicationController.getMyApplications);

/**
 * @openapi
 * /api/applications/{id}/withdraw:
 *   put:
 *     summary: Withdraw job application
 *     description: Withdraws a job application. Only pending or rejected applications can be withdrawn.
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Application ID to withdraw
 *     responses:
 *       200:
 *         description: Application withdrawn successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/WithdrawApplicationResponse'
 *       400:
 *         description: Cannot withdraw application (already accepted/withdrawn)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.put('/:id/withdraw', authenticateToken, validate(withdrawApplicationSchema), applicationController.withdrawApplication);

export { router as applicationRoutes };
