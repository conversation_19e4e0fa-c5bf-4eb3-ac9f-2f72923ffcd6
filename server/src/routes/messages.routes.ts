/**
 * Messages Routes
 * Defines routes for conversations and messages
 */

import { Router } from 'express';
import { MessagesController } from '@/controllers/messages.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { validate, commonSchemas } from '@/middleware/validation.middleware';
import Joi from 'joi';

const router: Router = Router();
const messagesController = new MessagesController();

// Validation schemas
const getConversationsSchema = {
  query: Joi.object({
    page: commonSchemas.pagination.page,
    limit: commonSchemas.pagination.limit
  })
};

const getConversationSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  })
};

const getMessagesSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  }),
  query: Joi.object({
    page: commonSchemas.pagination.page,
    limit: commonSchemas.pagination.limit,
    before: Joi.string().isoDate().optional()
  })
};

const sendMessageSchema = {
  params: Joi.object({
    id: commonSchemas.uuid
  }),
  body: Joi.object({
    text: Joi.string().required().max(500),
    messageType: Joi.string().valid('text', 'image', 'document').default('text'),
    attachments: Joi.array().items(
      Joi.object({
        url: Joi.string().uri().required(),
        fileName: Joi.string().optional(),
        fileSize: Joi.number().positive().optional(),
        mimeType: Joi.string().optional()
      })
    ).optional()
  })
};

const markMessageReadSchema = {
  params: Joi.object({
    messageId: commonSchemas.uuid
  })
};

/**
 * @openapi
 * /api/messages/conversations:
 *   get:
 *     summary: Get user's conversations
 *     description: Retrieves all conversations for the authenticated user with pagination.
 *     tags: [Messages]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of conversations per page
 *     responses:
 *       200:
 *         description: Conversations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ConversationsResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/conversations', authenticateToken, validate(getConversationsSchema), messagesController.getConversations);

/**
 * @openapi
 * /api/messages/conversations/{id}:
 *   get:
 *     summary: Get conversation details
 *     description: Retrieves details of a specific conversation including job information.
 *     tags: [Messages]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Conversation ID
 *     responses:
 *       200:
 *         description: Conversation details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ConversationResponse'
 *       404:
 *         description: Conversation not found or access denied
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/conversations/:id', authenticateToken, validate(getConversationSchema), messagesController.getConversation);

/**
 * @openapi
 * /api/messages/conversations/{id}/messages:
 *   get:
 *     summary: Get messages in a conversation
 *     description: Retrieves messages in a specific conversation with pagination.
 *     tags: [Messages]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Conversation ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Number of messages per page
 *       - in: query
 *         name: before
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Get messages before this timestamp
 *     responses:
 *       200:
 *         description: Messages retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MessagesResponse'
 *       404:
 *         description: Conversation not found or access denied
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/conversations/:id/messages', authenticateToken, validate(getMessagesSchema), messagesController.getMessages);

/**
 * @openapi
 * /api/messages/conversations/{id}/messages:
 *   post:
 *     summary: Send a message
 *     description: Sends a new message in a conversation. Message text is limited to 500 characters.
 *     tags: [Messages]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Conversation ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - text
 *             properties:
 *               text:
 *                 type: string
 *                 maxLength: 500
 *                 description: Message text content
 *                 example: "Hello, I'm interested in this job opportunity."
 *               messageType:
 *                 type: string
 *                 enum: [text, image, document]
 *                 default: text
 *                 description: Type of message
 *               attachments:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - url
 *                   properties:
 *                     url:
 *                       type: string
 *                       format: uri
 *                       description: URL of the attachment
 *                     fileName:
 *                       type: string
 *                       description: Original file name
 *                     fileSize:
 *                       type: integer
 *                       minimum: 1
 *                       description: File size in bytes
 *                     mimeType:
 *                       type: string
 *                       description: MIME type of the file
 *     responses:
 *       200:
 *         description: Message sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MessageResponse'
 *       404:
 *         description: Conversation not found or access denied
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       422:
 *         description: Message text exceeds 500 character limit
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/conversations/:id/messages', authenticateToken, validate(sendMessageSchema), messagesController.sendMessage);

/**
 * @openapi
 * /api/messages/{messageId}/read:
 *   put:
 *     summary: Mark message as read
 *     description: Marks a specific message as read by the authenticated user.
 *     tags: [Messages]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: messageId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Message ID to mark as read
 *     responses:
 *       200:
 *         description: Message marked as read successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       404:
 *         description: Message not found or access denied
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.put('/:messageId/read', authenticateToken, validate(markMessageReadSchema), messagesController.markMessageAsRead);

export { router as messageRoutes };
