/**
 * Upload Routes
 * Defines routes for file uploads
 */

import { Router } from 'express';
import multer from 'multer';
import { UploadController } from '@/controllers/upload.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { validate, commonSchemas } from '@/middleware/validation.middleware';
import Joi from 'joi';

const router: Router = Router();
const uploadController = new UploadController();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 10 // Maximum 10 files
  },
  fileFilter: (req, file, cb) => {
    // Allow only image files
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, and WebP images are allowed.'));
    }
  }
});

// Validation schemas
const uploadImagesSchema = {
  body: Joi.object({
    category: Joi.string().valid('profile', 'job', 'work_progress', 'portfolio', 'verification').required(),
    jobId: commonSchemas.uuid.optional(),
    captions: Joi.string().optional(), // JSON string array
    metadata: Joi.string().optional()  // JSON string object
  })
};

/**
 * @openapi
 * /api/upload/images:
 *   post:
 *     summary: Upload images
 *     description: Upload one or more images with processing and thumbnail generation. Supports various categories including job photos, work progress, and profile images.
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - category
 *               - images
 *             properties:
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Image files to upload (max 10 files, 10MB each)
 *               category:
 *                 type: string
 *                 enum: [profile, job, work_progress, portfolio, verification]
 *                 description: Category of the uploaded images
 *                 example: "work_progress"
 *               jobId:
 *                 type: string
 *                 format: uuid
 *                 description: Job ID (required for job and work_progress categories)
 *                 example: "550e8400-e29b-41d4-a716-************"
 *               captions:
 *                 type: string
 *                 description: JSON array of captions for each image
 *                 example: '["Before work", "After completion"]'
 *               metadata:
 *                 type: string
 *                 description: JSON object with additional metadata
 *                 example: '{"location": "Mumbai", "timestamp": "2024-01-15T10:30:00Z"}'
 *     responses:
 *       200:
 *         description: Images uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Successfully uploaded 2 image(s)"
 *                 data:
 *                   type: object
 *                   properties:
 *                     images:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                             example: "123e4567-e89b-12d3-a456-************"
 *                           originalName:
 *                             type: string
 *                             example: "work_photo.jpg"
 *                           url:
 *                             type: string
 *                             format: uri
 *                             example: "/uploads/user123_work_progress_1642234200000_abc123.jpeg"
 *                           thumbnailUrl:
 *                             type: string
 *                             format: uri
 *                             example: "/uploads/thumbnails/thumb_user123_work_progress_1642234200000_abc123.jpeg"
 *                           size:
 *                             type: integer
 *                             example: 1048576
 *                           mimeType:
 *                             type: string
 *                             example: "image/jpeg"
 *                           category:
 *                             type: string
 *                             example: "work_progress"
 *                           jobId:
 *                             type: string
 *                             format: uuid
 *                             example: "550e8400-e29b-41d4-a716-************"
 *                           uploadedAt:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-01-15T10:30:00Z"
 *       400:
 *         description: Validation error or invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Access denied to job
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       413:
 *         description: File too large
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       422:
 *         description: Image processing failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/images', 
  authenticateToken, 
  upload.array('images', 10), 
  validate(uploadImagesSchema), 
  uploadController.uploadImages
);

export { router as uploadRoutes };
