/**
 * Reviews Routes
 * Defines routes for reviews and ratings
 */

import { Router } from 'express';
import { ReviewsController } from '@/controllers/reviews.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { validate, commonSchemas } from '@/middleware/validation.middleware';
import Jo<PERSON> from 'joi';

const router: Router = Router();
const reviewsController = new ReviewsController();

// Validation schemas
const getReceivedReviewsSchema = {
  query: Joi.object({
    reviewType: Joi.string().valid('worker_to_poster', 'poster_to_worker').optional(),
    isPublic: Joi.boolean().optional(),
    page: commonSchemas.pagination.page,
    limit: commonSchemas.pagination.limit
  })
};

const postReviewSchema = {
  body: Joi.object({
    jobId: commonSchemas.uuid.required(),
    revieweeId: commonSchemas.uuid.required(),
    rating: Joi.number().integer().min(1).max(5).required(),
    comment: Joi.string().max(1000).optional(),
    communicationRating: Joi.number().integer().min(1).max(5).optional(),
    workQualityRating: Joi.number().integer().min(1).max(5).optional(),
    timelinessRating: Joi.number().integer().min(1).max(5).optional(),
    professionalismRating: Joi.number().integer().min(1).max(5).optional(),
    isPublic: Joi.boolean().optional()
  })
};

/**
 * @openapi
 * /api/reviews/received:
 *   get:
 *     summary: Get reviews received by user
 *     description: Retrieves all reviews received by the authenticated user with filtering and pagination. Includes summary statistics and rating distribution.
 *     tags: [Reviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: reviewType
 *         schema:
 *           type: string
 *           enum: [worker_to_poster, poster_to_worker]
 *         description: Filter reviews by type
 *       - in: query
 *         name: isPublic
 *         schema:
 *           type: boolean
 *         description: Filter reviews by public visibility
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of reviews per page
 *     responses:
 *       200:
 *         description: Reviews retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Reviews retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     reviews:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ReviewItem'
 *                     summary:
 *                       $ref: '#/components/schemas/ReviewsSummary'
 *                     pagination:
 *                       $ref: '#/components/schemas/PaginationResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/received', authenticateToken, validate(getReceivedReviewsSchema), reviewsController.getReceivedReviews);

/**
 * @openapi
 * /api/reviews:
 *   post:
 *     summary: Post a new review
 *     description: Creates a new review for a completed job. Only job participants can review each other, and only one review per pair per job is allowed.
 *     tags: [Reviews]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - jobId
 *               - revieweeId
 *               - rating
 *             properties:
 *               jobId:
 *                 type: string
 *                 format: uuid
 *                 description: Job ID for the review
 *                 example: "550e8400-e29b-41d4-a716-************"
 *               revieweeId:
 *                 type: string
 *                 format: uuid
 *                 description: User ID being reviewed
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               rating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *                 description: Overall rating (1-5 stars)
 *                 example: 5
 *               comment:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Review comment
 *                 example: "Excellent work quality and professional communication"
 *               communicationRating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *                 description: Communication rating (1-5 stars)
 *                 example: 5
 *               workQualityRating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *                 description: Work quality rating (1-5 stars)
 *                 example: 5
 *               timelinessRating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *                 description: Timeliness rating (1-5 stars)
 *                 example: 4
 *               professionalismRating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *                 description: Professionalism rating (1-5 stars)
 *                 example: 5
 *               isPublic:
 *                 type: boolean
 *                 default: true
 *                 description: Whether the review is publicly visible
 *                 example: true
 *     responses:
 *       200:
 *         description: Review posted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Review posted successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     review:
 *                       $ref: '#/components/schemas/ReviewItem'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       403:
 *         description: Not a job participant
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       409:
 *         description: Review already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       422:
 *         description: Job not completed or invalid reviewee
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/', authenticateToken, validate(postReviewSchema), reviewsController.postReview);

export { router as reviewRoutes };
