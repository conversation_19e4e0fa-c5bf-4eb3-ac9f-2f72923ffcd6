/**
 * Support Routes
 * Defines routes for help and support operations
 */

import { Router } from 'express';
import { SupportController } from '@/controllers/support.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { validate, commonSchemas } from '@/middleware/validation.middleware';
import Joi from 'joi';

const router: Router = Router();
const supportController = new SupportController();

// Validation schemas
const createSupportTicketSchema = {
  body: Joi.object({
    subject: Joi.string().min(5).max(200).required(),
    description: Joi.string().min(10).max(2000).required(),
    category: Joi.string().valid('technical', 'billing', 'account', 'general', 'bug_report', 'feature_request').required(),
    priority: Joi.string().valid('low', 'medium', 'high', 'urgent').required(),
    userEmail: Joi.string().email().optional(),
    userPhone: Joi.string().pattern(/^\+91[6-9]\d{9}$/).optional(),
    attachments: Joi.array().items(Joi.string().uri()).max(5).optional()
  })
};

/**
 * @openapi
 * /api/support/faq:
 *   get:
 *     summary: Get FAQs
 *     description: Retrieves frequently asked questions grouped by category. No authentication required.
 *     tags: [Support]
 *     responses:
 *       200:
 *         description: FAQs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "FAQs retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     categories:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           category:
 *                             type: string
 *                             example: "getting_started"
 *                           displayName:
 *                             type: string
 *                             example: "Getting Started"
 *                           order:
 *                             type: integer
 *                             example: 1
 *                           faqs:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                   example: "faq-001"
 *                                 question:
 *                                   type: string
 *                                   example: "How do I create an account?"
 *                                 answer:
 *                                   type: string
 *                                   example: "To create an account, download the app..."
 *                                 category:
 *                                   type: string
 *                                   example: "getting_started"
 *                                 order:
 *                                   type: integer
 *                                   example: 1
 *                                 isActive:
 *                                   type: boolean
 *                                   example: true
 *                                 createdAt:
 *                                   type: string
 *                                   format: date-time
 *                                   example: "2024-01-01T00:00:00Z"
 *                                 updatedAt:
 *                                   type: string
 *                                   format: date-time
 *                                   example: "2024-01-01T00:00:00Z"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/faq', supportController.getFAQs);

/**
 * @openapi
 * /api/support/tickets:
 *   post:
 *     summary: Create support ticket
 *     description: Creates a new support ticket with SLA deadline calculation. Can be used by authenticated users or anonymously.
 *     tags: [Support]
 *     security:
 *       - bearerAuth: []
 *       - {}
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - subject
 *               - description
 *               - category
 *               - priority
 *             properties:
 *               subject:
 *                 type: string
 *                 minLength: 5
 *                 maxLength: 200
 *                 description: Ticket subject
 *                 example: "Unable to apply for jobs"
 *               description:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 2000
 *                 description: Detailed description of the issue
 *                 example: "I'm getting an error when trying to apply for jobs. The app shows 'Network error' message."
 *               category:
 *                 type: string
 *                 enum: [technical, billing, account, general, bug_report, feature_request]
 *                 description: Ticket category
 *                 example: "technical"
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high, urgent]
 *                 description: Ticket priority (affects SLA deadline)
 *                 example: "medium"
 *               userEmail:
 *                 type: string
 *                 format: email
 *                 description: Contact email (optional if authenticated)
 *                 example: "<EMAIL>"
 *               userPhone:
 *                 type: string
 *                 pattern: '^\+91[6-9]\d{9}$'
 *                 description: Contact phone number
 *                 example: "+************"
 *               attachments:
 *                 type: array
 *                 maxItems: 5
 *                 items:
 *                   type: string
 *                   format: uri
 *                 description: Attachment URLs (max 5)
 *                 example: ["https://example.com/screenshot.png"]
 *     responses:
 *       200:
 *         description: Support ticket created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Support ticket created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     ticket:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                           example: "ticket-1234567890-abc123def"
 *                         ticketNumber:
 *                           type: string
 *                           example: "OZG-567890-ABCD"
 *                         userId:
 *                           type: string
 *                           example: "user-123e4567-e89b-12d3-a456-426614174000"
 *                         subject:
 *                           type: string
 *                           example: "Unable to apply for jobs"
 *                         description:
 *                           type: string
 *                           example: "I'm getting an error when trying to apply for jobs..."
 *                         category:
 *                           type: string
 *                           example: "technical"
 *                         priority:
 *                           type: string
 *                           example: "medium"
 *                         status:
 *                           type: string
 *                           example: "open"
 *                         slaDeadline:
 *                           type: string
 *                           format: date-time
 *                           example: "2024-01-02T00:00:00Z"
 *                         createdAt:
 *                           type: string
 *                           format: date-time
 *                           example: "2024-01-01T00:00:00Z"
 *                         updatedAt:
 *                           type: string
 *                           format: date-time
 *                           example: "2024-01-01T00:00:00Z"
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/tickets', validate(createSupportTicketSchema), supportController.createSupportTicket);

/**
 * @openapi
 * /api/support/contact-info:
 *   get:
 *     summary: Get contact information
 *     description: Retrieves company contact information including phone, email, address, and business hours. No authentication required.
 *     tags: [Support]
 *     responses:
 *       200:
 *         description: Contact information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Contact information retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     contactInfo:
 *                       type: object
 *                       properties:
 *                         email:
 *                           type: string
 *                           format: email
 *                           example: "<EMAIL>"
 *                         phone:
 *                           type: string
 *                           example: "+91-9876543210"
 *                         whatsapp:
 *                           type: string
 *                           example: "+91-9876543210"
 *                         address:
 *                           type: object
 *                           properties:
 *                             street:
 *                               type: string
 *                               example: "123 Tech Park"
 *                             city:
 *                               type: string
 *                               example: "Bangalore"
 *                             state:
 *                               type: string
 *                               example: "Karnataka"
 *                             country:
 *                               type: string
 *                               example: "India"
 *                             postalCode:
 *                               type: string
 *                               example: "560001"
 *                         businessHours:
 *                           type: object
 *                           properties:
 *                             monday:
 *                               type: string
 *                               example: "9:00 AM - 6:00 PM"
 *                             tuesday:
 *                               type: string
 *                               example: "9:00 AM - 6:00 PM"
 *                             wednesday:
 *                               type: string
 *                               example: "9:00 AM - 6:00 PM"
 *                             thursday:
 *                               type: string
 *                               example: "9:00 AM - 6:00 PM"
 *                             friday:
 *                               type: string
 *                               example: "9:00 AM - 6:00 PM"
 *                             saturday:
 *                               type: string
 *                               example: "10:00 AM - 4:00 PM"
 *                             sunday:
 *                               type: string
 *                               example: "Closed"
 *                         socialMedia:
 *                           type: object
 *                           properties:
 *                             facebook:
 *                               type: string
 *                               format: uri
 *                               example: "https://facebook.com/ozgaar"
 *                             twitter:
 *                               type: string
 *                               format: uri
 *                               example: "https://twitter.com/ozgaar"
 *                             instagram:
 *                               type: string
 *                               format: uri
 *                               example: "https://instagram.com/ozgaar"
 *                             linkedin:
 *                               type: string
 *                               format: uri
 *                               example: "https://linkedin.com/company/ozgaar"
 *                         emergencyContact:
 *                           type: object
 *                           properties:
 *                             phone:
 *                               type: string
 *                               example: "+91-9876543211"
 *                             hours:
 *                               type: string
 *                               example: "24/7 for urgent issues"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/contact-info', supportController.getContactInfo);

export { router as supportRoutes };
