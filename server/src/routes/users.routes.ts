/**
 * Users Routes
 * Routes for user profile and settings management
 * 
 * @openapi
 * tags:
 *   name: Users
 *   description: User profile and settings management endpoints
 */

import { Router } from 'express';
import { UsersController } from '@/controllers/users.controller';
import { ApplicationController } from '@/controllers/application.controller';
import { DiscoveryController } from '@/controllers/discovery.controller';
import { validate, commonSchemas } from '@/middleware/validation.middleware';
import { authenticateToken } from '@/middleware/auth.middleware';
import Joi from 'joi';

const router: Router = Router();
const usersController = new UsersController();
const applicationController = new ApplicationController();
const discoveryController = new DiscoveryController();

// Validation schemas
const createWorkerProfileSchema = {
  body: Joi.object({
    primarySkill: Joi.string().required(),
    skills: Joi.array().items(Joi.string()).required(),
    experience: Joi.string().valid('0-1', '2-5', '5-10', '10+').required(),
    hourlyRate: Joi.number().positive().optional(),
    dailyRate: Joi.number().positive().optional(),
    about: Joi.string().max(500).optional(),
    phoneVisible: Joi.boolean().default(false),
    portfolioPhotos: Joi.array().items(Joi.string().uri()).optional()
  })
};

const createPosterProfileSchema = {
  body: Joi.object({
    companyName: Joi.string().max(100).optional(),
    about: Joi.string().max(500).optional(),
    industry: Joi.string().max(100).optional(),
    companySize: Joi.string().valid('individual', 'small', 'medium', 'large', 'enterprise').default('individual'),
    website: Joi.string().uri().optional(),
    address: Joi.string().max(200).optional(),
    city: Joi.string().max(50).optional(),
    state: Joi.string().max(50).optional(),
    pincode: Joi.string().pattern(/^\d{6}$/).optional(),
    gstNumber: Joi.string().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).optional(),
    panNumber: Joi.string().pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/).optional(),
    businessVerified: Joi.boolean().default(false),
    businessDocuments: Joi.array().items(Joi.string().uri()).optional()
  })
};

const updateProfileSchema = {
  body: Joi.object({
    fullName: Joi.string().min(2).max(50).optional(),
    email: Joi.string().email().optional(),
    location: Joi.string().max(100).optional(),
    avatarUrl: Joi.string().uri().optional()
  })
};

const updateSettingsSchema = {
  body: Joi.object({
    notifications: Joi.object().optional(),
    privacy: Joi.object().optional(),
    language: Joi.string().valid('english', 'hindi').optional(),
    preferences: Joi.object().optional()
  })
};

const selectModeSchema = {
  body: Joi.object({
    mode: Joi.string().valid('worker', 'poster').required(),
    language: Joi.string().valid('english', 'hindi').default('english'),
    preferences: Joi.object({
      notifications: Joi.object().optional(),
      privacy: Joi.object().optional()
    }).optional()
  })
};

const initializeSettingsSchema = {
  body: Joi.object({
    language: Joi.string().valid('english', 'hindi').required(),
    notifications: Joi.object().optional(),
    privacy: Joi.object().optional(),
    preferences: Joi.object().optional()
  })
};

const createWorkerPersonaSchema = {
  body: Joi.object({
    skill: Joi.string().required(),
    experience: Joi.string().valid('0-1', '2-5', '5-10', '10+').optional(),
    hourlyRate: Joi.number().positive().optional(),
    dailyRate: Joi.number().positive().optional(),
    isActive: Joi.boolean().default(true)
  })
};

const updatePosterProfileSchema = {
  body: Joi.object({
    companyName: Joi.string().max(100).optional(),
    about: Joi.string().max(500).optional(),
    industry: Joi.string().max(100).optional(),
    companySize: Joi.string().valid('individual', 'small', 'medium', 'large', 'enterprise').optional(),
    website: Joi.string().uri().optional(),
    address: Joi.string().max(200).optional(),
    city: Joi.string().max(50).optional(),
    state: Joi.string().max(50).optional(),
    pincode: Joi.string().pattern(/^\d{6}$/).optional(),
    gstNumber: Joi.string().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).optional(),
    panNumber: Joi.string().pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/).optional()
  })
};

const searchWorkersSchema = {
  query: Joi.object({
    query: Joi.string().max(100).optional(),
    skills: Joi.array().items(Joi.string()).optional(),
    experience: Joi.string().valid('0-1', '2-5', '5-10', '10+').optional(),
    minHourlyRate: Joi.number().min(0).optional(),
    maxHourlyRate: Joi.number().min(0).optional(),
    minDailyRate: Joi.number().min(0).optional(),
    maxDailyRate: Joi.number().min(0).optional(),
    location: Joi.string().max(100).optional(),
    radius: Joi.number().min(1).max(200).optional(),
    minRating: Joi.number().min(0).max(5).optional(),
    availability: Joi.boolean().optional(),
    sortBy: Joi.string().valid('rating', 'distance', 'rate', 'experience', 'completedJobs').optional(),
    sortOrder: Joi.string().valid('asc', 'desc').optional(),
    page: commonSchemas.pagination.page,
    limit: commonSchemas.pagination.limit
  })
};

/**
 * @openapi
 * /api/users/worker-profile:
 *   post:
 *     summary: Create worker profile
 *     description: Creates a worker profile for the authenticated user. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateWorkerProfileRequest'
 *     responses:
 *       201:
 *         description: Worker profile created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CreateWorkerProfileResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/worker-profile', authenticateToken, validate(createWorkerProfileSchema), usersController.createWorkerProfile);

/**
 * @openapi
 * /api/users/poster-profile:
 *   post:
 *     summary: Create poster profile
 *     description: Creates a poster profile for the authenticated user. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreatePosterProfileRequest'
 *     responses:
 *       201:
 *         description: Poster profile created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CreatePosterProfileResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/poster-profile', authenticateToken, validate(createPosterProfileSchema), usersController.createPosterProfile);

/**
 * @openapi
 * /api/users/profile:
 *   put:
 *     summary: Update user profile
 *     description: Updates the authenticated user's profile information. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateUserProfileRequest'
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UpdateUserProfileResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.put('/profile', authenticateToken, validate(updateProfileSchema), usersController.updateProfile);

/**
 * @openapi
 * /api/users/settings:
 *   get:
 *     summary: Get user settings
 *     description: Retrieves the authenticated user's settings. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Settings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/GetUserSettingsResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/settings', authenticateToken, usersController.getSettings);

/**
 * @openapi
 * /api/users/settings:
 *   put:
 *     summary: Update user settings
 *     description: Updates the authenticated user's settings. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateUserSettingsRequest'
 *     responses:
 *       200:
 *         description: Settings updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UpdateUserSettingsResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.put('/settings', authenticateToken, validate(updateSettingsSchema), usersController.updateSettings);

/**
 * @openapi
 * /api/users/upload-avatar:
 *   post:
 *     summary: Upload user avatar
 *     description: Uploads a new avatar for the authenticated user. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Avatar image file to upload
 *     responses:
 *       200:
 *         description: Avatar uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Avatar uploaded successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     avatarUrl:
 *                       type: string
 *                       example: "https://example.com/avatar.jpg"
 *       400:
 *         description: Validation error or file upload error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/upload-avatar', authenticateToken, usersController.uploadAvatar);

/**
 * @openapi
 * /api/users/select-mode:
 *   post:
 *     summary: Select user mode
 *     description: Selects the user's mode (worker or poster) and initializes settings. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - mode
 *             properties:
 *               mode:
 *                 type: string
 *                 enum: [worker, poster]
 *                 example: "worker"
 *               language:
 *                 type: string
 *                 enum: [english, hindi]
 *                 example: "english"
 *               preferences:
 *                 type: object
 *                 properties:
 *                   notifications:
 *                     type: object
 *                     example: { jobNotifications: true }
 *                   privacy:
 *                     type: object
 *                     example: { profileVisibility: "public" }
 *     responses:
 *       200:
 *         description: Mode selected successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Mode selected as worker successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "user_id_here"
 *                     mode:
 *                       type: string
 *                       example: "worker"
 *                     hasProfile:
 *                       type: boolean
 *                       example: false
 *                     settings:
 *                       type: object
 *                       properties:
 *                         language:
 *                           type: string
 *                           example: "english"
 *                         notifications:
 *                           type: object
 *                           example: { jobNotifications: true }
 *                         privacy:
 *                           type: object
 *                           example: { profileVisibility: "public" }
 *                         preferences:
 *                           type: object
 *                           example: { theme: "light" }
 *                     nextStep:
 *                       type: string
 *                       example: "create_profile"
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/select-mode', authenticateToken, validate(selectModeSchema), usersController.selectMode);

/**
 * @openapi
 * /api/users/mode-status:
 *   get:
 *     summary: Get user mode status
 *     description: Retrieves the authenticated user's mode status. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Mode status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Mode status retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                       example: "user_id_here"
 *                     selectedMode:
 *                       type: string
 *                       example: "worker"
 *                     hasWorkerProfile:
 *                       type: boolean
 *                       example: false
 *                     hasPosterProfile:
 *                       type: boolean
 *                       example: false
 *                     hasSettings:
 *                       type: boolean
 *                       example: true
 *                     isProfileComplete:
 *                       type: boolean
 *                       example: false
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/mode-status', authenticateToken, usersController.getModeStatus);

/**
 * @openapi
 * /api/users/initialize-settings:
 *   post:
 *     summary: Initialize user settings
 *     description: Initializes the authenticated user's settings with defaults. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - language
 *             properties:
 *               language:
 *                 type: string
 *                 enum: [english, hindi]
 *                 example: "english"
 *               notifications:
 *                 type: object
 *                 example: { jobNotifications: true }
 *               privacy:
 *                 type: object
 *                 example: { profileVisibility: "public" }
 *               preferences:
 *                 type: object
 *                 example: { theme: "light" }
 *     responses:
 *       201:
 *         description: Settings initialized successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Settings initialized successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                       example: "user_id_here"
 *                     language:
 *                       type: string
 *                       example: "english"
 *                     notifications:
 *                       type: object
 *                       example: { jobNotifications: true }
 *                     privacy:
 *                       type: object
 *                       example: { profileVisibility: "public" }
 *                     preferences:
 *                       type: object
 *                       example: { theme: "light" }
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-01-01T00:00:00.000Z"
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/initialize-settings', authenticateToken, validate(initializeSettingsSchema), usersController.initializeSettings);

/**
 * @openapi
 * /api/users/skills/personas:
 *   post:
 *     summary: Create worker persona
 *     description: Creates a worker persona for the authenticated user. Requires authentication and worker profile.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - skill
 *             properties:
 *               skill:
 *                 type: string
 *                 example: "Plumbing"
 *               experience:
 *                 type: string
 *                 enum: [0-1, 2-5, 5-10, 10+]
 *                 example: "2-5"
 *               hourlyRate:
 *                 type: integer
 *                 example: 500
 *               dailyRate:
 *                 type: integer
 *                 example: 4000
 *               isActive:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       201:
 *         description: Worker persona created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Worker persona created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "persona_id_here"
 *                     userId:
 *                       type: string
 *                       example: "user_id_here"
 *                     skill:
 *                       type: string
 *                       example: "Plumbing"
 *                     experience:
 *                       type: string
 *                       example: "2-5"
 *                     hourlyRate:
 *                       type: integer
 *                       example: 500
 *                     dailyRate:
 *                       type: integer
 *                       example: 4000
 *                     isActive:
 *                       type: boolean
 *                       example: true
 *                     completedJobs:
 *                       type: integer
 *                       example: 0
 *                     rating:
 *                       type: number
 *                       example: 0
 *                     reviewCount:
 *                       type: integer
 *                       example: 0
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-01-01T00:00:00.000Z"
 *       400:
 *         description: Validation error or worker profile required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/skills/personas', authenticateToken, validate(createWorkerPersonaSchema), usersController.createWorkerPersona);

/**
 * @openapi
 * /api/users/skills/personas:
 *   get:
 *     summary: Get worker personas
 *     description: Retrieves all worker personas for the authenticated user. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Worker personas retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Worker personas retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     personas:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             example: "persona_id_here"
 *                           userId:
 *                             type: string
 *                             example: "user_id_here"
 *                           skill:
 *                             type: string
 *                             example: "Plumbing"
 *                           experience:
 *                             type: string
 *                             example: "2-5"
 *                           hourlyRate:
 *                             type: integer
 *                             example: 500
 *                           dailyRate:
 *                             type: integer
 *                             example: 4000
 *                           isActive:
 *                             type: boolean
 *                             example: true
 *                           completedJobs:
 *                             type: integer
 *                             example: 0
 *                           rating:
 *                             type: number
 *                             example: 0
 *                           reviewCount:
 *                             type: integer
 *                             example: 0
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-01-01T00:00:00.000Z"
 *                           updatedAt:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-01-01T00:00:00.000Z"
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/skills/personas', authenticateToken, usersController.getWorkerPersonas);

/**
 * @openapi
 * /api/users/poster-profile:
 *   put:
 *     summary: Update poster profile
 *     description: Updates the authenticated user's poster profile. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdatePosterProfileRequest'
 *     responses:
 *       200:
 *         description: Poster profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UpdatePosterProfileResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.put('/poster-profile', authenticateToken, validate(updatePosterProfileSchema), usersController.updatePosterProfile);

/**
 * @openapi
 * /api/users/poster-profile:
 *   get:
 *     summary: Get poster profile
 *     description: Retrieves the authenticated user's poster profile. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Poster profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/GetPosterProfileResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Poster profile not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/poster-profile', authenticateToken, usersController.getPosterProfile);

/**
 * @openapi
 * /api/users/poster-stats:
 *   get:
 *     summary: Get poster statistics
 *     description: Retrieves statistics for the authenticated poster. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Poster statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/GetPosterStatsResponse'
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/poster-stats', authenticateToken, usersController.getPosterStats);

/**
 * @openapi
 * /api/users/dashboard-stats:
 *   get:
 *     summary: Get worker dashboard statistics
 *     description: Retrieves comprehensive dashboard statistics for the authenticated worker. Requires authentication.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Dashboard statistics retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     stats:
 *                       type: object
 *                       properties:
 *                         applications:
 *                           type: object
 *                           properties:
 *                             total:
 *                               type: integer
 *                               example: 15
 *                             pending:
 *                               type: integer
 *                               example: 5
 *                             accepted:
 *                               type: integer
 *                               example: 8
 *                             rejected:
 *                               type: integer
 *                               example: 2
 *                             withdrawn:
 *                               type: integer
 *                               example: 0
 *                         bookmarks:
 *                           type: object
 *                           properties:
 *                             total:
 *                               type: integer
 *                               example: 12
 *                             recentCount:
 *                               type: integer
 *                               example: 3
 *                         profile:
 *                           type: object
 *                           properties:
 *                             completionPercentage:
 *                               type: integer
 *                               example: 85
 *                             missingFields:
 *                               type: array
 *                               items:
 *                                 type: string
 *                               example: ["about"]
 *                             hasWorkerProfile:
 *                               type: boolean
 *                               example: true
 *                             hasPersonas:
 *                               type: boolean
 *                               example: true
 *                             personaCount:
 *                               type: integer
 *                               example: 2
 *                         activity:
 *                           type: object
 *                           properties:
 *                             lastApplicationDate:
 *                               type: string
 *                               format: date-time
 *                               example: "2023-01-01T00:00:00.000Z"
 *                             lastLoginDate:
 *                               type: string
 *                               format: date-time
 *                               example: "2023-01-01T00:00:00.000Z"
 *                             jobsViewedToday:
 *                               type: integer
 *                               example: 5
 *                             jobsViewedThisWeek:
 *                               type: integer
 *                               example: 25
 *                         earnings:
 *                           type: object
 *                           properties:
 *                             totalEarnings:
 *                               type: integer
 *                               example: 15000
 *                             completedJobs:
 *                               type: integer
 *                               example: 8
 *                             averageRating:
 *                               type: number
 *                               example: 4.5
 *                             reviewCount:
 *                               type: integer
 *                               example: 6
 *                         recommendations:
 *                           type: object
 *                           properties:
 *                             newJobsCount:
 *                               type: integer
 *                               example: 3
 *                             urgentJobsCount:
 *                               type: integer
 *                               example: 1
 *                             nearbyJobsCount:
 *                               type: integer
 *                               example: 7
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/dashboard-stats', authenticateToken, usersController.getDashboardStats);

/**
 * @openapi
 * /api/users/application-limits:
 *   get:
 *     summary: Get application limits
 *     description: Retrieves daily application limits and current usage for the authenticated user.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Application limits retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Application limits retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     limits:
 *                       type: object
 *                       properties:
 *                         dailyLimit:
 *                           type: integer
 *                           example: 10
 *                         applicationsToday:
 *                           type: integer
 *                           example: 3
 *                         remainingToday:
 *                           type: integer
 *                           example: 7
 *                         nextResetAt:
 *                           type: string
 *                           format: date-time
 *                           example: "2023-01-02T00:00:00.000Z"
 *                         canApply:
 *                           type: boolean
 *                           example: true
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/application-limits', authenticateToken, applicationController.getApplicationLimits);

/**
 * @openapi
 * /api/users/workers/search:
 *   get:
 *     summary: Search workers
 *     description: Search and filter workers based on skills, experience, rates, location, and availability. Returns workers with their personas and detailed information.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: query
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Search query for skills or worker name
 *         example: "plumber"
 *       - in: query
 *         name: skills
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Filter by specific skills
 *         example: ["Plumbing", "Electrical"]
 *       - in: query
 *         name: experience
 *         schema:
 *           type: string
 *           enum: [0-1, 2-5, 5-10, 10+]
 *         description: Filter by experience level
 *         example: "2-5"
 *       - in: query
 *         name: minHourlyRate
 *         schema:
 *           type: number
 *           minimum: 0
 *         description: Minimum hourly rate filter
 *         example: 300
 *       - in: query
 *         name: maxHourlyRate
 *         schema:
 *           type: number
 *           minimum: 0
 *         description: Maximum hourly rate filter
 *         example: 800
 *       - in: query
 *         name: minDailyRate
 *         schema:
 *           type: number
 *           minimum: 0
 *         description: Minimum daily rate filter
 *         example: 2000
 *       - in: query
 *         name: maxDailyRate
 *         schema:
 *           type: number
 *           minimum: 0
 *         description: Maximum daily rate filter
 *         example: 5000
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Location filter
 *         example: "Bangalore"
 *       - in: query
 *         name: radius
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 200
 *         description: Search radius in kilometers
 *         example: 25
 *       - in: query
 *         name: minRating
 *         schema:
 *           type: number
 *           minimum: 0
 *           maximum: 5
 *         description: Minimum rating filter
 *         example: 4.0
 *       - in: query
 *         name: availability
 *         schema:
 *           type: boolean
 *         description: Filter by availability status
 *         example: true
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [rating, distance, rate, experience, completedJobs]
 *         description: Sort criteria
 *         example: "rating"
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort order
 *         example: "desc"
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of workers per page
 *     responses:
 *       200:
 *         description: Workers search completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Workers search completed successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     workers:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           userId:
 *                             type: string
 *                             example: "user-123e4567-e89b-12d3-a456-426614174000"
 *                           fullName:
 *                             type: string
 *                             example: "Rajesh Kumar"
 *                           avatarUrl:
 *                             type: string
 *                             format: uri
 *                             example: "https://example.com/avatar.jpg"
 *                           location:
 *                             type: string
 *                             example: "Bangalore, Karnataka"
 *                           distance:
 *                             type: number
 *                             example: 12.5
 *                           primarySkill:
 *                             type: string
 *                             example: "Plumbing"
 *                           skills:
 *                             type: array
 *                             items:
 *                               type: string
 *                             example: ["Plumbing", "Electrical", "Carpentry"]
 *                           experience:
 *                             type: string
 *                             example: "2-5"
 *                           hourlyRate:
 *                             type: integer
 *                             example: 500
 *                           dailyRate:
 *                             type: integer
 *                             example: 3000
 *                           about:
 *                             type: string
 *                             example: "Experienced plumber with 5 years of experience"
 *                           rating:
 *                             type: number
 *                             format: float
 *                             example: 4.5
 *                           reviewCount:
 *                             type: integer
 *                             example: 23
 *                           completedJobs:
 *                             type: integer
 *                             example: 45
 *                           totalEarnings:
 *                             type: integer
 *                             example: 125000
 *                           isAvailable:
 *                             type: boolean
 *                             example: true
 *                           phoneVisible:
 *                             type: boolean
 *                             example: false
 *                           portfolioPhotos:
 *                             type: array
 *                             items:
 *                               type: string
 *                               format: uri
 *                             example: ["https://example.com/work1.jpg"]
 *                           lastActive:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-01-15T10:30:00Z"
 *                           personas:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                   example: "persona-123"
 *                                 skill:
 *                                   type: string
 *                                   example: "Plumbing"
 *                                 experience:
 *                                   type: string
 *                                   example: "2-5"
 *                                 hourlyRate:
 *                                   type: integer
 *                                   example: 500
 *                                 dailyRate:
 *                                   type: integer
 *                                   example: 3000
 *                                 rating:
 *                                   type: number
 *                                   example: 4.5
 *                                 reviewCount:
 *                                   type: integer
 *                                   example: 15
 *                                 completedJobs:
 *                                   type: integer
 *                                   example: 25
 *                                 isActive:
 *                                   type: boolean
 *                                   example: true
 *                           matchScore:
 *                             type: number
 *                             example: 85
 *                     filters:
 *                       type: object
 *                       properties:
 *                         skills:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               skill:
 *                                 type: string
 *                                 example: "Plumbing"
 *                               count:
 *                                 type: integer
 *                                 example: 45
 *                         experienceLevels:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               level:
 *                                 type: string
 *                                 example: "2-5"
 *                               count:
 *                                 type: integer
 *                                 example: 23
 *                         rateRanges:
 *                           type: object
 *                           properties:
 *                             hourly:
 *                               type: object
 *                               properties:
 *                                 min:
 *                                   type: integer
 *                                   example: 200
 *                                 max:
 *                                   type: integer
 *                                   example: 1000
 *                                 average:
 *                                   type: integer
 *                                   example: 500
 *                             daily:
 *                               type: object
 *                               properties:
 *                                 min:
 *                                   type: integer
 *                                   example: 1500
 *                                 max:
 *                                   type: integer
 *                                   example: 6000
 *                                 average:
 *                                   type: integer
 *                                   example: 3500
 *                         locations:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               location:
 *                                 type: string
 *                                 example: "Bangalore"
 *                               count:
 *                                 type: integer
 *                                 example: 156
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                           example: 1
 *                         limit:
 *                           type: integer
 *                           example: 20
 *                         totalPages:
 *                           type: integer
 *                           example: 8
 *                         totalCount:
 *                           type: integer
 *                           example: 156
 *                         hasMore:
 *                           type: boolean
 *                           example: true
 *       401:
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/workers/search', authenticateToken, validate(searchWorkersSchema), discoveryController.searchWorkers);

export { router as userRoutes };