/**
 * Skills Routes
 * Routes for skills and market rates operations
 * 
 * @openapi
 * tags:
 *   name: Skills
 *   description: Skills and market rates endpoints
 */

import { Router } from 'express';
import { SkillsController } from '@/controllers/skills.controller';
import { validate } from '@/middleware/validation.middleware';
import Jo<PERSON> from 'joi';

const router: Router = Router();
const skillsController = new SkillsController();

// Validation schemas
const getMarketRatesSchema = {
  query: Joi.object({
    skills: Joi.array().items(Joi.string()).optional(),
    category: Joi.string().optional(),
    location: Joi.string().optional(),
    experienceLevel: Joi.string().valid('0-1', '2-5', '5-10', '10+', 'all').optional()
  })
};

/**
 * @openapi
 * /api/skills/categories:
 *   get:
 *     summary: Get skill categories
 *     description: Retrieves all available skill categories
 *     tags: [Skills]
 *     responses:
 *       200:
 *         description: Skill categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Categories retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     categories:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             example: "category_id_here"
 *                           name:
 *                             type: string
 *                             example: "Home Services"
 *                           description:
 *                             type: string
 *                             example: "Services for home maintenance and repair"
 *                           icon:
 *                             type: string
 *                             example: "home-icon"
 *                           skills:
 *                             type: array
 *                             items:
 *                               type: string
 *                             example: []
 *                           isActive:
 *                             type: boolean
 *                             example: true
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/categories', skillsController.getCategories);

/**
 * @openapi
 * /api/skills/market-rates:
 *   get:
 *     summary: Get market rates for skills
 *     description: Retrieves market rates for skills with optional filtering
 *     tags: [Skills]
 *     parameters:
 *       - in: query
 *         name: skills
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Filter by specific skills
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by skill category
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *         description: Filter by location
 *       - in: query
 *         name: experienceLevel
 *         schema:
 *           type: string
 *           enum: [0-1, 2-5, 5-10, 10+, all]
 *         description: Filter by experience level
 *     responses:
 *       200:
 *         description: Market rates retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Market rates retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     rates:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           skill:
 *                             type: string
 *                             example: "Plumbing"
 *                           category:
 *                             type: string
 *                             example: "Home Services"
 *                           averageHourlyRate:
 *                             type: integer
 *                             example: 500
 *                           averageDailyRate:
 *                             type: integer
 *                             example: 4000
 *                           minRate:
 *                             type: integer
 *                             example: 350
 *                           maxRate:
 *                             type: integer
 *                             example: 750
 *                           currency:
 *                             type: string
 *                             example: "INR"
 *                           location:
 *                             type: string
 *                             example: "India"
 *                           experienceLevel:
 *                             type: string
 *                             example: "all"
 *                           sampleSize:
 *                             type: integer
 *                             example: 100
 *                           lastUpdated:
 *                             type: string
 *                             format: date-time
 *                             example: "2023-01-01T00:00:00.000Z"
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/market-rates', validate(getMarketRatesSchema), skillsController.getMarketRates);

export { router as skillsRoutes };
