/**
 * Authentication Middleware
 * JWT token validation and user authentication
 */

import { Request, Response, NextFunction } from 'express';
import * as jwt from 'jsonwebtoken';
import { ResponseUtil } from '@/utils/response';
import { appConfig } from '@/config/app.config';
import type { JwtPayload } from '@ozgaar/types';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        phone: string;
      };
    }
  }
}

export const authenticateToken = (
  req: Request,
  res: Response,
  next: NextFunction
): Response | void => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return ResponseUtil.unauthorized(res, 'Access token required');
  }

  try {
    const decoded = (jwt as any).verify(token, appConfig.env.JWT_SECRET) as JwtPayload;
    
    req.user = {
      id: decoded.userId,
      phone: decoded.phone
    };

    next();
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      return ResponseUtil.unauthorized(res, 'Token expired');
    }
    
    if (error instanceof jwt.JsonWebTokenError) {
      return ResponseUtil.unauthorized(res, 'Invalid token');
    }

    return ResponseUtil.unauthorized(res, 'Token verification failed');
  }
};

export const optionalAuth = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return next();
  }

  try {
    const decoded = (jwt as any).verify(token, appConfig.env.JWT_SECRET) as JwtPayload;
    
    req.user = {
      id: decoded.userId,
      phone: decoded.phone
    };
  } catch (error) {
    // Ignore token errors for optional auth
  }

  next();
};
