/**
 * Validation Middleware
 * Request validation using Joi schemas
 */

import { Request, Response, NextFunction } from 'express';
import Jo<PERSON> from 'joi';
import { ResponseUtil } from '@/utils/response';
import type { ValidationSchema, ValidationOptions } from '@ozgaar/types';

export const validate = (
  schema: ValidationSchema,
  options: ValidationOptions = {}
) => {
  return (req: Request, res: Response, next: NextFunction): Response | void => {
    const validationOptions: Joi.ValidationOptions = {
      abortEarly: options.abortEarly ?? false,
      allowUnknown: options.allowUnknown ?? false,
      stripUnknown: options.stripUnknown ?? true
    };

    const errors: any = {};

    // Validate request body
    if (schema.body) {
      const { error, value } = schema.body.validate(req.body, validationOptions);
      if (error) {
        errors.body = error.details.map((detail: any) => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }));
      } else {
        req.body = value;
      }
    }

    // Validate query parameters
    if (schema.query) {
      const { error, value } = schema.query.validate(req.query, validationOptions);
      if (error) {
        errors.query = error.details.map((detail: any) => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }));
      } else {
        req.query = value;
      }
    }

    // Validate route parameters
    if (schema.params) {
      const { error, value } = schema.params.validate(req.params, validationOptions);
      if (error) {
        errors.params = error.details.map((detail: any) => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }));
      } else {
        req.params = value;
      }
    }

    // Validate headers
    if (schema.headers) {
      const { error, value } = schema.headers.validate(req.headers, validationOptions);
      if (error) {
        errors.headers = error.details.map((detail: any) => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }));
      } else {
        req.headers = value;
      }
    }

    // Return validation errors if any
    if (Object.keys(errors).length > 0) {
      return ResponseUtil.validationError(res, 'Validation failed', errors);
    }

    next();
  };
};

// Common validation schemas
export const commonSchemas = {
  uuid: Joi.string().uuid().required(),
  phone: Joi.string().pattern(/^\+91[6-9]\d{9}$/).required(),
  otp: Joi.string().length(6).pattern(/^\d{6}$/).required(),
  pagination: {
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20)
  }
};
