/**
 * Error Handling Middleware
 * Global error handler for Express application
 */

import { Request, Response, NextFunction } from 'express';
import { logger, logError } from '@/utils/logger';
import { ResponseUtil } from '@/utils/response';
import { appConfig } from '@/config/app.config';
import type { HttpError } from '@ozgaar/types';

export const errorHandler = (
  error: Error | HttpError,
  req: Request,
  res: Response,
  next: NextFunction
): Response | void => {
  // Log the error
  logError(error, {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Handle different types of errors
  if (error.name === 'ValidationError') {
    return ResponseUtil.validationError(res, error.message);
  }

  if (error.name === 'JsonWebTokenError') {
    return ResponseUtil.unauthorized(res, 'Invalid token');
  }

  if (error.name === 'TokenExpiredError') {
    return ResponseUtil.unauthorized(res, 'Token expired');
  }

  if (error.name === 'MulterError') {
    if (error.message.includes('File too large')) {
      return ResponseUtil.error(res, 'File size too large', 'File Upload Error', 413);
    }
    return ResponseUtil.error(res, 'File upload error', 'File Upload Error', 400);
  }

  // Handle HTTP errors
  if ('statusCode' in error && error.statusCode) {
    const httpError = error as HttpError;
    return ResponseUtil.error(
      res,
      httpError.message,
      httpError.message,
      httpError.statusCode
    );
  }

  // Handle database errors
  if (error.message.includes('duplicate key value')) {
    return ResponseUtil.conflict(res, 'Resource already exists');
  }

  if (error.message.includes('foreign key constraint')) {
    return ResponseUtil.error(res, 'Invalid reference', 'Database Error', 400);
  }

  // Default server error
  const message = appConfig.isDevelopment 
    ? error.message 
    : 'Internal server error';

  const stack = appConfig.isDevelopment 
    ? error.stack 
    : undefined;

  return res.status(500).json({
    success: false,
    message,
    error: 'Internal Server Error',
    statusCode: 500,
    ...(stack && { stack })
  });
};

// Async error wrapper
export const asyncHandler = <T>(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<T>
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Create HTTP error
export const createHttpError = (
  message: string,
  statusCode: number = 500,
  isOperational: boolean = true
): HttpError => {
  const error = new Error(message) as HttpError;
  error.statusCode = statusCode;
  error.isOperational = isOperational;
  return error;
};
