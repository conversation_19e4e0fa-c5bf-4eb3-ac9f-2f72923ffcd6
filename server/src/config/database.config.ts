/**
 * Database Configuration
 * Supabase client configuration and connection management
 */

import { createClient } from '@supabase/supabase-js';
import { logger } from '@/utils/logger';
import type { DatabaseServiceConfig } from '@ozgaar/types';
import { AppConfig } from './app.config';

export class DatabaseConfig {
  private static instance: DatabaseConfig;
  private supabaseClient: ReturnType<typeof createClient> | null = null;

  private constructor() {}

  public static getInstance(): DatabaseConfig {
    if (!DatabaseConfig.instance) {
      DatabaseConfig.instance = new DatabaseConfig();
    }
    return DatabaseConfig.instance;
  }

  public getClient() {
    if (!this.supabaseClient) {
      this.initializeClient();
    }
    return this.supabaseClient;
  }

  private initializeClient() {
    const config = this.getConfig();
    
    if (!config.supabaseUrl || !config.supabaseServiceKey) {
      throw new Error('Missing required Supabase configuration');
    }

    this.supabaseClient = createClient(
      config.supabaseUrl,
      config.supabaseServiceKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public' as any
        }
      }
    );

    logger.info('✅ Supabase client initialized');
  }

  private getConfig(): DatabaseServiceConfig {
    const appConfig = AppConfig.getInstance();
    return {
      supabaseUrl: appConfig.env.SUPABASE_URL,
      supabaseServiceKey: appConfig.env.SUPABASE_SERVICE_KEY || '',
      supabaseAnonKey: appConfig.env.SUPABASE_ANON_KEY || '',
      connectionPoolSize: appConfig.env.DB_POOL_SIZE,
      queryTimeout: appConfig.env.DB_QUERY_TIMEOUT
    };
  }

  public async testConnection(): Promise<boolean> {
    try {
      const client = this.getClient();
      if (!client) return false;

      const { data, error } = await client
        .from('user_profiles')
        .select('count')
        .limit(1);

      if (error) {
        logger.error('Database connection test failed:', error);
        return false;
      }

      logger.info('✅ Database connection test successful');
      return true;
    } catch (error) {
      logger.error('Database connection test error:', error);
      return false;
    }
  }
}

export const databaseConfig = DatabaseConfig.getInstance();
