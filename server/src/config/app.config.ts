/**
 * Application Configuration
 * Central configuration management for the server
 */

import type { Environment, LoggerConfig, CorsConfig, RateLimitConfig, UploadConfig } from '@ozgaar/types';

export class AppConfig {
  private static instance: AppConfig;
  private config: Environment;

  private constructor() {
    // Load dotenv if not already loaded
    if (!process.env.SUPABASE_SERVICE_KEY) {
      try {
        require('dotenv').config({ path: '.env' });
      } catch (e) {
        // dotenv might not be available in all environments
      }
    }
    
    this.config = this.loadEnvironment();
    this.validateConfig();
  }

  public static getInstance(): AppConfig {
    if (!AppConfig.instance) {
      // Load dotenv if not already loaded
      if (!process.env.SUPABASE_URL && !process.env.SUPABASE_SERVICE_KEY) {
        try {
          require('dotenv').config();
        } catch (e) {
          // dotenv might not be available in all environments
        }
      }
      AppConfig.instance = new AppConfig();
    }
    return AppConfig.instance;
  }

  private loadEnvironment(): Environment {
    return {
      NODE_ENV: (process.env.NODE_ENV as Environment['NODE_ENV']) || 'development',
      PORT: parseInt(process.env.PORT || '3000'),
      SUPABASE_URL: process.env.SUPABASE_URL || '',
      SUPABASE_SERVICE_KEY: process.env.SUPABASE_SERVICE_KEY || '',
      SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY || '',
      JWT_SECRET: process.env.JWT_SECRET || '',
      JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '1h',
      REFRESH_TOKEN_EXPIRES_IN: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d',
      TWOFACTOR_API_KEY: process.env.TWOFACTOR_API_KEY || '',
      MOCK_OTP: process.env.MOCK_OTP || 'false',
      MOCK_OTP_VALUE: process.env.MOCK_OTP_VALUE || '123456',
      UPLOAD_MAX_SIZE: parseInt(process.env.UPLOAD_MAX_SIZE || '10485760'), // 10MB
      RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
      RATE_LIMIT_MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
      DB_POOL_SIZE: parseInt(process.env.DB_POOL_SIZE || '10'),
      DB_QUERY_TIMEOUT: parseInt(process.env.DB_QUERY_TIMEOUT || '30000')
    };
  }

  private validateConfig(): void {
    // If not using mock OTP, require TWOFACTOR_API_KEY
    const required = [
      'SUPABASE_URL',
      'SUPABASE_SERVICE_KEY',
      'JWT_SECRET'
    ];

    const missing = required.filter(key => !this.config[key as keyof Environment]);
    
    // Check if we need 2FACTOR_API_KEY
    if (this.config.MOCK_OTP !== 'true' && !this.config.TWOFACTOR_API_KEY) {
      throw new Error('Missing required environment variable: TWOFACTOR_API_KEY (required when MOCK_OTP is not true)');
    }
    
    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
  }

  public get env(): Environment {
    return { ...this.config };
  }

  public get isDevelopment(): boolean {
    return this.config.NODE_ENV === 'development';
  }

  public get isProduction(): boolean {
    return this.config.NODE_ENV === 'production';
  }

  public get isTest(): boolean {
    return this.config.NODE_ENV === 'test';
  }

  public getLoggerConfig(): LoggerConfig {
    return {
      level: this.isDevelopment ? 'debug' : 'info',
      format: this.isProduction ? 'json' : 'simple',
      transports: this.isProduction ? ['console', 'file'] : ['console'],
      filename: 'logs/server.log',
      maxSize: '20m',
      maxFiles: 5
    };
  }

  public getCorsConfig(): CorsConfig {
    return {
      origin: this.isDevelopment ? true : (process.env.CORS_ORIGIN?.split(',') || false),
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      allowedHeaders: ['Content-Type', 'Authorization'],
      credentials: true
    };
  }

  public getRateLimitConfig(): RateLimitConfig {
    return {
      windowMs: this.config.RATE_LIMIT_WINDOW_MS,
      max: this.config.RATE_LIMIT_MAX_REQUESTS,
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false
    };
  }

  public getUploadConfig(): UploadConfig {
    return {
      maxSize: this.config.UPLOAD_MAX_SIZE,
      allowedMimeTypes: [
        'image/jpeg',
        'image/png',
        'image/webp',
        'application/pdf'
      ],
      destination: 'uploads/',
      filename: (originalName: string) => {
        const timestamp = Date.now();
        const extension = originalName.split('.').pop();
        return `${timestamp}-${Math.random().toString(36).substring(2)}.${extension}`;
      }
    };
  }
}

export const appConfig = AppConfig.getInstance();
