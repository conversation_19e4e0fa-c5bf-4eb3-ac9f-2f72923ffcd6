{"extends": "../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "paths": {"@/*": ["*"], "@controllers/*": ["controllers/*"], "@middleware/*": ["middleware/*"], "@routes/*": ["routes/*"], "@services/*": ["services/*"], "@utils/*": ["utils/*"], "@config/*": ["config/*"]}, "types": ["node", "jest"], "typeRoots": ["./node_modules/@types", "../node_modules/@types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "references": [{"path": "../packages/types"}]}