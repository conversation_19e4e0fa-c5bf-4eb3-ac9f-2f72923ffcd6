# Ozgaar Platform Architecture

## Overview

The Ozgaar job platform uses a clean **3-tier architecture** with clear separation of concerns:

```
┌─────────────────┐    HTTP API     ┌─────────────────┐    SQL/RPC     ┌─────────────────┐
│   Mobile App    │ ──────────────> │  Backend Server │ ─────────────> │    Database     │
│  (React Native) │                 │   (Node.js)     │                │   (Supabase)    │
└─────────────────┘                 └─────────────────┘                └─────────────────┘
```

## Directory Structure

```
ozgaar-android/
├── mobile/              # React Native mobile application
├── backend/             # Node.js API server (Express + TypeScript)
├── supabase-cloud-setup.sql  # One-time database setup script
└── docs/               # Documentation and specifications
```

## Component Responsibilities

### 1. Mobile App (`mobile/`)
**Purpose**: User interface and client-side logic
- **Technology**: React Native with Expo
- **Responsibilities**:
  - User interface and navigation
  - HTTP API calls to backend server
  - Local state management and caching
  - JWT token storage and management
- **Communication**: HTTP requests to Node.js backend only
- **Security**: No direct database access, no sensitive credentials

### 2. Backend Server (`backend/`)
**Purpose**: API layer and business logic
- **Technology**: Node.js + Express + TypeScript
- **Responsibilities**:
  - RESTful API endpoints
  - Authentication and authorization (JWT)
  - Business logic and data validation
  - Supabase database operations
  - Twilio SMS integration
  - Rate limiting and security
  - Logging and monitoring
- **Communication**: 
  - Receives HTTP requests from mobile app
  - Makes database calls to Supabase
  - Integrates with external services (Twilio)
- **Security**: Holds all sensitive credentials (service keys)

### 3. Database (Supabase Cloud)
**Purpose**: Hosted data storage and management
- **Technology**: Supabase Cloud (PostgreSQL + Auth + Storage)
- **Responsibilities**:
  - Persistent data storage
  - User authentication and authorization
  - Row Level Security (RLS) enforcement
  - Database functions and triggers
  - Automatic backups and scaling
- **Communication**: Only accessed by backend server via service role
- **Security**: Protected by RLS policies and service role authentication

## Data Flow

### Authentication Flow
```
Mobile App → Backend API → Supabase Database
    ↓           ↓              ↓
1. Send OTP  → Validate     → Check rate limits
2. Verify    → Generate JWT → Create/update user
3. Store JWT → Return token → Log activity
```

### API Request Flow
```
Mobile App → Backend Server → Supabase Database
    ↓            ↓               ↓
1. HTTP + JWT → Validate JWT  → Execute query
2. Request   → Business logic → Apply RLS
3. Response  → Format data   → Return results
```

## Security Architecture

### Mobile App Security
- **No sensitive credentials** stored in app
- **JWT tokens** stored securely in AsyncStorage
- **HTTPS only** communication with backend
- **Input validation** on client side (UX)

### Backend Server Security
- **JWT authentication** with refresh tokens
- **Rate limiting** on all endpoints
- **Input validation** with Joi schemas
- **SQL injection prevention** via Supabase client
- **CORS configuration** for cross-origin requests
- **Security headers** via Helmet.js
- **Request sanitization** to prevent XSS

### Database Security
- **Row Level Security (RLS)** on all tables
- **Service role authentication** for backend access
- **No direct client access** to database
- **Audit logging** of all operations
- **Encrypted connections** (TLS)

## Development Workflow

### Local Development Setup
1. **One-time Database Setup**: Run `supabase-cloud-setup.sql` in Supabase Cloud SQL Editor
2. **Start Backend**: `cd backend && npm run dev`
3. **Start Mobile**: `cd mobile && npm start`

### Database Changes
1. Create SQL scripts for schema changes
2. Test changes in Supabase Cloud SQL Editor
3. Apply to production database via Supabase dashboard
4. Update backend code if needed

### API Changes
1. Update backend code in `backend/src/`
2. Test locally with mobile app
3. Deploy backend server to production
4. Update mobile app if needed

## Deployment Architecture

### Production Deployment
```
Mobile App (App Stores) → Backend Server (Cloud) → Supabase (Cloud)
                              ↓
                         External Services
                         (Twilio SMS, etc.)
```

### Environment Configuration
- **Mobile**: Only needs backend API URL
- **Backend**: Needs all service credentials (Supabase, Twilio, JWT secrets)
- **Database**: Managed by Supabase cloud

## Benefits of This Architecture

### Security Benefits
- **Credential isolation**: Sensitive keys only on backend server
- **API control**: All database access controlled by backend
- **Rate limiting**: Centralized request throttling
- **Audit trail**: Complete logging of all operations

### Scalability Benefits
- **Independent scaling**: Each tier can scale separately
- **Caching layer**: Backend can implement caching strategies
- **Load balancing**: Multiple backend instances possible
- **Database optimization**: Centralized query optimization

### Maintainability Benefits
- **Clear separation**: Each component has single responsibility
- **Independent development**: Teams can work on different tiers
- **Testing isolation**: Each layer can be tested independently
- **Technology flexibility**: Can replace components without affecting others

## Technology Choices

### Why Node.js Backend?
- **TypeScript support**: Type safety and better developer experience
- **Rich ecosystem**: Extensive middleware and library support
- **Performance**: Excellent for I/O intensive operations
- **Team familiarity**: JavaScript/TypeScript across full stack

### Why Supabase?
- **PostgreSQL**: Robust, feature-rich relational database
- **Built-in auth**: Phone number authentication out of the box
- **Real-time**: WebSocket support for live updates
- **Row Level Security**: Database-level access control
- **Developer experience**: Excellent tooling and documentation

### Why React Native?
- **Cross-platform**: Single codebase for iOS and Android
- **Performance**: Near-native performance
- **Developer experience**: Hot reload, debugging tools
- **Community**: Large ecosystem and community support

## Future Considerations

### Potential Enhancements
- **Redis caching**: Add caching layer for frequently accessed data
- **Message queues**: Add background job processing
- **Microservices**: Split backend into domain-specific services
- **CDN**: Add content delivery network for static assets
- **Monitoring**: Add application performance monitoring

### Scaling Strategies
- **Horizontal scaling**: Multiple backend server instances
- **Database read replicas**: Separate read/write database instances
- **API versioning**: Support multiple API versions
- **Feature flags**: Dynamic feature enablement
