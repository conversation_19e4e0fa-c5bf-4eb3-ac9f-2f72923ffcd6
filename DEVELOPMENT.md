# Ozgaar Development Workflow

## 🚀 **Quick Start for Developers**

### **First Time Setup**
1. **Set up Supabase Cloud**: Follow [SUPABASE-CLOUD-SETUP.md](./SUPABASE-CLOUD-SETUP.md)
2. **Configure Backend**: Add your credentials to `backend/.env`
3. **Configure Mobile**: Set API URL in `mobile/.env`

### **Daily Development**
```bash
# Terminal 1: Start Backend Server
cd backend
npm run dev

# Terminal 2: Start Mobile App  
cd mobile
npm start
```

That's it! No local database to manage.

## 📁 **Project Structure**

```
ozgaar-android/
├── mobile/                    # React Native mobile app
│   ├── app/                   # App screens and navigation
│   ├── components/            # Reusable UI components
│   ├── contexts/              # React contexts (auth, etc.)
│   ├── lib/                   # API client and utilities
│   └── .env                   # Mobile app configuration
├── backend/                   # Node.js API server
│   ├── src/
│   │   ├── controllers/       # API endpoint handlers
│   │   ├── middleware/        # Auth, validation, security
│   │   ├── services/          # External service integrations
│   │   ├── utils/             # Helper functions
│   │   └── config/            # Configuration and database
│   ├── logs/                  # Application logs
│   └── .env                   # Backend configuration
├── supabase-cloud-setup.sql   # One-time database setup
└── docs/                      # Documentation
```

## 🔄 **Development Workflow**

### **Making Changes**

#### **Mobile App Changes**
1. Edit files in `mobile/app/` or `mobile/components/`
2. Changes auto-reload in Expo
3. Test on device/simulator

#### **Backend API Changes**
1. Edit files in `backend/src/`
2. Server auto-restarts with `npm run dev`
3. Test API endpoints with mobile app or Postman

#### **Database Changes**
1. Write SQL in Supabase Cloud SQL Editor
2. Test changes
3. Update backend code if needed
4. Test with mobile app

### **Adding New Features**

#### **New API Endpoint**
1. Add route in `backend/src/routes/`
2. Add controller in `backend/src/controllers/`
3. Add validation schema in `backend/src/utils/validation.ts`
4. Update mobile app to call new endpoint

#### **New Mobile Screen**
1. Create screen in `mobile/app/`
2. Add to navigation
3. Create API calls in `mobile/lib/`
4. Test authentication flow

## 🧪 **Testing**

### **Backend Testing**
```bash
cd backend
npm test                    # Run unit tests
npm run lint               # Check code style
npm run type-check         # TypeScript validation
```

### **Mobile Testing**
```bash
cd mobile
npm test                   # Run tests
expo doctor               # Check configuration
```

### **API Testing**
```bash
# Test health endpoint
curl http://localhost:3000/health

# Test authentication
curl -X POST http://localhost:3000/api/auth/send-otp \
  -H "Content-Type: application/json" \
  -d '{"phone": "+919876543210"}'
```

## 📊 **Monitoring**

### **Backend Logs**
```bash
cd backend
tail -f logs/combined.log     # All logs
tail -f logs/error.log        # Error logs only
```

### **Database Monitoring**
- **Supabase Dashboard**: Monitor queries and performance
- **Table Editor**: View data directly
- **SQL Editor**: Run analytics queries

### **SMS Monitoring**
- **Twilio Console**: SMS delivery rates and costs
- **Backend Logs**: SMS delivery attempts
- **Database**: Query `sms_logs` table

## 🔧 **Common Tasks**

### **Add New User Field**
1. **Database**: Add column in Supabase Table Editor
2. **Backend**: Update `User` interface in `backend/src/config/database.ts`
3. **Mobile**: Update user context and forms

### **Add New API Validation**
1. **Backend**: Add schema in `backend/src/utils/validation.ts`
2. **Backend**: Apply validation in route handler
3. **Mobile**: Handle validation errors in API calls

### **Add New Environment Variable**
1. **Backend**: Add to `backend/src/config/index.ts`
2. **Backend**: Add to `backend/.env.example`
3. **Documentation**: Update setup guides

## 🚨 **Troubleshooting**

### **Backend Won't Start**
```bash
cd backend
npm run type-check          # Check TypeScript errors
cat logs/error.log          # Check error logs
```

### **Mobile App Won't Connect**
1. Check `EXPO_PUBLIC_API_BASE_URL` in `mobile/.env`
2. Ensure backend is running on correct port
3. Check network connectivity

### **Database Connection Issues**
1. Verify Supabase credentials in `backend/.env`
2. Check Supabase project status (not paused)
3. Test connection: `curl http://localhost:3000/health`

### **SMS Not Working**
1. Check Twilio credentials in `backend/.env`
2. Verify phone number format (+91 prefix)
3. Check Twilio console for delivery status

## 📚 **Useful Commands**

### **Backend**
```bash
cd backend
npm run dev                 # Start development server
npm run build              # Build for production
npm start                  # Start production server
npm run lint:fix           # Fix linting issues
```

### **Mobile**
```bash
cd mobile
npm start                  # Start Expo development
npm run android           # Run on Android
npm run ios               # Run on iOS
npx expo install          # Install Expo dependencies
```

### **Database**
```bash
# No local commands needed - use Supabase Cloud dashboard
# SQL Editor: https://app.supabase.com/project/YOUR_PROJECT/sql
# Table Editor: https://app.supabase.com/project/YOUR_PROJECT/editor
```

## 🎯 **Best Practices**

### **Code Organization**
- Keep API routes RESTful and consistent
- Use TypeScript interfaces for all data structures
- Implement proper error handling and logging
- Follow React Native best practices for mobile

### **Security**
- Never commit `.env` files
- Use service role key only in backend
- Implement proper input validation
- Follow RLS policies in database

### **Performance**
- Use database indexes for frequent queries
- Implement proper caching strategies
- Optimize mobile app bundle size
- Monitor API response times

### **Documentation**
- Update API documentation for new endpoints
- Document database schema changes
- Keep README files current
- Add inline code comments for complex logic

## 🧰 **Real-time Debugging and Testing**

### **Socket.IO Admin UI**

A Socket.IO Admin UI dashboard has been integrated into the backend server for real-time debugging and testing of WebSocket events.

#### **Accessing the Dashboard**
1. Start your backend server:
   ```bash
   cd server
   pnpm dev
   ```
2. Open your browser and navigate to: https://admin.socket.io
3. Enter your server URL: `http://localhost:3000` (or your actual port)
4. Log in with credentials:
   - Username: `admin`
   - Password: `yourSecurePassword`

#### **Features**
- Monitor connected sockets and rooms
- View real-time event logs
- Send test events with custom payloads
- Inspect socket data and authentication details

For detailed usage instructions, see [SOCKET_IO_ADMIN_UI_GUIDE.md](./SOCKET_IO_ADMIN_UI_GUIDE.md).

### **1-on-1 Conversation Initiation**

The platform supports 1-on-1 conversations between workers and posters for specific jobs. Each conversation is uniquely identified by the combination of job_id, worker_id, and poster_id.

#### **How Conversations Work**
- Conversations are created when a worker applies to a job
- Each conversation is between exactly two users (one worker, one poster)
- Conversations are tied to a specific job
- Users can only access conversations they are participants in

#### **Conversation Creation**
When a worker applies to a job, a conversation is automatically created between the worker and the job poster. The system ensures there is only one conversation per job between any specific worker and poster pair.

#### **Accessing Conversations**
Users can retrieve their conversations through the `/api/messages/conversations` endpoint. The system returns all conversations where the user is either the worker or the poster.

For implementation details, see [CONVERSATION_INITIATION_GUIDE.md](./CONVERSATION_INITIATION_GUIDE.md).

### **Postman Socket.IO Testing**

You can also test Socket.IO functionality using Postman's Socket.IO client.

#### **Setup**
Follow the detailed instructions in [POSTMAN_SOCKET_IO_TESTING_GUIDE.md](./POSTMAN_SOCKET_IO_TESTING_GUIDE.md) to:
- Configure Postman environment variables
- Set up two Socket.IO tabs (one per user)
- Test all messaging functionalities including:
  - Joining conversations
  - Sending messages
  - Marking messages as read
  - Typing indicators
  - Reconnection scenarios

This provides an end-to-end testing workflow similar to what you would do with real mobile app clients.

## 🚀 **Deployment**

### **Backend Deployment**
- Deploy to Vercel, Railway, or similar platform
- Set environment variables in deployment platform
- Update mobile app with production API URL

### **Mobile Deployment**
- Build with `eas build` or `expo build`
- Submit to app stores
- Update API URL for production

### **Database**
- Already deployed on Supabase Cloud
- No additional deployment needed
- Monitor usage and scale as needed
