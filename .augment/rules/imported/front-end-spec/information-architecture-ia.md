---
type: "manual"
---

# Information Architecture (IA)

## Site Map / Screen Inventory

```mermaid
graph TD
    A[Launch/Language Selection] --> B[Role Selection]
    B --> C[Worker Flow]
    B --> D[Job Poster Flow]
    
    C --> C1[Worker Dashboard]
    C1 --> C2[Browse Jobs]
    C1 --> C3[My Applications]
    C1 --> C4[Worker Profile]
    C1 --> C5[Payment History]
    
    C2 --> C2a[Job Details]
    C2a --> C2b[Apply for Job]
    
    C4 --> C4a[Skills & Personas]
    C4a --> C4b[Add New Skill]
    C4 --> C4c[Verification Status]
    C4 --> C4d[Ratings & Reviews]
    
    D --> D1[Job Poster Dashboard]
    D1 --> D2[Post New Job]
    D1 --> D3[Active Jobs]
    D1 --> D4[Worker Search]
    D1 --> D5[Hiring History]
    
    D2 --> D2a[Job Category Selection]
    D2a --> D2b[Job Requirements]
    D2b --> D2c[Budget & Location]
    D2c --> D2d[Job Preview]
    
    D4 --> D4a[Worker Profiles]
    D4a --> D4b[Worker Details]
    D4b --> D4c[Hire Worker]
    
    C1 --> S[Shared Features]
    D1 --> S
    S --> S1[Messages/Chat]
    S --> S2[Notifications]
    S --> S3[Location Settings]
    S --> S4[Language Settings]
    S --> S5[Payment Settings]
```

## Navigation Structure

**Primary Navigation (Bottom Tab Bar):**
- **Workers**: Dashboard | Browse Jobs | Profile | Messages 
- **Job Posters**: Dashboard | Post Job | Find Workers | Messages

**Secondary Navigation:**
- Settings accessible via profile screen
- Notification center via bell icon in header
- Language switcher in settings (persistent across app)

**Breadcrumb Strategy:** 
- Minimal breadcrumbs due to mobile-first design
- Clear "Back" navigation with context labels ("Back to Jobs" instead of just arrow)
- Progress indicators for multi-step flows (job posting, profile completion)
