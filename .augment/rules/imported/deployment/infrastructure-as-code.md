---
type: "manual"
---

# Infrastructure as Code & Deployment Strategy

## Overview

This document defines the complete Infrastructure as Code (IaC) strategy for the Ozgaar platform, covering environment management, Supabase configuration, mobile app deployment via EAS, and monitoring setup.

## 1. Environment Architecture

### 1.1 Environment Strategy

| Environment | Purpose | Infrastructure | Data | External Services |
|-------------|---------|----------------|------|-------------------|
| **Development** | Local development | Local Supabase, Docker | Synthetic test data | Mock services |
| **Staging** | Testing & QA | Supabase staging project | Anonymized production data | Test API keys |
| **Production** | Live platform | Supabase production project | Real user data | Production API keys |

### 1.2 Environment Configuration Management

```yaml
# infrastructure/environments/development.yml
environment: development
supabase:
  project_id: "ozgaar-dev-xyz123"
  region: "ap-south-1"
  database:
    instance_size: "micro"
    backup_enabled: false
  auth:
    site_url: "http://localhost:3000"
    additional_redirect_urls:
      - "exp://localhost:19000"
  storage:
    file_size_limit: 50MB
    allowed_mime_types:
      - "image/jpeg"
      - "image/png"
      - "image/webp"

mobile_app:
  expo:
    slug: "ozgaar-dev"
    version: "1.0.0"
    platform: "android"
    orientation: "portrait"
  
external_services:
  google_maps:
    api_key: "${GOOGLE_MAPS_API_KEY_DEV}"
  sms_provider:
    primary: "2factor_test"
    backup: "msg91_test"
```

```yaml
# infrastructure/environments/production.yml
environment: production
supabase:
  project_id: "ozgaar-prod-abc789"
  region: "ap-south-1"
  database:
    instance_size: "small"
    backup_enabled: true
    backup_schedule: "daily"
    point_in_time_recovery: true
  auth:
    site_url: "https://ozgaar.app"
    password_min_length: 8
    external_email_confirmations: true
  storage:
    file_size_limit: 10MB
    image_transformation: enabled
  realtime:
    max_connections: 200
    max_channels_per_client: 10

mobile_app:
  expo:
    slug: "ozgaar"
    version: "1.0.0"
    platform: "android"
    bundle_identifier: "com.ozgaar.android"
  
monitoring:
  sentry_dsn: "${SENTRY_DSN}"
  analytics_enabled: true
  
external_services:
  google_maps:
    api_key: "${GOOGLE_MAPS_API_KEY_PROD}"
  sms_provider:
    primary: "2factor_prod"
    backup: "msg91_prod"
```

## 2. Supabase Infrastructure Management

### 2.1 Database Schema Management

```sql
-- infrastructure/supabase/migrations/20250814000001_initial_schema.sql
-- This file contains the complete database schema from docs/architecture.md

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Create custom types
CREATE TYPE user_role_enum AS ENUM ('worker', 'poster', 'both');
-- ... rest of the schema from the architecture document
```

### 2.2 Row Level Security Policies

```sql
-- infrastructure/supabase/migrations/20250814000002_rls_policies.sql

-- Users table policies
CREATE POLICY "Users can view own profile" 
  ON users FOR SELECT 
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" 
  ON users FOR UPDATE 
  USING (auth.uid() = id);

-- Worker personas policies  
CREATE POLICY "Users can manage own personas" 
  ON worker_personas FOR ALL 
  USING (user_id = auth.uid());

CREATE POLICY "Public can view active personas" 
  ON worker_personas FOR SELECT 
  USING (is_active = true);

-- Jobs policies
CREATE POLICY "Public can view active jobs" 
  ON jobs FOR SELECT 
  USING (status = 'active');

CREATE POLICY "Users can manage own jobs" 
  ON jobs FOR ALL 
  USING (poster_id = auth.uid());

-- Job applications policies
CREATE POLICY "Users can view own applications" 
  ON job_applications FOR SELECT 
  USING (
    worker_persona_id IN (
      SELECT id FROM worker_personas WHERE user_id = auth.uid()
    ) OR 
    job_id IN (
      SELECT id FROM jobs WHERE poster_id = auth.uid()  
    )
  );

CREATE POLICY "Workers can create applications" 
  ON job_applications FOR INSERT 
  WITH CHECK (
    worker_persona_id IN (
      SELECT id FROM worker_personas WHERE user_id = auth.uid()
    )
  );

-- Reviews policies  
CREATE POLICY "Users can view reviews for their jobs/profiles" 
  ON reviews FOR SELECT 
  USING (
    reviewer_id = auth.uid() OR 
    reviewee_id = auth.uid()
  );

CREATE POLICY "Users can create reviews for completed jobs" 
  ON reviews FOR INSERT 
  WITH CHECK (
    reviewer_id = auth.uid() AND
    job_id IN (
      SELECT ja.job_id 
      FROM job_applications ja
      WHERE ja.status = 'accepted' AND (
        ja.worker_persona_id IN (
          SELECT id FROM worker_personas WHERE user_id = auth.uid()
        ) OR 
        ja.job_id IN (
          SELECT id FROM jobs WHERE poster_id = auth.uid()
        )
      )
    )
  );
```

### 2.3 Supabase Edge Functions

```typescript
// infrastructure/supabase/functions/job-matching/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

interface JobMatchRequest {
  worker_persona_id: string;
  max_distance_km?: number;
  limit?: number;
}

interface MatchedJob {
  job: any;
  distance_km: number;
  match_score: number;
  match_reasons: string[];
}

serve(async (req) => {
  try {
    const { worker_persona_id, max_distance_km = 15, limit = 20 } = await req.json();
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );
    
    // Get worker persona details
    const { data: persona, error: personaError } = await supabase
      .from('worker_personas')
      .select(`
        *,
        users!inner(location)
      `)
      .eq('id', worker_persona_id)
      .single();
    
    if (personaError) throw personaError;
    
    // Find matching jobs using PostGIS
    const { data: jobs, error: jobsError } = await supabase
      .rpc('find_matching_jobs', {
        persona_skill_category: persona.skill_category,
        persona_location: persona.users.location,
        max_distance: max_distance_km * 1000, // Convert to meters
        job_limit: limit
      });
    
    if (jobsError) throw jobsError;
    
    // Calculate match scores and reasons
    const matchedJobs: MatchedJob[] = jobs.map(job => {
      const distance_km = job.distance_meters / 1000;
      const { score, reasons } = calculateMatchScore(job, persona, distance_km);
      
      return {
        job,
        distance_km: Math.round(distance_km * 10) / 10,
        match_score: score,
        match_reasons: reasons
      };
    });
    
    // Sort by match score descending
    matchedJobs.sort((a, b) => b.match_score - a.match_score);
    
    return new Response(JSON.stringify(matchedJobs), {
      headers: { 'Content-Type': 'application/json' },
    });
    
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }
});

function calculateMatchScore(job: any, persona: any, distance_km: number) {
  let score = 0;
  const reasons = [];
  
  // Distance scoring (40% weight)
  if (distance_km <= 5) {
    score += 40;
    reasons.push('Very close to your location');
  } else if (distance_km <= 10) {
    score += 30;
    reasons.push('Close to your location');
  } else if (distance_km <= 15) {
    score += 20;
    reasons.push('Within travel radius');
  }
  
  // Skill match scoring (30% weight)
  if (job.skill_category === persona.skill_category) {
    score += 30;
    reasons.push('Perfect skill match');
  }
  
  // Rate compatibility (15% weight)
  if (job.budget_max >= persona.daily_rate) {
    score += 15;
    reasons.push('Budget matches your rate');
  } else if (job.budget_max >= persona.daily_rate * 0.8) {
    score += 10;
    reasons.push('Budget close to your rate');
  }
  
  // Urgency bonus (10% weight)
  if (job.urgency === 'urgent') {
    score += 10;
    reasons.push('Urgent job - higher priority');
  } else if (job.urgency === 'high') {
    score += 7;
    reasons.push('High priority job');
  }
  
  // Experience match (5% weight)
  if (job.min_experience_years <= persona.experience_years) {
    score += 5;
    reasons.push('Experience requirement met');
  }
  
  return { score, reasons };
}
```

## 3. Mobile App Deployment (EAS)

### 3.1 EAS Configuration

```json
// eas.json
{
  "cli": {
    "version": ">= 3.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "android": {
        "gradleCommand": ":app:assembleDebug",
        "buildType": "apk"
      },
      "env": {
        "ENVIRONMENT": "development"
      }
    },
    "preview": {
      "distribution": "internal",
      "android": {
        "buildType": "apk"
      },
      "env": {
        "ENVIRONMENT": "staging"
      }
    },
    "production": {
      "android": {
        "buildType": "aab"
      },
      "env": {
        "ENVIRONMENT": "production"
      }
    }
  },
  "submit": {
    "production": {
      "android": {
        "serviceAccountKeyPath": "./google-play-service-account.json",
        "track": "internal"
      }
    }
  }
}
```

### 3.2 App Configuration

```typescript
// app.config.ts
import { ExpoConfig, ConfigContext } from '@expo/config';

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: process.env.ENVIRONMENT === 'production' ? 'Ozgaar' : `Ozgaar ${process.env.ENVIRONMENT}`,
  slug: process.env.ENVIRONMENT === 'production' ? 'ozgaar' : `ozgaar-${process.env.ENVIRONMENT}`,
  version: '1.0.0',
  orientation: 'portrait',
  icon: './assets/icon.png',
  userInterfaceStyle: 'light',
  splash: {
    image: './assets/splash.png',
    resizeMode: 'contain',
    backgroundColor: '#ffffff'
  },
  assetBundlePatterns: [
    '**/*'
  ],
  android: {
    package: process.env.ENVIRONMENT === 'production' 
      ? 'com.ozgaar.android' 
      : `com.ozgaar.android.${process.env.ENVIRONMENT}`,
    versionCode: 1,
    compileSdkVersion: 34,
    targetSdkVersion: 34,
    buildToolsVersion: '34.0.0',
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon.png',
      backgroundColor: '#FFFFFF'
    },
    permissions: [
      'ACCESS_COARSE_LOCATION',
      'ACCESS_FINE_LOCATION',
      'CAMERA',
      'READ_EXTERNAL_STORAGE',
      'WRITE_EXTERNAL_STORAGE'
    ],
    config: {
      googleMaps: {
        apiKey: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY
      }
    }
  },
  plugins: [
    'expo-location',
    'expo-camera',
    'expo-image-picker',
    [
      'expo-notifications',
      {
        icon: './assets/notification-icon.png',
        color: '#ffffff'
      }
    ],
    [
      '@react-native-google-signin/google-signin',
      {
        iosUrlScheme: 'com.ozgaar.android'
      }
    ]
  ],
  extra: {
    eas: {
      projectId: process.env.ENVIRONMENT === 'production' 
        ? '12345678-1234-1234-1234-123456789012'
        : '87654321-4321-4321-4321-210987654321'
    },
    supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL,
    supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY
  }
});
```

## 4. Secrets Management

### 4.1 Environment Variables Structure

```bash
# .env.template (for documentation)
# Development Environment
EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Google Services
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your-maps-api-key
GOOGLE_CLOUD_PROJECT_ID=your-project-id

# SMS Providers
TWILIO_ACCOUNT_SID=your-2factor-sid
TWILIO_AUTH_TOKEN=your-2factor-token
TWILIO_FROM_NUMBER=your-2factor-number
MSG91_AUTH_KEY=your-msg91-key

# Monitoring
SENTRY_DSN=your-sentry-dsn

# EAS & Expo
EXPO_TOKEN=your-expo-token
```

### 4.2 GitHub Actions Secrets

```yaml
# Required secrets in GitHub repository settings
secrets:
  # Supabase
  - SUPABASE_STAGING_URL
  - SUPABASE_STAGING_ANON_KEY  
  - SUPABASE_STAGING_SERVICE_KEY
  - SUPABASE_PRODUCTION_URL
  - SUPABASE_PRODUCTION_ANON_KEY
  - SUPABASE_PRODUCTION_SERVICE_KEY
  
  # External Services
  - GOOGLE_MAPS_API_KEY_STAGING
  - GOOGLE_MAPS_API_KEY_PRODUCTION
  - TWILIO_ACCOUNT_SID_PROD
  - TWILIO_AUTH_TOKEN_PROD
  - MSG91_AUTH_KEY_PROD
  
  # Mobile Deployment
  - EXPO_TOKEN
  - GOOGLE_PLAY_SERVICE_ACCOUNT_KEY
  
  # Monitoring
  - SENTRY_DSN
  - SLACK_WEBHOOK_URL
```

## 5. Deployment Scripts

### 5.1 Environment Setup Scripts

```bash
#!/bin/bash
# scripts/setup-environment.sh

set -e

ENVIRONMENT=${1:-development}

echo "Setting up $ENVIRONMENT environment..."

# Install dependencies
npm install

# Setup Supabase CLI
if ! command -v supabase &> /dev/null; then
    echo "Installing Supabase CLI..."
    npm install -g @supabase/cli
fi

# Environment-specific setup
case $ENVIRONMENT in
    "development")
        echo "Setting up local development environment..."
        supabase start
        supabase db reset --db-url $DATABASE_URL
        npm run db:seed:dev
        ;;
    "staging")
        echo "Setting up staging environment..."
        supabase link --project-ref $SUPABASE_STAGING_PROJECT_REF
        supabase db push
        npm run db:seed:staging
        ;;
    "production")
        echo "Setting up production environment..."
        # Production setup requires manual confirmation
        read -p "Are you sure you want to setup production? (y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            supabase link --project-ref $SUPABASE_PRODUCTION_PROJECT_REF
            supabase db push
        else
            echo "Production setup cancelled"
            exit 1
        fi
        ;;
    *)
        echo "Unknown environment: $ENVIRONMENT"
        exit 1
        ;;
esac

echo "$ENVIRONMENT environment setup complete!"
```

### 5.2 Database Migration Script

```bash
#!/bin/bash
# scripts/migrate-database.sh

set -e

ENVIRONMENT=${1:-development}
DRY_RUN=${2:-false}

echo "Running database migrations for $ENVIRONMENT environment..."

if [[ $DRY_RUN == "true" ]]; then
    echo "DRY RUN mode - no changes will be applied"
    supabase db diff --use-migra --schema public
else
    # Backup database before migration (production only)
    if [[ $ENVIRONMENT == "production" ]]; then
        echo "Creating database backup..."
        supabase db dump --data-only > backup-$(date +%Y%m%d-%H%M%S).sql
    fi
    
    # Apply migrations
    supabase db push
    
    # Verify migration success
    echo "Verifying migration..."
    supabase db reset --linked --debug
fi

echo "Database migration complete!"
```

## 6. Monitoring & Health Checks

### 6.1 Health Check Endpoints

```typescript
// infrastructure/supabase/functions/health-check/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

serve(async (req) => {
  const checks = {
    database: false,
    auth: false,
    storage: false,
    external_services: false,
    timestamp: new Date().toISOString()
  };
  
  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );
    
    // Database check
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    checks.database = !error;
    
    // Auth check
    const { data: authData } = await supabase.auth.getUser();
    checks.auth = true; // If no error, auth service is running
    
    // Storage check  
    const { data: storageData } = await supabase.storage.listBuckets();
    checks.storage = Array.isArray(storageData);
    
    // External services check
    try {
      const mapsResponse = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=Delhi&key=${Deno.env.get('GOOGLE_MAPS_API_KEY')}`
      );
      checks.external_services = mapsResponse.ok;
    } catch {
      checks.external_services = false;
    }
    
    const allHealthy = Object.values(checks).every(check => 
      typeof check === 'boolean' ? check : true
    );
    
    return new Response(JSON.stringify({
      status: allHealthy ? 'healthy' : 'degraded',
      checks
    }), {
      status: allHealthy ? 200 : 503,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    return new Response(JSON.stringify({
      status: 'unhealthy',
      error: error.message,
      checks
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
});
```

### 6.2 Monitoring Configuration

```yaml
# infrastructure/monitoring/alerts.yml
alerts:
  - name: "Database Connection Failed"
    condition: "health_check.database == false"
    severity: "critical"
    channels: ["slack", "email"]
    
  - name: "High Error Rate"
    condition: "error_rate > 5%"
    severity: "warning"
    channels: ["slack"]
    
  - name: "External Service Degraded"
    condition: "health_check.external_services == false"
    severity: "warning"
    channels: ["slack"]

uptime_monitoring:
  - endpoint: "https://your-project.supabase.co/functions/v1/health-check"
    interval: "5m"
    timeout: "30s"
    
performance_monitoring:
  - metric: "database_response_time"
    threshold: "2s"
    
  - metric: "app_startup_time"
    threshold: "3s"
```

This comprehensive infrastructure setup ensures reliable, scalable deployment with proper monitoring and environment management for the Ozgaar platform.