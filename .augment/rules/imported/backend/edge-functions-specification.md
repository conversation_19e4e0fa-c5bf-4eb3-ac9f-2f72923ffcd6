---
type: "manual"
---

# Supabase Edge Functions Specification

## Overview

This document provides comprehensive specifications for all Supabase Edge Functions required by the Ozgaar platform, including job matching algorithms, notification systems, and business logic implementations.

## 1. Function Architecture Overview

### 1.1 Function Organization

```
supabase/functions/
├── job-matching/           # Core job matching algorithm
├── worker-notifications/   # Push notification system  
├── trust-calculation/      # Trust score calculations
├── location-indexing/      # Geographic indexing
├── analytics-processor/    # Analytics data processing
├── content-moderation/     # Content filtering
├── rate-suggestions/       # Dynamic rate recommendations
└── health-check/          # System health monitoring
```

### 1.2 Shared Libraries

```typescript
// supabase/functions/_shared/types.ts
export interface JobMatchRequest {
  worker_persona_id: string;
  max_distance_km?: number;
  limit?: number;
  urgency_filter?: 'all' | 'urgent' | 'high';
}

export interface MatchedJob {
  job: Job;
  distance_km: number;
  match_score: number;
  match_reasons: string[];
  estimated_earnings: number;
  urgency_multiplier: number;
}

export interface NotificationPayload {
  user_id: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  channel: 'job_match' | 'application_update' | 'review_received' | 'general';
}

// Geographic utilities
export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
}

export interface DistanceCalculation {
  from: Location;
  to: Location;
  distance_km: number;
  travel_time_minutes?: number;
}
```

```typescript
// supabase/functions/_shared/utils.ts
export class GeographicUtils {
  static calculateDistance(point1: Location, point2: Location): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(point1.latitude)) * 
      Math.cos(this.toRadians(point2.latitude)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
      
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }
  
  private static toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}

export class RateCalculator {
  static calculateMarketRate(
    skill_category: string, 
    location: Location, 
    urgency: string,
    time_of_day: number
  ): { min_rate: number; max_rate: number; suggested_rate: number } {
    const baseRates = {
      'electrical': { min: 400, max: 1200 },
      'plumbing': { min: 350, max: 1000 },
      'carpentry': { min: 450, max: 1500 },
      'cooking': { min: 250, max: 800 },
      'cleaning': { min: 200, max: 600 },
      'driving': { min: 300, max: 900 },
      'delivery': { min: 250, max: 700 }
    };
    
    const base = baseRates[skill_category] || { min: 300, max: 800 };
    
    // Location multiplier (simplified)
    const isMetroArea = this.isMetroArea(location);
    const locationMultiplier = isMetroArea ? 1.3 : 1.0;
    
    // Urgency multiplier
    const urgencyMultiplier = {
      'urgent': 1.5,
      'high': 1.2,
      'normal': 1.0,
      'low': 0.9
    }[urgency] || 1.0;
    
    // Time multiplier (early morning/late evening premium)
    const timeMultiplier = (time_of_day < 7 || time_of_day > 20) ? 1.2 : 1.0;
    
    const total_multiplier = locationMultiplier * urgencyMultiplier * timeMultiplier;
    
    return {
      min_rate: Math.round(base.min * total_multiplier),
      max_rate: Math.round(base.max * total_multiplier),
      suggested_rate: Math.round((base.min + base.max) / 2 * total_multiplier)
    };
  }
  
  private static isMetroArea(location: Location): boolean {
    // Simplified check for major Indian metros
    const metroAreas = [
      { lat: 28.6139, lng: 77.2090, radius: 50 }, // Delhi NCR
      { lat: 19.0760, lng: 72.8777, radius: 40 }, // Mumbai
      { lat: 12.9716, lng: 77.5946, radius: 35 }, // Bangalore
      { lat: 17.3850, lng: 78.4867, radius: 30 }, // Hyderabad
    ];
    
    return metroAreas.some(metro => 
      GeographicUtils.calculateDistance(location, { latitude: metro.lat, longitude: metro.lng }) <= metro.radius
    );
  }
}
```

## 2. Core Functions Implementation

### 2.1 Job Matching Algorithm

```typescript
// supabase/functions/job-matching/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';
import { GeographicUtils, RateCalculator } from '../_shared/utils.ts';
import type { JobMatchRequest, MatchedJob } from '../_shared/types.ts';

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const requestData: JobMatchRequest = await req.json();
    const { worker_persona_id, max_distance_km = 15, limit = 20, urgency_filter = 'all' } = requestData;
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );
    
    // Get worker persona with user location
    const { data: persona, error: personaError } = await supabase
      .from('worker_personas')
      .select(`
        *,
        users!inner(id, location, preferred_language)
      `)
      .eq('id', worker_persona_id)
      .eq('is_active', true)
      .single();
    
    if (personaError) {
      throw new Error(`Persona not found: ${personaError.message}`);
    }
    
    const workerLocation = persona.users.location;
    if (!workerLocation) {
      throw new Error('Worker location not available');
    }
    
    // Build job query with filters
    let jobQuery = supabase
      .from('jobs')
      .select(`
        *,
        users!inner(full_name, location)
      `)
      .eq('status', 'active')
      .eq('skill_category', persona.skill_category)
      .gte('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false })
      .limit(limit * 2); // Get more to filter by distance
    
    // Apply urgency filter
    if (urgency_filter !== 'all') {
      if (urgency_filter === 'urgent') {
        jobQuery = jobQuery.eq('urgency', 'urgent');
      } else if (urgency_filter === 'high') {
        jobQuery = jobQuery.in('urgency', ['urgent', 'high']);
      }
    }
    
    const { data: jobs, error: jobsError } = await jobQuery;
    
    if (jobsError) {
      throw new Error(`Job query failed: ${jobsError.message}`);
    }
    
    // Calculate match scores for each job
    const matchedJobs: MatchedJob[] = [];
    
    for (const job of jobs) {
      const jobLocation = job.users.location;
      if (!jobLocation) continue;
      
      const distance_km = GeographicUtils.calculateDistance(
        { latitude: workerLocation.coordinates[1], longitude: workerLocation.coordinates[0] },
        { latitude: jobLocation.coordinates[1], longitude: jobLocation.coordinates[0] }
      );
      
      // Skip jobs outside maximum radius
      if (distance_km > max_distance_km) continue;
      
      const matchResult = calculateJobMatchScore(job, persona, distance_km);
      
      matchedJobs.push({
        job,
        distance_km: Math.round(distance_km * 10) / 10,
        match_score: matchResult.score,
        match_reasons: matchResult.reasons,
        estimated_earnings: matchResult.estimated_earnings,
        urgency_multiplier: matchResult.urgency_multiplier
      });
    }
    
    // Sort by match score and limit results
    matchedJobs.sort((a, b) => b.match_score - a.match_score);
    const finalResults = matchedJobs.slice(0, limit);
    
    return new Response(JSON.stringify({
      success: true,
      matches: finalResults,
      total_found: matchedJobs.length,
      search_criteria: {
        skill_category: persona.skill_category,
        max_distance_km,
        urgency_filter
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Job matching error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

function calculateJobMatchScore(job: any, persona: any, distance_km: number) {
  let score = 0;
  const reasons = [];
  
  // Distance scoring (40% weight)
  let distanceScore = 0;
  if (distance_km <= 2) {
    distanceScore = 40;
    reasons.push('बहुत पास है'); // Very close
  } else if (distance_km <= 5) {
    distanceScore = 35;
    reasons.push('पास है'); // Close by
  } else if (distance_km <= 10) {
    distanceScore = 25;
    reasons.push('पहुंचने योग्य'); // Reachable
  } else if (distance_km <= 15) {
    distanceScore = 15;
    reasons.push('यात्रा सीमा में'); // Within travel range
  }
  score += distanceScore;
  
  // Skill match (already filtered, so perfect match)
  score += 30;
  reasons.push('आपके हुनर से मेल'); // Matches your skill
  
  // Rate compatibility (20% weight)
  const rateScore = calculateRateCompatibility(job, persona);
  score += rateScore.score;
  if (rateScore.reason) reasons.push(rateScore.reason);
  
  // Urgency bonus (10% weight)
  let urgencyScore = 0;
  let urgencyMultiplier = 1.0;
  
  switch (job.urgency) {
    case 'urgent':
      urgencyScore = 10;
      urgencyMultiplier = 1.5;
      reasons.push('जरूरी काम'); // Urgent work
      break;
    case 'high':
      urgencyScore = 7;
      urgencyMultiplier = 1.2;
      reasons.push('तुरंत चाहिए'); // Needed soon
      break;
    case 'normal':
      urgencyScore = 5;
      break;
  }
  score += urgencyScore;
  
  // Experience compatibility bonus (5% weight)
  if (job.min_experience_years <= persona.experience_years) {
    score += 5;
    reasons.push('अनुभव मैच'); // Experience matches
  }
  
  // Recency bonus (newer jobs get slight preference)
  const hoursOld = (Date.now() - new Date(job.created_at).getTime()) / (1000 * 60 * 60);
  if (hoursOld < 2) {
    score += 3;
    reasons.push('नया जॉब'); // New job
  }
  
  // Calculate estimated earnings
  const baseEarnings = (job.budget_min + job.budget_max) / 2;
  const estimated_earnings = Math.round(baseEarnings * urgencyMultiplier);
  
  return {
    score: Math.min(100, Math.round(score)),
    reasons,
    estimated_earnings,
    urgency_multiplier: urgencyMultiplier
  };
}

function calculateRateCompatibility(job: any, persona: any) {
  const jobMaxBudget = job.budget_max;
  const personaRate = persona.daily_rate;
  
  if (!personaRate) {
    return { score: 10, reason: 'रेट negotiable' }; // Rate negotiable
  }
  
  if (jobMaxBudget >= personaRate) {
    return { score: 20, reason: 'आपके रेट से मैच' }; // Matches your rate
  } else if (jobMaxBudget >= personaRate * 0.8) {
    return { score: 15, reason: 'रेट करीब है' }; // Rate is close
  } else if (jobMaxBudget >= personaRate * 0.6) {
    return { score: 8, reason: 'कम बजट' }; // Lower budget
  } else {
    return { score: 0, reason: 'बजट बहुत कम' }; // Budget too low
  }
}
```

### 2.2 Worker Notification System

```typescript
// supabase/functions/worker-notifications/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

interface NotificationRequest {
  type: 'new_job' | 'job_update' | 'application_response' | 'review_received';
  recipient_ids?: string[]; // Specific users
  job_id?: string;
  application_id?: string;
  custom_message?: {
    title: string;
    body: string;
  };
  urgency?: 'low' | 'normal' | 'high';
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { type, recipient_ids, job_id, application_id, custom_message, urgency = 'normal' }: NotificationRequest = await req.json();
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );
    
    let notifications = [];
    
    switch (type) {
      case 'new_job':
        notifications = await handleNewJobNotifications(supabase, job_id, urgency);
        break;
        
      case 'application_response':
        notifications = await handleApplicationResponseNotifications(supabase, application_id);
        break;
        
      case 'review_received':
        notifications = await handleReviewNotifications(supabase, application_id);
        break;
        
      case 'job_update':
        notifications = await handleJobUpdateNotifications(supabase, job_id);
        break;
        
      default:
        throw new Error(`Unknown notification type: ${type}`);
    }
    
    // Send notifications (batch processing)
    const results = await Promise.allSettled(
      notifications.map(notification => sendNotification(notification))
    );
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    
    return new Response(JSON.stringify({
      success: true,
      sent: successful,
      failed: failed,
      total: notifications.length
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Notification error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

async function handleNewJobNotifications(supabase: any, job_id: string, urgency: string) {
  // Get job details
  const { data: job, error: jobError } = await supabase
    .from('jobs')
    .select('*')
    .eq('id', job_id)
    .single();
    
  if (jobError) throw jobError;
  
  // Find matching workers within radius
  const { data: matchingPersonas, error: personasError } = await supabase
    .rpc('find_workers_for_job_notification', {
      job_skill_category: job.skill_category,
      job_location: job.location,
      max_distance_km: urgency === 'high' ? 20 : 15
    });
    
  if (personasError) throw personasError;
  
  const notifications = matchingPersonas.map(persona => ({
    user_id: persona.user_id,
    title: urgency === 'high' ? '⚡ जरूरी काम!' : '💼 नया काम मिला',
    body: `${job.title} - ₹${job.budget_min}-${job.budget_max}`,
    data: {
      type: 'job_match',
      job_id: job.id,
      distance: persona.distance_km,
      urgency: job.urgency
    },
    channel: 'job_match',
    language: persona.preferred_language
  }));
  
  return notifications;
}

async function handleApplicationResponseNotifications(supabase: any, application_id: string) {
  const { data: application, error } = await supabase
    .from('job_applications')
    .select(`
      *,
      jobs(title, budget_max),
      worker_personas!inner(user_id, users!inner(preferred_language))
    `)
    .eq('id', application_id)
    .single();
    
  if (error) throw error;
  
  const isAccepted = application.status === 'accepted';
  
  return [{
    user_id: application.worker_personas.user_id,
    title: isAccepted ? '🎉 काम मिल गया!' : '😞 काम नहीं मिला',
    body: isAccepted 
      ? `${application.jobs.title} के लिए select हुए हैं`
      : `${application.jobs.title} के लिए select नहीं हुए`,
    data: {
      type: 'application_response',
      job_id: application.job_id,
      status: application.status
    },
    channel: 'application_update',
    language: application.worker_personas.users.preferred_language
  }];
}

async function sendNotification(notification: any) {
  // In a real implementation, this would integrate with:
  // - Firebase Cloud Messaging for push notifications
  // - In-app notification system
  // - SMS fallback for critical notifications
  
  console.log(`Sending notification to ${notification.user_id}:`, notification);
  
  // For now, we'll store in database for in-app notifications
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );
  
  return await supabase
    .from('notifications')
    .insert({
      user_id: notification.user_id,
      title: notification.title,
      body: notification.body,
      data: notification.data,
      channel: notification.channel,
      is_read: false,
      created_at: new Date().toISOString()
    });
}
```

### 2.3 Trust Score Calculation

```typescript
// supabase/functions/trust-calculation/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

interface TrustScoreRequest {
  user_id?: string;
  worker_persona_id?: string;
  recalculate_all?: boolean;
}

interface TrustScore {
  overall_score: number;
  persona_scores?: Record<string, number>;
  factors: {
    completion_rate: number;
    average_rating: number;
    review_count: number;
    profile_completeness: number;
    response_time: number;
    consistency_score: number;
  };
  level: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  next_level_requirements: string[];
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { user_id, worker_persona_id, recalculate_all = false }: TrustScoreRequest = await req.json();
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );
    
    let trustScores = [];
    
    if (recalculate_all) {
      // Recalculate for all active personas (admin function)
      const { data: allPersonas } = await supabase
        .from('worker_personas')
        .select('id, user_id')
        .eq('is_active', true);
        
      for (const persona of allPersonas || []) {
        const score = await calculateTrustScore(supabase, persona.user_id, persona.id);
        trustScores.push({ persona_id: persona.id, ...score });
        
        // Update persona trust score
        await supabase
          .from('worker_personas')
          .update({ 
            trust_score: score.overall_score,
            trust_level: score.level,
            updated_at: new Date().toISOString()
          })
          .eq('id', persona.id);
      }
    } else if (worker_persona_id) {
      // Calculate for specific persona
      const { data: persona } = await supabase
        .from('worker_personas')
        .select('user_id')
        .eq('id', worker_persona_id)
        .single();
        
      if (persona) {
        const score = await calculateTrustScore(supabase, persona.user_id, worker_persona_id);
        trustScores.push({ persona_id: worker_persona_id, ...score });
        
        // Update persona
        await supabase
          .from('worker_personas')
          .update({ 
            trust_score: score.overall_score,
            trust_level: score.level,
            updated_at: new Date().toISOString()
          })
          .eq('id', worker_persona_id);
      }
    } else if (user_id) {
      // Calculate for all user's personas
      const { data: userPersonas } = await supabase
        .from('worker_personas')
        .select('id')
        .eq('user_id', user_id)
        .eq('is_active', true);
        
      for (const persona of userPersonas || []) {
        const score = await calculateTrustScore(supabase, user_id, persona.id);
        trustScores.push({ persona_id: persona.id, ...score });
        
        await supabase
          .from('worker_personas')
          .update({ 
            trust_score: score.overall_score,
            trust_level: score.level,
            updated_at: new Date().toISOString()
          })
          .eq('id', persona.id);
      }
    }
    
    return new Response(JSON.stringify({
      success: true,
      trust_scores: trustScores
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Trust calculation error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

async function calculateTrustScore(supabase: any, user_id: string, persona_id: string): Promise<TrustScore> {
  // Get persona details
  const { data: persona } = await supabase
    .from('worker_personas')
    .select('*')
    .eq('id', persona_id)
    .single();
    
  // Get job applications and reviews
  const { data: applications } = await supabase
    .from('job_applications')
    .select(`
      *,
      jobs(status),
      reviews(rating, review_type)
    `)
    .eq('worker_persona_id', persona_id);
    
  // Calculate factors
  const factors = {
    completion_rate: calculateCompletionRate(applications || []),
    average_rating: persona.average_rating || 0,
    review_count: persona.total_reviews || 0,
    profile_completeness: calculateProfileCompleteness(persona),
    response_time: await calculateResponseTime(supabase, user_id),
    consistency_score: calculateConsistencyScore(applications || [])
  };
  
  // Weighted score calculation
  const overall_score = Math.round(
    factors.completion_rate * 0.25 +        // 25% weight
    (factors.average_rating / 5 * 100) * 0.20 + // 20% weight
    Math.min(factors.review_count * 10, 100) * 0.15 + // 15% weight, capped at 10 reviews
    factors.profile_completeness * 0.15 +    // 15% weight
    factors.response_time * 0.15 +           // 15% weight
    factors.consistency_score * 0.10         // 10% weight
  );
  
  // Determine level
  const level = getTrustLevel(overall_score);
  const next_level_requirements = getNextLevelRequirements(level, factors);
  
  return {
    overall_score,
    factors,
    level,
    next_level_requirements
  };
}

function calculateCompletionRate(applications: any[]): number {
  if (applications.length === 0) return 0;
  
  const acceptedApplications = applications.filter(app => app.status === 'accepted');
  const completedJobs = applications.filter(app => 
    app.status === 'accepted' && app.jobs?.status === 'filled'
  );
  
  if (acceptedApplications.length === 0) return 0;
  
  return (completedJobs.length / acceptedApplications.length) * 100;
}

function calculateProfileCompleteness(persona: any): number {
  let score = 0;
  const maxScore = 100;
  
  // Basic info (40 points)
  if (persona.title) score += 10;
  if (persona.description) score += 15;
  if (persona.profile_image_url) score += 15;
  
  // Experience details (30 points)
  if (persona.experience_years > 0) score += 15;
  if (persona.skill_subcategories && persona.skill_subcategories.length > 0) score += 15;
  
  // Pricing (20 points)
  if (persona.daily_rate || persona.hourly_rate) score += 20;
  
  // Availability (10 points)
  if (persona.availability_pattern && Object.keys(persona.availability_pattern).length > 0) score += 10;
  
  return score;
}

async function calculateResponseTime(supabase: any, user_id: string): number {
  // Get recent application response times
  const { data: recentApplications } = await supabase
    .from('job_applications')
    .select('applied_at, responded_at')
    .eq('worker_persona_id', user_id)
    .not('responded_at', 'is', null)
    .order('applied_at', { ascending: false })
    .limit(20);
    
  if (!recentApplications || recentApplications.length === 0) return 50; // Default score
  
  const responseTimes = recentApplications.map(app => {
    const applied = new Date(app.applied_at).getTime();
    const responded = new Date(app.responded_at).getTime();
    return (responded - applied) / (1000 * 60 * 60); // Hours
  });
  
  const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
  
  // Score based on average response time
  if (avgResponseTime <= 1) return 100;      // Within 1 hour
  if (avgResponseTime <= 6) return 80;       // Within 6 hours
  if (avgResponseTime <= 24) return 60;      // Within 1 day
  if (avgResponseTime <= 72) return 40;      // Within 3 days
  return 20; // More than 3 days
}

function calculateConsistencyScore(applications: any[]): number {
  if (applications.length < 5) return 70; // Default for new users
  
  // Calculate various consistency metrics
  const recentApps = applications.slice(0, 20); // Last 20 applications
  
  // Completion consistency
  const completionRates = recentApps.map(app => app.status === 'accepted' ? 1 : 0);
  const variance = calculateVariance(completionRates);
  
  // Lower variance = higher consistency
  return Math.max(0, 100 - (variance * 100));
}

function calculateVariance(values: number[]): number {
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
  return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
}

function getTrustLevel(score: number): 'Bronze' | 'Silver' | 'Gold' | 'Platinum' {
  if (score >= 90) return 'Platinum';
  if (score >= 75) return 'Gold';
  if (score >= 60) return 'Silver';
  return 'Bronze';
}

function getNextLevelRequirements(level: string, factors: any): string[] {
  const requirements = [];
  
  switch (level) {
    case 'Bronze':
      if (factors.completion_rate < 80) requirements.push('काम complete करने की rate 80% से ज्यादा करें');
      if (factors.average_rating < 4.0) requirements.push('4+ स्टार rating maintain करें');
      if (factors.review_count < 5) requirements.push('कम से कम 5 reviews लें');
      if (factors.profile_completeness < 80) requirements.push('प्रोफाइल 80% complete करें');
      break;
      
    case 'Silver':
      if (factors.completion_rate < 90) requirements.push('काम complete करने की rate 90% से ज्यादा करें');
      if (factors.average_rating < 4.3) requirements.push('4.3+ स्टार rating maintain करें');
      if (factors.review_count < 15) requirements.push('कम से कम 15 reviews लें');
      break;
      
    case 'Gold':
      if (factors.completion_rate < 95) requirements.push('काम complete करने की rate 95% से ज्यादा करें');
      if (factors.average_rating < 4.5) requirements.push('4.5+ स्टार rating maintain करें');
      if (factors.review_count < 30) requirements.push('कम से कम 30 reviews लें');
      if (factors.response_time < 90) requirements.push('जल्दी respond करें (1 घंटे के अंदर)');
      break;
  }
  
  return requirements;
}
```

## 3. Supporting Functions

### 3.1 Rate Suggestions Function

```typescript
// supabase/functions/rate-suggestions/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';
import { RateCalculator } from '../_shared/utils.ts';

interface RateSuggestionRequest {
  skill_category: string;
  location: {
    latitude: number;
    longitude: number;
  };
  urgency?: string;
  job_duration_hours?: number;
  time_of_posting?: string; // ISO datetime
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { skill_category, location, urgency = 'normal', job_duration_hours, time_of_posting }: RateSuggestionRequest = await req.json();
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );
    
    const timeOfDay = time_of_posting ? new Date(time_of_posting).getHours() : new Date().getHours();
    
    // Get market rates
    const marketRates = RateCalculator.calculateMarketRate(skill_category, location, urgency, timeOfDay);
    
    // Get recent job rates in the area for comparison
    const { data: recentJobs } = await supabase
      .rpc('get_nearby_jobs_rates', {
        skill_cat: skill_category,
        center_lat: location.latitude,
        center_lng: location.longitude,
        radius_km: 10,
        days_back: 30
      });
    
    // Calculate percentiles from recent jobs
    const recentRates = (recentJobs || []).map(job => (job.budget_min + job.budget_max) / 2);
    const percentiles = calculatePercentiles(recentRates);
    
    // Get average worker rates in the area
    const { data: workerRates } = await supabase
      .from('worker_personas')
      .select('daily_rate, hourly_rate, average_rating, total_jobs_completed')
      .eq('skill_category', skill_category)
      .not('daily_rate', 'is', null)
      .gte('average_rating', 3.5)
      .gte('total_jobs_completed', 3);
    
    const avgWorkerRate = workerRates?.reduce((sum, worker) => sum + (worker.daily_rate || 0), 0) / (workerRates?.length || 1);
    
    return new Response(JSON.stringify({
      success: true,
      suggestions: {
        market_based: marketRates,
        area_average: Math.round(avgWorkerRate || marketRates.suggested_rate),
        recent_jobs: {
          p25: percentiles.p25,
          p50: percentiles.p50,
          p75: percentiles.p75,
          sample_size: recentRates.length
        },
        recommendations: {
          budget_friendly: Math.round(marketRates.min_rate),
          competitive: Math.round(marketRates.suggested_rate),
          premium: Math.round(marketRates.max_rate),
          optimal: Math.round((marketRates.suggested_rate + avgWorkerRate) / 2)
        }
      },
      factors: {
        location_premium: RateCalculator.isMetroArea ? '30%' : '0%',
        urgency_multiplier: urgency,
        time_premium: (timeOfDay < 7 || timeOfDay > 20) ? '20%' : '0%'
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Rate suggestion error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

function calculatePercentiles(values: number[]) {
  if (values.length === 0) return { p25: 0, p50: 0, p75: 0 };
  
  const sorted = [...values].sort((a, b) => a - b);
  const n = sorted.length;
  
  return {
    p25: sorted[Math.floor(n * 0.25)],
    p50: sorted[Math.floor(n * 0.5)],
    p75: sorted[Math.floor(n * 0.75)]
  };
}
```

## 4. Database Functions (SQL)

```sql
-- Database functions to support Edge Functions

-- Function to find workers for job notifications
CREATE OR REPLACE FUNCTION find_workers_for_job_notification(
  job_skill_category skill_category_enum,
  job_location geography,
  max_distance_km integer DEFAULT 15
)
RETURNS TABLE (
  user_id uuid,
  persona_id uuid,
  distance_km numeric,
  preferred_language supported_language_enum
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    wp.user_id,
    wp.id as persona_id,
    ROUND((ST_Distance(u.location, job_location) / 1000.0)::numeric, 2) as distance_km,
    u.preferred_language
  FROM worker_personas wp
  JOIN users u ON wp.user_id = u.id
  WHERE wp.skill_category = job_skill_category
    AND wp.is_active = true
    AND u.location IS NOT NULL
    AND ST_DWithin(u.location, job_location, max_distance_km * 1000)
  ORDER BY ST_Distance(u.location, job_location);
END;
$$ LANGUAGE plpgsql;

-- Function to get nearby job rates for market analysis
CREATE OR REPLACE FUNCTION get_nearby_jobs_rates(
  skill_cat skill_category_enum,
  center_lat double precision,
  center_lng double precision,
  radius_km integer DEFAULT 10,
  days_back integer DEFAULT 30
)
RETURNS TABLE (
  job_id uuid,
  budget_min numeric,
  budget_max numeric,
  distance_km numeric,
  created_at timestamp
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    j.id as job_id,
    j.budget_min,
    j.budget_max,
    ROUND((ST_Distance(j.location, ST_SetSRID(ST_Point(center_lng, center_lat), 4326)) / 1000.0)::numeric, 2) as distance_km,
    j.created_at
  FROM jobs j
  WHERE j.skill_category = skill_cat
    AND j.created_at >= NOW() - (days_back || ' days')::interval
    AND ST_DWithin(j.location, ST_SetSRID(ST_Point(center_lng, center_lat), 4326), radius_km * 1000)
  ORDER BY j.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to update persona ratings when reviews are added
CREATE OR REPLACE FUNCTION update_persona_ratings()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    UPDATE worker_personas 
    SET 
      average_rating = (
        SELECT COALESCE(ROUND(AVG(rating)::numeric, 2), 0)
        FROM reviews 
        WHERE worker_persona_id = NEW.worker_persona_id 
        AND review_type = 'worker_review'
      ),
      total_reviews = (
        SELECT COUNT(*)
        FROM reviews 
        WHERE worker_persona_id = NEW.worker_persona_id 
        AND review_type = 'worker_review'
      ),
      updated_at = NOW()
    WHERE id = NEW.worker_persona_id;
    
    RETURN NEW;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS trigger_update_persona_ratings ON reviews;
CREATE TRIGGER trigger_update_persona_ratings
  AFTER INSERT OR UPDATE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_persona_ratings();
```

This comprehensive Edge Functions specification provides the complete business logic layer for the Ozgaar platform, handling job matching, notifications, trust calculations, and market rate suggestions with proper error handling and performance optimization.