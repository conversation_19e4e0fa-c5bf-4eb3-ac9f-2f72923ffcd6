---
type: "manual"
---

## **Document Information**

| Field | Value |
|-------|-------|
| **Product** | Ozgaar MVP - Trust-First Job & Gig Platform |
| **Version** | 2.0 (Enhanced Implementation Spec) |
| **Document Type** | Implementation-Ready PRD |
| **Author** | Senior Product Manager |
| **Status** | Development Ready |
| **Target Release** | Q4 2025 (MVP) |
| **Last Updated** | August 14, 2025 |
| **Dependencies** | None (Greenfield) |

***

## **1. Project Context & Real-World Constraints**

### **1.1 Market Reality Check**

**What We're Actually Building:**
- A hyperlocal job platform targeting India's **442 million** grey/blue-collar workers[1]
- MVP targeting **Delhi NCR** and **Hyderabad** - markets with established gig economy infrastructure
- Focus on **cash-heavy, trust-deficit** employment sectors where existing platforms fail

**Critical Market Constraints:**
- **Digital Literacy Gap**: 68% of target users have basic smartphone skills only
- **Language Barrier**: 73% prefer native language over English for transactions
- **Trust Deficit**: 85% of workers report payment delays/fraud in informal sector
- **Network Limitations**: Must work on 2G/3G networks (common in tier-2 areas)

### **1.2 Competitive Landscape Reality**

**Direct Competitors:**
- **UrbanCompany**: Premium service, expensive for target market
- **Taskrabbit/Dunzo**: Limited blue-collar focus, English-centric
- **Local WhatsApp Groups**: Primary current solution, zero trust mechanisms

**Differentiation Strategy:**
- **Persona-based profiles** (unique to market)
- **Cash payment verification** (addresses primary pain point)
- **Hyper-local matching** (sub-5km radius priority)
- **Native language UX** (8 languages from day 1)

***

## **2. Enhanced User Research & Persona Analysis**

### **2.1 Primary Research Insights**

**Field Research Data (Delhi NCR, July 2025):**
- **Sample Size**: 150 workers, 75 job posters
- **Key Finding**: Average time to find work = 4.2 days
- **Pain Point**: 67% report fake job postings on existing platforms
- **Opportunity**: 82% willing to pay ₹10-20 for verified job leads

### **2.2 Detailed Persona Specifications**

**Persona 1: Rajesh Kumar - Multi-Skilled Blue-Collar Worker**

| Attribute | Details |
|-----------|---------|
| **Demographics** | Age 28, Electrician + AC Repair, Gurgaon |
| **Income Goal** | ₹25,000-35,000/month |
| **Tech Comfort** | WhatsApp expert, basic smartphone, no email |
| **Languages** | Hindi (primary), Haryanvi (local), English (limited) |
| **Work Patterns** | 6 days/week, prefers morning start, travels up to 15km |
| **Pain Points** | Payment delays (73% of jobs), fake job posts, travel time waste |
| **Success Metrics** | 80% job completion rate, 4.2/5 average rating |

**Persona 2: Priya Sharma - Urban Professional (Job Poster)**

| Attribute | Details |
|-----------|---------|
| **Demographics** | Age 35, Working mother, Delhi |
| **Household Income** | ₹75,000/month |
| **Service Needs** | Domestic help, home repairs, elderly care |
| **Budget Range** | ₹500-2000/service |
| **Tech Comfort** | Smartphone savvy, uses multiple apps |
| **Languages** | Hindi, English |
| **Decision Factors** | Reliability > Cost, reviews crucial |
| **Pain Points** | Worker no-shows (45% experience), quality inconsistency |

**Persona 3: Amit Patel - Small Business Owner**

| Attribute | Details |
|-----------|---------|
| **Demographics** | Age 42, Restaurant owner, Hyderabad |
| **Business Size** | 15-20 covers, 3 staff |
| **Hiring Needs** | Kitchen help, delivery boys, cleaning staff |
| **Budget Constraints** | ₹12,000-18,000/month per employee |
| **Languages** | Telugu (primary), Hindi, English |
| **Work Hours** | 10am-11pm daily |
| **Hiring Frequency** | 2-3 positions per month |
| **Critical Needs** | Immediate availability, local candidates only |

***

## **3. Comprehensive Feature Specification**

### **3.1 Core User Stories with Implementation Details**

## **Epic 1: User Onboarding & Authentication**

**US-001: Phone Number Registration with Regional Optimization**

```
As a new user in India
I want to register using my phone number with minimal friction
So that I can access the platform immediately without barriers

DETAILED ACCEPTANCE CRITERIA:
✓ Phone input supports +91 country code (locked, no selection needed)
✓ 10-digit validation with real-time formatting (98765-43210)
✓ OTP delivery within 15 seconds (Firebase Auth + SMS backup)
✓ OTP verification with 3 attempts, 5-minute expiry
✓ Auto-progression to language selection on success
✓ Offline capability: Store partial data, sync on connectivity
✓ Error handling: Clear messages in user's language
✓ Analytics: Track completion rates by city/carrier

TECHNICAL IMPLEMENTATION:
- Firebase Authentication for OTP
- Twilio SMS as backup provider
- Rate limiting: 3 OTP attempts per phone/hour
- Store registration attempt data for analytics
```

**US-002: Smart Language Selection with Regional Defaults**

```
As a user with varying language preferences
I want intelligent language detection and easy switching
So that I can use the app in my most comfortable language

DETAILED ACCEPTANCE CRITERIA:
✓ Auto-detect regional language based on phone number area code
✓ Display 8 languages with native script + English names
✓ Language grid with flag icons and native text samples
✓ Immediate UI translation preview on selection
✓ Language preference stored locally + synced to profile
✓ Easy language switching in settings (no re-authentication)
✓ Fallback to English if translation missing
✓ Voice input support detection per language

TECHNICAL IMPLEMENTATION:
- i18next for translation management
- React Native Localize for device language detection
- Custom translation keys for Indian context
- Font support for Devanagari, Tamil, Telugu scripts
- AsyncStorage for offline language persistence
```

## **Epic 2: Advanced Worker Profile & Persona System**

**US-003: Multi-Persona Profile Architecture**

```
As a multi-skilled worker
I want to create distinct professional identities for different skills
So that I can get targeted job recommendations and maintain separate reputations

DETAILED ACCEPTANCE CRITERIA:
✓ Create up to 5 personas per account (electrician, driver, cook, etc.)
✓ Each persona has independent: skills, rates, availability, reviews
✓ Primary persona selection affects dashboard job recommendations
✓ Quick persona switching without logout
✓ Persona-specific photo uploads (work environment shots)
✓ Individual availability calendars per persona
✓ Cross-persona analytics dashboard
✓ Persona performance comparison metrics

TECHNICAL IMPLEMENTATION:
- Normalized database schema for persona separation
- Redux state management for persona switching
- Separate review aggregation per persona
- Background job matching for all active personas
```

**US-004: Smart Availability Management**

```
As a worker with varying schedule
I want to set complex availability patterns
So that I only receive relevant job notifications

DETAILED ACCEPTANCE CRITERIA:
✓ Weekly availability grid (7 days × 4 time slots)
✓ Holiday/vacation blocking with advance notice
✓ Emergency availability toggle (available now for 2-4 hours)
✓ Recurring availability patterns (weekdays only, weekends, etc.)
✓ Travel radius settings per time slot
✓ Automatic unavailability when job accepted
✓ Bulk availability updates for multiple personas

BUSINESS LOGIC:
- Jobs matched only during available hours
- Notifications sent 1 hour before available slots
- Overtime availability (20% rate increase) for urgent jobs
```

## **Epic 3: Advanced Job Posting & Management**

**US-005: Intelligent Job Posting with Auto-Enhancement**

```
As a job poster with limited time
I want the system to help me create comprehensive job posts
So that I attract the right candidates efficiently

DETAILED ACCEPTANCE CRITERIA:
✓ Voice-to-text job description with Hindi/English support
✓ Auto-suggested titles based on description keywords
✓ Smart category detection from job description
✓ Rate suggestions based on local market data
✓ Photo upload with automatic compression and orientation
✓ Location auto-complete with landmark recognition
✓ Urgency level selection affects worker notification priority
✓ Budget range validation against market rates

SMART FEATURES:
- ML-based category suggestion (trained on Indian job data)
- Dynamic rate recommendations based on location/time
- Duplicate job detection to prevent spam
- Auto-translation of job descriptions to worker's preferred language
```

**US-006: Job Performance Analytics for Posters**

```
As a frequent job poster
I want insights into my job posting performance
So that I can optimize my hiring success rate

DETAILED ACCEPTANCE CRITERIA:
✓ Application rate tracking (views → applications)
✓ Time-to-hire analytics
✓ Worker quality scores (completion rate, ratings)
✓ Cost-per-hire analysis
✓ Reposting suggestions for low-performing jobs
✓ Optimal posting time recommendations
✓ Similar job benchmarking

ANALYTICS DASHBOARD:
- Job post reach and engagement metrics
- Worker response time analysis
- Success rate by job category and time posted
- ROI calculation for recurring job posters
```

### **3.2 Matching Algorithm Specification**

**Core Matching Parameters (Weighted):**

| Factor | Weight | Logic |
|--------|--------|-------|
| **Distance** | 40% | 0-5km (100%), 5-15km (60%), 15-30km (20%) |
| **Skill Match** | 30% | Exact match (100%), related skill (70%) |
| **Availability** | 15% | Available now (100%), available today (80%) |
| **Rating** | 10% | 4.5+ stars (100%), 4-4.5 (90%), >'lat')::float), ((location->>'lng')::float)),
    INDEX idx_users_active (is_active, last_active_at)
);

-- Enum for user roles
CREATE TYPE user_role_enum AS ENUM ('worker', 'poster', 'both');
```

**Worker Personas Table (Core Feature)**

```sql
CREATE TABLE worker_personas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(100) NOT NULL, -- "Experienced Electrician", "Part-time Driver"
    skill_category skill_category_enum NOT NULL,
    skill_subcategory VARCHAR(50),
    description TEXT,
    experience_years INTEGER CHECK (experience_years >= 0),
    
    -- Rate Structure (Indian market specific)
    hourly_rate DECIMAL(8,2), -- In INR
    daily_rate DECIMAL(8,2),
    monthly_rate DECIMAL(10,2),
    rate_negotiable BOOLEAN DEFAULT true,
    
    -- Availability (Complex Indian work patterns)
    availability JSONB NOT NULL, -- Complex weekly schedule
    travel_radius INTEGER DEFAULT 10, -- km
    vehicle_type vehicle_enum, -- For delivery/driver personas
    
    -- Performance Metrics
    total_jobs_completed INTEGER DEFAULT 0,
    total_earnings DECIMAL(12,2) DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0,
    total_reviews INTEGER DEFAULT 0,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    verification_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_rating CHECK (average_rating >= 0 AND average_rating >'lat')::float), ((location->>'lng')::float)),
    INDEX idx_jobs_category_status (category, status, created_at),
    INDEX idx_jobs_poster (poster_id, status),
    INDEX idx_jobs_urgency_status (urgency, status, created_at)
);

-- Job-related enums
CREATE TYPE job_type_enum AS ENUM ('one_time', 'recurring', 'permanent', 'trial');
CREATE TYPE urgency_enum AS ENUM ('low', 'normal', 'high', 'urgent');
CREATE TYPE rate_type_enum AS ENUM ('hourly', 'daily', 'weekly', 'monthly', 'fixed', 'range');
CREATE TYPE payment_method_enum AS ENUM ('cash', 'upi', 'bank_transfer', 'cheque');
CREATE TYPE location_type_enum AS ENUM ('on_site', 'remote', 'hybrid');
CREATE TYPE gender_preference_enum AS ENUM ('any', 'male', 'female');
CREATE TYPE job_status_enum AS ENUM ('draft', 'active', 'paused', 'filled', 'cancelled', 'expired');
```

***

## **6. Implementation Strategy & Risk Mitigation**

### **6.1 Real-World Development Challenges**

**Technical Debt Prevention:**

- **Code Quality Gates**: ESLint + Prettier enforced in CI/CD
- **Database Migration Strategy**: All schema changes versioned and reversible
- **API Versioning**: /v1/ namespace from day 1, deprecation policy defined
- **Testing Coverage**: Minimum 80% unit test coverage enforced
- **Performance Monitoring**: New Relic APM integration from MVP launch

**Indian Market Specific Challenges:**

| Challenge | Risk Level | Mitigation Strategy |
|-----------|------------|-------------------|
| **Network Connectivity** | High | Offline-first architecture, data synchronization |
| **Device Fragmentation** | Medium | Support Android 7+ (API 24+), tested on budget devices |
| **Language Complexity** | High | Native speakers for translation review, cultural context |
| **Payment Fraud** | High | Photo verification, manual review for suspicious activity |
| **Fake Profiles** | Medium | Phone verification + social proof system |
| **Regulatory Compliance** | Medium | Legal review for labor law compliance |

### **6.2 Launch Strategy with Indian Market Focus**

**Phase 1: Controlled Beta (Week 9-10)**
- **Target**: 50 workers + 25 job posters in Gurgaon sector 14-17
- **Focus**: Core functionality validation, payment flow testing
- **Success Criteria**: 70% onboarding completion, 80% first job application rate

**Phase 2: Local Market Expansion (Week 11-12)**
- **Target**: 200 users across Gurgaon, Delhi South, Noida
- **Focus**: Cross-area job matching, travel distance optimization
- **Success Criteria**: 60% job application to completion rate

**Phase 3: City-Wide Launch (Month 2)**
- **Target**: 1,000 users across Delhi NCR
- **Focus**: Scale testing, trust system validation
- **Success Criteria**: 4.0+ average platform rating, 50% repeat usage

**Hyderabad Launch (Month 3-4)**
- **Target**: Replicate Delhi NCR success in Telugu-speaking market
- **Focus**: Language localization effectiveness, cultural adaptation
- **Success Criteria**: 80% feature parity with Delhi metrics

### **6.3 Success Metrics Framework**

**North Star Metrics:**

| Metric | MVP Target | 3-Month Target | 6-Month Target |
|--------|------------|----------------|----------------|
| **Active Workers** | 500 | 2,000 | 8,000 |
| **Job Success Rate** | 60% | 75% | 80% |
| **Worker Monthly Earnings** | ₹15,000 avg | ₹20,000 avg | ₹25,000 avg |
| **Platform Rating** | 4.0+ | 4.2+ | 4.5+ |
| **Geographic Coverage** | 3 areas | 15 areas | 2 cities |

**Leading Indicators:**

- **Onboarding Completion Rate**: >80% (indicates UX effectiveness)
- **Time to First Job Application**: 90% (indicates engagement)
- **Repeat Job Posting Rate**: >40% (indicates poster satisfaction)

**Business Intelligence Dashboard:**

- Daily active users by persona type and location
- Job posting to application conversion rates
- Worker earnings distribution and trends
- Trust score improvements over time
- Customer acquisition cost by channel

***

## **7. Post-MVP Roadmap & Scaling Strategy**

### **7.1 Feature Roadmap (Next 12 Months)**

**Quarter 1 Post-MVP (Month 1-3):**
- WhatsApp integration for notifications
- In-app chat system with translation
- Advanced search and filtering
- Worker skill verification badges

**Quarter 2 (Month 4-6):**
- Payment escrow system integration
- Video profile introductions
- Referral reward system
- Basic analytics dashboard for frequent posters

**Quarter 3 (Month 7-9):**
- AI-powered job matching optimization
- Seasonal work prediction system
- Integration with local skill training centers
- Corporate client onboarding system

**Quarter 4 (Month 10-12):**
- Expansion to 5 major cities (Mumbai, Pune, Chennai, Bangalore, Kolkata)
- Advanced reputation system with skill endorsements
- Integration with government employment schemes
- White-collar job categories pilot

### **7.2 Technology Evolution Plan**

**Scaling Architecture:**

- **Database**: PostgreSQL → PostgreSQL cluster with read replicas
- **Storage**: S3 → S3 + CloudFront + image optimization
- **Search**: Basic filtering → Elasticsearch with ML ranking
- **Matching**: Rule-based → Machine learning with user behavior data
- **Analytics**: Basic metrics → Real-time analytics with predictive insights

**Mobile App Evolution:**

- **Platform Expansion**: Android → iOS → Progressive Web App
- **Features**: Core MVP → Advanced features → AI-powered assistance
- **Offline Capability**: Basic → Full offline mode with sync
- **Performance**: Standard → Optimized for low-end devices
