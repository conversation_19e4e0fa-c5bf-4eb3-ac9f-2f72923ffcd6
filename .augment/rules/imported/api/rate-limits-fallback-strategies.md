---
type: "manual"
---

# API Rate Limits & Fallback Strategies

## Overview

This document defines comprehensive rate limiting policies and fallback strategies for all APIs and external services used by the Ozgaar platform, ensuring reliable service delivery even under high load or service degradation scenarios.

## 1. Internal API Rate Limiting

### 1.1 Supabase API Rate Limits

**Default Limits by Plan:**

| Resource | Free Tier | Pro Tier | Enterprise |
|----------|-----------|----------|------------|
| **Database Requests** | 500/minute | 1000/minute | Custom |
| **Auth Requests** | 200/hour | 10,000/hour | Custom |
| **Storage Requests** | 200/minute | 1000/minute | Custom |
| **Edge Function Invocations** | 500/month | 2M/month | Custom |
| **Realtime Connections** | 200 concurrent | 500 concurrent | Custom |
| **Database Size** | 500 MB | 8 GB | Custom |

**Rate Limiting Implementation:**

```typescript
// src/services/api/rateLimiter.ts
export class RateLimiter {
  private static requestCounts = new Map<string, { count: number; resetTime: number }>();
  private static readonly WINDOW_SIZE = 60 * 1000; // 1 minute window

  static async checkRateLimit(
    endpoint: string, 
    userId: string, 
    limit: number
  ): Promise<{ allowed: boolean; retryAfter?: number }> {
    const key = `${endpoint}:${userId}`;
    const now = Date.now();
    
    const current = this.requestCounts.get(key);
    
    if (!current || now >= current.resetTime) {
      // Reset window
      this.requestCounts.set(key, { count: 1, resetTime: now + this.WINDOW_SIZE });
      return { allowed: true };
    }
    
    if (current.count >= limit) {
      const retryAfter = Math.ceil((current.resetTime - now) / 1000);
      return { allowed: false, retryAfter };
    }
    
    current.count++;
    return { allowed: true };
  }

  // Rate limiting decorator for service methods
  static withRateLimit(endpoint: string, limit: number) {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
      const method = descriptor.value;

      descriptor.value = async function (...args: any[]) {
        const userId = this.getCurrentUserId?.() || 'anonymous';
        const rateLimitResult = await RateLimiter.checkRateLimit(endpoint, userId, limit);
        
        if (!rateLimitResult.allowed) {
          throw new AppError({
            code: 'RATE_LIMIT_EXCEEDED',
            category: ErrorCategory.SYSTEM,
            severity: ErrorSeverity.MEDIUM,
            message: `Rate limit exceeded for ${endpoint}`,
            userMessage: {
              hindi: 'बहुत सारे requests भेजे गए। कुछ देर बाद कोशिश करें।',
              english: 'Too many requests. Please try again later.'
            },
            retryable: true,
            autoRetry: true,
            maxRetries: 1,
            retryAfter: rateLimitResult.retryAfter
          });
        }

        return method.apply(this, args);
      };
    };
  }
}

// Usage example
export class JobService {
  @RateLimiter.withRateLimit('job_applications', 10) // 10 applications per minute
  async applyToJob(jobId: string, personaId: string) {
    // Implementation
  }

  @RateLimiter.withRateLimit('job_search', 30) // 30 searches per minute
  async searchJobs(criteria: SearchCriteria) {
    // Implementation
  }
}
```

### 1.2 Priority-Based Rate Limiting

```typescript
// src/services/api/priorityRateLimiter.ts
export enum RequestPriority {
  CRITICAL = 'critical',     // Authentication, safety
  HIGH = 'high',            // Job applications, messaging
  MEDIUM = 'medium',        // Job browsing, profile updates
  LOW = 'low'               // Analytics, non-essential features
}

export class PriorityRateLimiter {
  private static quotas = {
    [RequestPriority.CRITICAL]: { limit: 100, window: 60000 },
    [RequestPriority.HIGH]: { limit: 50, window: 60000 },
    [RequestPriority.MEDIUM]: { limit: 30, window: 60000 },
    [RequestPriority.LOW]: { limit: 10, window: 60000 }
  };

  static async checkPriorityLimit(
    userId: string,
    priority: RequestPriority
  ): Promise<{ allowed: boolean; reason?: string }> {
    const quota = this.quotas[priority];
    const key = `${userId}:${priority}`;
    
    // Check current usage
    const usage = await this.getCurrentUsage(key);
    
    if (usage >= quota.limit) {
      return { 
        allowed: false, 
        reason: `Priority ${priority} quota exceeded` 
      };
    }

    // Check if lower priority requests are consuming too many resources
    if (priority === RequestPriority.LOW) {
      const totalUsage = await this.getTotalUsage(userId);
      const maxTotal = Object.values(this.quotas).reduce((sum, q) => sum + q.limit, 0);
      
      if (totalUsage > maxTotal * 0.8) { // 80% of total capacity
        return { 
          allowed: false, 
          reason: 'System under high load, low priority requests throttled' 
        };
      }
    }

    await this.incrementUsage(key);
    return { allowed: true };
  }
}
```

## 2. External Service Rate Limits & Management

### 2.1 Google Maps API Rate Limiting

**Service Limits:**
- Geocoding API: 50 requests/second, 40,000 requests/day
- Distance Matrix API: 100 elements/10 seconds  
- Places API: 100 requests/10 seconds

**Implementation:**

```typescript
// src/services/external/googleMapsService.ts
export class GoogleMapsService {
  private static geocodingLimiter = new TokenBucket(50, 1000); // 50 req/sec
  private static distanceMatrixLimiter = new TokenBucket(10, 10000); // 10 req/10sec
  private static placesLimiter = new TokenBucket(10, 10000); // 10 req/10sec

  @withFallback(GoogleMapsService.geocodeFallback)
  @withRetry({ maxRetries: 3, backoff: 'exponential' })
  static async geocodeAddress(address: string): Promise<GeocodeResult> {
    await this.geocodingLimiter.consume(1);
    
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?` +
        `address=${encodeURIComponent(address)}&` +
        `key=${process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY}&` +
        `region=IN&language=en`
      );
      
      if (response.status === 429) {
        throw new RateLimitError('Google Maps API rate limit exceeded');
      }
      
      const data = await response.json();
      
      if (data.status === 'OVER_QUERY_LIMIT') {
        throw new RateLimitError('Google Maps API quota exceeded');
      }
      
      return this.parseGeocodeResponse(data);
    } catch (error) {
      if (error instanceof RateLimitError) {
        // Wait and retry
        await new Promise(resolve => setTimeout(resolve, 1000));
        throw error;
      }
      
      throw new ExternalServiceError('Geocoding failed', error);
    }
  }

  // Fallback for geocoding using cached data
  private static async geocodeFallback(address: string): Promise<GeocodeResult> {
    // Try cached geocoding results first
    const cached = await CacheService.get(`geocode:${address}`);
    if (cached) {
      return cached;
    }

    // Fallback to approximate location based on city/area keywords
    return this.approximateGeocode(address);
  }

  private static async approximateGeocode(address: string): Promise<GeocodeResult> {
    const cityMapping = {
      'delhi': { lat: 28.6139, lng: 77.2090 },
      'gurgaon': { lat: 28.4595, lng: 77.0266 },
      'noida': { lat: 28.5355, lng: 77.3910 },
      'hyderabad': { lat: 17.3850, lng: 78.4867 },
      'mumbai': { lat: 19.0760, lng: 72.8777 }
    };

    const addressLower = address.toLowerCase();
    for (const [city, coords] of Object.entries(cityMapping)) {
      if (addressLower.includes(city)) {
        return {
          latitude: coords.lat,
          longitude: coords.lng,
          formatted_address: `Approximate location in ${city}`,
          accuracy: 'city_level'
        };
      }
    }

    // Default to Delhi if no city match
    return {
      latitude: 28.6139,
      longitude: 77.2090,
      formatted_address: 'Approximate location in Delhi NCR',
      accuracy: 'region_level'
    };
  }
}

class TokenBucket {
  private tokens: number;
  private readonly capacity: number;
  private readonly refillRate: number;
  private lastRefill: number;

  constructor(capacity: number, refillInterval: number) {
    this.capacity = capacity;
    this.tokens = capacity;
    this.refillRate = capacity / refillInterval * 1000; // tokens per second
    this.lastRefill = Date.now();
  }

  async consume(tokens: number): Promise<void> {
    await this.refill();
    
    if (this.tokens < tokens) {
      const waitTime = (tokens - this.tokens) / this.refillRate * 1000;
      await new Promise(resolve => setTimeout(resolve, waitTime));
      await this.refill();
    }
    
    this.tokens -= tokens;
  }

  private async refill(): Promise<void> {
    const now = Date.now();
    const timePassed = (now - this.lastRefill) / 1000;
    const tokensToAdd = timePassed * this.refillRate;
    
    this.tokens = Math.min(this.capacity, this.tokens + tokensToAdd);
    this.lastRefill = now;
  }
}
```

### 2.2 SMS Provider Rate Limiting

**Twilio Limits:**
- 1 request/second per phone number (OTP)
- 100 SMS/day per trial account
- 10,000 SMS/day per paid account

**MSG91 Limits:**
- 100 SMS/minute
- 10,000 SMS/day (standard plan)

```typescript
// src/services/external/smsService.ts
export class SMSService {
  private static 2factorLimiter = new Map<string, number>(); // phone -> lastSent
  private static msg91Limiter = new TokenBucket(100, 60000); // 100/minute
  private static dailyQuota = new DailyQuotaTracker(10000);

  static async sendOTP(phoneNumber: string, otp: string): Promise<SMSResult> {
    // Check daily quota
    if (!await this.dailyQuota.canConsume(1)) {
      throw new QuotaExceededError('Daily SMS quota exceeded');
    }

    // Primary provider: Twilio with per-phone rate limiting
    try {
      const canSendTwilio = this.checkTwilioRateLimit(phoneNumber);
      if (canSendTwilio) {
        const result = await this.sendTwilioSMS(phoneNumber, otp);
        await this.dailyQuota.consume(1);
        return result;
      }
    } catch (error) {
      console.warn('Twilio SMS failed:', error);
    }

    // Fallback: MSG91 with token bucket limiting
    try {
      await this.msg91Limiter.consume(1);
      const result = await this.sendMSG91SMS(phoneNumber, otp);
      await this.dailyQuota.consume(1);
      return result;
    } catch (error) {
      console.error('MSG91 SMS failed:', error);
      throw new ExternalServiceError('All SMS providers failed', error);
    }
  }

  private static checkTwilioRateLimit(phoneNumber: string): boolean {
    const now = Date.now();
    const lastSent = this.2factorLimiter.get(phoneNumber) || 0;
    
    if (now - lastSent < 60000) { // 1 minute cooldown per phone
      return false;
    }
    
    this.2factorLimiter.set(phoneNumber, now);
    return true;
  }

  @withRetry({ maxRetries: 2 })
  private static async sendTwilioSMS(phoneNumber: string, otp: string): Promise<SMSResult> {
    const response = await fetch('https://api.2factor.com/2010-04-01/Accounts/{AccountSid}/Messages.json', {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${btoa(`${process.env.TWILIO_ACCOUNT_SID}:${process.env.TWILIO_AUTH_TOKEN}`)}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        From: process.env.TWILIO_FROM_NUMBER!,
        To: phoneNumber,
        Body: `आपका Ozgaar OTP: ${otp}। 5 मिनट में expire हो जाएगा। किसी को न बताएं।`
      })
    });

    if (response.status === 429) {
      throw new RateLimitError('Twilio rate limit exceeded');
    }

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Twilio API error: ${data.message}`);
    }

    return {
      success: true,
      messageId: data.sid,
      provider: '2factor'
    };
  }
}

class DailyQuotaTracker {
  private quota: number;
  private used: number = 0;
  private resetDate: string;

  constructor(dailyQuota: number) {
    this.quota = dailyQuota;
    this.resetDate = new Date().toDateString();
  }

  async canConsume(amount: number): Promise<boolean> {
    await this.checkReset();
    return this.used + amount <= this.quota;
  }

  async consume(amount: number): Promise<void> {
    await this.checkReset();
    if (this.used + amount > this.quota) {
      throw new Error('Daily quota exceeded');
    }
    this.used += amount;
    await this.persistUsage();
  }

  private async checkReset(): Promise<void> {
    const today = new Date().toDateString();
    if (today !== this.resetDate) {
      this.used = 0;
      this.resetDate = today;
      await this.persistUsage();
    }
  }

  private async persistUsage(): Promise<void> {
    // Store in AsyncStorage or database
    await AsyncStorage.setItem('sms_quota', JSON.stringify({
      used: this.used,
      date: this.resetDate
    }));
  }
}
```

## 3. Intelligent Caching Strategies

### 3.1 Multi-Level Caching

```typescript
// src/services/cache/multiLevelCache.ts
export class MultiLevelCache {
  private memoryCache = new Map<string, CacheEntry>();
  private readonly MAX_MEMORY_SIZE = 100;
  private readonly MEMORY_TTL = 5 * 60 * 1000; // 5 minutes

  async get(key: string): Promise<any> {
    // Level 1: Memory cache
    const memoryEntry = this.memoryCache.get(key);
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      return memoryEntry.value;
    }

    // Level 2: AsyncStorage
    try {
      const stored = await AsyncStorage.getItem(`cache:${key}`);
      if (stored) {
        const entry: CacheEntry = JSON.parse(stored);
        if (!this.isExpired(entry)) {
          // Promote to memory cache
          this.setMemory(key, entry.value, entry.ttl);
          return entry.value;
        }
      }
    } catch (error) {
      console.warn('AsyncStorage cache read failed:', error);
    }

    return null;
  }

  async set(key: string, value: any, ttl: number = this.MEMORY_TTL): Promise<void> {
    const entry: CacheEntry = {
      value,
      ttl,
      createdAt: Date.now()
    };

    // Set in memory cache
    this.setMemory(key, value, ttl);

    // Set in persistent storage for longer TTL
    if (ttl > this.MEMORY_TTL) {
      try {
        await AsyncStorage.setItem(`cache:${key}`, JSON.stringify(entry));
      } catch (error) {
        console.warn('AsyncStorage cache write failed:', error);
      }
    }
  }

  private setMemory(key: string, value: any, ttl: number): void {
    // Evict if memory cache is full
    if (this.memoryCache.size >= this.MAX_MEMORY_SIZE) {
      const oldestKey = Array.from(this.memoryCache.keys())[0];
      this.memoryCache.delete(oldestKey);
    }

    this.memoryCache.set(key, {
      value,
      ttl,
      createdAt: Date.now()
    });
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.createdAt > entry.ttl;
  }
}

interface CacheEntry {
  value: any;
  ttl: number;
  createdAt: number;
}
```

### 3.2 Smart Cache Warming

```typescript
// src/services/cache/cacheWarmingService.ts
export class CacheWarmingService {
  static async warmEssentialData(userId: string): Promise<void> {
    const warmingTasks = [
      this.warmUserProfile(userId),
      this.warmNearbyJobs(userId),
      this.warmSkillCategories(),
      this.warmLocationData(userId)
    ];

    // Run in background without blocking UI
    Promise.allSettled(warmingTasks).then(results => {
      const failed = results.filter(r => r.status === 'rejected').length;
      if (failed > 0) {
        console.warn(`${failed} cache warming tasks failed`);
      }
    });
  }

  private static async warmUserProfile(userId: string): Promise<void> {
    try {
      const profile = await UserService.getProfile(userId);
      await CacheService.set(`profile:${userId}`, profile, 10 * 60 * 1000); // 10 minutes
    } catch (error) {
      console.warn('Failed to warm user profile cache:', error);
    }
  }

  private static async warmNearbyJobs(userId: string): Promise<void> {
    try {
      const userLocation = await LocationService.getCurrentLocation();
      if (userLocation) {
        const jobs = await JobService.searchJobs({
          location: userLocation,
          radius: 15,
          limit: 20
        });
        await CacheService.set(`nearby_jobs:${userId}`, jobs, 5 * 60 * 1000); // 5 minutes
      }
    } catch (error) {
      console.warn('Failed to warm nearby jobs cache:', error);
    }
  }
}
```

## 4. Circuit Breaker Pattern

### 4.1 Service Circuit Breakers

```typescript
// src/services/resilience/circuitBreaker.ts
export enum CircuitState {
  CLOSED = 'closed',     // Normal operation
  OPEN = 'open',         // Failing, reject all requests
  HALF_OPEN = 'half_open' // Testing if service recovered
}

export class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount: number = 0;
  private successCount: number = 0;
  private nextAttempt: number = 0;

  constructor(
    private readonly failureThreshold: number = 5,
    private readonly recoveryTimeout: number = 60000, // 1 minute
    private readonly successThreshold: number = 3
  ) {}

  async execute<T>(operation: () => Promise<T>, fallback?: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (Date.now() < this.nextAttempt) {
        if (fallback) {
          return await fallback();
        }
        throw new CircuitOpenError('Service circuit breaker is open');
      } else {
        this.state = CircuitState.HALF_OPEN;
        this.successCount = 0;
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      
      if (fallback && this.state === CircuitState.OPEN) {
        return await fallback();
      }
      
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;
    
    if (this.state === CircuitState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= this.successThreshold) {
        this.state = CircuitState.CLOSED;
      }
    }
  }

  private onFailure(): void {
    this.failureCount++;
    
    if (this.failureCount >= this.failureThreshold) {
      this.state = CircuitState.OPEN;
      this.nextAttempt = Date.now() + this.recoveryTimeout;
    }
  }

  getState(): CircuitState {
    return this.state;
  }
}

// Service-specific circuit breakers
export class ServiceCircuitBreakers {
  private static breakers = new Map<string, CircuitBreaker>();

  static getBreaker(serviceName: string): CircuitBreaker {
    if (!this.breakers.has(serviceName)) {
      const config = this.getServiceConfig(serviceName);
      this.breakers.set(serviceName, new CircuitBreaker(
        config.failureThreshold,
        config.recoveryTimeout,
        config.successThreshold
      ));
    }
    return this.breakers.get(serviceName)!;
  }

  private static getServiceConfig(serviceName: string) {
    const configs = {
      'google_maps': { failureThreshold: 3, recoveryTimeout: 30000, successThreshold: 2 },
      'sms_service': { failureThreshold: 5, recoveryTimeout: 60000, successThreshold: 3 },
      'translation': { failureThreshold: 3, recoveryTimeout: 30000, successThreshold: 2 },
      'supabase': { failureThreshold: 5, recoveryTimeout: 10000, successThreshold: 3 }
    };
    
    return configs[serviceName] || { failureThreshold: 5, recoveryTimeout: 60000, successThreshold: 3 };
  }
}
```

## 5. Load Balancing & Request Distribution

### 5.1 Provider Load Balancing

```typescript
// src/services/loadBalancing/providerLoadBalancer.ts
export class ProviderLoadBalancer<T> {
  private providers: Provider<T>[] = [];
  private currentIndex: number = 0;

  addProvider(provider: Provider<T>, weight: number = 1): void {
    // Add multiple entries based on weight for weighted round-robin
    for (let i = 0; i < weight; i++) {
      this.providers.push(provider);
    }
  }

  async execute(operation: string, ...args: any[]): Promise<T> {
    let lastError: Error | null = null;
    const startIndex = this.currentIndex;

    do {
      const provider = this.providers[this.currentIndex];
      this.currentIndex = (this.currentIndex + 1) % this.providers.length;

      try {
        const circuitBreaker = ServiceCircuitBreakers.getBreaker(provider.name);
        return await circuitBreaker.execute(
          () => provider[operation](...args),
          provider.fallback ? () => provider.fallback![operation](...args) : undefined
        );
      } catch (error) {
        lastError = error;
        console.warn(`Provider ${provider.name} failed:`, error);
        continue;
      }
    } while (this.currentIndex !== startIndex);

    throw new Error(`All providers failed. Last error: ${lastError?.message}`);
  }
}

interface Provider<T> {
  name: string;
  fallback?: Provider<T>;
  [key: string]: any;
}

// SMS service load balancing example
export class LoadBalancedSMSService {
  private static loadBalancer = new ProviderLoadBalancer<SMSProvider>();

  static initialize(): void {
    this.loadBalancer.addProvider({
      name: '2factor',
      sendOTP: TwilioService.sendOTP.bind(TwilioService)
    }, 3); // Weight 3 (primary)

    this.loadBalancer.addProvider({
      name: 'msg91',
      sendOTP: MSG91Service.sendOTP.bind(MSG91Service)
    }, 2); // Weight 2 (secondary)
  }

  static async sendOTP(phoneNumber: string, otp: string): Promise<SMSResult> {
    return await this.loadBalancer.execute('sendOTP', phoneNumber, otp);
  }
}
```

## 6. Monitoring & Alerting

### 6.1 Rate Limit Monitoring

```typescript
// src/services/monitoring/rateLimitMonitor.ts
export class RateLimitMonitor {
  private static readonly ALERT_THRESHOLDS = {
    WARNING: 0.7,  // 70% of limit
    CRITICAL: 0.9  // 90% of limit
  };

  static async monitorUsage(): void {
    const services = ['google_maps', 'sms_service', 'supabase_api'];
    
    for (const service of services) {
      const usage = await this.getServiceUsage(service);
      const threshold = this.getServiceThreshold(service);
      
      const utilizationRate = usage.current / usage.limit;
      
      if (utilizationRate >= this.ALERT_THRESHOLDS.CRITICAL) {
        await this.sendAlert({
          level: 'CRITICAL',
          service,
          message: `${service} is at ${Math.round(utilizationRate * 100)}% capacity`,
          usage,
          recommendation: this.getRecommendation(service, utilizationRate)
        });
      } else if (utilizationRate >= this.ALERT_THRESHOLDS.WARNING) {
        await this.sendAlert({
          level: 'WARNING',
          service,
          message: `${service} is approaching rate limit`,
          usage,
          recommendation: 'Monitor closely'
        });
      }
    }
  }

  private static getRecommendation(service: string, utilization: number): string {
    if (utilization >= 0.95) {
      return 'Enable degraded mode and activate fallbacks immediately';
    } else if (utilization >= 0.8) {
      return 'Prepare fallback services and consider request throttling';
    }
    return 'Continue monitoring';
  }

  private static async sendAlert(alert: any): Promise<void> {
    // Send to monitoring service
    console.warn('Rate Limit Alert:', alert);
    
    // In production, send to Slack/PagerDuty/etc.
    if (process.env.NODE_ENV === 'production') {
      await fetch(process.env.ALERT_WEBHOOK_URL!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(alert)
      });
    }
  }
}
```

## 7. Testing Rate Limits

### 7.1 Rate Limit Testing

```typescript
// src/testing/rateLimits.test.ts
describe('Rate Limiting', () => {
  describe('Internal API Rate Limits', () => {
    it('should enforce job application rate limits', async () => {
      const userId = 'test-user-123';
      const jobId = 'test-job-456';
      const personaId = 'test-persona-789';

      // Apply to job 10 times quickly (should hit limit)
      const applications = Array(12).fill(0).map(() => 
        JobService.applyToJob(jobId, personaId)
      );

      const results = await Promise.allSettled(applications);
      const failed = results.filter(r => r.status === 'rejected');
      
      expect(failed.length).toBeGreaterThan(0);
      expect(failed[0].reason.code).toBe('RATE_LIMIT_EXCEEDED');
    });
  });

  describe('External Service Fallbacks', () => {
    it('should fallback to secondary SMS provider when primary fails', async () => {
      // Mock primary provider failure
      jest.spyOn(TwilioService, 'sendOTP').mockRejectedValue(
        new RateLimitError('Rate limit exceeded')
      );
      
      const mockMSG91 = jest.spyOn(MSG91Service, 'sendOTP').mockResolvedValue({
        success: true,
        messageId: 'msg91-123',
        provider: 'msg91'
      });

      const result = await SMSService.sendOTP('+919876543210', '123456');

      expect(result.provider).toBe('msg91');
      expect(mockMSG91).toHaveBeenCalled();
    });
  });
});
```

This comprehensive rate limiting and fallback strategy ensures the Ozgaar platform remains reliable and performant even under high load or external service failures.