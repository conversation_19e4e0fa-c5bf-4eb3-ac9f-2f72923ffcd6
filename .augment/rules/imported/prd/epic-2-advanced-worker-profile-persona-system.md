---
type: "manual"
---

# **Epic 2: Advanced Worker Profile & Persona System**

**US-003: Multi-Persona Profile Architecture**

```
As a multi-skilled worker
I want to create distinct professional identities for different skills
So that I can get targeted job recommendations and maintain separate reputations

DETAILED ACCEPTANCE CRITERIA:
✓ Create up to 5 personas per account (electrician, driver, cook, etc.)
✓ Each persona has independent: skills, rates, availability, reviews
✓ Primary persona selection affects dashboard job recommendations
✓ Quick persona switching without logout
✓ Persona-specific photo uploads (work environment shots)
✓ Individual availability calendars per persona
✓ Cross-persona analytics dashboard
✓ Persona performance comparison metrics

TECHNICAL IMPLEMENTATION:
- Normalized database schema for persona separation
- Redux state management for persona switching
- Separate review aggregation per persona
- Background job matching for all active personas
```

**US-004: Smart Availability Management**

```
As a worker with varying schedule
I want to set complex availability patterns
So that I only receive relevant job notifications

DETAILED ACCEPTANCE CRITERIA:
✓ Weekly availability grid (7 days × 4 time slots)
✓ Holiday/vacation blocking with advance notice
✓ Emergency availability toggle (available now for 2-4 hours)
✓ Recurring availability patterns (weekdays only, weekends, etc.)
✓ Travel radius settings per time slot
✓ Automatic unavailability when job accepted
✓ Bulk availability updates for multiple personas

BUSINESS LOGIC:
- Jobs matched only during available hours
- Notifications sent 1 hour before available slots
- Overtime availability (20% rate increase) for urgent jobs
```
