---
type: "manual"
---

# **Epic 3: Advanced Job Posting & Management**

**US-005: Intelligent Job Posting with Auto-Enhancement**

```
As a job poster with limited time
I want the system to help me create comprehensive job posts
So that I attract the right candidates efficiently

DETAILED ACCEPTANCE CRITERIA:
✓ Voice-to-text job description with Hindi/English support
✓ Auto-suggested titles based on description keywords
✓ Smart category detection from job description
✓ Rate suggestions based on local market data
✓ Photo upload with automatic compression and orientation
✓ Location auto-complete with landmark recognition
✓ Urgency level selection affects worker notification priority
✓ Budget range validation against market rates

SMART FEATURES:
- ML-based category suggestion (trained on Indian job data)
- Dynamic rate recommendations based on location/time
- Duplicate job detection to prevent spam
- Auto-translation of job descriptions to worker's preferred language
```

**US-006: Job Performance Analytics for Posters**

```
As a frequent job poster
I want insights into my job posting performance
So that I can optimize my hiring success rate

DETAILED ACCEPTANCE CRITERIA:
✓ Application rate tracking (views → applications)
✓ Time-to-hire analytics
✓ Worker quality scores (completion rate, ratings)
✓ Cost-per-hire analysis
✓ Reposting suggestions for low-performing jobs
✓ Optimal posting time recommendations
✓ Similar job benchmarking

ANALYTICS DASHBOARD:
- Job post reach and engagement metrics
- Worker response time analysis
- Success rate by job category and time posted
- ROI calculation for recurring job posters
```

## **3.2 Matching Algorithm Specification**

**Core Matching Parameters (Weighted):**

| Factor | Weight | Logic |
|--------|--------|-------|
| **Distance** | 40% | 0-5km (100%), 5-15km (60%), 15-30km (20%) |
| **Skill Match** | 30% | Exact match (100%), related skill (70%) |
| **Availability** | 15% | Available now (100%), available today (80%) |
| **Rating** | 10% | 4.5+ stars (100%), 4-4.5 (90%), >'lat')::float), ((location->>'lng')::float)),
    INDEX idx_users_active (is_active, last_active_at)
);

-- Enum for user roles
CREATE TYPE user_role_enum AS ENUM ('worker', 'poster', 'both');
```

**Worker Personas Table (Core Feature)**

```sql
CREATE TABLE worker_personas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(100) NOT NULL, -- "Experienced Electrician", "Part-time Driver"
    skill_category skill_category_enum NOT NULL,
    skill_subcategory VARCHAR(50),
    description TEXT,
    experience_years INTEGER CHECK (experience_years >= 0),
    
    -- Rate Structure (Indian market specific)
    hourly_rate DECIMAL(8,2), -- In INR
    daily_rate DECIMAL(8,2),
    monthly_rate DECIMAL(10,2),
    rate_negotiable BOOLEAN DEFAULT true,
    
    -- Availability (Complex Indian work patterns)
    availability JSONB NOT NULL, -- Complex weekly schedule
    travel_radius INTEGER DEFAULT 10, -- km
    vehicle_type vehicle_enum, -- For delivery/driver personas
    
    -- Performance Metrics
    total_jobs_completed INTEGER DEFAULT 0,
    total_earnings DECIMAL(12,2) DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0,
    total_reviews INTEGER DEFAULT 0,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    verification_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_rating CHECK (average_rating >= 0 AND average_rating >'lat')::float), ((location->>'lng')::float)),
    INDEX idx_jobs_category_status (category, status, created_at),
    INDEX idx_jobs_poster (poster_id, status),
    INDEX idx_jobs_urgency_status (urgency, status, created_at)
);

-- Job-related enums
CREATE TYPE job_type_enum AS ENUM ('one_time', 'recurring', 'permanent', 'trial');
CREATE TYPE urgency_enum AS ENUM ('low', 'normal', 'high', 'urgent');
CREATE TYPE rate_type_enum AS ENUM ('hourly', 'daily', 'weekly', 'monthly', 'fixed', 'range');
CREATE TYPE payment_method_enum AS ENUM ('cash', 'upi', 'bank_transfer', 'cheque');
CREATE TYPE location_type_enum AS ENUM ('on_site', 'remote', 'hybrid');
CREATE TYPE gender_preference_enum AS ENUM ('any', 'male', 'female');
CREATE TYPE job_status_enum AS ENUM ('draft', 'active', 'paused', 'filled', 'cancelled', 'expired');
```

***
