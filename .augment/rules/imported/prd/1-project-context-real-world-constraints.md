---
type: "manual"
---

# **1. Project Context & Real-World Constraints**

## **1.1 Market Reality Check**

**What We're Actually Building:**
- A hyperlocal job platform targeting India's **442 million** grey/blue-collar workers[1]
- MVP targeting **Delhi NCR** and **Hyderabad** - markets with established gig economy infrastructure
- Focus on **cash-heavy, trust-deficit** employment sectors where existing platforms fail

**Critical Market Constraints:**
- **Digital Literacy Gap**: 68% of target users have basic smartphone skills only
- **Language Barrier**: 73% prefer native language over English for transactions
- **Trust Deficit**: 85% of workers report payment delays/fraud in informal sector
- **Network Limitations**: Must work on 2G/3G networks (common in tier-2 areas)

## **1.2 Competitive Landscape Reality**

**Direct Competitors:**
- **UrbanCompany**: Premium service, expensive for target market
- **Taskrabbit/Dunzo**: Limited blue-collar focus, English-centric
- **Local WhatsApp Groups**: Primary current solution, zero trust mechanisms

**Differentiation Strategy:**
- **Persona-based profiles** (unique to market)
- **Cash payment verification** (addresses primary pain point)
- **Hyper-local matching** (sub-5km radius priority)
- **Native language UX** (8 languages from day 1)

***
