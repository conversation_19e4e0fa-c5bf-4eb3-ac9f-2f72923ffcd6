---
type: "always_apply"
---

## Doc 1: supabase.sql (Postgres schema for Supabase)
```sql
-- Extensions (Supabase)
create extension if not exists pgcrypto;
create extension if not exists pg_trgm;

-- Enums
do $$
begin
  if not exists (select 1 from pg_type where typname = 'rate_type') then
    create type rate_type as enum ('hourly', 'daily', 'fixed');
  end if;
  if not exists (select 1 from pg_type where typname = 'job_status') then
    create type job_status as enum ('draft', 'active', 'paused', 'completed', 'cancelled');
  end if;
  if not exists (select 1 from pg_type where typname = 'urgency_type') then
    create type urgency_type as enum ('normal', 'urgent');
  end if;
  if not exists (select 1 from pg_type where typname = 'application_status') then
    create type application_status as enum ('pending', 'accepted', 'rejected', 'withdrawn');
  end if;
  if not exists (select 1 from pg_type where typname = 'message_type') then
    create type message_type as enum ('text', 'image', 'document');
  end if;
  if not exists (select 1 from pg_type where typname = 'message_status') then
    create type message_status as enum ('sent', 'delivered', 'read');
  end if;
  if not exists (select 1 from pg_type where typname = 'payment_status_type') then
    create type payment_status_type as enum ('pending', 'processing', 'completed', 'failed', 'disputed', 'refunded');
  end if;
  if not exists (select 1 from pg_type where typname = 'review_type') then
    create type review_type as enum ('worker_to_poster', 'poster_to_worker');
  end if;
  if not exists (select 1 from pg_type where typname = 'dispute_status_type') then
    create type dispute_status_type as enum ('open', 'resolved', 'escalated');
  end if;
  if not exists (select 1 from pg_type where typname = 'visibility_level') then
    create type visibility_level as enum ('public', 'private', 'contacts');
  end if;
  if not exists (select 1 from pg_type where typname = 'app_language') then
    create type app_language as enum ('english', 'hindi');
  end if;
  if not exists (select 1 from pg_type where typname = 'day_of_week') then
    create type day_of_week as enum ('monday','tuesday','wednesday','thursday','friday','saturday','sunday');
  end if;
end$$;

-- Users: Profile mapped to auth.users
create table if not exists public.user_profiles (
  user_id uuid primary key references auth.users(id) on delete cascade,
  phone text unique not null check (phone ~ '^\+91[6-9]\d{9}$'),
  phone_verified boolean default false,
  email text,
  full_name text not null,
  avatar_url text,
  location text,
  is_active boolean default true,
  is_verified boolean default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

-- Worker profile
create table if not exists public.worker_profiles (
  user_id uuid primary key references auth.users(id) on delete cascade,
  primary_skill text,
  skills text[],
  experience text, -- "0-1" | "2-5" | "5-10" | "10+"
  hourly_rate integer,
  daily_rate integer,
  about text,
  phone_visible boolean default false,
  completed_jobs integer default 0,
  total_earnings integer default 0,
  rating numeric(3,2) default 0,
  review_count integer default 0,
  portfolio_photos text[],
  is_available boolean default true,
  updated_at timestamptz not null default now()
);

-- Poster profile
create table if not exists public.poster_profiles (
  user_id uuid primary key references auth.users(id) on delete cascade,
  company_name text,
  about text,
  jobs_posted integer default 0,
  total_spent integer default 0,
  rating numeric(3,2) default 0,
  review_count integer default 0,
  business_verified boolean default false,
  business_documents text[],
  updated_at timestamptz not null default now()
);

-- Worker personas
create table if not exists public.worker_personas (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  skill text not null,
  experience text,
  hourly_rate integer,
  daily_rate integer,
  is_active boolean default true,
  completed_jobs integer default 0,
  rating numeric(3,2) default 0,
  review_count integer default 0,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  unique(user_id, skill)
);

-- Availability schedule (user-level)
create table if not exists public.availability_schedules (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);
create table if not exists public.availability_time_slots (
  id uuid primary key default gen_random_uuid(),
  schedule_id uuid not null references public.availability_schedules(id) on delete cascade,
  day day_of_week not null,
  start_time time not null,
  end_time time not null,
  check (end_time > start_time)
);

-- Skills and categories + market rates
create table if not exists public.job_categories (
  id uuid primary key default gen_random_uuid(),
  name text unique not null
);
create table if not exists public.skills (
  id uuid primary key default gen_random_uuid(),
  category_id uuid references public.job_categories(id) on delete set null,
  name text not null,
  unique(category_id, name)
);
create table if not exists public.market_rates (
  id uuid primary key default gen_random_uuid(),
  skill text not null,
  location text,
  hourly_min integer,
  hourly_max integer,
  daily_min integer,
  daily_max integer,
  currency text default 'INR'
);

-- Jobs
create table if not exists public.jobs (
  id uuid primary key default gen_random_uuid(),
  poster_id uuid not null references auth.users(id) on delete cascade,
  title text not null,
  description text not null,
  category text not null,
  subcategory text,
  location text not null,
  latitude numeric(10,8),
  longitude numeric(11,8),
  rate integer not null,
  rate_type rate_type not null,
  duration text,
  requirements text[],
  skills_required text[],
  experience_level text,
  status job_status not null default 'active',
  urgency urgency_type not null default 'normal',
  photos text[],
  view_count integer default 0,
  application_count integer default 0,
  max_applications integer,
  auto_accept boolean default false,
  assigned_worker_id uuid references auth.users(id),
  started_at timestamptz,
  completed_at timestamptz,
  work_photos text[],
  payment_status payment_status_type default 'pending',
  payment_method text,
  transaction_id text,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  expires_at timestamptz
);

-- Job Bookmarks
create table if not exists public.job_bookmarks (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  job_id uuid not null references public.jobs(id) on delete cascade,
  created_at timestamptz not null default now()
);

-- Job photos (normalized)
create table if not exists public.job_photos (
  id uuid primary key default gen_random_uuid(),
  job_id uuid not null references public.jobs(id) on delete cascade,
  url text not null,
  caption text,
  uploaded_by uuid references auth.users(id),
  uploaded_at timestamptz not null default now()
);

-- Job invites (poster → worker)
create table if not exists public.job_invites (
  id uuid primary key default gen_random_uuid(),
  job_id uuid not null references public.jobs(id) on delete cascade,
  worker_id uuid not null references auth.users(id) on delete cascade,
  inviter_id uuid not null references auth.users(id) on delete cascade,
  message text,
  status application_status default 'pending',
  created_at timestamptz not null default now(),
  responded_at timestamptz
);

-- Applications
create table if not exists public.job_applications (
  id uuid primary key default gen_random_uuid(),
  job_id uuid not null references public.jobs(id) on delete cascade,
  worker_id uuid not null references auth.users(id) on delete cascade,
  persona_id uuid references public.worker_personas(id) on delete set null,
  cover_letter text,
  proposed_rate integer,
  proposed_rate_type rate_type,
  status application_status not null default 'pending',
  applied_at timestamptz not null default now(),
  responded_at timestamptz,
  has_messages boolean default false,
  last_message_at timestamptz
);

-- Conversations and messages
create table if not exists public.conversations (
  id uuid primary key default gen_random_uuid(),
  job_id uuid not null references public.jobs(id) on delete cascade,
  worker_id uuid not null references auth.users(id) on delete cascade,
  poster_id uuid not null references auth.users(id) on delete cascade,
  is_active boolean default true,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  unique(job_id, worker_id, poster_id)
);

create table if not exists public.messages (
  id uuid primary key default gen_random_uuid(),
  conversation_id uuid not null references public.conversations(id) on delete cascade,
  sender_id uuid not null references auth.users(id) on delete cascade,
  receiver_id uuid not null references auth.users(id) on delete cascade,
  text text not null,
  message_type message_type not null default 'text',
  status message_status not null default 'sent',
  timestamp timestamptz not null default now()
);

create table if not exists public.message_attachments (
  id uuid primary key default gen_random_uuid(),
  message_id uuid not null references public.messages(id) on delete cascade,
  url text not null,
  file_name text,
  file_size integer,
  mime_type text
);

-- Payments
create table if not exists public.payments (
  id uuid primary key default gen_random_uuid(),
  job_id uuid not null references public.jobs(id) on delete cascade,
  payer_id uuid not null references auth.users(id) on delete cascade,
  payee_id uuid not null references auth.users(id) on delete cascade,
  amount integer not null,
  currency text not null default 'INR',
  method text not null, -- 'cash' (current)
  status payment_status_type not null default 'pending',
  transaction_id text,
  gateway_response jsonb,
  has_dispute boolean default false,
  dispute_reason text,
  dispute_status dispute_status_type,
  description text,
  receipt_url text,
  created_at timestamptz not null default now(),
  processed_at timestamptz,
  completed_at timestamptz
);

-- Reviews
create table if not exists public.reviews (
  id uuid primary key default gen_random_uuid(),
  job_id uuid not null references public.jobs(id) on delete cascade,
  reviewer_id uuid not null references auth.users(id) on delete cascade,
  reviewee_id uuid not null references auth.users(id) on delete cascade,
  rating integer not null check (rating between 1 and 5),
  comment text,
  review_type review_type not null,
  communication_rating integer,
  work_quality_rating integer,
  timeliness_rating integer,
  professionalism_rating integer,
  is_public boolean default true,
  is_verified boolean default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

-- Notifications
create table if not exists public.notifications (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  type text not null,
  title text not null,
  message text not null,
  is_read boolean default false,
  is_urgent boolean default false,
  action_url text,
  metadata jsonb,
  created_at timestamptz not null default now()
);

-- Settings
create table if not exists public.user_settings (
  user_id uuid primary key references auth.users(id) on delete cascade,
  notifications jsonb default '{}'::jsonb,
  privacy jsonb default '{}'::jsonb,
  language app_language default 'english',
  preferences jsonb default '{}'::jsonb,
  updated_at timestamptz not null default now()
);

-- Verification documents
create table if not exists public.verification_documents (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  url text not null,
  doc_type text,
  is_verified boolean default false,
  created_at timestamptz not null default now()
);

-- Upload audit
create table if not exists public.uploads (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users(id) on delete set null,
  category text not null, -- 'profile' | 'job' | 'work_progress' | 'portfolio' | 'verification'
  job_id uuid references public.jobs(id) on delete set null,
  original_name text,
  url text not null,
  thumbnail_url text,
  size integer,
  mime_type text,
  created_at timestamptz not null default now()
);

-- Device registrations for push
create table if not exists public.notification_devices (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  device_token text not null,
  platform text not null, -- 'ios' | 'android' | 'web'
  created_at timestamptz not null default now(),
  unique(user_id, device_token)
);

-- OTP audit (for rate limiting and logs)
create table if not exists public.otp_requests (
  id uuid primary key default gen_random_uuid(),
  phone text not null,
  otp_id text,
  requested_at timestamptz not null default now(),
  status text not null default 'sent',
  ip text
);

-- Triggers: updated_at
create or replace function public.set_updated_at()
returns trigger language plpgsql as $$
begin
  new.updated_at = now();
  return new;
end$$;

drop trigger if exists set_updated_at_user_profiles on public.user_profiles;
create trigger set_updated_at_user_profiles before update on public.user_profiles
for each row execute function public.set_updated_at();

drop trigger if exists set_updated_at_worker_profiles on public.worker_profiles;
create trigger set_updated_at_worker_profiles before update on public.worker_profiles
for each row execute function public.set_updated_at();

drop trigger if exists set_updated_at_poster_profiles on public.poster_profiles;
create trigger set_updated_at_poster_profiles before update on public.poster_profiles
for each row execute function public.set_updated_at();

drop trigger if exists set_updated_at_jobs on public.jobs;
create trigger set_updated_at_jobs before update on public.jobs
for each row execute function public.set_updated_at();

drop trigger if exists set_updated_at_conversations on public.conversations;
create trigger set_updated_at_conversations before update on public.conversations
for each row execute function public.set_updated_at();

-- Counters: increment application_count on jobs
create or replace function public.inc_job_application_count()
returns trigger language plpgsql as $$
begin
  update public.jobs
     set application_count = coalesce(application_count,0) + 1
   where id = new.job_id;
  return new;
end$$;

drop trigger if exists trg_inc_job_app_count on public.job_applications;
create trigger trg_inc_job_app_count
after insert on public.job_applications
for each row execute function public.inc_job_application_count();

-- Indexes (from implementation guide)
create index if not exists idx_jobs_category on public.jobs (category);
create index if not exists idx_jobs_status_created on public.jobs (status, created_at desc);
create index if not exists idx_applications_worker_status on public.job_applications (worker_id, status);
create index if not exists idx_messages_conv_ts on public.messages (conversation_id, timestamp desc);
create index if not exists idx_notifications_user_unread on public.notifications (user_id, is_read, created_at desc);
create index if not exists idx_jobs_location on public.jobs using gin (location gin_trgm_ops);

-- RLS
alter table public.user_profiles enable row level security;
alter table public.worker_profiles enable row level security;
alter table public.poster_profiles enable row level security;
alter table public.worker_personas enable row level security;
alter table public.availability_schedules enable row level security;
alter table public.availability_time_slots enable row level security;
alter table public.job_categories enable row level security;
alter table public.skills enable row level security;
alter table public.market_rates enable row level security;
alter table public.jobs enable row level security;
alter table public.job_photos enable row level security;
alter table public.job_invites enable row level security;
alter table public.job_applications enable row level security;
alter table public.conversations enable row level security;
alter table public.messages enable row level security;
alter table public.message_attachments enable row level security;
alter table public.payments enable row level security;
alter table public.reviews enable row level security;
alter table public.notifications enable row level security;
alter table public.user_settings enable row level security;
alter table public.verification_documents enable row level security;
alter table public.uploads enable row level security;
alter table public.notification_devices enable row level security;
alter table public.otp_requests enable row level security;

-- Policies: only owners can update their own profile; profiles readable to self; minimal public profile select can be added via a view later.
create policy "read_own_profile"
on public.user_profiles for select
to authenticated
using (auth.uid() = user_id);

create policy "update_own_profile"
on public.user_profiles for update
to authenticated
using (auth.uid() = user_id)
with check (auth.uid() = user_id);

-- Worker/Poster profiles (owner read/write)
create policy "rw_own_worker_profile" on public.worker_profiles
for all to authenticated
using (auth.uid() = user_id)
with check (auth.uid() = user_id);

create policy "rw_own_poster_profile" on public.poster_profiles
for all to authenticated
using (auth.uid() = user_id)
with check (auth.uid() = user_id);

-- Personas (owner rw, public read for discovery)
create policy "persona_read_all" on public.worker_personas
for select to authenticated using (true);

create policy "persona_rw_owner" on public.worker_personas
for all to authenticated
using (auth.uid() = user_id)
with check (auth.uid() = user_id);

-- Schedules (owner rw)
create policy "schedule_rw_owner" on public.availability_schedules
for all to authenticated
using (auth.uid() = user_id)
with check (auth.uid() = user_id);

create policy "slots_rw_viaparent" on public.availability_time_slots
for all to authenticated
using (
  exists (select 1 from public.availability_schedules s
          where s.id = schedule_id and s.user_id = auth.uid())
)
with check (
  exists (select 1 from public.availability_schedules s
          where s.id = schedule_id and s.user_id = auth.uid())
);

-- Catalog tables read-only
create policy "read_categories" on public.job_categories for select to authenticated using (true);
create policy "read_skills" on public.skills for select to authenticated using (true);
create policy "read_market_rates" on public.market_rates for select to authenticated using (true);

-- Jobs: anyone authenticated can read active; owners (poster) rw
create policy "jobs_select_active" on public.jobs
for select to authenticated
using (status in ('active','paused','completed'));

create policy "jobs_rw_owner" on public.jobs
for all to authenticated
using (auth.uid() = poster_id)
with check (auth.uid() = poster_id);

-- Job photos: read if job visible or owner; write owner
create policy "job_photos_read" on public.job_photos
for select to authenticated
using (
  exists (select 1 from public.jobs j
          where j.id = job_id and (j.status in ('active','paused','completed') or j.poster_id = auth.uid()))
);

create policy "job_photos_write_owner" on public.job_photos
for all to authenticated
using (
  exists (select 1 from public.jobs j where j.id = job_id and j.poster_id = auth.uid())
)
with check (
  exists (select 1 from public.jobs j where j.id = job_id and j.poster_id = auth.uid())
);

-- Invites: participants read; poster writes
create policy "invites_read_participants" on public.job_invites
for select to authenticated
using (inviter_id = auth.uid() or worker_id = auth.uid());

create policy "invites_write_poster" on public.job_invites
for all to authenticated
using (inviter_id = auth.uid())
with check (inviter_id = auth.uid());

-- Applications: worker rw own; job poster read
create policy "apps_rw_worker" on public.job_applications
for all to authenticated
using (worker_id = auth.uid())
with check (worker_id = auth.uid());

create policy "apps_read_poster" on public.job_applications
for select to authenticated
using (
  exists (select 1 from public.jobs j where j.id = job_id and j.poster_id = auth.uid())
);

-- Conversations: participants rw
create policy "conv_rw_participants" on public.conversations
for all to authenticated
using (worker_id = auth.uid() or poster_id = auth.uid())
with check (worker_id = auth.uid() or poster_id = auth.uid());

-- Messages: participants rw
create policy "msg_rw_participants" on public.messages
for all to authenticated
using (
  exists (select 1 from public.conversations c
          where c.id = conversation_id and (c.worker_id = auth.uid() or c.poster_id = auth.uid()))
)
with check (
  exists (select 1 from public.conversations c
          where c.id = conversation_id and (c.worker_id = auth.uid() or c.poster_id = auth.uid()))
);

create policy "msg_attach_rw_participants" on public.message_attachments
for all to authenticated
using (
  exists (select 1 from public.messages m
          join public.conversations c on c.id = m.conversation_id
          where m.id = message_id and (c.worker_id = auth.uid() or c.poster_id = auth.uid()))
)
with check (
  exists (select 1 from public.messages m
          join public.conversations c on c.id = m.conversation_id
          where m.id = message_id and (c.worker_id = auth.uid() or c.poster_id = auth.uid()))
);

-- Payments: job participants rw
create policy "payments_rw_participants" on public.payments
for all to authenticated
using (payer_id = auth.uid() or payee_id = auth.uid())
with check (payer_id = auth.uid() or payee_id = auth.uid());

-- Reviews: reviewer rw; reviewee read
create policy "reviews_rw_reviewer" on public.reviews
for all to authenticated
using (reviewer_id = auth.uid())
with check (reviewer_id = auth.uid());

create policy "reviews_read_reviewee" on public.reviews
for select to authenticated
using (reviewee_id = auth.uid());

-- Notifications: owner rw
create policy "notif_rw_owner" on public.notifications
for all to authenticated
using (user_id = auth.uid())
with check (user_id = auth.uid());

-- Settings: owner rw
create policy "settings_rw_owner" on public.user_settings
for all to authenticated
using (user_id = auth.uid())
with check (user_id = auth.uid());

-- Verification documents: owner rw
create policy "verif_docs_rw_owner" on public.verification_documents
for all to authenticated
using (user_id = auth.uid())
with check (user_id = auth.uid());

-- Uploads: owner rw
create policy "uploads_rw_owner" on public.uploads
for all to authenticated
using (user_id = auth.uid())
with check (user_id = auth.uid());

-- Devices: owner rw
create policy "devices_rw_owner" on public.notification_devices
for all to authenticated
using (user_id = auth.uid())
with check (user_id = auth.uid());

-- OTP audit: anon insert (send-otp), authenticated read own phone
create policy "otp_insert_anon" on public.otp_requests
for insert to anon
with check (true);

create policy "otp_read_self_phone" on public.otp_requests
for select to authenticated
using (true);

-- Done
```