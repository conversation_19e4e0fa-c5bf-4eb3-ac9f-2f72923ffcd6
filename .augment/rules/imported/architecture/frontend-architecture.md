---
type: "manual"
---

# Frontend Architecture

## Component Architecture

**Component Organization:**
```
src/
├── components/           # Reusable UI components
│   ├── auth/            # Authentication components
│   ├── persona/         # Multi-persona components
│   ├── jobs/            # Job-related components
│   ├── reviews/         # Review and rating components
│   └── common/          # Shared components
├── screens/             # Screen components
│   ├── AuthScreens/
│   ├── PersonaScreens/
│   ├── JobScreens/
│   └── ProfileScreens/
├── navigation/          # Navigation configuration
├── services/            # API and business logic
├── stores/              # Zustand state stores
├── utils/               # Helper functions
└── types/               # TypeScript definitions
```

**Component Template:**
```typescript
// components/persona/PersonaCard.tsx
import React from 'react';
import { Box, Text, Image, Badge, HStack } from 'native-base';
import { WorkerPersona } from '../../types/database';

interface PersonaCardProps {
  persona: WorkerPersona;
  onPress: () => void;
  isActive?: boolean;
}

export const PersonaCard: React.FC<PersonaCardProps> = ({
  persona,
  onPress,
  isActive = false
}) => {
  return (
    <Box
      bg={isActive ? 'blue.50' : 'white'}
      borderWidth={isActive ? 2 : 1}
      borderColor={isActive ? 'blue.500' : 'gray.200'}
      rounded="lg"
      p={4}
      shadow={1}
      onTouchEnd={onPress}
    >
      <HStack space={3} alignItems="center">
        <Image
          source={{ uri: persona.profile_image_url }}
          alt={persona.title}
          size="md"
          rounded="full"
        />
        <Box flex={1}>
          <Text fontSize="lg" fontWeight="bold">
            {persona.title}
          </Text>
          <Text color="gray.600" fontSize="sm">
            {persona.experience_years} years experience
          </Text>
          <HStack space={2} mt={1}>
            <Badge colorScheme="green">
              {persona.skill_category}
            </Badge>
            <Badge variant="outline">
              ₹{persona.daily_rate}/day
            </Badge>
          </HStack>
        </Box>
      </HStack>
    </Box>
  );
};
```

## State Management Architecture

**State Structure:**
```typescript
// stores/authStore.ts
import { create } from 'zustand';
import { User } from '../types/database';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (phone: string) => Promise<void>;
  verifyOTP: (otp: string) => Promise<void>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,
  
  login: async (phone: string) => {
    set({ isLoading: true });
    try {
      await authService.sendOTP(phone);
      // Handle success
    } catch (error) {
      // Handle error
    } finally {
      set({ isLoading: false });
    }
  },
  
  verifyOTP: async (otp: string) => {
    // Implementation
  },
  
  logout: () => {
    set({ user: null, isAuthenticated: false });
  },
  
  updateProfile: async (updates: Partial<User>) => {
    // Implementation
  }
}));
```

**State Management Patterns:**
- Separate stores for different domains (auth, personas, jobs)
- Async actions with loading states
- Optimistic updates for better UX
- Persistent state for critical data (auth tokens, preferences)

## Routing Architecture

**Route Organization:**
```
navigation/
├── AuthNavigator.tsx     # Authentication flow
├── MainNavigator.tsx     # Main app navigation
├── PersonaNavigator.tsx  # Persona management
└── JobNavigator.tsx      # Job-related screens
```

**Protected Route Pattern:**
```typescript
// navigation/ProtectedRoute.tsx
import React from 'react';
import { useAuthStore } from '../stores/authStore';
import { AuthNavigator } from './AuthNavigator';
import { MainNavigator } from './MainNavigator';

export const ProtectedRoute: React.FC = () => {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  
  return isAuthenticated ? <MainNavigator /> : <AuthNavigator />;
};
```

## Frontend Services Layer

**API Client Setup:**
```typescript
// services/supabase.ts
import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});
```

**Service Example:**
```typescript
// services/personaService.ts
import { supabase } from './supabase';
import { WorkerPersona, CreatePersonaData } from '../types/database';

export const personaService = {
  async getUserPersonas(userId: string): Promise<WorkerPersona[]> {
    const { data, error } = await supabase
      .from('worker_personas')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },
  
  async createPersona(personaData: CreatePersonaData): Promise<WorkerPersona> {
    const { data, error } = await supabase
      .from('worker_personas')
      .insert(personaData)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },
  
  async updatePersonaAvailability(
    personaId: string, 
    availability: object
  ): Promise<void> {
    const { error } = await supabase
      .from('worker_personas')
      .update({ 
        availability_pattern: availability,
        updated_at: new Date().toISOString()
      })
      .eq('id', personaId);
    
    if (error) throw error;
  },
};
```

This completes the Frontend Architecture section. The architecture is designed for React Native with Expo, focusing on the multi-persona job matching platform requirements with clean separation of concerns and optimized performance for Indian mobile networks.