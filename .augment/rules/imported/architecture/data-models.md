---
type: "manual"
---

# Data Models

## User
**Purpose:** Base user entity supporting both workers and job posters, with phone-based authentication for the Indian market.

**Key Attributes:**
- id: UUID - Primary identifier
- phone: string - +91 phone number (unique)
- email: string - Optional email address  
- full_name: string - User's full name
- preferred_language: enum - One of 8 supported Indian languages
- location: Point - Current/primary location for matching
- user_type: enum - 'worker', 'poster', 'both'
- is_verified: boolean - Phone verification status
- created_at: timestamp - Registration date
- last_active_at: timestamp - For matching algorithms

**TypeScript Interface:**
```typescript
interface User {
  id: string;
  phone: string;
  email?: string;
  full_name: string;
  preferred_language: SupportedLanguage;
  location: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  user_type: 'worker' | 'poster' | 'both';
  is_verified: boolean;
  created_at: string;
  last_active_at: string;
}

type SupportedLanguage = 'hindi' | 'english' | 'tamil' | 'telugu' | 'bengali' | 'marathi' | 'gujarati' | 'kannada';
```

**Relationships:**
- One-to-many with WorkerPersonas
- One-to-many with Jobs (as poster)
- One-to-many with JobApplications
- One-to-many with Reviews (given and received)

## WorkerPersona
**Purpose:** Core feature enabling multi-skilled workers to maintain separate professional identities for different services (electrician, driver, cook, etc.).

**Key Attributes:**
- id: UUID - Primary identifier
- user_id: UUID - Foreign key to User
- title: string - "Experienced Electrician", "Part-time Driver"
- skill_category: enum - Primary skill category
- skill_subcategories: array - Related skills
- description: text - Persona-specific bio
- experience_years: integer - Years of experience in this skill
- hourly_rate: decimal - Rate in INR
- daily_rate: decimal - Daily rate in INR
- is_rate_negotiable: boolean - Flexibility on pricing
- availability_pattern: JSON - Weekly availability schedule
- travel_radius_km: integer - Willing to travel distance
- is_active: boolean - Currently accepting jobs for this persona
- total_jobs_completed: integer - Performance metric
- average_rating: decimal - Persona-specific rating
- profile_image_url: string - Persona-specific photo

**TypeScript Interface:**
```typescript
interface WorkerPersona {
  id: string;
  user_id: string;
  title: string;
  skill_category: SkillCategory;
  skill_subcategories: string[];
  description: string;
  experience_years: number;
  hourly_rate?: number;
  daily_rate?: number;
  is_rate_negotiable: boolean;
  availability_pattern: WeeklyAvailability;
  travel_radius_km: number;
  is_active: boolean;
  total_jobs_completed: number;
  average_rating: number;
  profile_image_url?: string;
  created_at: string;
  updated_at: string;
}

type SkillCategory = 'electrical' | 'plumbing' | 'carpentry' | 'cooking' | 'cleaning' | 'driving' | 'delivery' | 'security' | 'gardening' | 'tutoring';

interface WeeklyAvailability {
  [day: string]: TimeSlot[];
}

interface TimeSlot {
  start: string; // "09:00"
  end: string;   // "17:00"
}
```

**Relationships:**
- Many-to-one with User
- One-to-many with JobApplications
- One-to-many with Reviews
- Many-to-many with Jobs (through applications)

## Job
**Purpose:** Job postings from employers seeking workers, with hyper-local matching and urgency levels.

**Key Attributes:**
- id: UUID - Primary identifier
- poster_id: UUID - Foreign key to User
- title: string - Job title
- description: text - Job description (supports multiple languages)
- skill_category: enum - Required skill category
- location: Point - Job location with address
- job_type: enum - 'one_time', 'recurring', 'permanent'
- urgency: enum - 'low', 'normal', 'high', 'urgent'
- budget_min: decimal - Minimum budget in INR
- budget_max: decimal - Maximum budget in INR
- estimated_duration_hours: integer - Expected job duration
- requirements: text - Specific requirements
- preferred_gender: enum - 'any', 'male', 'female'
- status: enum - 'active', 'paused', 'filled', 'cancelled', 'expired'
- applications_count: integer - Number of applications received
- views_count: integer - Number of views
- expires_at: timestamp - Job expiry date
- created_at: timestamp - Posted date

**TypeScript Interface:**
```typescript
interface Job {
  id: string;
  poster_id: string;
  title: string;
  description: string;
  skill_category: SkillCategory;
  location: {
    latitude: number;
    longitude: number;
    address: string;
    landmark?: string;
  };
  job_type: 'one_time' | 'recurring' | 'permanent';
  urgency: 'low' | 'normal' | 'high' | 'urgent';
  budget_min: number;
  budget_max: number;
  estimated_duration_hours: number;
  requirements?: string;
  preferred_gender: 'any' | 'male' | 'female';
  status: 'active' | 'paused' | 'filled' | 'cancelled' | 'expired';
  applications_count: number;
  views_count: number;
  expires_at: string;
  created_at: string;
  updated_at: string;
}
```

**Relationships:**
- Many-to-one with User (poster)
- One-to-many with JobApplications
- One-to-many with Reviews

## JobApplication
**Purpose:** Connection between worker personas and jobs, tracking application status and communication.

**Key Attributes:**
- id: UUID - Primary identifier
- job_id: UUID - Foreign key to Job
- worker_persona_id: UUID - Foreign key to WorkerPersona
- status: enum - Application status
- proposed_rate: decimal - Worker's proposed rate
- message: text - Application message
- applied_at: timestamp - Application timestamp
- responded_at: timestamp - When poster responded
- poster_response: text - Poster's response message

**TypeScript Interface:**
```typescript
interface JobApplication {
  id: string;
  job_id: string;
  worker_persona_id: string;
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
  proposed_rate?: number;
  message?: string;
  applied_at: string;
  responded_at?: string;
  poster_response?: string;
}
```

**Relationships:**
- Many-to-one with Job
- Many-to-one with WorkerPersona
- One-to-one with Review (after job completion)

## Review
**Purpose:** Trust verification system through ratings and reviews, supporting both worker and poster reviews.

**Key Attributes:**
- id: UUID - Primary identifier
- job_id: UUID - Foreign key to Job
- reviewer_id: UUID - User who wrote review
- reviewee_id: UUID - User being reviewed
- worker_persona_id: UUID - Specific persona being reviewed (if applicable)
- rating: integer - 1-5 star rating
- review_text: text - Written review
- review_type: enum - 'worker_review', 'poster_review'
- is_verified: boolean - Review verification status

**TypeScript Interface:**
```typescript
interface Review {
  id: string;
  job_id: string;
  reviewer_id: string;
  reviewee_id: string;
  worker_persona_id?: string;
  rating: 1 | 2 | 3 | 4 | 5;
  review_text?: string;
  review_type: 'worker_review' | 'poster_review';
  is_verified: boolean;
  created_at: string;
}
```

**Relationships:**
- Many-to-one with Job
- Many-to-one with User (reviewer)
- Many-to-one with User (reviewee)
- Many-to-one with WorkerPersona (optional)
