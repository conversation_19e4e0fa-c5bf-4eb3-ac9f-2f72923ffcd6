---
type: "manual"
---

# API Specification

Based on the Supabase client chosen in the Tech Stack, here are the API patterns and key endpoints that will be auto-generated by Supabase, along with custom Edge Functions for complex business logic.

## Supabase Auto-Generated REST API

Supabase automatically generates REST endpoints for all database tables with full CRUD operations:

**Base URL:** `https://your-project.supabase.co/rest/v1/`

**Key Endpoints:**

```yaml